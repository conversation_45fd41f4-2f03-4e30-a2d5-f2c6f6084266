package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// orderId 为 test
// 获取力量排行榜信息
func (_ KnightsHandlers) GetPowerRankInfoHandler(ctx *wctx.Context, req *wrpc.GetPowerRankInfoRequest) (*wrpc.GetPowerRankInfoReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		ret := rank.GetPowerRankInfoList(ctx, req.UID, 99)
		replay := wrpc.GetPowerRankInfoReply{
			Result: ret,
		}
		rawResp = &replay
		err = nil
	})
	return rawResp.(*wrpc.GetPowerRankInfoReply), err
}
