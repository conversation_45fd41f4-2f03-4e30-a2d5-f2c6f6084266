package main_line_stage

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/benefit"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/function_open"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/item"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"encoding/json"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

func FormatHeroInfo(ctx context.Context, uid int64, battlePos map[int32]*minirpc.BattlePosInfo) string {
	heroInfoMap := make(map[string]map[string]interface{})
	for _, battleInfo := range battlePos {

		hero, _ := model.GetHero(ctx, uid, battleInfo.HeroId)
		if hero != nil {
			levelLine := cfg_mgr.Cfg.HeroLevelTable.Get(hero.LevelId())
			starLine := cfg_mgr.Cfg.HeroStarTable.Get(hero.StarId())
			geneLine := cfg_mgr.Cfg.HeroGeneTable.Get(hero.GeneConfigId())
			heroInfoMap[strconv.FormatInt(int64(battleInfo.HeroId), 10)] = map[string]interface{}{
				"level":      levelLine.HeroLevel,
				"star_level": starLine.HeroStarLevel,
				"power":      hero.Power(),
				"gene_level": geneLine.HeroGeneLevel,
			}

		}
	}
	battlePosJsonData, _ := json.Marshal(heroInfoMap)
	return string(battlePosJsonData)
}

func FinishMainStage(ctx context.Context, uid int64, LevelStruct *wrpc.LevelStruct, isFail bool, stageId int32, isElite bool, perfectIndex int32, MaxHpPercent float32, max_time int32, heroHealth map[int32]float32, heroAtk map[int32]float32, bossProgress map[int32]float32) ([]*wrpc.Rewards, error) {
	stage, err := model.GetMainLineStage(ctx, uid)
	if err != nil {
		return nil, err
	}

	if stage.CurStageFinish() {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("stage is finish")
	}
	curLine := cfg_mgr.Cfg.MainLevelTable.Get(stageId)
	if curLine == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("invalid stage id")
	}
	if !function_open.CheckReqChapter(ctx, uid, curLine.ChapterLevelRef.Unlock) {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("need unlock chapter first")
	}

	// 确定关卡类型
	questType := "tower"
	if isElite {
		questType = "tower_elite"
	}
	if curLine.LevelType == servercfg.LevelType_ParkOur {
		questType = "parkour"
	}

	// 获取阵容信息
	user, _ := model.GetUserModel(ctx, uid)
	battlePos := user.BattlePosInfo()
	battlePosJsonData := FormatHeroInfo(ctx, uid, battlePos)
	// 获取羁绊信息
	bondsInfo := getBonds(ctx, uid)
	bossProgressJsonData, _ := json.Marshal(bossProgress)

	// 构建进度信息
	progressInfo := map[string]interface{}{
		"round":         stage.CurNormalCardsRefreshTimes(),
		"boss_progress": string(bossProgressJsonData), // 转换为百分比
		"level":         GetRougeLevel(ctx, uid, 0),
		"step":          perfectIndex,
	}

	// 构建打点数据

	bondsJsonData, _ := json.Marshal(bondsInfo)
	progressInfoJsonData, _ := json.Marshal(progressInfo)
	selectIdListJsonData, _ := json.Marshal(stage.CurSelectCardIds())

	// ... 原有的关卡完成逻辑 ...
	totalRewards := map[int32]int64{}
	if err != nil {
		return nil, err
	}
	//stageId = stage.StageId() + 1 //test
	if stageId > stage.UnlockStageId() {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("more than unlock stage id")
	}
	curId := stageId

	nextLine := cfg_mgr.Cfg.MainLevelTable.Get(curId + 1)
	nextChapterId := curLine.Chapter
	if nextLine != nil {
		nextChapterId = nextLine.Chapter
		if nextLine.Id > stage.UnlockStageId() {
			if !isFail {
				//发放首过奖励
				sequence := util.GenerateSequenceString()
				if curLine.ExtraReward != nil {
					for _, v := range curLine.ExtraReward {
						model.AddItem(ctx, uid, v.RewardType, int64(v.RewardValue), bi.ItemFlowReason_FINISH_STAGE, strconv.Itoa(int(stageId)), sequence)
					}
				}
				stage.SetUnlockStageId(ctx, nextLine.Id)
			}

		}
	}
	stage.SetStageId(ctx, curId)
	stage.SetCurStageFinish(ctx, true)

	stageSruct := stage.LevelStruct()
	if curLine.Chapter != nextChapterId {
		stageSruct = nil
	}
	if stageSruct == nil {
		stageSruct = []*minirpc.LevelStruct{}
	}
	curLevelStruct := &minirpc.LevelStruct{
		CardIds:  LevelStruct.CardIds,
		TotalExp: LevelStruct.TotalExp,
		HeroHp:   LevelStruct.HeroHp,
	}
	if curLevelStruct.CardIds == nil {
		curLevelStruct.CardIds = []int32{}
	}

	stageSruct = append(stageSruct, curLevelStruct)
	stage.SetLevelStruct(ctx, stageSruct)
	//stageRewards := stage.StageRewards()
	//if stageRewards != nil {
	//	for k, v := range stageRewards {
	//		if totalRewards[k] == 0 {
	//			itemIdsArr = append(itemIdsArr, k)
	//		}
	//		totalRewards[k] += v
	//	}
	//}

	//获取挑战奖励
	challengeRewardId := int32(0)
	if isElite {
		challengeRewardId = curLine.EliteChallengeReward
	} else {
		challengeRewardId = curLine.CommonChallengeReward
	}
	challengeRewardLine := cfg_mgr.Cfg.MainLevelRewardTable.Get(challengeRewardId)
	if challengeRewardLine != nil {
		rougeLevel := GetRougeLevel(ctx, uid, 0)

		if rougeLevel > 0 {
			rougeRatio := float32(0)
			rougeRatioLines := cfg_mgr.Cfg.MainLevelRewardRatioTable.FilterSlice(func(v *servercfg.MainLevelRewardRatioTableCfg) bool {
				return true
			})
			sort.Slice(rougeRatioLines, func(i, j int) bool {
				return rougeRatioLines[i].MaxRougeLevel < rougeRatioLines[j].MaxRougeLevel
			})
			for _, v := range rougeRatioLines {
				if v.MaxRougeLevel > rougeLevel {
					break
				}
				rougeRatio = v.RewardRatio
			}
			//如果到达最大关卡，直接设置为 1
			mainChapterLevels := cfg_mgr.Cfg.MainChapterLevelTable.FilterSlice(func(v *servercfg.MainChapterLevelTableCfg) bool {
				return v.Chapter == stage.StageId() && v.Level == rougeLevel
			})
			if mainChapterLevels != nil && len(mainChapterLevels) != 0 && mainChapterLevels[0].IsMax {
				rougeRatio = 1
			}
			for _, v := range challengeRewardLine.Reward {
				totalRewards[v.RewardType] += int64(math.Round(float64(v.RewardValue) * float64(rougeRatio)))
			}
		}

	}
	var retRewards []*wrpc.Rewards
	sequence := util.GenerateSequenceString()
	totalRewards, _ = model.ChangeItem(ctx, uid, totalRewards)
	for k, v := range totalRewards {
		retRewards = append(retRewards, &wrpc.Rewards{
			ItemId:    k,
			ItemValue: v,
		})
		model.AddItem(ctx, uid, k, v, bi.ItemFlowReason_FINISH_STAGE, strconv.Itoa(int(stageId)), sequence)
	}

	//设置对应关卡的信息
	if !isFail {
		stageFinishStatus := stage.StageFinishStatus()
		if stageFinishStatus == nil {
			stageFinishStatus = map[int32]*minirpc.StageFinishStatus{}
		}

		curFinishStatus := stageFinishStatus[curId]
		if curFinishStatus == nil {
			curFinishStatus = &minirpc.StageFinishStatus{
				StageId:       stageId,
				PerfectStatus: perfectIndex,
			}
		}
		curFinishStatus.PerfectStatus = util.MaxInt32(perfectIndex, curFinishStatus.PerfectStatus)
		curFinishStatus.MaxHpPercent = util.MaxFloat32(MaxHpPercent, curFinishStatus.MaxHpPercent)
		if max_time > curFinishStatus.MaxTime {
			curFinishStatus.MaxTime = max_time
		}
		if curFinishStatus.MinTime == 0 {
			curFinishStatus.MinTime = max_time
		} else {
			curFinishStatus.MinTime = util.MinInt32(curFinishStatus.MinTime, max_time)
		}

		if isElite {
			curFinishStatus.FinishElite = true
		}
		stageFinishStatus[curId] = curFinishStatus
		stage.SetStageFinishStatus(ctx, stageFinishStatus)
	}

	//city.GetIdleReward(ctx, uid)
	if !isFail {
		updateStageLevelRank(ctx, uid, stageId, max_time, battlePos)
		var model string
		if isElite {
			model = "hard"
		} else {
			model = "common"
		}
		quest.Notify(ctx, uid, servercfg.TaskType_LevelPass, map[string]string{"level": strconv.Itoa(int(stageId)), "mode": model}, 1)
		quest.Notify(ctx, uid, servercfg.TaskType_LevelPassTo, map[string]string{"level": strconv.Itoa(int(stageId)), "mode": model}, 1)
		quest.Notify(ctx, uid, servercfg.TaskType_TotalMainStar, map[string]string{}, 1)
	}

	// 构建combat_info
	combatInfo := map[string]map[int32]float32{
		"health": heroHealth,
		"attack": heroAtk,
	}
	combatInfoJson, _ := json.Marshal(combatInfo)

	// 修改日志记录部分，添加combat_info字段
	retRewardsJsonData, _ := json.Marshal(totalRewards)
	logger.LogCustom(ctx, uid, "quest", logger.NewDetail().
		Put("action", "finish").
		Put("version", "2.0.22").
		Put("quest_type", questType).
		Put("group_id", curLine.Chapter).
		Put("quest_id", 1).
		Put("hero_info", battlePosJsonData).
		Put("bonds", string(bondsJsonData)).
		Put("elapse_time", max_time).
		Put("result", map[bool]int{true: -1, false: 1}[isFail]).
		Put("progress_info", string(progressInfoJsonData)).
		Put("refresh_type", stage.CurPaidRefreshTimes()[stageId]).
		Put("select_id_list", string(selectIdListJsonData)).
		Put("rewards", string(retRewardsJsonData)).
		Put("time", time.Now().Unix()-stage.CurStageStartTime()).
		Put("combat_info", string(combatInfoJson)))

	return retRewards, nil
}

func updateStageLevelRank(ctx context.Context, uid int64, stageId int32, finishTime int32, battleInfo map[int32]*minirpc.BattlePosInfo) {
	if function_open.CheckReqMainLevel(ctx, uid, constDef.FUNCTION_RANK) {
		//更新总排行榜
		rank.SetStageLevelScore(uid, stageId, finishTime)
	}

	//更新单独关卡排行榜
	userModel, _ := model.GetUserModel(ctx, uid)
	infoModel := model.GetGlobalStageLevelModel(ctx, userModel.WorldId())
	if infoModel == nil {
		return
	}
	levelInfos := infoModel.LevelInfo()
	if levelInfos == nil {
		levelInfos = map[int32]*minirpc.GlobalStagePerLevel{}
	}
	levelInfo := levelInfos[stageId]
	if levelInfo == nil {
		levelInfo = &minirpc.GlobalStagePerLevel{}
	}
	playerScores := levelInfo.PlayerScore
	if playerScores == nil {
		playerScores = []*minirpc.PerPlayerScore{}
	}
	if len(playerScores) >= 5 {
		return
	}
	challengeHero := map[int32]int32{}
	for k, v := range battleInfo {
		challengeHero[k] = v.HeroId
	}
	playerScores = append(playerScores, &minirpc.PerPlayerScore{
		Uid:           uid,
		Score:         finishTime,
		ChallengeTime: time.Now().Unix(),
		ChallengeHero: challengeHero,
	})
	//sort.Slice(playerScores, func(i, j int) bool {
	//	return playerScores[i].Score < playerScores[j].Score
	//})
	////只要前5名
	//maxLen := 5
	//maxLen = util.MinInt(maxLen, len(playerScores))
	//playerScores = playerScores[0:maxLen]
	levelInfo.PlayerScore = playerScores
	levelInfos[stageId] = levelInfo
	infoModel.SetLevelInfo(ctx, levelInfos)

}

func StartMainStage(ctx context.Context, uid int64, stageId int32, IsElite bool) error {
	stage, err := model.GetMainLineStage(ctx, uid)
	if err != nil {
		return err
	}
	stage.SetStageRewards(ctx, nil)
	//扣除体力道具
	curLine := cfg_mgr.Cfg.MainLevelTable.Get(stageId)
	if curLine != nil {
		if !function_open.CheckReqChapter(ctx, uid, curLine.ChapterLevelRef.Unlock) {
			return kdmerr.SysInvalidArguments.CastErrorf("need unlock chapter first")
		}
		deductionValue := cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_ENERGY_LEVEL_DEDUCTION)
		sequence := util.GenerateSequenceString()
		err = consumable.DeductItem(ctx, uid, constDef.ITEM_ENERGY_BASE, deductionValue, bi.ItemFlowReason_START_STAGE, strconv.Itoa(int(stageId)), sequence)
	}

	stage.SetStageId(ctx, stageId)

	stage.SetCurKillMonster(ctx, 0)
	stage.SetCurRefreshRougeLevel(ctx, 1)
	stage.SetCurPaidRefreshTimes(ctx, map[int32]int32{})
	stage.SetIsElite(ctx, IsElite)
	stage.SetKilledCardIds(ctx, []int32{})
	stage.SetEliteRefreshCardIds(ctx, nil)
	stage.SetCurEliteCardsRefreshTimes(ctx, 1) // 初始化为1
	stage.SetCurNormalCardsRefreshTimes(ctx, 1)
	stage.SetCurStageFinish(ctx, false)
	//初始化英雄上阵卡
	cardIds := []int32{}
	userModel, _ := model.GetUserModel(ctx, uid)
	battlePos := userModel.BattlePosInfo()
	if battlePos != nil {
		for _, v := range battlePos {
			rougeTabGroupLines := cfg_mgr.Cfg.RougeTabGroupTable.FilterSlice(func(line *servercfg.RougeTabGroupTableCfg) bool {
				return line.Hero == v.HeroId
			})
			if len(rougeTabGroupLines) == 0 {
				continue
			}
			rougeTabLies := cfg_mgr.Cfg.RougeTabTable.FilterSlice(func(line *servercfg.RougeTabTableCfg) bool {
				return line.RougeTabGroup == rougeTabGroupLines[0].Id && line.RougeTabType == servercfg.RougeTabType_ConfigTab
			})
			if len(rougeTabLies) == 0 {
				continue
			}
			cardIds = append(cardIds, rougeTabLies[0].Id)
		}
	}
	stage.SetCurSelectCardIds(ctx, cardIds)
	stage.SetCurStageStartTime(ctx, time.Now().Unix())
	var model string
	if IsElite {
		model = "hard"
	} else {
		model = "common"
	}
	questType := "tower"
	if IsElite {
		questType = "tower_elite"
	}
	if curLine.LevelType == servercfg.LevelType_ParkOur {
		questType = "parkour"
	}
	//quest.Notify(ctx, uid, servercfg.TaskType_LevelBeginTo, map[string]string{"level": strconv.Itoa(int(stageId)), "mode": model}, 1)
	quest.Notify(ctx, uid, servercfg.TaskType_LevelBegin, map[string]string{"level": strconv.Itoa(int(stageId)), "mode": model}, 1)
	bondsJsonData, _ := json.Marshal(getBonds(ctx, uid))
	battlePosJsonData := FormatHeroInfo(ctx, uid, battlePos)
	logger.LogCustom(ctx, uid, "quest", logger.NewDetail().
		Put("action", "start").
		Put("version", "2.0.22").
		Put("quest_type", questType).
		Put("group_id", curLine.Chapter).
		Put("quest_id", 1).
		Put("hero_info", string(battlePosJsonData)).
		Put("bonds", string(bondsJsonData)))
	return nil
}

func getBonds(ctx context.Context, uid int64) []int32 {
	user, _ := model.GetUserModel(ctx, uid)
	battlePos := user.BattlePosInfo()
	if battlePos == nil {
		return nil
	}

	// 统计每个英雄的数量
	heroTypeCounts := make(map[int32]int32)
	for _, pos := range battlePos {
		if pos.HeroId != 0 {
			heroLine := cfg_mgr.Cfg.HeroTable.Get(pos.HeroId)
			heroTypeCounts[int32(heroLine.HeroType)]++
		}
	}

	// 获取所有可能的羁绊配置
	bondsTable := cfg_mgr.Cfg.HeroBondsTable.GetAll()
	var triggeredBonds []int32

	// 遍历所有羁绊配置
	for bondId, bondCfg := range bondsTable {
		// 解析 Remark 字段，获取触发所需的英雄数量
		remarkStr := bondCfg.Remark

		// 处理类似 "3" 或 "3+2" 这样的格式
		parts := strings.Split(remarkStr, "+")
		newHeroCounts := util.CopyMap(heroTypeCounts)
		flag := false
		for _, part := range parts {
			requiredCount, err := strconv.Atoi(strings.TrimSpace(part))
			if err != nil {
				continue
			}
			// 检查是否有足够数量的同类型英雄
			flag = false
			for _, c := range newHeroCounts {
				if c >= int32(requiredCount) {
					flag = true
				}
			}

		}
		if flag {
			triggeredBonds = append(triggeredBonds, bondId)
		}

	}

	// 按 ID 排序
	sort.Slice(triggeredBonds, func(i, j int) bool {
		return triggeredBonds[i] < triggeredBonds[j]
	})

	return triggeredBonds
}

func ResetMainStage(ctx context.Context, uid int64, stageId int32) error {
	stage, err := model.GetMainLineStage(ctx, uid)
	if err != nil {
		return err
	}
	// 	preLine := cfg_mgr.Cfg.MainLevelTable.Get(stage.StageId())
	if stageId > stage.StageId() {
		return kdmerr.SysInvalidArguments.CastErrorf("invalid stage id reset stageId = %d, curStageId = %d", stageId, stage.StageId())
	}
	curLine := cfg_mgr.Cfg.MainLevelTable.Get(stageId)
	if curLine == nil && stageId != 0 {
		return kdmerr.SysInvalidArguments.CastErrorf("invalid stage id")
	}
	// 	if preLine.Chapter != curLine.Chapter {
	// 		return kdmerr.SysInvalidArguments.CastErrorf("invalid stage id")
	// 	}
	stage.SetStageId(ctx, stageId)
	//stageSruct := stage.LevelStruct()
	//stageSruct = stageSruct[:stageId]
	stage.SetLevelStruct(ctx, nil)
	return nil
}

func KillMonster(ctx context.Context, uid int64, monsterId []int32, isUpLevel bool, dungeonType int32, w http.ResponseWriter) ([]*wrpc.Rewards, []int32, []int32, int32, error) {
	stage, err := model.GetMainLineStage(ctx, uid)
	if err != nil {
		return nil, nil, nil, 0, err
	}
	//stageReward := stage.StageRewards()
	//if stageReward == nil {
	//	stageReward = map[int32]int64{}
	//}
	stageReward := map[int32]int64{}
	var retRewards []*wrpc.Rewards
	var unSelectIds []int32
	sequence := util.GenerateSequenceString()
	for _, id := range monsterId {
		line := cfg_mgr.Cfg.MonsterTable.Get(id)
		if line == nil {
			return nil, nil, nil, 0, kdmerr.SysInvalidArguments.CastErrorf("invalid monster id")
		}

		if line.DropGroupId != 0 {
			rewards := item.GetDropItem(line.DropGroupId)
			for k, v := range rewards {
				model.AddItem(ctx, uid, k, v, bi.ItemFlowReason_KILL_MONSTER, strconv.Itoa(int(id)), sequence)
				stageReward[k] += v
			}
		}
		monsterTypeLine := cfg_mgr.Cfg.MonsterTypeTable.Get(line.MonsterType)
		if monsterTypeLine != nil {
			monsterGradeLine := cfg_mgr.Cfg.MonsterGradeTable.Get(monsterTypeLine.MonsterGrade)
			if monsterGradeLine != nil {
				if monsterGradeLine.MonsterGrade != servercfg.MonsterGrade_Common {
					if GetRougeLevel(ctx, uid, dungeonType) <= int32(cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_ROUGE_LEVEL_MAX_LIMIT)) {
						unSelectIds = RefreshRougeByKillMonster(ctx, uid, w)
					}

				}
			}
		}
		quest.Notify(ctx, uid, servercfg.TaskType_KillMonster, map[string]string{}, 1)
	}
	stage.SetStageRewards(ctx, stageReward)

	//获取挑战奖励
	if dungeonType == 0 {
		challengeRewardId := int32(0)
		curLine := cfg_mgr.Cfg.MainLevelTable.Get(stage.StageId())
		if stage.IsElite() {
			challengeRewardId = curLine.EliteChallengeReward
		} else {
			challengeRewardId = curLine.CommonChallengeReward
		}
		challengeRewardLine := cfg_mgr.Cfg.MainLevelRewardTable.Get(challengeRewardId)
		if challengeRewardLine != nil {
			rougeLevel := GetRougeLevel(ctx, uid, dungeonType)

			if rougeLevel > 0 {
				rougeRatio := float32(0)
				rougeRatioLines := cfg_mgr.Cfg.MainLevelRewardRatioTable.FilterSlice(func(v *servercfg.MainLevelRewardRatioTableCfg) bool {
					return true
				})
				sort.Slice(rougeRatioLines, func(i, j int) bool {
					return rougeRatioLines[i].MaxRougeLevel < rougeRatioLines[j].MaxRougeLevel
				})
				for _, v := range rougeRatioLines {
					if v.MaxRougeLevel > rougeLevel {
						break
					}
					rougeRatio = v.RewardRatio
				}
				for _, v := range challengeRewardLine.Reward {
					stageReward[v.RewardType] += int64(float32(v.RewardValue) * rougeRatio)
				}
			}
		}
	}

	for k, v := range stageReward {
		retRewards = append(retRewards, &wrpc.Rewards{
			ItemId:    k,
			ItemValue: v,
		})
	}

	stage.SetCurKillMonster(ctx, stage.CurKillMonster()+int32(len(monsterId)))
	//计算卡牌
	rougeLevel := GetRougeLevel(ctx, uid, dungeonType)
	var selectIds []int32
	if rougeLevel > stage.CurRefreshRougeLevel() {

		selectIds = RefreshRougeGroup(ctx, uid, dungeonType, w)
		stage.SetCurRefreshRougeLevel(ctx, stage.CurRefreshRougeLevel()+1)
		isUpLevel = false
	}
	if isUpLevel {
		selectIds = RefreshRougeGroup(ctx, uid, dungeonType, w)
	}
	return retRewards, selectIds, unSelectIds, stage.CurKillMonster(), nil
}

func RefreshRougeSkill(ctx context.Context, uid int64, rougeId int32, dungeonType int32) ([]int32, error) {
	if rougeId != 0 {
		rougeLine := cfg_mgr.Cfg.RougeRefreshTable.Get(rougeId)
		if rougeLine == nil {
			return nil, kdmerr.SysInvalidArguments.CastErrorf("invalid rouge id")
		}
		stageModel, _ := model.GetMainLineStage(ctx, uid)
		if stageModel == nil {
			return nil, kdmerr.SysInvalidArguments.CastError("stage is null")
		}
		curPaidRefreshTimes := stageModel.CurPaidRefreshTimes()
		addCnt := math.Round(benefit.GetBenefitValue(ctx, uid, constDef.BENEFIT_ROUGE_REFRESH_CNT_UP))
		if curPaidRefreshTimes[rougeId] >= rougeLine.DiamondRefreshCnt+int32(addCnt) {
			return nil, kdmerr.SysInvalidArguments.CastError("paid refresh times is used")
		}
		curPaidRefreshTimes[rougeId]++
		stageModel.SetCurPaidRefreshTimes(ctx, curPaidRefreshTimes)
		sequence := util.GenerateSequenceString()
		if math.Round(benefit.GetBenefitValue(ctx, uid, constDef.BENEFIT_ROUGE_REFRESH_FREE)) == 0 {
			err := consumable.DeductItem(ctx, uid, rougeLine.DiamondRefreshCost.CostType, int64(rougeLine.DiamondRefreshCost.CostValue), bi.ItemFlowReason_REFRESH_ROUGE_SKILL, strconv.Itoa(int(rougeId)), sequence)
			if err != nil {
				return nil, err
			}
		}

	}

	if rougeId == 1 {
		return RefreshRougeGroup(ctx, uid, dungeonType, nil), nil
	} else {
		return RefreshRougeByKillMonster(ctx, uid, nil), nil
	}

}

func getMainLevelRewardsByLine(line *servercfg.MainLevelTableCfg, index int32) []*servercfg.RewardKVS {
	rewardId := int32(0)
	if index == 1 {
		rewardId = line.OneStarReward
	} else if index == 2 {
		rewardId = line.TwoStarReward
	} else if index == 3 {
		rewardId = line.ThreeStarReward
	}
	if rewardId == 0 {
		return nil
	}
	rewardLine := cfg_mgr.Cfg.MainLevelPassRewardTable.Get(rewardId)
	if rewardLine == nil {
		return nil
	}
	return rewardLine.Reward
}

func CollectMainStageReward(ctx context.Context, uid int64, stageId int32) ([]*wrpc.Rewards, error) {
	stage, err := model.GetMainLineStage(ctx, uid)
	if err != nil {
		return nil, err
	}
	if stageId > stage.UnlockStageId() {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("invalid stage id")
	}

	stageFinishStatus := stage.StageFinishStatus()
	if stageFinishStatus == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("empty finish status")
	}
	curFinishStatus := stageFinishStatus[stageId]
	if curFinishStatus == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("invalid cur finish status")
	}
	if curFinishStatus.CollectReward == nil {
		curFinishStatus.CollectReward = []int32{}
	}
	retRewardsMap := map[int32]int64{}
	sequence := util.GenerateSequenceString()
	line := cfg_mgr.Cfg.MainLevelTable.Get(stageId)
	for rewardIndex := int32(1); rewardIndex <= curFinishStatus.PerfectStatus; rewardIndex++ {
		flag := false
		for _, v := range curFinishStatus.CollectReward {
			if v == rewardIndex {
				flag = true
				break
			}
		}
		if flag {
			continue
		}

		if curFinishStatus.PerfectStatus < rewardIndex {
			continue
		}

		rewards := getMainLevelRewardsByLine(line, rewardIndex)
		if rewards == nil {
			continue
		}
		retRewards, _ := model.ChangeItemByKvs(ctx, uid, rewards)
		curFinishStatus.CollectReward = append(curFinishStatus.CollectReward, rewardIndex)
		for k, v := range retRewards {
			model.AddItem(ctx, uid, k, v, bi.ItemFlowReason_STAGE_REWARD, strconv.Itoa(int(stageId)), sequence)
			retRewardsMap[k] += int64(v)
		}
	}
	var retRewards []*wrpc.Rewards
	for k, v := range retRewardsMap {
		retRewards = append(retRewards, &wrpc.Rewards{
			ItemId:    k,
			ItemValue: v,
		})
	}
	stage.SetStageFinishStatus(ctx, stageFinishStatus)
	quest.Notify(ctx, uid, servercfg.TaskType_claim_pass_level_reward, map[string]string{}, 1)
	// 构建奖励数据用于打点
	rewardInfo := make(map[string]int64)
	for k, v := range retRewardsMap {
		rewardInfo[strconv.FormatInt(int64(k), 10)] = v
	}
	rewardJsonData, _ := json.Marshal(rewardInfo)
	// 确定关卡类型
	questType := "tower"
	if stage.IsElite() {
		questType = "tower_elite"
	}
	if line.LevelType == servercfg.LevelType_ParkOur {
		questType = "parkour"
	}
	// 打点
	logger.LogCustom(ctx, uid, "quest", logger.NewDetail().
		Put("action", "level_reward").
		Put("quest_type", questType).
		Put("group_id", line.Chapter).
		Put("quest_id", 1).
		Put("step", curFinishStatus.PerfectStatus).
		Put("reward", string(rewardJsonData)))

	return retRewards, nil
}

// 挂机扫荡
func SweepMainStage(ctx context.Context, uid int64) ([]*wrpc.Rewards, error) {
	stageModel, err := model.GetMainLineStage(ctx, uid)
	if err != nil {
		return nil, err
	}
	//是否拥有无限扫荡特权
	if math.Round(benefit.GetBenefitValue(ctx, uid, constDef.BENEFIT_AFK_SWEEP_UNLIMIT)) == 0 {
		if stageModel.TodaySweepTimes() >= int32(cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_FREE_SWEEP_TIMES)) {
			return nil, kdmerr.SysInvalidArguments.CastErrorf("free sweep times is used")
		}
	}

	if stageModel.UnlockStageId() <= 1 {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("invalid stage id")
	}
	stageLine := cfg_mgr.Cfg.MainLevelTable.Get(stageModel.UnlockStageId() - 1)
	if stageLine == nil {
		stageLines := cfg_mgr.Cfg.MainLevelTable.FilterSlice(func(v *servercfg.MainLevelTableCfg) bool {
			return v.IsMaxLevel == true
		})
		if stageLines == nil {
			return nil, kdmerr.SysInvalidArguments.CastErrorf("invalid stage id")
		}
		stageLine = stageLines[0]

	}
	stageModel.SetTodaySweepTimes(ctx, stageModel.TodaySweepTimes()+1)
	sequence := util.GenerateSequenceString()
	challengeRewardLine := cfg_mgr.Cfg.MainLevelRewardTable.Get(stageLine.CommonChallengeReward)
	if challengeRewardLine == nil {
		tempStageLine := cfg_mgr.Cfg.MainLevelTable.Get(stageLine.Id - 1)
		challengeRewardLine = cfg_mgr.Cfg.MainLevelRewardTable.Get(tempStageLine.CommonChallengeReward)
		if challengeRewardLine == nil {
			return nil, kdmerr.SysInvalidArguments.CastErrorf("stage id " + strconv.Itoa(int(stageModel.UnlockStageId()-2)))
		}
	}
	deductionValue := cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_ENERGY_LEVEL_DEDUCTION)
	err = consumable.DeductItem(ctx, uid, constDef.ITEM_ENERGY_BASE, deductionValue, bi.ItemFlowReason_START_STAGE, strconv.Itoa(int(stageLine.Id)), sequence)
	var retRewards []*wrpc.Rewards
	//月卡权益增加奖励
	challRewards := challengeRewardLine.Reward
	if benefit.GetBenefitValue(ctx, uid, constDef.BENEFIT_AFK_REWARD_UP) > 0 {
		for _, v := range challRewards {
			v.RewardValue = int32(math.Round(float64(v.RewardValue) * (1 + benefit.GetBenefitValue(ctx, uid, constDef.BENEFIT_AFK_REWARD_UP))))
		}
	}

	challengeReward, _ := model.ChangeItemByKvs(ctx, uid, challRewards)
	for k, v := range challengeReward {
		retRewards = append(retRewards, &wrpc.Rewards{
			ItemId:    k,
			ItemValue: v,
		})
		model.AddItem(ctx, uid, k, v, bi.ItemFlowReason_SWEEP_STAGE, strconv.Itoa(int(stageLine.Id)), sequence)
	}
	jsonData, _ := json.Marshal(challengeReward)
	curQuestId := ""
	if stageLine != nil {
		curQuestId = stageLine.StringId
	}
	logger.LogCustom(ctx, uid, "idle", logger.NewDetail().Put("action", "reward").Put("type", 1).
		Put("level", curQuestId).Put("reward", string(jsonData)).Put("time_duration", 0))
	quest.Notify(ctx, uid, servercfg.TaskType_Sweep, map[string]string{}, 1)
	return retRewards, nil
}

func HeroBeKilled(ctx context.Context, uid int64, heroId int32) error {
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	killedList := stageModel.KilledCardIds()
	if killedList == nil {
		killedList = []int32{}
	}
	for _, v := range killedList {
		if v == heroId {
			return nil
		}
	}
	killedList = append(killedList, heroId)
	stageModel.SetKilledCardIds(ctx, killedList)
	return nil
}
