// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type LTCRechargeScoreTableCfg struct {
	Id       int32   `json:"Id"`       // Id
	StringId string  `json:"StringId"` // StringId
	Score    int32   `json:"Score"`    // 积分数
	Usd      float32 `json:"Usd"`      // 多少美元
	Cny      float32 `json:"Cny"`      // 多少人民币
	Krw      float32 `json:"Krw"`      // 多少韩元
}

func NewLTCRechargeScoreTableCfg() *LTCRechargeScoreTableCfg {
	return &LTCRechargeScoreTableCfg{
		Id:       0,
		StringId: "",
		Score:    0,
		Usd:      0.0,
		Cny:      0.0,
		Krw:      0.0,
	}
}

type LTCRechargeScoreTable struct {
	records  map[int32]*LTCRechargeScoreTableCfg
	localIds map[int32]struct{}
}

func NewLTCRechargeScoreTable() *LTCRechargeScoreTable {
	return &LTCRechargeScoreTable{
		records:  map[int32]*LTCRechargeScoreTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *LTCRechargeScoreTable) Get(key int32) *LTCRechargeScoreTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *LTCRechargeScoreTable) GetAll() map[int32]*LTCRechargeScoreTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *LTCRechargeScoreTable) put(key int32, value *LTCRechargeScoreTableCfg, local bool) *LTCRechargeScoreTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *LTCRechargeScoreTable) Range(f func(v *LTCRechargeScoreTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *LTCRechargeScoreTable) Filter(filterFuncs ...func(v *LTCRechargeScoreTableCfg) bool) map[int32]*LTCRechargeScoreTableCfg {
	filtered := map[int32]*LTCRechargeScoreTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *LTCRechargeScoreTable) FilterSlice(filterFuncs ...func(v *LTCRechargeScoreTableCfg) bool) []*LTCRechargeScoreTableCfg {
	filtered := []*LTCRechargeScoreTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *LTCRechargeScoreTable) FilterKeys(filterFuncs ...func(v *LTCRechargeScoreTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *LTCRechargeScoreTable) satisfied(v *LTCRechargeScoreTableCfg, filterFuncs ...func(v *LTCRechargeScoreTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *LTCRechargeScoreTable) setupIndexes() error {
	return nil
}

func (t *LTCRechargeScoreTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *LTCRechargeScoreTableCfg) bindRefs(c *Configs) {
}
