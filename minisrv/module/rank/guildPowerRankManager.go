package rank

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"github.com/go-redis/redis"
	"strconv"
)

// 公会战力排行榜数据结构
type GuildPower struct {
	GuildId int64 `json:"guild_id"`
	Power   int64 `json:"power"`
}

// 获取公会战力排行榜
func GetGuildPowerRank(guildId int64) ([]GuildPower, error) {
	rdb := GetRankClient()
	val, err := rdb.ZRevRangeWithScores(RANK_GUILD_POWER, 0, 99).Result()
	if err != nil {
		return nil, err
	}

	retInfos := []GuildPower{}
	for _, z := range val {
		guildIdStr := z.Member.(string)
		power := int64(z.Score)
		var guildPowerStruct GuildPower
		guildPowerStruct.GuildId, err = strconv.ParseInt(guildIdStr, 10, 64)
		guildPowerStruct.Power = power

		if err == nil {
			retInfos = append(retInfos, guildPowerStruct)
		}
	}
	return retInfos, nil
}

// 更新公会战力到排行榜
func UpdateGuildPowerRank(ctx context.Context, guildId int64, power int64) error {
	rdb := GetRankClient()
	return rdb.ZAdd(RANK_GUILD_POWER, redis.Z{
		Score:  float64(power),
		Member: strconv.FormatInt(guildId, 10),
	}).Err()
}

// 获取公会战力排行榜信息列表
func GetGuildPowerRankInfoList(ctx context.Context, guildId int64) []*wrpc.GuildPowerRankInfo {
	rankInfos, err := GetGuildPowerRank(guildId)
	if err != nil {
		return nil
	}

	retInfos := []*wrpc.GuildPowerRankInfo{}
	index := 1
	for _, info := range rankInfos {
		alliance, _ := model.GetAllianceModel(ctx, info.GuildId)
		if alliance == nil {
			continue
		}
		rankInfo := &wrpc.GuildPowerRankInfo{
			Rank:       int32(index),
			AllianceId: info.GuildId,
			Power:      info.Power,
			Name:       alliance.Name(),
			FlagBase:   alliance.FlagBase(),
			FlagEmblem: alliance.FlagEmblem(),
		}
		retInfos = append(retInfos, rankInfo)
		index++
	}
	return retInfos
}
