package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/user"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
	"strconv"
	"time"
)

func init() {
	http_post.AddPostHandler("clearUserStatus", clearUserStatus)
}
func clearUserStatus(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid") // 获取查询参数q的值
		if uidStr != "" {
			uid, _ := strconv.ParseInt(uidStr, 10, 64)
			resetType := values.Get("type")
			if resetType == "" || resetType == "sign7" {
				userData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeSign7))
				userData.SetValue(ctx, time.Now().Unix())
				content := userData.Content()
				if content == nil {
					content = make(map[int32]int64)
				}
				if content[int32(minirpc.Sign7RecordTypeCanCollect)] == 0 {
					content[int32(minirpc.Sign7RecordCollectedDay)] += 1
					content[int32(minirpc.Sign7RecordTypeCanCollect)] = 1
					userData.SetContent(ctx, content)
				}

				fmt.Fprintf(w, "success reset sign7")
			} else if resetType == "day7" {
				userModel, _ := model.GetUserModel(ctx, uid)
				userModel.SetCtime(ctx, userModel.Ctime()-86400)
				fmt.Fprintf(w, "success reset day7")
			} else if resetType == "idle" {
				resetIdleReward(ctx, uid)
				fmt.Fprintf(w, "success reset idle")
			} else if resetType == "sweep" {
				resetSweep(ctx, uid)
				fmt.Fprintf(w, "success reset sweep")
			} else if resetType == "finishDay7Quest" {
				tasks, _ := orm.GetAll[*model.ActivityTask](ctx, uid)
				for _, task := range tasks {
					if task.Status() == int32(minirpc.QuestActivity) {
						task.SetStatus(ctx, int32(minirpc.QuestComplete))
						line := cfg_mgr.Cfg.SevenDayTasksTable.Get(task.QuestId())
						task.SetProgress(ctx, int64(line.Value))
					}
				}
				fmt.Fprintf(w, "success reset finishDay7Quest")
			} else if resetType == "DailyDataReset" {
				userData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeDailyRefresh))
				userData.SetValue(ctx, 0)
				userModel, _ := model.GetUserModel(ctx, uid)
				user.DailyDataReset(ctx, userModel)
				fmt.Fprintf(w, "success reset DailyDataReset")
			}
		}
	})

}

func resetIdleReward(ctx context.Context, uid int64) {
	userDataKeys := []minirpc.UserDataType{minirpc.UserDataTypeIdleRewardTime1, minirpc.UserDataTypeIdleRewardTime2, minirpc.UserDataTypeIdleRewardTime3, minirpc.UserDataTypeIdleRewardTime4}
	userDataUpdateKeys := []minirpc.UserDataType{minirpc.UserDataTypeIdleUpdateTime1, minirpc.UserDataTypeIdleUpdateTime2, minirpc.UserDataTypeIdleUpdateTime3, minirpc.UserDataTypeIdleUpdateTime4, minirpc.UserDataTypeIdleLastCollectTime}
	for k, _ := range userDataKeys {
		userData, _ := model.GetUserData(ctx, uid, int32(userDataKeys[k]))
		userData.Delete(ctx)
	}
	for k, _ := range userDataUpdateKeys {
		userData, _ := model.GetUserData(ctx, uid, int32(userDataUpdateKeys[k]))
		userData.Delete(ctx)
	}
}

func resetSweep(ctx context.Context, uid int64) {
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	stageModel.SetTodaySweepTimes(ctx, 0)
}
