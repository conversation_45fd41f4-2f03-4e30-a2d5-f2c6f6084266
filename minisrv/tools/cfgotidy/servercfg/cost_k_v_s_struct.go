// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type CostKVS struct {
	CostType    int32         `json:"CostType"`  // 消耗道具ID
	CostTypeRef *ItemTableCfg `json:"-"`         // 消耗道具ID
	CostValue   int32         `json:"CostValue"` // 消耗数量
}

func NewCostKVS() *CostKVS {
	return &CostKVS{
		CostType:    0,
		CostTypeRef: nil,
		CostValue:   0,
	}
}

func NewMockCostKVS() *CostKVS {
	return &CostKVS{
		CostType:    0,
		CostTypeRef: nil,
		CostValue:   0,
	}
}

func (s *CostKVS) bindRefs(c *Configs) {
	s.CostTypeRef = c.ItemTable.Get(s.CostType)
}
