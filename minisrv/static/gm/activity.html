<!DOCTYPE html>
<html>
<head>
    <title>GM管理后台 - 活动管理</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 200px;
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
        }

        .sidebar .header {
            padding: 0 20px 20px 20px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar .menu {
            margin-top: 20px;
        }

        .sidebar .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .sidebar .menu-item:hover {
            background-color: #34495e;
        }

        .sidebar .menu-item.active {
            background-color: #3498db;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .top-bar {
            background-color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-info img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
            overflow-y: auto;
        }

        .panel {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        h2 {
            margin-bottom: 20px;
            color: #2c3e50;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        table th, table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .search-container {
            display: flex;
            margin-bottom: 20px;
        }

        .search-container input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }

        .action-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .edit-btn {
            background-color: #3498db;
            color: white;
        }

        .delete-btn {
            background-color: #e74c3c;
            color: white;
        }

        .dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .dialog-content {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            width: 500px;
            max-width: 90%;
        }

        .close-btn {
            float: right;
            font-size: 24px;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .form-group button {
            margin-top: 10px;
        }

        /* 子菜单样式 */
        .submenu {
            margin-top: 5px;
            padding-left: 20px;
            display: none;
        }

        .submenu-item {
            padding: 8px 15px;
            margin: 2px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .submenu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .submenu-item.active {
            background: #007bff;
            color: white;
        }

        .menu-item:hover .submenu {
            display: block;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="header">
            <h2>GM管理后台</h2>
        </div>
        <div class="menu">
            <div class="menu-item" onclick="window.location.href='/gm/'">数据概览</div>
            <div class="menu-item" onclick="window.location.href='/gm/country'">国家限制</div>
            <div class="menu-item" onclick="window.location.href='/gm/users'">用户管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/items'">道具管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/mail'">邮件系统</div>
            <div class="menu-item" onclick="window.location.href='/gm/announce'">公告管理</div>
            <div class="menu-item active">活动管理</div>
            <div class="menu-item">
                版本配置
                <div class="submenu">
                    <div class="submenu-item" onclick="window.location.href='/gm/version'">客户端版本管理</div>
                    <div class="submenu-item" onclick="window.location.href='/gm/huatuo_release'">华佗发版</div>
                </div>
            </div>
            <div class="menu-item" onclick="window.location.href='/gm/logs'">系统日志</div>
        </div>
    </div>

    <div class="main-content">
        <div class="top-bar">
            <div class="page-title">活动管理</div>
            <div class="user-info">
                <img src="/static/gm/img/avatar.png" alt="User Avatar">
                <span>管理员</span>
                <button onclick="logout()" style="margin-left: 10px;">登出</button>
            </div>
        </div>

        <div class="content-area">
            <div class="panel">
                <h2>活动管理</h2>
                <div class="search-container">
                    <button class="action-btn edit-btn" onclick="showAddActivityDialog()">添加活动</button>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>活动名称</th>
                            <th>活动类型</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="activities-table-body">
                        <!-- 活动数据将在这里动态加载 -->
                    </tbody>
                </table>

                <!-- 添加活动对话框 -->
                <div id="add-activity-dialog" class="dialog" style="display: none;">
                    <div class="dialog-content">
                        <span class="close-btn" onclick="closeAddActivityDialog()">&times;</span>
                        <h3>添加活动</h3>
                        <div class="form-group">
                            <label for="activityType">活动类型:</label>
                            <select id="activityType">
                                <!-- 活动类型将从配置中动态加载 -->
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="activityStartTime">开始时间:</label>
                            <input type="datetime-local" id="activityStartTime">
                        </div>
                        <div class="form-group">
                            <label for="activityEndTime">结束时间:</label>
                            <input type="datetime-local" id="activityEndTime">
                        </div>
                        <div class="form-group">
                            <button onclick="submitAddActivity()" class="action-btn edit-btn">提交</button>
                        </div>
                    </div>
                </div>

                <!-- 编辑活动对话框 -->
                <div id="edit-activity-dialog" class="dialog" style="display: none;">
                    <div class="dialog-content">
                        <span class="close-btn" onclick="document.getElementById('edit-activity-dialog').style.display='none'">&times;</span>
                        <h3>编辑活动</h3>
                        <input type="hidden" id="editActivityId">
                        <div class="form-group">
                            <label for="editActivityStartTime">开始时间:</label>
                            <input type="datetime-local" id="editActivityStartTime">
                        </div>
                        <div class="form-group">
                            <label for="editActivityEndTime">结束时间:</label>
                            <input type="datetime-local" id="editActivityEndTime">
                        </div>
                        <div class="form-group">
                            <button onclick="submitEditActivity()" class="action-btn edit-btn">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 格式化日期为YYYY-MM-DD格式
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // 活动管理相关函数
        function loadActivities() {
            fetch('/gm/api/activities', {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                const tbody = document.getElementById('activities-table-body');
                if (tbody) {
                    tbody.innerHTML = '';
                    data.data.activities.forEach(activity => {
                        // 获取活动状态文本
                        const statusText = activity.status ? '开启' : '关闭';
                        const statusBtnText = activity.status ? '关闭' : '开启';
                        const statusBtnClass = activity.status ? 'delete-btn' : 'edit-btn';

                        const tr = document.createElement('tr');
                        tr.innerHTML = `
                            <td>${activity.id}</td>
                            <td>${activity.name}</td>
                            <td>${activity.typeName || getActivityTypeName(activity.activityType)}</td>
                            <td>${new Date(activity.startTime * 1000).toLocaleString()}</td>
                            <td>${new Date(activity.endTime * 1000).toLocaleString()}</td>
                            <td>${statusText}</td>
                            <td>
                                <button class="action-btn ${statusBtnClass}"
                                        onclick="toggleActivity(${activity.id}, ${!activity.status})">
                                    ${statusBtnText}
                                </button>
                                <button class="action-btn edit-btn" onclick="editActivity(${activity.id})">
                                    编辑
                                </button>
                            </td>
                        `;
                        tbody.appendChild(tr);
                    });
                }
            })
            .catch(error => console.error('Failed to load activities:', error));
        }

        // 全局活动类型映射（从后端动态获取）
        let activityTypeMap = {};

        // 获取活动类型名称
        function getActivityTypeName(typeId) {
            return activityTypeMap[typeId] || `未知类型(${typeId})`;
        }

        // 初始化活动类型映射
        function initActivityTypeMap() {
            fetch('/gm/api/activity-types', {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.data && data.data.types) {
                    // 构建类型映射
                    data.data.types.forEach(type => {
                        activityTypeMap[type.type] = type.name;
                    });
                }
            })
            .catch(error => console.error('Failed to load activity type map:', error));
        }

        function loadActivityTypes() {
            fetch('/gm/api/activity-types', {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                const select = document.getElementById('activityType');
                if (select) {
                    select.innerHTML = '';
                    data.data.types.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type.type; // 使用type字段作为值
                        option.textContent = type.name;
                        select.appendChild(option);
                    });
                }
            })
            .catch(error => console.error('Failed to load activity types:', error));
        }

        function showAddActivityDialog() {
            loadActivityTypes();

            // 设置默认开始和结束时间
            const now = new Date();
            const startTime = new Date(now);
            startTime.setHours(0, 0, 0, 0); // 今天0点

            const endTime = new Date(now);
            endTime.setDate(endTime.getDate() + 7); // 7天后
            endTime.setHours(23, 59, 59, 0);

            document.getElementById('activityStartTime').value = startTime.toISOString().slice(0, 16);
            document.getElementById('activityEndTime').value = endTime.toISOString().slice(0, 16);

            document.getElementById('add-activity-dialog').style.display = 'block';
        }

        function closeAddActivityDialog() {
            document.getElementById('add-activity-dialog').style.display = 'none';
        }

        function submitAddActivity() {
            const activityType = document.getElementById('activityType').value;
            const startTime = new Date(document.getElementById('activityStartTime').value).getTime() / 1000;
            const endTime = new Date(document.getElementById('activityEndTime').value).getTime() / 1000;

            fetch('/gm/api/activities/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify({
                    activityType: parseInt(activityType),
                    startTime: startTime,
                    endTime: endTime,
                    ActivityName: "活动名称"
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert('活动添加成功');
                    closeAddActivityDialog();
                    loadActivities();
                } else {
                    alert('添加失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('添加活动失败:', error);
                alert('添加失败，请检查网络连接');
            });
        }

        function editActivity(activityId) {
            // 获取活动详情
            fetch(`/gm/api/activities/${activityId}`, {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log(data.code);
                if (data.code === 200) {
                    // 显示编辑对话框
                    console.log("sucess");
                    document.getElementById('editActivityId').value = activityId;
                    document.getElementById('editActivityStartTime').value = new Date(data.data.activity.startTime * 1000).toISOString().slice(0, 16);
                    document.getElementById('editActivityEndTime').value = new Date(data.data.activity.endTime * 1000).toISOString().slice(0, 16);
                    document.getElementById('edit-activity-dialog').style.display = 'block';
                } else {
                    alert('获取活动详情失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('获取活动详情失败:', error);
                alert('获取活动详情失败，请检查网络连接');
            });
        }

        function submitEditActivity() {
            const activityId = document.getElementById('editActivityId').value;
            const startTime = new Date(document.getElementById('editActivityStartTime').value).getTime() / 1000;
            const endTime = new Date(document.getElementById('editActivityEndTime').value).getTime() / 1000;

            fetch('/gm/api/activities/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify({
                    activityId: parseInt(activityId),
                    startTime: startTime,
                    endTime: endTime
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert('活动更新成功');
                    document.getElementById('edit-activity-dialog').style.display = 'none';
                    loadActivities();
                } else {
                    alert('更新失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('更新活动失败:', error);
                alert('更新失败，请检查网络连接');
            });
        }

        function toggleActivity(activityId, status) {
            fetch('/gm/api/activities/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify({
                    activityId: activityId,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert(status ? '活动已开启' : '活动已关闭');
                    loadActivities();
                } else {
                    alert('操作失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('操作活动失败:', error);
                alert('操作失败，请检查网络连接');
            });
        }

        function logout() {
            localStorage.removeItem('gmToken');
            window.location.href = '/gm/login';
        }

        // 页面加载时初始化
        window.onload = function() {
            const token = localStorage.getItem('gmToken');
            if (!token) {
                window.location.href = '/gm/login';
                return;
            }

            // 验证token并加载数据
            fetch('/gm/api/verify-token', {
                headers: {
                    'X-GM-Token': token
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Token invalid');
                }
                // 初始化活动类型映射，然后加载活动列表
                initActivityTypeMap();
                loadActivities();
            })
            .catch(error => {
                console.error('Token validation failed:', error);
                localStorage.removeItem('gmToken');
                window.location.href = '/gm/login';
            });
        }
    </script>
</body>
</html>