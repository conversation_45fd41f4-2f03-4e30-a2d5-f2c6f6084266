package item

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"context"
	"strconv"
)

func ChestSelfSelect(ctx context.Context, uid int64, line *servercfg.ItemTableCfg, amount int64, extParas map[int32]int32) (int64, error) {
	if extParas == nil {
		return 0, nil
	}
	groupLine := line.ChestSelectRef
	if groupLine == nil {
		return 0, nil
	}
	useNum := int64(0)
	seq := util.GenerateSequenceString()
	for _, v := range groupLine.Reward {
		if extParas[v.RewardType] == 0 {
			continue
		}
		if useNum+int64(v.RewardValue) > amount {
			break
		}
		useNum += int64(v.RewardValue)
		model.AddItem(ctx, uid, v.RewardType, int64(v.RewardValue), bi.ItemFlowReason_Chest_Self_Select, strconv.Itoa(int(line.Id)), seq)
	}
	return useNum, nil
}
