package dungeon

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/main_line_stage"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"net/http"
	"reflect"
	"strconv"
)

type DungeonLine struct {
	Id      int32                  // Id
	Dungeon int32                  // 副本类型
	Level   int32                  // 等级
	IsMax   bool                   // 是否最大关卡
	Reward  []*servercfg.RewardKVS // 奖励类型

}

// 定义配置表获取函数类型
type DungeonGetter func(int32) interface{}

// 定义公共转换闭包
func lineConverter(line interface{}) *DungeonLine {
	// 使用反射动态读取字段（若字段名一致）
	val := reflect.ValueOf(line).Elem()
	return &DungeonLine{
		Id:      val.FieldByName("Id").Interface().(int32),
		Dungeon: val.FieldByName("Dungeon").Interface().(int32),
		Level:   val.FieldByName("Level").Interface().(int32),
		IsMax:   val.FieldByName("IsMax").Interface().(bool),
		Reward:  val.FieldByName("Reward").Interface().([]*servercfg.RewardKVS),
	}
}

// 配置类型映射表
var dungeonConfigMap = map[int32]DungeonGetter{
	int32(servercfg.DungeonType_CoinDungeon):      func(id int32) interface{} { return cfg_mgr.Cfg.DungeonCoinLevelTable.Get(id) },
	int32(servercfg.DungeonType_GeneDungeon):      func(id int32) interface{} { return cfg_mgr.Cfg.DungeonGeneLevelTable.Get(id) },
	int32(servercfg.DungeonType_LordEquipDungeon): func(id int32) interface{} { return cfg_mgr.Cfg.DungeonLordEquipLevelTable.Get(id) },
	int32(servercfg.DungeonType_SunshineDungeon):  func(id int32) interface{} { return cfg_mgr.Cfg.DungeonSunshineLevelTable.Get(id) },
}

// 统一初始化入口
func initDungeonLine(dungeonId int32, dungeonType int32) *DungeonLine {
	getter, exists := dungeonConfigMap[dungeonType]
	if !exists {
		return nil
	}

	if rawLine := getter(dungeonId); rawLine != nil {
		return lineConverter(rawLine)
	}
	return nil
}
func FinishDungeonStage(ctx context.Context, uid int64, dungeonId int32, dungeonType int32, isWin bool, estimateTime int32) ([]*wrpc.Rewards, error) {
	line := initDungeonLine(dungeonId, dungeonType)
	if line == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("dungeon is error")
	}
	dungeonModel := model.GetOrCreateDungeonModel(ctx, uid, dungeonType)
	if line.Id > dungeonModel.MaxLevel()+1 && line.Level != 1 {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("need finish last stage first")
	}
	if !isWin {
		logger.LogCustom(ctx, uid, "resource_quest", logger.NewDetail().
			Put("action", "finish").
			Put("id", dungeonId).
			Put("type", dungeonType).
			Put("difficult", 1).
			Put("result", isWin).
			Put("elapse_time", estimateTime))
		return nil, nil
	}
	// 检查并扣除钥匙
	keyItemId := getKeyItemId(dungeonType)
	sequence := util.GenerateSequenceString()
	err := consumable.DeductItem(ctx, uid, keyItemId, 1, bi.ItemFlowReason_SWEEP_STAGE, strconv.Itoa(int(dungeonId)), sequence)
	if err != nil {
		return nil, err
	}
	dungeonModel.SetMaxLevel(ctx, util.MaxInt32(line.Id, dungeonModel.MaxLevel()+1))
	var retRewards []*wrpc.Rewards
	rewardMap := make(map[int32]int32)
	for _, v := range line.Reward {
		retRewards = append(retRewards, &wrpc.Rewards{
			ItemId:    v.RewardType,
			ItemValue: int64(v.RewardValue),
		})
		rewardMap[v.RewardType] += v.RewardValue
		_, err := consumable.AddItem(ctx, uid, v.RewardType, int64(v.RewardValue), bi.ItemFlowReason_Dungeon, strconv.Itoa(int(dungeonId)), sequence)
		if err != nil {
			return nil, err
		}
	}
	trace := map[string]string{
		"type": strconv.Itoa(int(dungeonType)),
	}
	quest.Notify(ctx, uid, servercfg.TaskType_DungeonChallenge, trace, 1)
	userModel, _ := model.GetUserModel(ctx, uid)
	battlePos := userModel.BattlePosInfo()
	quest.Notify(ctx, uid, servercfg.TaskType_DungeonLevel, trace, 1)
	logger.LogCustom(ctx, uid, "resource_quest", logger.NewDetail().
		Put("action", "finish").
		Put("id", dungeonId).
		Put("type", dungeonType).
		Put("difficult", 1).
		Put("result", isWin).
		Put("cost", util.FormatRewards(map[int32]int32{keyItemId: 1})).
		Put("elapse_time", estimateTime).
		Put("hero_info", main_line_stage.FormatHeroInfo(ctx, uid, battlePos)).
		Put("rewards", util.FormatRewards(rewardMap)))
	return retRewards, nil
}

func CollectFirstPassReward(ctx context.Context, uid int64, dungeonId int32) error {
	//dungeonLine := cfg_mgr.Cfg.DungeonHeroLevelUpTable.Get(dungeonId)
	//if dungeonLine == nil {
	//	return kdmerr.SysInvalidArguments.CastErrorf("dungeon is error")
	//}
	//dungeonModel, _ := model.GetOrCreateDungeonModel(ctx, uid, dungeonLine.DungeonGroup)
	//if dungeonLine.Level > dungeonModel.MaxLevel() {
	//	return kdmerr.SysInvalidArguments.CastErrorf("level not enough")
	//}
	//levelReward := dungeonModel.LevelRewards()
	//if levelReward[dungeonId] > 0 {
	//	return kdmerr.SysInvalidArguments.CastErrorf("reward has be collect")
	//}
	//levelReward[dungeonId] = 1
	//dungeonModel.SetLevelRewards(ctx, levelReward)
	//for i := 0; i < len(dungeonLine.FirstPassLevelRewardType); i++ {
	//	model.AddItem(ctx, uid, dungeonLine.FirstPassLevelRewardType[i], int64(dungeonLine.FirstPassLevelRewardValue[i]))
	//}
	return nil
}

// SweepDungeon 副本扫荡
// uid: 用户ID
// dungeonType: 副本类型
// level: 副本等级
func SweepDungeon(ctx context.Context, uid int64, dungeonType int32, dungeonId int32) ([]*wrpc.Rewards, error) {
	// 获取副本数据
	dungeonModel := model.GetOrCreateDungeonModel(ctx, uid, dungeonType)
	// 获取副本配置
	dungeonLine := initDungeonLine(dungeonId, dungeonType)
	if dungeonLine == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("invalid dungeon config")
	}
	// 检查副本等级
	if dungeonLine.Level > dungeonModel.MaxLevel() {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("invalid dungeon level")
	}

	// 检查并扣除钥匙
	keyItemId := getKeyItemId(dungeonType)
	sequence := util.GenerateSequenceString()
	err := consumable.DeductItem(ctx, uid, keyItemId, 1, bi.ItemFlowReason_SWEEP_STAGE, strconv.Itoa(int(dungeonId)), sequence)
	if err != nil {
		return nil, err
	}

	// 发放奖励
	var retRewards []*wrpc.Rewards
	for _, v := range dungeonLine.Reward {
		itemValue := int64(v.RewardValue)
		retRewards = append(retRewards, &wrpc.Rewards{
			ItemId:    v.RewardType,
			ItemValue: itemValue,
		})
		_, err := consumable.AddItem(ctx, uid, v.RewardType, itemValue, bi.ItemFlowReason_SWEEP_STAGE, strconv.Itoa(int(dungeonId)), sequence)
		if err != nil {
			return nil, err
		}
	}
	trace := map[string]string{
		"type": strconv.Itoa(int(dungeonType)),
	}
	quest.Notify(ctx, uid, servercfg.TaskType_DungeonSweep, trace, 1)
	return retRewards, nil
}

// getKeyItemId 获取对应副本类型的钥匙道具ID
func getKeyItemId(dungeonType int32) int32 {
	switch dungeonType {
	case int32(servercfg.DungeonType_CoinDungeon):
		return constDef.ITEM_COIN_DUNGEON_KEY
	case int32(servercfg.DungeonType_GeneDungeon):
		return constDef.ITEM_GENE_DUNGEON_KEY
	case int32(servercfg.DungeonType_LordEquipDungeon):
		return constDef.ITEM_LORD_EQUIP_DUNGEON_KEY
	case int32(servercfg.DungeonType_SunshineDungeon):
		return constDef.ITEM_SUNSHINE_DUNGEON_KEY
	default:
		return 0
	}
}

// 开始副本
func StartDungeon(ctx context.Context, uid int64, dungeonType int32, dungeonId int32) error {
	stage, _ := model.GetMainLineStage(ctx, uid)
	stage.SetCurKillMonster(ctx, 0)
	stage.SetCurRefreshRougeLevel(ctx, 1)
	stage.SetKilledCardIds(ctx, []int32{})
	//初始化英雄上阵卡
	cardIds := []int32{}
	userModel, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userModel)
	defer orm.Unlock(userModel)
	dungeonModel := model.GetOrCreateDungeonModel(ctx, uid, dungeonType)
	battlePos := dungeonModel.BattlePosInfo()
	if battlePos == nil {
		battlePos = userModel.BattlePosInfo()
	}
	if battlePos != nil {
		for _, v := range battlePos {
			rougeTabGroupLines := cfg_mgr.Cfg.RougeTabGroupTable.FilterSlice(func(line *servercfg.RougeTabGroupTableCfg) bool {
				return line.Hero == v.HeroId
			})
			if len(rougeTabGroupLines) == 0 {
				continue
			}
			rougeTabLies := cfg_mgr.Cfg.RougeTabTable.FilterSlice(func(line *servercfg.RougeTabTableCfg) bool {
				return line.RougeTabGroup == rougeTabGroupLines[0].Id && line.RougeTabType == servercfg.RougeTabType_ConfigTab
			})
			if len(rougeTabLies) == 0 {
				continue
			}
			cardIds = append(cardIds, rougeTabLies[0].Id)
		}
	}
	stage.SetCurSelectCardIds(ctx, cardIds)
	line := initDungeonLine(dungeonId, dungeonType)
	dungeonModel.SetCurStageId(ctx, line.Level)
	return nil
}

// 肉鸽卡牌刷新
func RefreshDungeonRougeTab(ctx context.Context, uid int64, dungeonType int32, w http.ResponseWriter) []int32 {
	return RefreshCoinRougeTab(ctx, uid, w)
}

// 金币副本肉鸽刷新
func RefreshCoinRougeTab(ctx context.Context, uid int64, w http.ResponseWriter) []int32 {
	return main_line_stage.RefreshRougeGroupByCondition(ctx, uid, 4, true, false, w)
}

// 选择肉鸽卡牌
func SelectDungeonRougeTab(ctx context.Context, uid int64, dungeonType int32, skillIds []int32) error {
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	curSelectIds := stageModel.CurSelectCardIds()
	if curSelectIds == nil {
		curSelectIds = []int32{}
	}
	for _, v := range skillIds {
		curSelectIds = append(curSelectIds, v)
	}
	stageModel.SetCurSelectCardIds(ctx, curSelectIds)
	stageModel.SetRefreshCardIds(ctx, nil)
	return nil
}

// SetDungeonHeroBattle 设置副本英雄上阵
func SetDungeonHeroBattle(ctx context.Context, uid int64, dungeonType int32, posId int32, heroId int32) error {
	dungeonModel := model.GetOrCreateDungeonModel(ctx, uid, dungeonType)

	// 获取副本上阵信息
	dungeonBattlePos := dungeonModel.BattlePosInfo()
	if dungeonBattlePos == nil {
		dungeonBattlePos = map[int32]*minirpc.BattlePosInfo{}
	}

	// 如果该位置已有英雄，先清除原英雄的上阵状态
	if dungeonBattlePos[posId] != nil {
		oldHeroId := dungeonBattlePos[posId].HeroId
		if oldHeroId != 0 {
			heroModel, _ := model.GetHero(ctx, uid, oldHeroId)
			if heroModel != nil {
				heroModel.SetIsBattle(ctx, false)
			}
		}
	}

	if heroId == 0 {
		// 下阵英雄
		delete(dungeonBattlePos, posId)
	} else {
		// 上阵英雄
		hero, err := model.GetHero(ctx, uid, heroId)
		if err != nil {
			return err
		}
		if hero == nil {
			return kdmerr.SysDBError.CastErrorf("can not find hero")
		}

		dungeonBattlePos[posId] = &minirpc.BattlePosInfo{
			HeroId: heroId,
		}
		hero.SetIsBattle(ctx, true)
	}

	dungeonModel.SetBattlePosInfo(ctx, dungeonBattlePos)
	return nil
}
