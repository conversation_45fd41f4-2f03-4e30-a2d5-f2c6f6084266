
��
share_replicas.protominirpc-github.com/gogo/protobuf/gogoproto/gogo.protoorm/extension.protoclient/extension.proto"G
BuffInfo
buff_id (RbuffId"

buff_end_time (RbuffEndTime"L
	EntityKey
type (Rtype
world_id (RworldId
uid (Ruid"7
HeroDice
dice_id (RdiceId
lock (Rlock"k

HeroEquipment
pos (Rpos
level_id (RlevelId
grade_id (RgradeId
lock (Rlock"'

BattlePosInfo
heroId (RheroId"�
UserInfo
_id (	RId
world_id (RworldId
uid (B��#��#��#Ruid
name (	BȔ#��#Rname%
alliance_id (B��#R
allianceId
power (B��#Rpower 
portrait (	B��#Rportrait
icon (	B��#Ricon+
chat_channel	 (B��#Ȕ#RchatChannela
ctime
 (BK��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSeconds��#Rctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion
frame
 (	BȔ#��#Rframe-
formation_power (B��#RformationPower&
born_zone_id (B��#R
bornZoneId%
	vip_level (BȔ#��#RvipLevelo
last_login_time (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsR
lastLoginTimeR
battle_pos_info (2$.minirpc.UserInfo.BattlePosInfoEntryB��#R
battlePosInfo,
default_battle_pos (RdefaultBattlePos9
research_pay_queue_status (RresearchPayQueueStatus(
avatar_config_id (RavatarConfigId3
free_change_name_times (RfreeChangeNameTimesR
alliance_app_list (2&.minirpc.UserInfo.AllianceAppListEntryRallianceAppList;
free_create_alliance_times (RfreeCreateAllianceTimesR
friend_apply_list (2&.minirpc.UserInfo.FriendApplyListEntryRfriendApplyList-
recommendations_id (	RrecommendationsId,
friend_stage_limit (RfriendStageLimit'
allow_strangers (RallowStrangers+
cur_gem_info_id (B��#RcurGemInfoId.
total_login_days (B��#RtotalLoginDays#

ban_reason (B��#R	banReason+
hero_symbiotic  (B��#R
heroSymbioticX
BattlePosInfoEntry
key (Rkey,
value (2.minirpc.BattlePosInfoRvalue:8B
AllianceAppListEntry
key (Rkey
value (Rvalue:8B
FriendApplyListEntry
key (Rkey
value (Rvalue:8:5��"��"kingdom��"user��'	user_info��'	user_info��""�
MonsterInfo
id (B��#��#Rid
	config_id (RconfigId
_version (RVersion]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime:F��"��"kingdom��"monster��"info��'monster_info��'monster_info��""�	
HeroInfo
uid (B��#��#Ruid%
	config_id (B��#��#RconfigId
exp (Rexp
starId (RstarId
levelId (RlevelId
_version (RVersion;
benefits	 (2.minirpc.HeroInfo.BenefitsEntryRbenefitsN
equipment_level
 (2%.minirpc.HeroInfo.EquipmentLevelEntryRequipmentLevelN
equipment_grade (2%.minirpc.HeroInfo.EquipmentGradeEntryRequipmentGrade]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime.
	hero_dice (2.minirpc.HeroDiceRheroDiceK
hero_equipment (2$.minirpc.HeroInfo.HeroEquipmentEntryR
heroEquipment
	troop_num (RtroopNum
power (Rpower
	is_battle (RisBattle$
gene_config_id (RgeneConfigId;

BenefitsEntry
key (Rkey
value (Rvalue:8A
EquipmentLevelEntry
key (Rkey
value (Rvalue:8A
EquipmentGradeEntry
key (Rkey
value (Rvalue:8X
HeroEquipmentEntry
key (Rkey,
value (2.minirpc.HeroEquipmentRvalue:8:=��"��"kingdom��"hero��"info��'	hero_info��'	hero_info��""�
	HeroSkill
_id (	RId
uid (B��#��#Ruid#
group_id (B��#��#RgroupId
	config_id (RconfigId]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion
hero_id (B��#RheroId:=��"��"kingdom��"
hero_skill��'
hero_skill��'
hero_skill��""�
BuildingInfo
_id (	RId
uid (B��#��#Ruid-
building_id (B��#��#��#R
buildingId
	config_id (RconfigId
job_id (RjobId
type (Rtype
	work_hero (RworkHero
_version (RVersion]
ctime	 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime=
ext_data (2".minirpc.BuildingInfo.ExtDataEntryRextData 
train_job_id (R
trainJobId!
work_village
 (RworkVillage
status (Rstatusb
end_time (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRendTime6
trace (2 .minirpc.BuildingInfo.TraceEntryRtrace:
ExtDataEntry
key (Rkey
value (Rvalue:88

TraceEntry
key (Rkey
value (Rvalue:8:A��"��"kingdom��"building��'
building_info��'
building_info��""�

Consumable
uid (B��#��#Ruid%
	config_id (B��#��#RconfigId
quantity (Rquantity
_version (RVersion]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime:E��"��"kingdom��"
consumable��"info��'
consumable��'
consumable��""�

OutConsumable
uid (B��#��#Ruid%
	config_id (B��#��#RconfigId
quantity (Rquantity
_version (RVersion]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime:Q��"��"kingdom��"out_consumable��"info��'out_consumable��'out_consumable��""�

UserLookup
_id (	RId%
	device_id (	B��#��#RdeviceId
uid (Ruid
_version (RVersion]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime:@��"��"kingdom��"user_lookup��'user_lookup��'user_lookup��""�
UserJob
_id (	RId
world_id (RworldId
uid (B��#��#Ruid#
job_id (B��#��#��#RjobId
hint_id (RhintId'

event_type (B��#��#R	eventType
state (	Rstate%
retry_times (B��#R
retryTimes1
trace	 (2.minirpc.UserJob.TraceEntryRtrace%
stack_trace
 (	B��#R
stackTracef

time_start (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsR	timeStartb
time_end (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRtimeEnd]
ctime
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersionA
float_trace (2 .minirpc.UserJob.FloatTraceEntryR
floatTrace8

TraceEntry
key (Rkey
value (Rvalue:8=
FloatTraceEntry
key (Rkey
value (Rvalue:8:7��"��"kingdom��"user_job��'user_job��'user_job��""�
UserBenefit
uid (B��#��#Ruid%
	config_id (B��#��#RconfigId
trace (	B��#��#Rtrace
value (Rvalue]
ctime
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion:G��"��"kingdom��"user_benefit��"info��'user_benefit��'user_benefit"�
	UserTroop
uid (B��#��#Ruid'

troop_type (B��#��#R	troopType!
total_amount (RtotalAmount'
hospital_amount (RhospitalAmount@

hero_troop (2!.minirpc.UserTroop.HeroTroopEntryR	heroTroop]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion)
unclaimed_amount	 (RunclaimedAmount
status
 (Rstatusb
end_time (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRendTime3
trace (2.minirpc.UserTroop.TraceEntryRtrace<
HeroTroopEntry
key (Rkey
value (Rvalue:88

TraceEntry
key (Rkey
value (Rvalue:8:<��"��"kingdom��"troop��"info��'
user_troop��'
user_troop"�
HeroLottery
uid (B��#��#Ruid
level_id (RlevelId

sum_amount (R	sumAmountK

level_rewards (2&.minirpc.HeroLottery.LevelRewardsEntryRlevelRewards]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersionB

acc_amount	 (2#.minirpc.HeroLottery.AccAmountEntryR	accAmountB

acc_reward
 (2#.minirpc.HeroLottery.AccRewardEntryR	accReward3
last_free_lottery_time (RlastFreeLotteryTime7
today_free_lottery_times (RtodayFreeLotteryTimes"

to_must_times
 (RtoMustTimes&
not_in_tutorial (R
notInTutorial-
not_in_first_second (RnotInFirstSecond+
first_must_hero_id (RfirstMustHeroId?
LevelRewardsEntry
key (Rkey
value (Rvalue:8<
AccAmountEntry
key (Rkey
value (Rvalue:8<
AccRewardEntry
key (Rkey
value (Rvalue:8:G��"��"kingdom��"hero_lottery��"info��'hero_lottery��'hero_lottery"�

LordGemRandom
_id (	RId
uid (B��#��#Ruid#
group_id (B��#��#RgroupId

must_times (R	mustTimess
last_refresh_time (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRlastRefreshTime]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion5
today_free_random_times	 (RtodayFreeRandomTimesz
last_free_random_time
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRlastFreeRandomTime:H��"��"kingdom��"lord_gem_random��'lord_gem_random��'lord_gem_random"�
Research
uid (B��#��#Ruid!
tech_id (B��#��#RtechId
level_id (RlevelId
job_id (RjobId]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion
status	 (Rstatusb
end_time
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRendTime2
trace (2.minirpc.Research.TraceEntryRtrace8

TraceEntry
key (Rkey
value (Rvalue:8:;��"��"kingdom��"research��"info��'research��'research"�
Lord
uid (B��#��#Ruid
level (Rlevel]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion:/��"��"kingdom��"lord��"info��'lord��'lord"�
MainTask
_id (	RId
uid (B��#��#Ruid#
quest_id (B��#��#RquestId
progress (Rprogress
status (Rstatus]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion
type	 (Rtype:6��"��"kingdom��"	main_task��'	main_task��'	main_task"�

GrowthTask
_id (	RId
uid (B��#��#Ruid#
quest_id (B��#��#RquestId
progress (Rprogress
status (Rstatus]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion
group_id	 (RgroupId
type
 (Rtype:<��"��"kingdom��"growth_task��'growth_task��'growth_task"�
	DailyTask
uid (B��#��#Ruid#
quest_id (B��#��#RquestId
type (Rtype
progress (Rprogress
status (Rstatus]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion
_id	 (	RId:9��"��"kingdom��"
daily_task��'
daily_task��'
daily_task"�

WeeklyTask
uid (B��#��#Ruid#
quest_id (B��#��#RquestId
type (Rtype
progress (Rprogress
status (Rstatus]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion
_id	 (	RId:<��"��"kingdom��"weekly_task��'weekly_task��'weekly_task"�

RepeatTask
uid (B��#��#Ruid#
quest_id (B��#��#RquestId
type (Rtype
progress (Rprogress
status (Rstatus]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion:D��"��"kingdom��"repeat_task��"info��'repeat_task��'repeat_task"�
ActivityTask
_id (	RId
uid (B��#��#Ruid#
quest_id (B��#��#RquestId
progress (Rprogress
status (Rstatus]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion
type	 (Rtype
activity_id
 (R
activityId#

activity_type (RactivityType:B��"��"kingdom��"
activity_task��'
activity_task��'
activity_task"�
AchievementTask
_id (	RId
uid (B��#��#Ruid#
quest_id (B��#��#RquestId
progress (Rprogress
status (Rstatus]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion
type	 (Rtype:K��"��"kingdom��"achievement_task��'achievement_task��'achievement_task"(
MapEventRegion
unlock (Runlock"�
MapEventSectionR
section_status (2+.minirpc.MapEventSection.SectionStatusEntryR
sectionStatusY
SectionStatusEntry
key (Rkey-
value (2.minirpc.MapEventRegionRvalue:8"�
MapEventChapterR
section_status (2+.minirpc.MapEventChapter.SectionStatusEntryR
sectionStatus#

explode_value (RexplodeValue?
rewards (2%.minirpc.MapEventChapter.RewardsEntryRrewardsZ
SectionStatusEntry
key (Rkey.
value (2.minirpc.MapEventSectionRvalue:8:
RewardsEntry
key (Rkey
value (Rvalue:8"6
MapEventLastKillTimes

kill_times (R	killTimes"�
CurStageInfo
stage_id (RstageId
duration (Rduration

hp_percent (R	hpPercent#

finish_status (RfinishStatus0
finish_status_reward (RfinishStatusReward"�
MapEvent
uid (B��#��#Ruid%
unlock_chapter (R
unlockChapter<
	id_status (2.minirpc.MapEvent.IdStatusEntryRidStatusK
chapter_status (2$.minirpc.MapEvent.ChapterStatusEntryR
chapterStatusI
last_kill_time (2#.minirpc.MapEvent.LastKillTimeEntryRlastKillTime]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion;

IdStatusEntry
key (Rkey
value (Rvalue:8Z
ChapterStatusEntry
key (Rkey.
value (2.minirpc.MapEventChapterRvalue:8_
LastKillTimeEntry
key (Rkey4
value (2.minirpc.MapEventLastKillTimesRvalue:8:>��"��"kingdom��"	map_event��"info��'	map_event��'	map_event"�
Villager
uid (B��#��#Ruid%
	config_id (B��#��#RconfigId#

work_building (RworkBuilding]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion:;��"��"kingdom��"villager��"info��'villager��'villager"�
Animal
uid (B��#��#Ruid%
	config_id (B��#��#RconfigId#

work_building (RworkBuilding]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion:5��"��"kingdom��"animal��"info��'animal��'animal"�
Dungeon
uid (B��#��#Ruid+
dungeon_type (B��#��#RdungeonType
	max_level (RmaxLevel*
today_reset_times (RtodayResetTimes$
today_ad_times (RtodayAdTimeso
last_reset_time (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsR
lastResetTime]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version	 (RVersionG

level_rewards
 (2".minirpc.Dungeon.LevelRewardsEntryRlevelRewards 
cur_stage_id (R
curStageIdK
battle_pos_info (2#.minirpc.Dungeon.BattlePosInfoEntryR
battlePosInfo?
LevelRewardsEntry
key (Rkey
value (Rvalue:8X
BattlePosInfoEntry
key (Rkey,
value (2.minirpc.BattlePosInfoRvalue:8:8��"��"kingdom��"dungeon��"info��'dungeon��'dungeon"�
Dave
_id (	RId
uid (B��#��#Ruid
level_id (RlevelId]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion:'��"��"kingdom��"dave��'dave��'dave"�
UserData
_id (	RId
uid (B��#��#Ruid
type (B��#��#Rtype
value (Rvalue]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion8
content (2.minirpc.UserData.ContentEntryRcontent:
ContentEntry
key (Rkey
value (Rvalue:8:6��"��"kingdom��"	user_data��'	user_data��'	user_data"^
LevelStruct
card_ids (RcardIds
	total_exp (RtotalExp
hero_hp (RheroHp"�
StageFinishStatus
stage_id (RstageId%
perfect_status (R
perfectStatus%
collect_reward (R
collectReward!
finish_elite (RfinishElite
max_time (RmaxTime$
max_hp_percent (RmaxHpPercent
min_time (RminTime"�

MainLineStage
_id (	RId
uid (B��#��#Ruid
stage_id (RstageId]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion7
level_struct (2.minirpc.LevelStructRlevelStructM

stage_rewards (2(.minirpc.MainLineStage.StageRewardsEntryRstageRewards&
unlock_stage_id	 (R
unlockStageId
is_fail
 (RisFail(
refresh_card_ids (RrefreshCardIds]
stage_finish_status (2-.minirpc.MainLineStage.StageFinishStatusEntryRstageFinishStatus(
cur_kill_monster
 (RcurKillMonster5
cur_refresh_rouge_level (RcurRefreshRougeLevel-
cur_select_card_ids (RcurSelectCardIds3
elite_refresh_card_ids (ReliteRefreshCardIdsf
collection_rank_reward (20.minirpc.MainLineStage.CollectionRankRewardEntryRcollectionRankReward*
today_sweep_times (RtodaySweepTimes
is_elite (RisElited
cur_paid_refresh_times (2/.minirpc.MainLineStage.CurPaidRefreshTimesEntryRcurPaidRefreshTimes&
killed_card_ids (R
killedCardIds@
cur_elite_cards_refresh_times (RcurEliteCardsRefreshTimes/
cur_stage_start_time (RcurStageStartTimeB
cur_normal_cards_refresh_times (RcurNormalCardsRefreshTimes(
cur_stage_finish (RcurStageFinish?
StageRewardsEntry
key (Rkey
value (Rvalue:8`
StageFinishStatusEntry
key (Rkey0
value (2.minirpc.StageFinishStatusRvalue:8G
CollectionRankRewardEntry
key (Rkey
value (Rvalue:8F
CurPaidRefreshTimesEntry
key (Rkey
value (Rvalue:8:H��"��"kingdom��"main_line_stage��'main_line_stage��'main_line_stage"�	
UserDevices
_id (	RId
uid (B��#��#Ruid]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion

android_id (	R	androidId$
app_install_ts (RappInstallTs&
app_instance_id	 (	R
appInstanceId
app_version
 (	R
appVersion!
appsflyer_id (	RappsflyerId
	bundle_id (	RbundleId

channel_id
 (	R	channelId
	device_id (	RdeviceId
device_lang (	R
deviceLang
device_type (	R
deviceType!
display_size (	RdisplaySize
du_cdid (	RduCdid
du_idfa (	RduIdfa
du_oaid (	RduOaid 
fp_device_id (	R
fpDeviceId$
fpid_create_ts (RfpidCreateTs
gaid (	Rgaid
game_uid (	RgameUid+
game_uid_create_ts (RgameUidCreateTs#

gameserver_id (	RgameserverId
idfa (	Ridfa
idfv (	Ridfv
imei (	Rimei
ip (	Rip
lang (	Rlang
level  (Rlevel
oaid! (	Roaid
os" (	Ros

os_version# (	R	osVersion
pkg_channel$ (	R
pkgChannel!
data_version% (	RdataVersion
fpid& (	Rfpid:?��"��"kingdom��"user_devices��'user_devices��'user_devices"�
PaymentOrder
_id (	RId
uid (B��#��#Ruid#
order_id (	B��#��#RorderId

product_id (R	productId6
extra (2 .minirpc.PaymentOrder.ExtraEntryRextra]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion
status	 (Rstatus8

ExtraEntry
key (	Rkey
value (	Rvalue:8:B��"��"kingdom��"
payment_order��'
payment_order��'
payment_order"�
UserIapBuyTimes
_id (	RId
uid (B��#��#Ruid]
iap_daily_buy_times (2..minirpc.UserIapBuyTimes.IapDailyBuyTimesEntryRiapDailyBuyTimes`
iap_weekly_buy_times (2/.minirpc.UserIapBuyTimes.IapWeeklyBuyTimesEntryRiapWeeklyBuyTimesc
iap_monthly_buy_times (20.minirpc.UserIapBuyTimes.IapMonthlyBuyTimesEntryRiapMonthlyBuyTimes]
iap_total_buy_times (2..minirpc.UserIapBuyTimes.IapTotalBuyTimesEntryRiapTotalBuyTimes]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version	 (RVersiono
last_reset_time
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsR
lastResetTimeC
IapDailyBuyTimesEntry
key (Rkey
value (Rvalue:8D
IapWeeklyBuyTimesEntry
key (Rkey
value (Rvalue:8E
IapMonthlyBuyTimesEntry
key (Rkey
value (Rvalue:8C
IapTotalBuyTimesEntry
key (Rkey
value (Rvalue:8:K��"��"kingdom��"user_iap_buy_log��'user_iap_buy_log��'user_iap_buy_log"�
PerPlayerScore
uid (Ruid
score (Rscore%
challenge_time (R
challengeTimeQ
challenge_hero (2*.minirpc.PerPlayerScore.ChallengeHeroEntryR
challengeHero@
ChallengeHeroEntry
key (Rkey
value (Rvalue:8"l
GlobalStagePerLevel:
player_score (2.minirpc.PerPlayerScoreRplayerScore
max_time (RmaxTime"�
GlobalStageLevel
_id (	RId#
world_id (B��#��#RworldIdG

level_info (2(.minirpc.GlobalStageLevel.LevelInfoEntryR	levelInfo]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersionZ
LevelInfoEntry
key (Rkey2
value (2.minirpc.GlobalStagePerLevelRvalue:8:Q��"��"kingdom��"global_stage_level��'global_stage_level��'global_stage_level"�

ArenaRankInfo
_id (	RId
world_id (B��#RworldId
rank_id (B��#RrankId
	entity_id (RentityId
is_robot (RisRobot]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion:&��"��"kingdom��"arena_rank_info��"3"�
GeneInfoList@
	gene_info (2#.minirpc.GeneInfoList.GeneInfoEntryRgeneInfo;

GeneInfoEntry
key (Rkey
value (Rvalue:8"�
	LordEquip
_id (	RId
uid (B��#��#Ruid!
type_id (B��#��#RtypeId
	config_id (RconfigId=
	gene_info (2 .minirpc.LordEquip.GeneInfoEntryRgeneInfo]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version	 (RVersion
grade_id
 (RgradeId;
gene_info_list (2.minirpc.GeneInfoListRgeneInfoList
power (Rpower;

GeneInfoEntry
key (Rkey
value (Rvalue:8:9��"��"kingdom��"
lord_equip��'
lord_equip��'
lord_equip"�
LordGem
_id (	RId
uid (B��#��#Ruid%
	config_id (B��#��#RconfigId
amount (Ramount]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version	 (RVersion
is_lock
 (RisLock
be_used (RbeUsed:3��"��"kingdom��"lord_gem��'lord_gem��'lord_gem"�
OrderResult
order_id (	RorderId

package_id (R	packageId
seq (Rseq;
rewards (2!.minirpc.OrderResult.RewardsEntryRrewards:
RewardsEntry
key (Rkey
value (Rvalue:8"|
AgreeJoinAlliance
alliance_id (R
allianceId
name (	Rname
acronym (	Racronym
isAgree (RisAgree";

AddFriendInfo
uid (Ruid
isAgree (RisAgree"
UserPush
uid (Ruid"�
UserMail
_id (	RId
uid (B��#��#Ruid%
mail_id (B��#��#��#RmailId
title_id (	RtitleId

title_attr (	R	titleAttr
body_id (	RbodyId
	body_attr (	RbodyAttrA

attachment (2!.minirpc.UserMail.AttachmentEntryR
attachment
status	 (Rstatus]
ctime
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion
type
 (Rtype
expire_time (R
expireTime
image (	Rimage=
AttachmentEntry
key (Rkey
value (Rvalue:8:6��"��"kingdom��"	user_mail��'	user_mail��'	user_mail"�	
Alliance
_id (	RId-
alliance_id (B��#��#��#R
allianceId
world_id (RworldId
name (	Rname
acronym (	Racronym
	flag_base (RflagBase
boss_uid (RbossUid
level (Rlevel
notice	 (	Rnotice]
ctime
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion)
application_list
 (RapplicationList'
recruit_setting (RrecruitSetting
flag_emblem (R
flagEmblem<
	step_name (2.minirpc.Alliance.StepNameEntryRstepName3
free_change_name_times (RfreeChangeNameTimes9
free_change_acronym_times (RfreeChangeAcronymTimes'
power_condition (RpowerCondition.
max_stage_condition (RmaxStageCondition
exp (Rexp
power (Rpower#

member_amount (RmemberAmountx
shop_last_reset_time (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRshopLastResetTime&
cur_shop_period (R
curShopPeriod;

StepNameEntry
key (Rkey
value (	Rvalue:8:3��"��"kingdom��"alliance��'alliance��'alliance"�
AllianceMember
_id (	RId)
alliance_id (B��#��#R
allianceId
uid (B��#��#Ruid
step (Rstep]
ctime
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion:H��"��"kingdom��"alliance_member��'alliance_member��'alliance_member"�
AllianceShopBuy
_id (	RId)
alliance_id (B��#��#R
allianceId
uid (B��#��#Ruid+
commodity_id (B��#��#RcommodityId
	buy_times (RbuyTimes]
ctime
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion:N��"��"kingdom��"alliance_shop_buy��'alliance_shop_buy��'alliance_shop_buy"�
AllianceTask
uid (B��#��#Ruid#
quest_id (B��#��#RquestId
type (Rtype
progress (Rprogress
status (Rstatus]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion
_id	 (	RId:B��"��"kingdom��"
alliance_task��'
alliance_task��'
alliance_task"�
FunctionOpen
_id (	RId
uid (B��#��#Ruid@
	open_list (2#.minirpc.FunctionOpen.OpenListEntryRopenList]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion;

OpenListEntry
key (Rkey
value (Rvalue:8:B��"��"kingdom��"
function_open��'
function_open��'
function_open"�
NewbieGuide
_id (	RId
uid (B��#��#RuidB

guide_list (2#.minirpc.NewbieGuide.GuideListEntryR	guideList 
cur_guide_id (R
curGuideId]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion<
GuideListEntry
key (Rkey
value (Rvalue:8:?��"��"kingdom��"newbie_guide��'newbie_guide��'newbie_guide"�
RegularPack
_id (	RId
uid (B��#��#Ruid#
group_id (B��#��#RgroupIdI

iap_buy_times (2%.minirpc.RegularPack.IapBuyTimesEntryRiapBuyTimes]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersionf

start_time	 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsR	startTime>
IapBuyTimesEntry
key (Rkey
value (Rvalue:8:?��"��"kingdom��"regular_pack��'regular_pack��'regular_pack"�

GrowthFund
_id (	RId
uid (B��#��#Ruid!
fund_id (B��#��#RfundId
	buy_times (RbuyTimes]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion]
normal_reward_status	 (2+.minirpc.GrowthFund.NormalRewardStatusEntryRnormalRewardStatusT
vip_reward_status
 (2(.minirpc.GrowthFund.VipRewardStatusEntryRvipRewardStatusE
NormalRewardStatusEntry
key (Rkey
value (Rvalue:8B
VipRewardStatusEntry
key (Rkey
value (Rvalue:8:<��"��"kingdom��"growth_fund��'growth_fund��'growth_fund"�
FirstCharge
_id (	RId
uid (B��#��#Ruid%
	charge_id (B��#��#RchargeIdN
collect_status (2'.minirpc.FirstCharge.CollectStatusEntryR
collectStatus]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersions
first_charge_time	 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRfirstChargeTime@
CollectStatusEntry
key (Rkey
value (Rvalue:8:?��"��"kingdom��"first_charge��'first_charge��'first_charge"�
	MonthCard
_id (	RId
uid (B��#��#Ruid!
card_id (B��#��#RcardIdh
expire_time (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsR
expireTimes
last_receive_time (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRlastReceiveTime,
has_received_today (RhasReceivedToday]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version	 (RVersion:9��"��"kingdom��"
month_card��'
month_card��'
month_card"�

ClientVersion
_id (	RId$
platform (	B��#��#Rplatform/
client_version (	B��#��#R
clientVersion(
bundle_version_r (	RbundleVersionR(
bundle_version_g (	RbundleVersionG

bundle_md5 (	R	bundleMd5
preload_md5 (	R
preloadMd5

config_md5 (	R	configMd5
_version	 (RVersion]
ctime
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime:E��"��"kingdom��"client_version��'client_version��'client_version"�

HuatuoVersion
_id (	RId$
platform (	B��#��#Rplatform/
client_version (	B��#��#R
clientVersion%
target_version (	R
targetVersion
data (	Rdata$
bundle_path_mc (	RbundlePathMc"

bundle_cfg_mc (	RbundleCfgMc&
bundle_path_gog (	R
bundlePathGog$
bundle_cfg_gog	 (	RbundleCfgGog
job_id
 (	RjobId
_version (RVersion]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime
 (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime:E��"��"kingdom��"huatuo_version��'huatuo_version��'huatuo_version"�
TaskCounter
_id (	RId
uid (B��#��#Ruid'

counter_id (B��#��#R	counterIdK

counter_value (2&.minirpc.TaskCounter.CounterValueEntryRcounterValue]
ctime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRctime]
mtime (BG��Cgitlab-ee.funplus.io/backend-platform/zplus-go/timestamp.UTCSecondsRmtime
_version (RVersion?
CounterValueEntry
key (	Rkey
value (Rvalue:8:?��"��"kingdom��"task_counter��'task_counter��'task_counterB���������� ����"bproto3
�
share_enum.protominirpc-github.com/gogo/protobuf/gogoproto/gogo.protoclient/extension.proto*�
PushCmdType
PushCmdType_Sync -
PushCmdType_Order_Finish��(OrderResult:
PushCmdType_AGREE_JOIN_ALLIANCE��(AgreeJoinAlliance/
PushCmdType_Apply_Friend��(
AddFriendInfo6
PushCmdType_Handle_Apply_Friend��(
AddFriendInfo-
PushCmdType_Del_Friend��(
AddFriendInfo1
PushCmdType_Apply_Join_Alliance��(UserPush*�
UserJobEventType
BuildUpgradeLevel 
TrainTroops

HealTroops
ResearchJob
MonthCardEnd
TimeBenefitClear*�
TaskType
TaskTypeMain 
TaskTypeGrowth

TaskTypeDaily
TaskTypeWeekly
TaskTypeRepeat
TaskTypeAlliance
TaskTypeActivity
TaskTypeAchievement*^
QuestStatus
	QuestFree 

QuestActivity

QuestComplete

QuestRewarded���������*
ItemUseType

BoostSpeed *@
BuildingExtDataType
LastRefreshTime 
LastRefreshValue*;
	DebugType

DebugTypeNone 
DebugTypeFinishMainTask*Q
BuildingStatusType
BuildingStatusTypeNone 
BuildingStatusTypeUpgrading*�
UserDataType
UserDataTypeNone 
UserDataTypeIdleRewardTime1
UserDataTypeIdleRewardTime2#
UserDataTypeIdleLastCollectTime
UserDataTypeIdleUpdateTime1
UserDataTypeIdleUpdateTime2 
UserDataTypeEnergyUpdateTime&
"UserDataTypePhotoVoltaicUpdateTime!
UserDataTypeMonsterBookReward
UserDataTypeIdleRewardTime3	
UserDataTypeIdleUpdateTime3

UserDataTypeDailyTaskChest 
UserDataTypeDailyTaskRefresh#
UserDataTypeAllianceTaskRefresh
!
UserDataTypeAllianceTaskChest
UserDataTypeIdleRewardTime4
UserDataTypeIdleUpdateTime4
UserDataTypeSign7
UserDataTypeDay7QuestChest
UserDataTypeDay7QuestSelect#
UserDataTypeEnergyRecoveryValue!
UserDataTypeDay7NurseMailSend
UserDataTypeDailyRefresh
UserDataTypePatch
UserDataTypeDailySale
UserDataTypeMonthCard
UserDataTypeWeeklyRefresh"
UserDataTypeDailyWeekTaskChest*f
Sign7RecordType
Sign7RecordTypeNone 
Sign7RecordCollectedDay
Sign7RecordTypeCanCollect*[

RewardType
RewardTypeNone 
RewardTypeItem
RewardTypeHero

RewardTypeGem*�
MainLevelRewardType
MainLevelRewardTypeNone 
MainLevelRewardTypeOne
MainLevelRewardTypeTwo
MainLevelRewardTypeThree*N

MailStatus
MailStatusUnRead 
MailStatusRead
MailStatusRewarded*?
ActivityType
ActivityTypeNone 
ActivityTypeDay7QuestB���������� �� bproto3