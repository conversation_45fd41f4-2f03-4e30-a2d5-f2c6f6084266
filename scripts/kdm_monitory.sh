#!/bin/bash

if [ $# != 2 ]
then
    echo "Please input [kingdom id] [pprof port]"
    exit 1
fi

KID=$1
PPROF_PORT=$2
THRESHOLD=200
COMBAT_NUMS=600

OUTDIR=$(readlink -f "./out")
if [ ! -d "$OUTDIR" ]; then
  mkdir -p "$OUTDIR"
fi

# 飞书机器人webhook地址
WEBHOOK_URL="https://open.feishu.cn/open-apis/bot/v2/hook/b6dbdd2e-fd71-471e-826e-1042a36af45f"

# 设置curl请求的地址
url="http://127.0.0.1:$PPROF_PORT/metrics"
while true; do
    # 获取进程CPU占用率
    kingdomProcId=$(ps aux | grep "addrPProf" | grep ":$PPROF_PORT" | grep -v grep | awk '{print $2}')
    CPU=$(ps -p $kingdomProcId -o %cpu | tail -n 1 | tr -d ' ')
    formatted_date=$(date +"%Y-%m-%d %H:%M:%S")

    # 发送curl请求并获取响应
    response=$(curl -s "$url" | grep "march_state" | grep "combat")
    # 逐行读取响应内容，并提取最后一个数字
    while read -r line; do
      last_num=$(echo "$line" | awk '{print $NF}')
      if [ "$last_num" -ge $COMBAT_NUMS ]; then
        CONTENT="【报警】服务[K-$KID]战斗压力预警,战斗并发:$last_num CPU:$CPU% 关注服务器负载情况，UTC:$formatted_date"
        JSON='{"msg_type": "text", "content": {"text": "'$CONTENT'"}}'
        # 发送消息通知
        curl -H "Content-Type: application/json" -X POST -d "$JSON" $WEBHOOK_URL
      fi
      break
    done <<< "$response"

    # 判断是否超过阈值
    if (( $(echo "$CPU > $THRESHOLD" | bc -l) )); then
      CONTENT="【报警】服务[K-$KID]负载预警 CPU:$CPU% 关注服务器负载情况，UTC:$formatted_date"
      JSON='{"msg_type": "text", "content": {"text": "'$CONTENT'"}}'
      # 发送消息通知
      curl -H "Content-Type: application/json" -X POST -d "$JSON" $WEBHOOK_URL
    fi
    sleep 15
done
