package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"context"
	"encoding/json"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
)

func init() {
	http_post.AddPostHandler("getLoginRemain", getLoginRemain)
	http_post.AddPostHandler("getLoginData", getLoginData)
}
func getLoginRemain(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		values := r.URL.Query()
		day1 := values.Get("day1")
		if day1 == "" {
			fmt.Fprintf(w, "need day1 2024-01-01 ")
			return
		}
		day2 := values.Get("day2")
		if day2 == "" {
			fmt.Fprintf(w, "need day2 2024-01-01 ")
			return
		}
		result := rank.GetLoginRetention(day1, day2)

		// 将结果转换为JSON并返回
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(result)
	})
}

func getLoginData(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		values := r.URL.Query()
		date := values.Get("date")
		if date == "" {
			fmt.Fprintf(w, "need date parameter (format: 2024-01-01)")
			return
		}

		// 获取指定日期的登录数据
		result := rank.GetLoginData(date)

		// 设置响应头为JSON
		w.Header().Set("Content-Type", "application/json")
		// 返回JSON格式的数据
		json.NewEncoder(w).Encode(result)
	})
}
