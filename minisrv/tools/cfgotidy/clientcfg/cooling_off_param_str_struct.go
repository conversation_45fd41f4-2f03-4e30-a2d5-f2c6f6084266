// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

type CoolingOffParamStr struct {
	Hp      float32                `json:"Hp"`   // 生命警戒值
	Stop    bool                   `json:"Stop"` // 是否停止移动
	Time    float32                `json:"Time"` // 回血时间
	Buff    int32                  `json:"Buff"` // 回血Buff
	BuffRef *HeroSkillBuffTableCfg `json:"-"`    // 回血Buff
}

func NewCoolingOffParamStr() *CoolingOffParamStr {
	return &CoolingOffParamStr{
		Hp:      0.0,
		Stop:    false,
		Time:    0.0,
		Buff:    0,
		BuffRef: nil,
	}
}

func NewMockCoolingOffParamStr() *CoolingOffParamStr {
	return &CoolingOffParamStr{
		Hp:      0.0,
		Stop:    false,
		Time:    0.0,
		Buff:    0,
		BuffRef: nil,
	}
}

func (s *CoolingOffParamStr) bindRefs(c *Configs) {
	s.BuffRef = c.HeroSkillBuffTable.Get(s.Buff)
}
