package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/item"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 使用道具
func (_ KnightsHandlers) UserItemHandler(ctx *wctx.Context, req *wrpc.UserItemRequest) (*wrpc.UserItemReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		unlock := model.LockUser(ctx, req.UID)
		defer unlock()
		err = item.UserItem(ctx, req.UID, req.ItemId, req.Amount, req.Type, req.Para1, req.ExParas)
		rawResp = &wrpc.UserItemReply{
			Result: err == nil,
		}
	})
	return rawResp.(*wrpc.UserItemReply), err
}
