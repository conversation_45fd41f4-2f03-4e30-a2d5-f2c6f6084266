// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type SelectChestGroupTableCfg struct {
	Id      int32         `json:"Id"`     // Id
	Item    int32         `json:"Item"`   // 对应道具
	ItemRef *ItemTableCfg `json:"-"`      // 对应道具
	Reward  []*RewardKVS  `json:"Reward"` // 自选选项
}

func NewSelectChestGroupTableCfg() *SelectChestGroupTableCfg {
	return &SelectChestGroupTableCfg{
		Id:      0,
		Item:    0,
		ItemRef: nil,
		Reward:  []*RewardKVS{},
	}
}

type SelectChestGroupTable struct {
	records  map[int32]*SelectChestGroupTableCfg
	localIds map[int32]struct{}
}

func NewSelectChestGroupTable() *SelectChestGroupTable {
	return &SelectChestGroupTable{
		records:  map[int32]*SelectChestGroupTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *SelectChestGroupTable) Get(key int32) *SelectChestGroupTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *SelectChestGroupTable) GetAll() map[int32]*SelectChestGroupTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *SelectChestGroupTable) put(key int32, value *SelectChestGroupTableCfg, local bool) *SelectChestGroupTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *SelectChestGroupTable) Range(f func(v *SelectChestGroupTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *SelectChestGroupTable) Filter(filterFuncs ...func(v *SelectChestGroupTableCfg) bool) map[int32]*SelectChestGroupTableCfg {
	filtered := map[int32]*SelectChestGroupTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *SelectChestGroupTable) FilterSlice(filterFuncs ...func(v *SelectChestGroupTableCfg) bool) []*SelectChestGroupTableCfg {
	filtered := []*SelectChestGroupTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *SelectChestGroupTable) FilterKeys(filterFuncs ...func(v *SelectChestGroupTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *SelectChestGroupTable) satisfied(v *SelectChestGroupTableCfg, filterFuncs ...func(v *SelectChestGroupTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *SelectChestGroupTable) setupIndexes() error {
	return nil
}

func (t *SelectChestGroupTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *SelectChestGroupTableCfg) bindRefs(c *Configs) {
	r.ItemRef = c.ItemTable.Get(r.Item)
	for _, e := range r.Reward {
		e.bindRefs(c)
	}
}
