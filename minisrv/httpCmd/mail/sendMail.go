package mail

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
)

func init() {
	http_post.AddPostHandler("sendMail", sendMail)
}
func sendMail(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		//values := r.URL.Query()
		////通过uid删除
		//uidStr := values.Get("uid") // 获取查询参数q的值
		//if uidStr == "" {
		//	fmt.Fprintf(w, "need uid")
		//	return
		//}
		//uid, _ := strconv.ParseInt(uidStr, 10, 64)
		//model.SendSystemMail(ctx, uid, 1, "test", "test", int64(time.Now().Unix()+128400))
		fmt.Fprintf(w, "success send mail")
	})
}
