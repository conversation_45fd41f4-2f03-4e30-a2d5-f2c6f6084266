package model

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"context"
	"github.com/sasha-s/go-deadlock"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/locker"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"sync"
)

func init() {
	orm.RegisterModel[*HuatuoVersionModel](nil)
}

type HuatuoVersionModel struct {
	mu     deadlock.RWMutex
	locker sync.RWMutex
	minirpc.HuatuoVersionRecord
}

func (h *HuatuoVersionModel) Locker() locker.RWLocker {
	return nil
}

func (h *HuatuoVersionModel) LockPriority() int {
	return 0
}

func (h *HuatuoVersionModel) IsGroup() bool {
	return false
}

func GetHuatuoVersionModel(ctx context.Context, platform string, huatuoVersion string) (*HuatuoVersionModel, error) {
	m, err := orm.Get[*HuatuoVersionModel](ctx, platform, huatuoVersion)
	return m, err
}

func GetPlatformHuatuoVersion(ctx context.Context, platform string) ([]*HuatuoVersionModel, error) {
	ms, err := orm.GetAll[*HuatuoVersionModel](ctx, platform)
	return ms, err
}
