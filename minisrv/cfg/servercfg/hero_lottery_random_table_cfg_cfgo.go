// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/invopop/jsonschema"
	"github.com/xeipuuv/gojsonschema"
	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t *HeroLotteryRandomTable) loadJson(dir string, configs *Configs) error {
	jsonPath := filepath.Join(dir, "HeroLotteryRandomTable.json")
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[HeroLotteryRandomTable]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*HeroLotteryRandomTableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[HeroLotteryRandomTable]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		t.put(k, v, true)

	}
	return nil
}

func (t *HeroLotteryRandomTable) checkCompatibility(jsonStr string) error {
	reflector := &jsonschema.Reflector{}
	reflector.AllowAdditionalProperties = true

	schema := reflector.Reflect(&HeroLotteryRandomTableCfg{})
	signatureBytes, err := schema.MarshalJSON()
	if err != nil {
		return fmt.Errorf("[HeroLotteryRandomTable]marshal schema error, err=[%w]", err)
	}
	validateSchema, err := gojsonschema.NewSchema(gojsonschema.NewStringLoader(string(signatureBytes)))
	if err != nil {
		return fmt.Errorf("[HeroLotteryRandomTable]new validate schema error, err=[%w]", err)
	}
	result, err := validateSchema.Validate(gojsonschema.NewStringLoader(jsonStr))
	if err != nil {
		return fmt.Errorf("[HeroLotteryRandomTable]validate json error, err=[%w]", err)
	}
	if !result.Valid() {
		return &ValidateError{
			TableName: "HeroLotteryRandomTable",
			Errors:    result.Errors(),
		}
	}
	return nil
}
