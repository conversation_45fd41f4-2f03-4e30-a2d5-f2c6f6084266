// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type BossDungeonTableCfg struct {
	Id        int32            `json:"Id"`       // Id
	StringId  string           `json:"StringId"` // StringId
	Day       int32            `json:"Day"`      // 每周第x天
	Boss      int32            `json:"Boss"`     // Boss信息
	BossRef   *MonsterTableCfg `json:"-"`        // Boss信息
	Reward    []int32          `json:"Reward"`   // 奖励预览
	RewardRef []*ItemTableCfg  `json:"-"`        // 奖励预览
}

func NewBossDungeonTableCfg() *BossDungeonTableCfg {
	return &BossDungeonTableCfg{
		Id:        0,
		StringId:  "",
		Day:       0,
		Boss:      0,
		BossRef:   nil,
		Reward:    []int32{},
		RewardRef: []*ItemTableCfg{},
	}
}

func NewMockBossDungeonTableCfg() *BossDungeonTableCfg {
	return &BossDungeonTableCfg{
		Id:        0,
		StringId:  "",
		Day:       0,
		Boss:      0,
		BossRef:   nil,
		Reward:    []int32{0},
		RewardRef: []*ItemTableCfg{},
	}
}

type BossDungeonTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*BossDungeonTableCfg
	localIds         map[int32]struct{}
}

func NewBossDungeonTable(configs *Configs) *BossDungeonTable {
	return &BossDungeonTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*BossDungeonTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *BossDungeonTable) Get(key int32) *BossDungeonTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *BossDungeonTable) GetAll() map[int32]*BossDungeonTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *BossDungeonTable) put(key int32, value *BossDungeonTableCfg, local bool) *BossDungeonTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *BossDungeonTable) putFromInheritedTable(key int32, value *BossDungeonTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[BossDungeonTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *BossDungeonTable) Put(key int32, value *BossDungeonTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[BossDungeonTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *BossDungeonTable) PutAll(m map[int32]*BossDungeonTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *BossDungeonTable) Range(f func(v *BossDungeonTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *BossDungeonTable) Filter(filterFuncs ...func(v *BossDungeonTableCfg) bool) map[int32]*BossDungeonTableCfg {
	filtered := map[int32]*BossDungeonTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *BossDungeonTable) FilterSlice(filterFuncs ...func(v *BossDungeonTableCfg) bool) []*BossDungeonTableCfg {
	filtered := []*BossDungeonTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *BossDungeonTable) FilterKeys(filterFuncs ...func(v *BossDungeonTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *BossDungeonTable) satisfied(v *BossDungeonTableCfg, filterFuncs ...func(v *BossDungeonTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *BossDungeonTable) setupIndexes() error {
	return nil
}

func (t *BossDungeonTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *BossDungeonTableCfg) bindRefs(c *Configs) {
	r.BossRef = c.MonsterTable.Get(r.Boss)
	for _, e := range r.Reward {
		cfgoRefRecord := c.ItemTable.Get(e)
		r.RewardRef = append(r.RewardRef, cfgoRefRecord)
	}
}

func (t *BossDungeonTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[BossDungeonTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewBossDungeonTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [BossDungeonTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[BossDungeonTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// Day
		{
			if record[t.getIndexInCsv("Day")] == "" {
				recordCfg.Day = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Day")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [BossDungeonTable]unmarshal csv record failed, varName=Day, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Day")], err)
					} else {
						return fmt.Errorf("[BossDungeonTable]unmarshal csv record failed, varName=Day, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Day")], err)
					}
				}
				recordCfg.Day = int32(cfgoInt)
			}
		}
		// Boss
		if record[t.getIndexInCsv("Boss")] == "" {
			recordCfg.Boss = 0
		} else {
			var err error
			recordCfg.Boss, err = configs.MonsterTable.getIdByRef(record[t.getIndexInCsv("Boss")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [BossDungeonTable]unmarshal csv record failed, varName=Boss, type=ref@MonsterTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Boss")], err)
				} else {
					return fmt.Errorf("[BossDungeonTable]unmarshal csv record failed, varName=Boss, type=ref@MonsterTable, value=%s, err:[%s]", record[t.getIndexInCsv("Boss")], err)
				}
			}
		}
		// Reward
		{
			cfgoMeetNilForRewardOfRecordCfg := false
			// element 0 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Reward1")] != "" {
					cfgoMeetNilForRewardOfRecordCfg = false
					var err error
					cfgoElemOfRewardOfRecordCfg, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward1")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [BossDungeonTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward1")], err)
						} else {
							return fmt.Errorf("[BossDungeonTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Reward1")], err)
						}
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}
			// element 1 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Reward2")] != "" {
					cfgoMeetNilForRewardOfRecordCfg = false
					var err error
					cfgoElemOfRewardOfRecordCfg, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward2")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [BossDungeonTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward2")], err)
						} else {
							return fmt.Errorf("[BossDungeonTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Reward2")], err)
						}
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}
			// element 2 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Reward3")] != "" {
					cfgoMeetNilForRewardOfRecordCfg = false
					var err error
					cfgoElemOfRewardOfRecordCfg, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward3")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [BossDungeonTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward3")], err)
						} else {
							return fmt.Errorf("[BossDungeonTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Reward3")], err)
						}
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}
			// element 3 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Reward4")] != "" {
					cfgoMeetNilForRewardOfRecordCfg = false
					var err error
					cfgoElemOfRewardOfRecordCfg, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward4")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [BossDungeonTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward4")], err)
						} else {
							return fmt.Errorf("[BossDungeonTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Reward4")], err)
						}
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}

		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [BossDungeonTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[BossDungeonTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *BossDungeonTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "BossDungeonTable.csv") && (!strings.HasPrefix(fileName, "BossDungeonTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for BossDungeonTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[BossDungeonTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[BossDungeonTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[BossDungeonTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[BossDungeonTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[BossDungeonTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[BossDungeonTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[BossDungeonTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[BossDungeonTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [BossDungeonTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *BossDungeonTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[BossDungeonTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [BossDungeonTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *BossDungeonTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[BossDungeonTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *BossDungeonTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[BossDungeonTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[BossDungeonTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
