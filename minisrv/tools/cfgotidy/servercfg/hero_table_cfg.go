// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type HeroTableCfg struct {
	Id                 int32                   `json:"Id"`                // Id
	ItemId             int32                   `json:"ItemId"`            // 对应道具表ID
	ItemIdRef          *ItemTableCfg           `json:"-"`                 // 对应道具表ID
	ItemGeneId         int32                   `json:"ItemGeneId"`        // 对应基因道具ID
	ItemGeneIdRef      *ItemTableCfg           `json:"-"`                 // 对应基因道具ID
	StarUpCostItem     int32                   `json:"StarUpCostItem"`    // 升星消耗道具
	StarUpCostItemRef  *ItemTableCfg           `json:"-"`                 // 升星消耗道具
	GradeUpCostItem    int32                   `json:"GradeUpCostItem"`   // 升品消耗道具
	GradeUpCostItemRef *ItemTableCfg           `json:"-"`                 // 升品消耗道具
	StarPlanID         HeroStarUpPlan          `json:"StarPlanID"`        // 升星方案
	LevelPlanID        HeroLevelUpPlan         `json:"LevelPlanID"`       // 升级方案
	HeroCareer         HeroCareer              `json:"HeroCareer"`        // 英雄职业
	HeroElement        int32                   `json:"HeroElement"`       // 英雄元素
	HeroElementRef     *HeroElementTableCfg    `json:"-"`                 // 英雄元素
	HeroQuality        HeroQuality             `json:"HeroQuality"`       // 英雄品质
	HeroType           HeroType                `json:"HeroType"`          // 英雄类型
	HasAtkFirstCareer  bool                    `json:"HasAtkFirstCareer"` // 是否存在优先攻击职业
	AtkFirstCareer     HeroCareer              `json:"AtkFirstCareer"`    // 优先攻击职业
	SkillRectangle     HeroSkillRangePolygon   `json:"SkillRectangle"`    // 施法形状，没扇形
	CollisionRadius    float32                 `json:"CollisionRadius"`   // 碰撞半径
	HitSkill           int32                   `json:"HitSkill"`          // 主动
	HitSkillRef        *HeroSkillGroupTableCfg `json:"-"`                 // 主动
	NegativeSkill      int32                   `json:"NegativeSkill"`     // 被动
	NegativeSkillRef   *HeroSkillGroupTableCfg `json:"-"`                 // 被动
	GiftSkill          int32                   `json:"GiftSkill"`         // 天赋
	GiftSkillRef       *HeroSkillGroupTableCfg `json:"-"`                 // 天赋
	GiftSkillBenefits  []*BenefitsKVS          `json:"GiftSkillBenefits"` // 天赋技能属性
}

func NewHeroTableCfg() *HeroTableCfg {
	return &HeroTableCfg{
		Id:                 0,
		ItemId:             0,
		ItemIdRef:          nil,
		ItemGeneId:         0,
		ItemGeneIdRef:      nil,
		StarUpCostItem:     0,
		StarUpCostItemRef:  nil,
		GradeUpCostItem:    0,
		GradeUpCostItemRef: nil,
		StarPlanID:         HeroStarUpPlan(enumDefaultValue),
		LevelPlanID:        HeroLevelUpPlan(enumDefaultValue),
		HeroCareer:         HeroCareer(enumDefaultValue),
		HeroElement:        0,
		HeroElementRef:     nil,
		HeroQuality:        HeroQuality(enumDefaultValue),
		HeroType:           HeroType(enumDefaultValue),
		HasAtkFirstCareer:  false,
		AtkFirstCareer:     HeroCareer(enumDefaultValue),
		SkillRectangle:     HeroSkillRangePolygon(enumDefaultValue),
		CollisionRadius:    0.0,
		HitSkill:           0,
		HitSkillRef:        nil,
		NegativeSkill:      0,
		NegativeSkillRef:   nil,
		GiftSkill:          0,
		GiftSkillRef:       nil,
		GiftSkillBenefits:  []*BenefitsKVS{},
	}
}

func NewMockHeroTableCfg() *HeroTableCfg {
	return &HeroTableCfg{
		Id:                 0,
		ItemId:             0,
		ItemIdRef:          nil,
		ItemGeneId:         0,
		ItemGeneIdRef:      nil,
		StarUpCostItem:     0,
		StarUpCostItemRef:  nil,
		GradeUpCostItem:    0,
		GradeUpCostItemRef: nil,
		StarPlanID:         HeroStarUpPlan(enumDefaultValue),
		LevelPlanID:        HeroLevelUpPlan(enumDefaultValue),
		HeroCareer:         HeroCareer(enumDefaultValue),
		HeroElement:        0,
		HeroElementRef:     nil,
		HeroQuality:        HeroQuality(enumDefaultValue),
		HeroType:           HeroType(enumDefaultValue),
		HasAtkFirstCareer:  false,
		AtkFirstCareer:     HeroCareer(enumDefaultValue),
		SkillRectangle:     HeroSkillRangePolygon(enumDefaultValue),
		CollisionRadius:    0.0,
		HitSkill:           0,
		HitSkillRef:        nil,
		NegativeSkill:      0,
		NegativeSkillRef:   nil,
		GiftSkill:          0,
		GiftSkillRef:       nil,
		GiftSkillBenefits:  []*BenefitsKVS{NewMockBenefitsKVS()},
	}
}

type HeroTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*HeroTableCfg
	localIds         map[int32]struct{}
}

func NewHeroTable(configs *Configs) *HeroTable {
	return &HeroTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*HeroTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *HeroTable) Get(key int32) *HeroTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *HeroTable) GetAll() map[int32]*HeroTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *HeroTable) put(key int32, value *HeroTableCfg, local bool) *HeroTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *HeroTable) putFromInheritedTable(key int32, value *HeroTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[HeroTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *HeroTable) Put(key int32, value *HeroTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[HeroTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *HeroTable) PutAll(m map[int32]*HeroTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *HeroTable) Range(f func(v *HeroTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *HeroTable) Filter(filterFuncs ...func(v *HeroTableCfg) bool) map[int32]*HeroTableCfg {
	filtered := map[int32]*HeroTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *HeroTable) FilterSlice(filterFuncs ...func(v *HeroTableCfg) bool) []*HeroTableCfg {
	filtered := []*HeroTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *HeroTable) FilterKeys(filterFuncs ...func(v *HeroTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *HeroTable) satisfied(v *HeroTableCfg, filterFuncs ...func(v *HeroTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *HeroTable) setupIndexes() error {
	return nil
}

func (t *HeroTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *HeroTableCfg) bindRefs(c *Configs) {
	r.ItemIdRef = c.ItemTable.Get(r.ItemId)
	r.ItemGeneIdRef = c.ItemTable.Get(r.ItemGeneId)
	r.StarUpCostItemRef = c.ItemTable.Get(r.StarUpCostItem)
	r.GradeUpCostItemRef = c.ItemTable.Get(r.GradeUpCostItem)
	r.HeroElementRef = c.HeroElementTable.Get(r.HeroElement)
	r.HitSkillRef = c.HeroSkillGroupTable.Get(r.HitSkill)
	r.NegativeSkillRef = c.HeroSkillGroupTable.Get(r.NegativeSkill)
	r.GiftSkillRef = c.HeroSkillGroupTable.Get(r.GiftSkill)
	for _, e := range r.GiftSkillBenefits {
		e.bindRefs(c)
	}
}

func (t *HeroTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[HeroTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewHeroTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// ItemId
		if record[t.getIndexInCsv("ItemId")] == "" {
			recordCfg.ItemId = 0
		} else {
			var err error
			recordCfg.ItemId, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("ItemId")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=ItemId, type=ref@ItemTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("ItemId")], err)
				} else {
					return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=ItemId, type=ref@ItemTable, value=%s, err:[%s]", record[t.getIndexInCsv("ItemId")], err)
				}
			}
		}
		// ItemGeneId
		if record[t.getIndexInCsv("ItemGeneId")] == "" {
			recordCfg.ItemGeneId = 0
		} else {
			var err error
			recordCfg.ItemGeneId, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("ItemGeneId")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=ItemGeneId, type=ref@ItemTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("ItemGeneId")], err)
				} else {
					return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=ItemGeneId, type=ref@ItemTable, value=%s, err:[%s]", record[t.getIndexInCsv("ItemGeneId")], err)
				}
			}
		}
		// StarUpCostItem
		if record[t.getIndexInCsv("StarUpCostItem")] == "" {
			recordCfg.StarUpCostItem = 0
		} else {
			var err error
			recordCfg.StarUpCostItem, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("StarUpCostItem")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=StarUpCostItem, type=ref@ItemTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("StarUpCostItem")], err)
				} else {
					return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=StarUpCostItem, type=ref@ItemTable, value=%s, err:[%s]", record[t.getIndexInCsv("StarUpCostItem")], err)
				}
			}
		}
		// GradeUpCostItem
		if record[t.getIndexInCsv("GradeUpCostItem")] == "" {
			recordCfg.GradeUpCostItem = 0
		} else {
			var err error
			recordCfg.GradeUpCostItem, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("GradeUpCostItem")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=GradeUpCostItem, type=ref@ItemTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("GradeUpCostItem")], err)
				} else {
					return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=GradeUpCostItem, type=ref@ItemTable, value=%s, err:[%s]", record[t.getIndexInCsv("GradeUpCostItem")], err)
				}
			}
		}
		// StarPlanID
		{
			if record[t.getIndexInCsv("StarPlanID")] == "" {
				recordCfg.StarPlanID = HeroStarUpPlan(enumDefaultValue)
			} else {
				cfgoEnum, err := parseHeroStarUpPlan(record[t.getIndexInCsv("StarPlanID")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=StarPlanID, type=enum@HeroStarUpPlan, value=%s, err:[%s]\n", record[t.getIndexInCsv("StarPlanID")], err)
					} else {
						return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=StarPlanID, type=enum@HeroStarUpPlan, value=%s, err:[%s]", record[t.getIndexInCsv("StarPlanID")], err)
					}
				}
				recordCfg.StarPlanID = cfgoEnum
			}
		}
		// LevelPlanID
		{
			if record[t.getIndexInCsv("LevelPlanID")] == "" {
				recordCfg.LevelPlanID = HeroLevelUpPlan(enumDefaultValue)
			} else {
				cfgoEnum, err := parseHeroLevelUpPlan(record[t.getIndexInCsv("LevelPlanID")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=LevelPlanID, type=enum@HeroLevelUpPlan, value=%s, err:[%s]\n", record[t.getIndexInCsv("LevelPlanID")], err)
					} else {
						return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=LevelPlanID, type=enum@HeroLevelUpPlan, value=%s, err:[%s]", record[t.getIndexInCsv("LevelPlanID")], err)
					}
				}
				recordCfg.LevelPlanID = cfgoEnum
			}
		}
		// HeroCareer
		{
			if record[t.getIndexInCsv("HeroCareer")] == "" {
				recordCfg.HeroCareer = HeroCareer(enumDefaultValue)
			} else {
				cfgoEnum, err := parseHeroCareer(record[t.getIndexInCsv("HeroCareer")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=HeroCareer, type=enum@HeroCareer, value=%s, err:[%s]\n", record[t.getIndexInCsv("HeroCareer")], err)
					} else {
						return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=HeroCareer, type=enum@HeroCareer, value=%s, err:[%s]", record[t.getIndexInCsv("HeroCareer")], err)
					}
				}
				recordCfg.HeroCareer = cfgoEnum
			}
		}
		// HeroElement
		if record[t.getIndexInCsv("HeroElement")] == "" {
			recordCfg.HeroElement = 0
		} else {
			var err error
			recordCfg.HeroElement, err = configs.HeroElementTable.getIdByRef(record[t.getIndexInCsv("HeroElement")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=HeroElement, type=ref@HeroElementTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("HeroElement")], err)
				} else {
					return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=HeroElement, type=ref@HeroElementTable, value=%s, err:[%s]", record[t.getIndexInCsv("HeroElement")], err)
				}
			}
		}
		// HeroQuality
		{
			if record[t.getIndexInCsv("HeroQuality")] == "" {
				recordCfg.HeroQuality = HeroQuality(enumDefaultValue)
			} else {
				cfgoEnum, err := parseHeroQuality(record[t.getIndexInCsv("HeroQuality")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=HeroQuality, type=enum@HeroQuality, value=%s, err:[%s]\n", record[t.getIndexInCsv("HeroQuality")], err)
					} else {
						return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=HeroQuality, type=enum@HeroQuality, value=%s, err:[%s]", record[t.getIndexInCsv("HeroQuality")], err)
					}
				}
				recordCfg.HeroQuality = cfgoEnum
			}
		}
		// HeroType
		{
			if record[t.getIndexInCsv("HeroType")] == "" {
				recordCfg.HeroType = HeroType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseHeroType(record[t.getIndexInCsv("HeroType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=HeroType, type=enum@HeroType, value=%s, err:[%s]\n", record[t.getIndexInCsv("HeroType")], err)
					} else {
						return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=HeroType, type=enum@HeroType, value=%s, err:[%s]", record[t.getIndexInCsv("HeroType")], err)
					}
				}
				recordCfg.HeroType = cfgoEnum
			}
		}
		// HasAtkFirstCareer
		{
			if record[t.getIndexInCsv("HasAtkFirstCareer")] == "" {
				recordCfg.HasAtkFirstCareer = false
			} else {
				var err error
				recordCfg.HasAtkFirstCareer, err = strconv.ParseBool(record[t.getIndexInCsv("HasAtkFirstCareer")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=HasAtkFirstCareer, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("HasAtkFirstCareer")], err)
					} else {
						return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=HasAtkFirstCareer, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("HasAtkFirstCareer")], err)
					}
				}
			}
		}
		// AtkFirstCareer
		{
			if record[t.getIndexInCsv("AtkFirstCareer")] == "" {
				recordCfg.AtkFirstCareer = HeroCareer(enumDefaultValue)
			} else {
				cfgoEnum, err := parseHeroCareer(record[t.getIndexInCsv("AtkFirstCareer")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=AtkFirstCareer, type=enum@HeroCareer, value=%s, err:[%s]\n", record[t.getIndexInCsv("AtkFirstCareer")], err)
					} else {
						return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=AtkFirstCareer, type=enum@HeroCareer, value=%s, err:[%s]", record[t.getIndexInCsv("AtkFirstCareer")], err)
					}
				}
				recordCfg.AtkFirstCareer = cfgoEnum
			}
		}
		// SkillRectangle
		{
			if record[t.getIndexInCsv("SkillRectangle")] == "" {
				recordCfg.SkillRectangle = HeroSkillRangePolygon(enumDefaultValue)
			} else {
				cfgoEnum, err := parseHeroSkillRangePolygon(record[t.getIndexInCsv("SkillRectangle")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=SkillRectangle, type=enum@HeroSkillRangePolygon, value=%s, err:[%s]\n", record[t.getIndexInCsv("SkillRectangle")], err)
					} else {
						return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=SkillRectangle, type=enum@HeroSkillRangePolygon, value=%s, err:[%s]", record[t.getIndexInCsv("SkillRectangle")], err)
					}
				}
				recordCfg.SkillRectangle = cfgoEnum
			}
		}
		// CollisionRadius
		{
			if record[t.getIndexInCsv("CollisionRadius")] == "" {
				recordCfg.CollisionRadius = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("CollisionRadius")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=CollisionRadius, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("CollisionRadius")], err)
					} else {
						return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=CollisionRadius, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("CollisionRadius")], err)
					}
				}
				recordCfg.CollisionRadius = float32(cfgoFloat)
			}
		}
		// HitSkill
		if record[t.getIndexInCsv("HitSkill")] == "" {
			recordCfg.HitSkill = 0
		} else {
			var err error
			recordCfg.HitSkill, err = configs.HeroSkillGroupTable.getIdByRef(record[t.getIndexInCsv("HitSkill")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=HitSkill, type=ref@HeroSkillGroupTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("HitSkill")], err)
				} else {
					return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=HitSkill, type=ref@HeroSkillGroupTable, value=%s, err:[%s]", record[t.getIndexInCsv("HitSkill")], err)
				}
			}
		}
		// NegativeSkill
		if record[t.getIndexInCsv("NegativeSkill")] == "" {
			recordCfg.NegativeSkill = 0
		} else {
			var err error
			recordCfg.NegativeSkill, err = configs.HeroSkillGroupTable.getIdByRef(record[t.getIndexInCsv("NegativeSkill")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=NegativeSkill, type=ref@HeroSkillGroupTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("NegativeSkill")], err)
				} else {
					return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=NegativeSkill, type=ref@HeroSkillGroupTable, value=%s, err:[%s]", record[t.getIndexInCsv("NegativeSkill")], err)
				}
			}
		}
		// GiftSkill
		if record[t.getIndexInCsv("GiftSkill")] == "" {
			recordCfg.GiftSkill = 0
		} else {
			var err error
			recordCfg.GiftSkill, err = configs.HeroSkillGroupTable.getIdByRef(record[t.getIndexInCsv("GiftSkill")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, varName=GiftSkill, type=ref@HeroSkillGroupTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("GiftSkill")], err)
				} else {
					return fmt.Errorf("[HeroTable]unmarshal csv record failed, varName=GiftSkill, type=ref@HeroSkillGroupTable, value=%s, err:[%s]", record[t.getIndexInCsv("GiftSkill")], err)
				}
			}
		}
		// GiftSkillBenefits
		{
			cfgoMeetNilForGiftSkillBenefitsOfRecordCfg := false
			// element 0 of GiftSkillBenefits
			if !cfgoMeetNilForGiftSkillBenefitsOfRecordCfg {
				cfgoMeetNilForGiftSkillBenefitsOfRecordCfg = true
				var cfgoElemOfGiftSkillBenefitsOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
				{
					if record[t.getIndexInCsv("GiftSkillBenefits1BenefitsID")] != "" {
						cfgoMeetNilForGiftSkillBenefitsOfRecordCfg = false
						var err error
						cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("GiftSkillBenefits1BenefitsID")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("GiftSkillBenefits1BenefitsID")], err)
							} else {
								return fmt.Errorf("[HeroTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("GiftSkillBenefits1BenefitsID")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("GiftSkillBenefits1BenefitsValue")] != "" {
						cfgoMeetNilForGiftSkillBenefitsOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GiftSkillBenefits1BenefitsValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GiftSkillBenefits1BenefitsValue")], err)
							} else {
								return fmt.Errorf("[HeroTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("GiftSkillBenefits1BenefitsValue")], err)
							}
						}
						cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsValue = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForGiftSkillBenefitsOfRecordCfg {
					recordCfg.GiftSkillBenefits = append(recordCfg.GiftSkillBenefits, cfgoElemOfGiftSkillBenefitsOfRecordCfg)
				}
			}
			// element 1 of GiftSkillBenefits
			if !cfgoMeetNilForGiftSkillBenefitsOfRecordCfg {
				cfgoMeetNilForGiftSkillBenefitsOfRecordCfg = true
				var cfgoElemOfGiftSkillBenefitsOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
				{
					if record[t.getIndexInCsv("GiftSkillBenefits2BenefitsID")] != "" {
						cfgoMeetNilForGiftSkillBenefitsOfRecordCfg = false
						var err error
						cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("GiftSkillBenefits2BenefitsID")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("GiftSkillBenefits2BenefitsID")], err)
							} else {
								return fmt.Errorf("[HeroTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("GiftSkillBenefits2BenefitsID")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("GiftSkillBenefits2BenefitsValue")] != "" {
						cfgoMeetNilForGiftSkillBenefitsOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GiftSkillBenefits2BenefitsValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GiftSkillBenefits2BenefitsValue")], err)
							} else {
								return fmt.Errorf("[HeroTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("GiftSkillBenefits2BenefitsValue")], err)
							}
						}
						cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsValue = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForGiftSkillBenefitsOfRecordCfg {
					recordCfg.GiftSkillBenefits = append(recordCfg.GiftSkillBenefits, cfgoElemOfGiftSkillBenefitsOfRecordCfg)
				}
			}
			// element 2 of GiftSkillBenefits
			if !cfgoMeetNilForGiftSkillBenefitsOfRecordCfg {
				cfgoMeetNilForGiftSkillBenefitsOfRecordCfg = true
				var cfgoElemOfGiftSkillBenefitsOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
				{
					if record[t.getIndexInCsv("GiftSkillBenefits3BenefitsID")] != "" {
						cfgoMeetNilForGiftSkillBenefitsOfRecordCfg = false
						var err error
						cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("GiftSkillBenefits3BenefitsID")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("GiftSkillBenefits3BenefitsID")], err)
							} else {
								return fmt.Errorf("[HeroTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("GiftSkillBenefits3BenefitsID")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("GiftSkillBenefits3BenefitsValue")] != "" {
						cfgoMeetNilForGiftSkillBenefitsOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GiftSkillBenefits3BenefitsValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GiftSkillBenefits3BenefitsValue")], err)
							} else {
								return fmt.Errorf("[HeroTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("GiftSkillBenefits3BenefitsValue")], err)
							}
						}
						cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsValue = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForGiftSkillBenefitsOfRecordCfg {
					recordCfg.GiftSkillBenefits = append(recordCfg.GiftSkillBenefits, cfgoElemOfGiftSkillBenefitsOfRecordCfg)
				}
			}
			// element 3 of GiftSkillBenefits
			if !cfgoMeetNilForGiftSkillBenefitsOfRecordCfg {
				cfgoMeetNilForGiftSkillBenefitsOfRecordCfg = true
				var cfgoElemOfGiftSkillBenefitsOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
				{
					if record[t.getIndexInCsv("GiftSkillBenefits4BenefitsID")] != "" {
						cfgoMeetNilForGiftSkillBenefitsOfRecordCfg = false
						var err error
						cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("GiftSkillBenefits4BenefitsID")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("GiftSkillBenefits4BenefitsID")], err)
							} else {
								return fmt.Errorf("[HeroTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("GiftSkillBenefits4BenefitsID")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("GiftSkillBenefits4BenefitsValue")] != "" {
						cfgoMeetNilForGiftSkillBenefitsOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GiftSkillBenefits4BenefitsValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GiftSkillBenefits4BenefitsValue")], err)
							} else {
								return fmt.Errorf("[HeroTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("GiftSkillBenefits4BenefitsValue")], err)
							}
						}
						cfgoElemOfGiftSkillBenefitsOfRecordCfg.BenefitsValue = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForGiftSkillBenefitsOfRecordCfg {
					recordCfg.GiftSkillBenefits = append(recordCfg.GiftSkillBenefits, cfgoElemOfGiftSkillBenefitsOfRecordCfg)
				}
			}

		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [HeroTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[HeroTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *HeroTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "HeroTable.csv") && (!strings.HasPrefix(fileName, "HeroTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for HeroTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[HeroTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[HeroTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[HeroTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[HeroTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[HeroTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[HeroTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[HeroTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[HeroTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [HeroTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *HeroTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[HeroTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [HeroTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *HeroTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[HeroTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *HeroTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[HeroTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[HeroTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
