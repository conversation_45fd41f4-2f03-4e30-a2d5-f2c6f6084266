package hero

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/function_open"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"sort"
	"strconv"
)

func InitSymbiotic(ctx context.Context, uid int64) *wrpc.InitSymbioticRet {

	if !function_open.CheckReqMainLevel(ctx, uid, constDef.FUNCTION_SYMBIOTIC) {
		return nil
	}

	//获取等级最高的 5 个英雄
	heroes, _ := model.GetAllHeros(ctx, uid)
	if heroes == nil {
		return nil
	}
	if len(heroes) < 5 {
		return nil
	}
	sort.Slice(heroes, func(i, j int) bool {
		heroLevelLine := cfg_mgr.Cfg.HeroLevelTable.Get(heroes[i].LevelId())
		heroLevelLine2 := cfg_mgr.Cfg.HeroLevelTable.Get(heroes[j].LevelId())
		return heroLevelLine.HeroLevel > heroLevelLine2.HeroLevel
	})
	var symbioticHeroIds []int32
	for _, v := range heroes[0:5] {
		symbioticHeroIds = append(symbioticHeroIds, v.ConfigId())
	}
	userModel, _ := model.GetUserModel(ctx, uid)
	userModel.SetHeroSymbiotic(ctx, symbioticHeroIds)
	resetHeroIds := []int32{}
	resetItemMap := map[int32]int32{}
	//将剩下的英雄设置为 1 级，并且退换升级材料
	for _, v := range heroes[5:] {
		hero, _ := model.GetHero(ctx, uid, v.ConfigId())
		if hero == nil {
			continue
		}
		//获取升级到当前等级需要的原料
		heroLevelLine := cfg_mgr.Cfg.HeroLevelTable.Get(hero.LevelId())
		if heroLevelLine == nil {
			continue
		}
		if heroLevelLine.HeroLevel == 1 {
			continue
		}
		heroLevelLines := cfg_mgr.Cfg.HeroLevelTable.FilterSlice(func(line *servercfg.HeroLevelTableCfg) bool {
			return line.PlanID == heroLevelLine.PlanID && line.HeroLevel < heroLevelLine.HeroLevel
		})
		for _, lv := range heroLevelLines {
			for _, cost := range lv.LevelUp {
				model.AddItem(ctx, uid, cost.CostType, int64(cost.CostValue), bi.ItemFlowReason_HERO_SYMBIOTIC, strconv.Itoa(int(hero.ConfigId())), util.GenerateSequenceString())
				resetItemMap[cost.CostType] += cost.CostValue
			}
			if lv.HeroLevel == 1 {
				hero.SetLevelId(ctx, lv.Id)
				resetHeroIds = append(resetHeroIds, hero.ConfigId())
			}
		}

	}
	updateSymbioticLevel(ctx, uid)
	return &wrpc.InitSymbioticRet{
		ResetHeroId:  resetHeroIds,
		ResetItemMap: resetItemMap,
	}

}

func updateSymbioticLevel(ctx context.Context, uid int64) {
	if !function_open.CheckReqMainLevel(ctx, uid, constDef.FUNCTION_SIGN7) {
		return
	}
	userModel, _ := model.GetUserModel(ctx, uid)
	symbioticHeroIds := userModel.HeroSymbiotic()
	if symbioticHeroIds == nil {
		return
	}
	minLevel := int32(0)
	for _, v := range symbioticHeroIds {
		hero, _ := model.GetHero(ctx, uid, v)
		if hero == nil {
			continue
		}
		heroLevelLine := cfg_mgr.Cfg.HeroLevelTable.Get(hero.LevelId())
		if heroLevelLine == nil {
			continue
		}
		if minLevel == 0 {
			minLevel = heroLevelLine.HeroLevel
		} else {
			minLevel = util.MinInt32(minLevel, heroLevelLine.HeroLevel)
		}
	}
	heros, _ := model.GetAllHeros(ctx, uid)
	if heros == nil {
		return
	}
	for _, v := range heros {
		heroLevelLine := cfg_mgr.Cfg.HeroLevelTable.Get(v.LevelId())
		if heroLevelLine == nil {
			continue
		}
		if heroLevelLine.HeroLevel >= minLevel {
			continue
		}
		minLevelLine := cfg_mgr.Cfg.HeroLevelTable.FilterSlice(func(line *servercfg.HeroLevelTableCfg) bool {
			return line.PlanID == heroLevelLine.PlanID && line.HeroLevel == minLevel
		})
		if minLevelLine == nil {
			continue
		}
		v.SetLevelId(ctx, minLevelLine[0].Id)
	}
}

func SwitchSymbioticHero(ctx context.Context, uid int64, inHeroId int32, outHeroId int32) error {
	//判断 outHeroId 是否在共生列表中
	userModel, _ := model.GetUserModel(ctx, uid)
	symbioticHeroIds := userModel.HeroSymbiotic()
	if symbioticHeroIds == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("not have a symbiotic list")
	}

	//交换两个英雄在共生列表中的位置
	for i, v := range symbioticHeroIds {
		if v == inHeroId {
			symbioticHeroIds[i] = outHeroId
		} else if v == outHeroId {
			symbioticHeroIds[i] = inHeroId
		}
	}

	//交换两个英雄的等级
	inHero, _ := model.GetHero(ctx, uid, inHeroId)
	outHero, _ := model.GetHero(ctx, uid, outHeroId)
	if inHero == nil || outHero == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("hero not found")
	}
	inHeroLevelLine := cfg_mgr.Cfg.HeroLevelTable.Get(inHero.LevelId())
	outHeroLevelLine := cfg_mgr.Cfg.HeroLevelTable.Get(outHero.LevelId())
	if inHeroLevelLine == nil || outHeroLevelLine == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("hero level not found")
	}
	inHeroDestLevelLines := cfg_mgr.Cfg.HeroLevelTable.FilterSlice(func(line *servercfg.HeroLevelTableCfg) bool {
		return line.PlanID == inHeroLevelLine.PlanID && line.HeroLevel == outHeroLevelLine.HeroLevel
	})
	outHeroDestLevelLines := cfg_mgr.Cfg.HeroLevelTable.FilterSlice(func(line *servercfg.HeroLevelTableCfg) bool {
		return line.PlanID == outHeroLevelLine.PlanID && line.HeroLevel == inHeroLevelLine.HeroLevel
	})

	inHero.SetLevelId(ctx, inHeroDestLevelLines[0].Id)
	outHero.SetLevelId(ctx, outHeroDestLevelLines[0].Id)
	userModel.SetHeroSymbiotic(ctx, symbioticHeroIds)
	updateSymbioticLevel(ctx, uid)
	return nil
}
