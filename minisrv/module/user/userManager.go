package user

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/g"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/activity/day7quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/activity/sign7"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/alliance"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/benefit"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/hero"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/iap"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/lord"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/misc"
	"context"
	"fmt"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/logging"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/timestamp"
	"math"
	"sort"
	"time"
)

func RegisterUser(ctx context.Context, worldId int64, uid int64) (*model.UserModel, error) {
	// 不存在，注册
	u, err := model.NewUser(ctx, int32(worldId), uid)
	if err != nil {
		logging.Errorf(ctx, "user %d - %d nil, err %+v, caller %s", worldId, uid, err, misc.GetCallerName(2))
		return nil, err
	}

	//发放初始奖励
	uid = u.Uid()
	sendInitItem(ctx, uid)
	quest.GenMainQuest(ctx, 1, uid)
	quest.GenAchievementQuest(ctx, uid)
	day7quest.InitQuest(ctx, uid)
	verifyPlayerName(u.Name())

	// 添加注册用户统计
	rank.OnUserRegister(uid)
	sendWelcomeMail(ctx, uid)
	initPatchData(ctx, uid)
	return u, nil
}

func sendWelcomeMail(ctx context.Context, uid int64) {
	line := cfg_mgr.Cfg.MailTable.Get(constDef.MAIL_ID_WELCOME)
	userModel, _ := model.GetUserModel(ctx, uid)
	//7,30；1,100
	attachment := make(map[int32]int64)
	attachment[constDef.ITEM_ENERGY_BASE] = 60
	attachment[constDef.ITEM_DIAMOND] = 100
	bodyAttr := []string{userModel.Name()}
	model.SendSystemMail(ctx, uid, 1, line.Title, line.Subject, line.Image, attachment, int64(time.Now().Unix()+128400), nil, bodyAttr)
}

func sendInitItem(ctx context.Context, uid int64) {
	lines := cfg_mgr.Cfg.PresetsTable.FilterSlice(func(line *servercfg.PresetsTableCfg) bool {
		return true
	})
	//对 lines 排序
	sort.Slice(lines, func(i, j int) bool {
		return lines[i].Id < lines[j].Id
	})
	sequence := util.GenerateSequenceString()
	//haveSetBattlePos := false

	for _, v := range lines {
		if v.Cnt > 0 {
			model.AddItem(ctx, uid, v.ItemId, int64(v.Cnt), bi.ItemFlowReason_PRESET, "", sequence)
		}
	}
	hero.SetHeroBattle(ctx, uid, 5, 101)
	hero.SetHeroBattle(ctx, uid, 4, 200)
	//初始化装备
	lord.InitLordEquip(ctx, uid)
	initTestData(ctx, uid)
}

// 初始化测试数据
func initTestData(ctx context.Context, uid int64) {
	userData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypePhotoVoltaicUpdateTime))
	now := time.Now()
	// 获取1天之前的时间
	oneDayAgo := now.Add(-24 * time.Hour)
	userData.SetValue(ctx, oneDayAgo.Unix())
}

// 用户登陆
func LoginIn(ctx context.Context, worldId int64, uid int64, accountId string) (*model.UserModel, bool) {
	isNewPlayer := false
	if accountId != "" {
		lookUpUid, _ := model.GetUidByAccountId(ctx, accountId)
		if lookUpUid == 0 {
			model.CreateUserLookup(ctx, accountId, uid)
		}
	}

	u, err := model.GetUserModel(ctx, uid)

	if err != nil {
		panic(err)
	}
	if u == nil {
		u, err = RegisterUser(ctx, worldId, uid)
		isNewPlayer = true
		if err != nil {
			panic(err)
		}
	}
	unlock := model.LockUser(ctx, uid)
	defer unlock()
	loginHook(ctx, u)
	logger.LogCore(ctx, uid, "session_start_gs", logger.NewDetail())
	rank.OnUserLogin(uid)
	g.ClientRtm.AddRoomMember(int64(u.WorldId()), uid)
	return u, isNewPlayer

}

func loginHook(ctx context.Context, user *model.UserModel) {
	lastLoginTime := user.LastLoginTime()
	now := timestamp.NewUTCSeconds(time.Now())
	if !util.IsSameDay(lastLoginTime.Time(), now.Time()) {
		if !util.IsSameWeek(lastLoginTime.Time(), now.Time()) {
			weekFirstLogin(ctx, user)
		}
		dayFirstLogin(ctx, user)
		user.SetLastLoginTime(ctx, now)
	}
	//刷新体力数据
	model.AddItem(ctx, user.Uid(), constDef.ITEM_ENERGY_BASE, 0, bi.ItemFlowReason_TIME_DURATION, "", util.GenerateSequenceString())
	dataReset(ctx, user.Uid())
	checkPatch(ctx, user.Uid())
	alliance.GetAllianceQuest(ctx, user.Uid())
	checkMainStageFinish(ctx, user.Uid())

}

func checkMainStageFinish(ctx context.Context, uid int64) {
	stage, _ := model.GetMainLineStage(ctx, uid)
	if stage == nil {
		return
	}
	if stage.StageId() == 0 {
		return
	}
	if stage.CurStageFinish() {
		return
	}
	stageLine := cfg_mgr.Cfg.MainLevelTable.Get(stage.StageId())
	if stageLine == nil {
		return
	}
	stage.SetCurStageFinish(ctx, true)
	challengeRewardLine := cfg_mgr.Cfg.MainLevelRewardTable.Get(stageLine.CommonChallengeReward)
	if challengeRewardLine != nil {
		userInfo, _ := model.GetUserModel(ctx, uid)
		mailLine := cfg_mgr.Cfg.MailTable.Get(constDef.MAIL_LOST_REWARD_REISSUE)
		attachment, _ := model.ChangeItemByKvs(ctx, uid, challengeRewardLine.Reward)
		expireTime := time.Now().Unix() + 7*24*60*60
		bodyAttr := []string{userInfo.Name()}
		model.SendSystemMail(ctx, uid, 1, mailLine.Title, mailLine.Subject, mailLine.Image, attachment, expireTime, nil, bodyAttr)
	}
}

func dataReset(ctx context.Context, uid int64) {
	calcPower(ctx, uid)
	sign7.GetActivityInfo(ctx, uid)
	lordGemReset(ctx, uid)
}

func lordGemReset(ctx context.Context, uid int64) {
	equips := model.GetAllLordEquipModels(ctx, uid)
	if equips != nil {
		for _, v := range equips {
			lord.UpdateEquipGem(ctx, v)
		}
	}
}

func calcPower(ctx context.Context, uid int64) {
	//英雄战力
	heros, _ := model.GetAllHeros(ctx, uid)
	if heros != nil {
		for _, v := range heros {
			hero.CalcHeroPower(ctx, uid, v.ConfigId())
		}
	}
}

// replenishDungeonKey 补齐副本钥匙到配置上限
// itemId: 道具ID
// configId: 配置ID
// uid: 用户ID
// sequence: 操作序列号
func replenishDungeonKey(ctx context.Context, itemId int32, configId int32, uid int64, sequence string) {
	currentQuant := model.GetConsumableQuantity(ctx, uid, itemId)
	configLimit := cfg_mgr.GetGameConfigInt64Value(configId)
	if currentQuant < configLimit {
		addAmount := configLimit - currentQuant
		consumable.AddItem(ctx, uid, itemId, addAmount, bi.ItemFlowReason_DAILY_RESET, "", sequence)
	}
}

// 发送补充体力邮件
func sendEnergyReissueMail(ctx context.Context, uid int64) {
	//如果玩家注册不到一天，直接返回
	user, _ := model.GetUserModel(ctx, uid)
	if util.IsSameDay(user.Ctime().Time(), time.Now()) {
		return
	}
	//如果玩家没有特权，直接返回
	if math.Round(benefit.GetBenefitValue(ctx, uid, constDef.BENEFIT_ENERGY_MAIL_REISSUE)) == 0 {
		return
	}

	userData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeEnergyRecoveryValue))
	//计算增加的体力
	energyValue := cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_ENERGY_RESUMPTION)
	//计算增加的体力
	addEnergy := 3600*24/energyValue - userData.Value()
	userData.SetValue(ctx, 0)
	if addEnergy == 0 {
		return
	}
	line := cfg_mgr.Cfg.MailTable.Get(constDef.MAIL_ENERGY_REISSUE)
	attachment := make(map[int32]int64)
	attachment[constDef.ITEM_ENERGY_BASE] = addEnergy
	//获取三天后 0 点的时间戳
	now := time.Now()
	nextDay := now.Add(3 * 24 * time.Hour)
	nextDay = time.Date(nextDay.Year(), nextDay.Month(), nextDay.Day(), 0, 0, 0, 0, nextDay.Location())
	bodyAttr := []string{user.Name()}
	model.SendSystemMail(ctx, uid, 1, line.Title, line.Subject, line.Image, attachment, int64(nextDay.Unix()), nil, bodyAttr)
}

func dayFirstLogin(ctx context.Context, user *model.UserModel) {
	quest.Notify(ctx, user.Uid(), servercfg.TaskType_Login, nil, 1)

	// 生成统一的序列号用于记录
	sequence := util.GenerateSequenceString()

	// 补齐各种副本钥匙
	coinAddAmount := int32(benefit.GetBenefitValue(ctx, user.Uid(), constDef.BENEFIT_COIN_DUNGEON_DAILY_CHALLENGE_LIMIT_UP))
	geneAddAmount := int32(benefit.GetBenefitValue(ctx, user.Uid(), constDef.BENEFIT_GENE_DUNGEON_DAILY_CHALLENGE_LIMIT_UP))
	equipAddAmount := int32(benefit.GetBenefitValue(ctx, user.Uid(), constDef.BENEFIT_EQUIP_DUNGEON_DAILY_CHALLENGE_LIMIT_UP))
	sunshineAddAmount := int32(benefit.GetBenefitValue(ctx, user.Uid(), constDef.BENEFIT_SUNSHINE_DUNGEON_DAILY_CHALLENGE_LIMIT_UP))
	replenishDungeonKey(ctx, constDef.ITEM_COIN_DUNGEON_KEY, constDef.GAME_CONFIG_COIN_DUNGEON_KEY_LIMIT+coinAddAmount, user.Uid(), sequence)
	replenishDungeonKey(ctx, constDef.ITEM_GENE_DUNGEON_KEY, constDef.GAME_CONFIG_GENE_DUNGEON_KEY_LIMIT+geneAddAmount, user.Uid(), sequence)
	replenishDungeonKey(ctx, constDef.ITEM_LORD_EQUIP_DUNGEON_KEY, constDef.GAME_CONFIG_LORD_EQUIP_DUNGEON_KEY_LIMIT+equipAddAmount, user.Uid(), sequence)
	replenishDungeonKey(ctx, constDef.ITEM_SUNSHINE_DUNGEON_KEY, constDef.GAME_CONFIG_SUNSHINE_DUNGEON_KEY_LIMIT+sunshineAddAmount, user.Uid(), sequence)
	user.SetTotalLoginDays(ctx, user.TotalLoginDays()+1)
	quest.Notify(ctx, user.Uid(), servercfg.TaskType_TotalLogin, map[string]string{}, 1)
	sendEnergyReissueMail(ctx, user.Uid())
	DailyDataReset(ctx, user)
}

// 检查每日数据刷新
func DailyDataReset(ctx context.Context, user *model.UserModel) {
	if user == nil {
		return
	}
	userData, _ := model.GetUserData(ctx, user.Uid(), int32(minirpc.UserDataTypeDailyRefresh))
	if util.IsSameDay(time.Unix(userData.Value(), 0), time.Now()) {
		return
	}
	userData.SetValue(ctx, time.Now().Unix())
	// 清空每天的扫荡次数
	stage, _ := model.GetMainLineStage(ctx, user.Uid())
	if stage != nil {
		stage.SetTodaySweepTimes(ctx, 0)
	}
	//刷新签到任务
	sign7.GetActivityInfo(ctx, user.Uid())
	quest.GetDailyQuest(ctx, user.Uid())
	//刷新招募英雄今日次数
	heroLotteryModel, _ := model.GetLotteryModel(ctx, user.Uid())
	heroLotteryModel.SetTodayFreeLotteryTimes(ctx, 0)
	//刷新宝石招募次数
	for _, v := range cfg_mgr.Cfg.LordGemRandomGroupTable.GetAll() {
		gemRandomModel, _ := model.GetLordGemRandomModel(ctx, user.Uid(), v.Id)
		gemRandomModel.SetTodayFreeRandomTimes(ctx, 0)
	}
	iap.ResetDailySale(ctx, user.Uid()) //重置每日特惠
	WeeklyDataReset(ctx, user)
}

func WeeklyDataReset(ctx context.Context, user *model.UserModel) {
	if user == nil {
		return
	}
	userData, _ := model.GetUserData(ctx, user.Uid(), int32(minirpc.UserDataTypeWeeklyRefresh))
	if util.IsSameWeek(time.Unix(userData.Value(), 0), time.Now()) {
		return
	}
	userData.SetValue(ctx, time.Now().Unix())
	//刷新礼包
	regularGroupLines := cfg_mgr.Cfg.LordGemRandomGroupTable.GetAll()
	for _, v := range regularGroupLines {
		regularModel, _ := model.GetRegularPackModel(ctx, user.Uid(), v.Id)
		if regularModel == nil {
			continue
		}
		regularModel.SetIapBuyTimes(ctx, map[int32]int32{})
	}
	//刷新每日任务周数据
	weekUserData, _ := model.GetUserData(ctx, user.Uid(), int32(minirpc.UserDataTypeDailyWeekTaskChest))
	weekUserData.SetValue(ctx, 0)
	weekUserData.SetContent(ctx, map[int32]int64{})

}

func weekFirstLogin(ctx context.Context, user *model.UserModel) {

}

func verifyPlayerName(username string) bool {
	key := fmt.Sprintf(rank.REDIS_USERNAME, username) // 使用一个唯一的键格式
	rdb := rank.GetRankClient()
	ok, err := rdb.SetNX(key, 1, 0).Result()
	if err != nil {

		return false
	}

	if !ok {
		return false
	}
	return true
}

func removePlayerNameInRedis(userName string) {
	key := fmt.Sprintf(rank.REDIS_USERNAME, userName) // 使用一个唯一的键格式
	rdb := rank.GetRankClient()
	rdb.Del(key).Result()
}

func ChangeName(ctx context.Context, uid int64, name string, isFree bool) int32 {
	if name == "" {
		return 2
	}

	userModel, _ := model.GetUserModel(ctx, uid)
	if isFree {
		if userModel.FreeChangeNameTimes() >= int32(cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_NICK_NAME_CHANGE_FREE_CNT)) {
			return 2
		}
		userModel.SetFreeChangeNameTimes(ctx, userModel.FreeChangeNameTimes()+1)
	} else {
		needValue := cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_RENAME_COST_DIAMOND)
		err := consumable.DeductItem(ctx, uid, constDef.ITEM_DIAMOND, int64(needValue), bi.ItemFlowReason_RENAME, "", util.GenerateSequenceString())
		if err != nil {
			return 3
		}
	}
	if !verifyPlayerName(name) {
		return 1
	}
	removePlayerNameInRedis(userModel.Name())
	userModel.SetName(ctx, name)
	trace := map[string]string{}
	quest.Notify(ctx, uid, servercfg.TaskType_Rename, trace, 1)
	return 0
}

// 修改头像
func ChangeAvatar(ctx context.Context, uid int64, avatarId int32) error {

	line := cfg_mgr.Cfg.AvatarTable.Get(avatarId)
	if !line.Default {
		//检查是否有对应英雄
		heroId := line.Hero
		heroModel, _ := model.GetHero(ctx, uid, heroId)
		if heroModel == nil {
			return kdmerr.SysInvalidArguments.CastErrorf("hero not found")
		}
	}

	userModel, _ := model.GetUserModel(ctx, uid)
	userModel.SetAvatarConfigId(ctx, avatarId)
	trace := map[string]string{}
	quest.Notify(ctx, uid, servercfg.TaskType_Avatar, trace, 1)
	return nil
}

// GetUserInfoList 获取玩家信息
func GetUserInfoList(ctx context.Context, uidList []int64) []*wrpc.GetUserInfo {
	if uidList == nil {
		return nil
	}

	var retList []*wrpc.GetUserInfo
	for _, uid := range uidList {
		destUserInfo, _ := model.GetUserModel(ctx, uid)
		if destUserInfo == nil {
			continue
		}
		orm.Lock(destUserInfo)
		allianceName := ""
		allianceAcronym := ""
		if destUserInfo.AllianceId() != 0 {
			allianceModel, _ := model.GetAllianceModel(ctx, destUserInfo.AllianceId())
			if allianceModel != nil {
				allianceName = allianceModel.Name()
				allianceAcronym = allianceModel.Acronym()
			}
		}
		stageModel, _ := model.GetMainLineStage(ctx, uid)
		heros := []*minirpc.HeroInfo{}

		for _, v := range destUserInfo.BattlePosInfo() {
			hero, _ := model.GetHero(ctx, uid, v.HeroId)
			if hero != nil {
				heros = append(heros, hero.Snapshoot().(*minirpc.HeroInfo))
			}
		}

		lordEquip := []*minirpc.LordEquip{}
		powerMap := map[int32]int32{}
		for _, v := range model.GetAllLordEquipModels(ctx, uid) {
			lordEquip = append(lordEquip, v.Snapshoot().(*minirpc.LordEquip))
			for k, gemList := range v.GeneInfoList() {
				totalPower := int32(0)
				for _, gemId := range gemList.GeneInfo {
					line := cfg_mgr.Cfg.LordGemTable.Get(gemId)
					if line != nil {
						totalPower += line.GemQualityTypeRef.Power
					}
				}
				powerMap[int32(k)] += totalPower
			}
		}

		ret := &wrpc.GetUserInfo{
			Name:             destUserInfo.Name(),
			Uid:              uid,
			Power:            destUserInfo.Power(),
			AllianceName:     allianceName,
			AllianceAcronym:  allianceAcronym,
			MaxStage:         stageModel.UnlockStageId(),
			AvatarConfigId:   destUserInfo.AvatarConfigId(),
			AllowStrangers:   destUserInfo.AllowStrangers(),
			FriendStageLimit: destUserInfo.FriendStageLimit(),
			Hero:             heros,
			LordEquip:        lordEquip,
			LordGemPower:     powerMap,
		}
		retList = append(retList, ret)
		orm.Unlock(destUserInfo)
	}
	return retList
}
