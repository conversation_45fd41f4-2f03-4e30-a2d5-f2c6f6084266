// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type IapOrderCfg struct {
	Id          int32             `json:"Id"`       // Id
	StringId    string            `json:"StringId"` // StringId
	Order       int32             `json:"Order"`    // 优先级（小在前）
	Funciton    int32             `json:"Funciton"` // 功能
	FuncitonRef *FunctionTableCfg `json:"-"`        // 功能
	Goto        int32             `json:"Goto"`     // 跳转
	GotoRef     *GoToTableCfg     `json:"-"`        // 跳转
}

func NewIapOrderCfg() *IapOrderCfg {
	return &IapOrderCfg{
		Id:          0,
		StringId:    "",
		Order:       0,
		Funciton:    0,
		FuncitonRef: nil,
		Goto:        0,
		GotoRef:     nil,
	}
}

type IapOrder struct {
	records  map[int32]*IapOrderCfg
	localIds map[int32]struct{}
}

func NewIapOrder() *IapOrder {
	return &IapOrder{
		records:  map[int32]*IapOrderCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *IapOrder) Get(key int32) *IapOrderCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapOrder) GetAll() map[int32]*IapOrderCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapOrder) put(key int32, value *IapOrderCfg, local bool) *IapOrderCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapOrder) Range(f func(v *IapOrderCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapOrder) Filter(filterFuncs ...func(v *IapOrderCfg) bool) map[int32]*IapOrderCfg {
	filtered := map[int32]*IapOrderCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapOrder) FilterSlice(filterFuncs ...func(v *IapOrderCfg) bool) []*IapOrderCfg {
	filtered := []*IapOrderCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapOrder) FilterKeys(filterFuncs ...func(v *IapOrderCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapOrder) satisfied(v *IapOrderCfg, filterFuncs ...func(v *IapOrderCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapOrder) setupIndexes() error {
	return nil
}

func (t *IapOrder) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapOrderCfg) bindRefs(c *Configs) {
	r.FuncitonRef = c.FunctionTable.Get(r.Funciton)
	r.GotoRef = c.GoToTable.Get(r.Goto)
}
