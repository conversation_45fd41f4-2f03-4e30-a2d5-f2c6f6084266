package gmApi

import (
	"github.com/julienschmidt/httprouter"
)

// RegisterVersionRoutes 注册版本管理路由
func RegisterVersionRoutes(router *httprouter.Router) {
	// 版本管理API路由
	router.GET("/gm/api/versions", GetVersionList)
	router.POST("/gm/api/versions", AddVersion)
	router.PUT("/gm/api/versions/:clientVersion/:platform", UpdateVersion)
	router.DELETE("/gm/api/versions/:clientVersion/:platform", DeleteVersion)
	//router.GET("/gm/api/server-version", GetServerVersion)

	// 华佗发版API路由
	router.POST("/gm/api/huatuo/versions", AddHuatuoVersion)
	router.GET("/gm/api/huatuo/versions", GetHuatuoVersions)
	router.PUT("/gm/api/huatuo/versions/:platform/:version", UpdateHuatuoVersion)
	router.DELETE("/gm/api/huatuo/versions/:platform/:version", DeleteHuatuoVersion)
	router.POST("/gm/api/huatuo/bundles", AddHuatuoBundle)
	router.POST("/gm/api/huatuo/build", StartHuatuoBuild)
	router.GET("/gm/api/huatuo/builds", GetHuattuoBuilds)
	router.GET("/gm/api/huatuo/build/:buildId/log", GetHuatuoBuildLog)
	router.GET("/gm/api/huatuo/build/:buildId/download", DownloadHuatuoBuild)

	// 功能路由
	router.POST("/gm/api/version/generate-config", GenerateConfig)
	router.POST("/gm/api/version/save-upload-s3", SaveAndUploadS3)
	router.POST("/gm/api/version/deploy-online", DeployOnline)
	router.POST("/gm/api/version/settings", UpdateSettings)
}
