package function_open

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"context"
)

func CheckReqBuilding(ctx context.Context, uid int64, buildingId int32) bool {
	//buildingLine := cfg_mgr.Cfg.BuildingsTable.Get(buildingId)
	//if buildingLine == nil {
	//	return false
	//}
	//filter := bson.D{{"uid", bson.M{"$eq": uid}}, {"type", bson.M{"$eq": buildingLine.BuildingType}}}
	//buildingModels, err := orm.Find[*model.Building](ctx, filter)
	//if err != nil {
	//	return false
	//}
	//for _, v := range buildingModels {
	//	line := cfg_mgr.Cfg.BuildingsTable.Get(v.ConfigId())
	//	if line.BuildingLevel >= buildingLine.BuildingLevel {
	//		return true
	//	}
	//}
	return false
}

func CheckReqMainLevel(ctx context.Context, uid int64, id int32) bool {
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	if stageModel == nil {
		return false
	}
	functionLine := cfg_mgr.Cfg.FunctionTable.Get(id)
	if functionLine == nil {
		return false
	}
	if stageModel.UnlockStageId() >= functionLine.Level {
		return true
	}
	return false
}

func CheckReqChapter(ctx context.Context, uid int64, chapterId int32) bool {
	//chapterQuest, _ := model.GetAllMainTaskModel(ctx, uid)
	//if chapterQuest == nil {
	//	return true
	//}
	//for _, v := range chapterQuest {
	//	mainLine := cfg_mgr.Cfg.ChapterTaskTable.Get(v.QuestId())
	//	if mainLine == nil {
	//		continue
	//	}
	//	if mainLine.Chapter < chapterId {
	//		return false
	//	}
	//}
	return true
}

func SaveFunctionOpenStatus(ctx context.Context, uid int64, id int32, status int32) error {
	functionModel, _ := model.GetFunctionOpenModel(ctx, uid)
	openList := functionModel.OpenList()
	if openList == nil {
		openList = map[int32]int32{}
	}
	openList[id] = status
	functionModel.SetOpenList(ctx, openList)
	return nil
}

func SaveNewBieGuideProgress(ctx context.Context, uid int64, id int32, progress int32) error {
	newbieModel, _ := model.GetNewBieGuildModel(ctx, uid)
	guildList := newbieModel.GuideList()
	if guildList == nil {
		guildList = map[int32]int32{}
	}
	guildList[id] = progress
	newbieModel.SetGuideList(ctx, guildList)
	return nil
}
