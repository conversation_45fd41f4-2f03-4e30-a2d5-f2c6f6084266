// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type HeroBondsTableCfg struct {
	Id            int32          `json:"Id"`            // Id
	Remark        string         `json:"Remark"`        // 备注
	BondsBenefits []*BenefitsKVS `json:"BondsBenefits"` // 羁绊增益
}

func NewHeroBondsTableCfg() *HeroBondsTableCfg {
	return &HeroBondsTableCfg{
		Id:            0,
		Remark:        "",
		BondsBenefits: []*BenefitsKVS{},
	}
}

func NewMockHeroBondsTableCfg() *HeroBondsTableCfg {
	return &HeroBondsTableCfg{
		Id:            0,
		Remark:        "",
		BondsBenefits: []*BenefitsKVS{NewMockBenefitsKVS()},
	}
}

type HeroBondsTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*HeroBondsTableCfg
	localIds         map[int32]struct{}
}

func NewHeroBondsTable(configs *Configs) *HeroBondsTable {
	return &HeroBondsTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*HeroBondsTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *HeroBondsTable) Get(key int32) *HeroBondsTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *HeroBondsTable) GetAll() map[int32]*HeroBondsTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *HeroBondsTable) put(key int32, value *HeroBondsTableCfg, local bool) *HeroBondsTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *HeroBondsTable) putFromInheritedTable(key int32, value *HeroBondsTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[HeroBondsTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *HeroBondsTable) Put(key int32, value *HeroBondsTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[HeroBondsTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *HeroBondsTable) PutAll(m map[int32]*HeroBondsTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *HeroBondsTable) Range(f func(v *HeroBondsTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *HeroBondsTable) Filter(filterFuncs ...func(v *HeroBondsTableCfg) bool) map[int32]*HeroBondsTableCfg {
	filtered := map[int32]*HeroBondsTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *HeroBondsTable) FilterSlice(filterFuncs ...func(v *HeroBondsTableCfg) bool) []*HeroBondsTableCfg {
	filtered := []*HeroBondsTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *HeroBondsTable) FilterKeys(filterFuncs ...func(v *HeroBondsTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *HeroBondsTable) satisfied(v *HeroBondsTableCfg, filterFuncs ...func(v *HeroBondsTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *HeroBondsTable) setupIndexes() error {
	return nil
}

func (t *HeroBondsTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *HeroBondsTableCfg) bindRefs(c *Configs) {
	for _, e := range r.BondsBenefits {
		e.bindRefs(c)
	}
}

func (t *HeroBondsTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[HeroBondsTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewHeroBondsTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroBondsTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[HeroBondsTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// Remark
		{
			recordCfg.Remark = strings.TrimSpace(record[t.getIndexInCsv("Remark")])
		}
		// BondsBenefits
		{
			cfgoMeetNilForBondsBenefitsOfRecordCfg := false
			// element 0 of BondsBenefits
			if !cfgoMeetNilForBondsBenefitsOfRecordCfg {
				cfgoMeetNilForBondsBenefitsOfRecordCfg = true
				var cfgoElemOfBondsBenefitsOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
				{
					if record[t.getIndexInCsv("BondsBenefits1BenefitsID")] != "" {
						cfgoMeetNilForBondsBenefitsOfRecordCfg = false
						var err error
						cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("BondsBenefits1BenefitsID")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroBondsTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("BondsBenefits1BenefitsID")], err)
							} else {
								return fmt.Errorf("[HeroBondsTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("BondsBenefits1BenefitsID")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("BondsBenefits1BenefitsValue")] != "" {
						cfgoMeetNilForBondsBenefitsOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("BondsBenefits1BenefitsValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroBondsTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("BondsBenefits1BenefitsValue")], err)
							} else {
								return fmt.Errorf("[HeroBondsTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("BondsBenefits1BenefitsValue")], err)
							}
						}
						cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsValue = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForBondsBenefitsOfRecordCfg {
					recordCfg.BondsBenefits = append(recordCfg.BondsBenefits, cfgoElemOfBondsBenefitsOfRecordCfg)
				}
			}
			// element 1 of BondsBenefits
			if !cfgoMeetNilForBondsBenefitsOfRecordCfg {
				cfgoMeetNilForBondsBenefitsOfRecordCfg = true
				var cfgoElemOfBondsBenefitsOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
				{
					if record[t.getIndexInCsv("BondsBenefits2BenefitsID")] != "" {
						cfgoMeetNilForBondsBenefitsOfRecordCfg = false
						var err error
						cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("BondsBenefits2BenefitsID")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroBondsTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("BondsBenefits2BenefitsID")], err)
							} else {
								return fmt.Errorf("[HeroBondsTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("BondsBenefits2BenefitsID")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("BondsBenefits2BenefitsValue")] != "" {
						cfgoMeetNilForBondsBenefitsOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("BondsBenefits2BenefitsValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroBondsTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("BondsBenefits2BenefitsValue")], err)
							} else {
								return fmt.Errorf("[HeroBondsTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("BondsBenefits2BenefitsValue")], err)
							}
						}
						cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsValue = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForBondsBenefitsOfRecordCfg {
					recordCfg.BondsBenefits = append(recordCfg.BondsBenefits, cfgoElemOfBondsBenefitsOfRecordCfg)
				}
			}
			// element 2 of BondsBenefits
			if !cfgoMeetNilForBondsBenefitsOfRecordCfg {
				cfgoMeetNilForBondsBenefitsOfRecordCfg = true
				var cfgoElemOfBondsBenefitsOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
				{
					if record[t.getIndexInCsv("BondsBenefits3BenefitsID")] != "" {
						cfgoMeetNilForBondsBenefitsOfRecordCfg = false
						var err error
						cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("BondsBenefits3BenefitsID")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroBondsTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("BondsBenefits3BenefitsID")], err)
							} else {
								return fmt.Errorf("[HeroBondsTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("BondsBenefits3BenefitsID")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("BondsBenefits3BenefitsValue")] != "" {
						cfgoMeetNilForBondsBenefitsOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("BondsBenefits3BenefitsValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroBondsTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("BondsBenefits3BenefitsValue")], err)
							} else {
								return fmt.Errorf("[HeroBondsTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("BondsBenefits3BenefitsValue")], err)
							}
						}
						cfgoElemOfBondsBenefitsOfRecordCfg.BenefitsValue = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForBondsBenefitsOfRecordCfg {
					recordCfg.BondsBenefits = append(recordCfg.BondsBenefits, cfgoElemOfBondsBenefitsOfRecordCfg)
				}
			}

		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [HeroBondsTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[HeroBondsTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *HeroBondsTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "HeroBondsTable.csv") && (!strings.HasPrefix(fileName, "HeroBondsTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for HeroBondsTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[HeroBondsTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[HeroBondsTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[HeroBondsTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[HeroBondsTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[HeroBondsTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[HeroBondsTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[HeroBondsTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[HeroBondsTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [HeroBondsTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *HeroBondsTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[HeroBondsTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [HeroBondsTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *HeroBondsTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[HeroBondsTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *HeroBondsTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[HeroBondsTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[HeroBondsTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
