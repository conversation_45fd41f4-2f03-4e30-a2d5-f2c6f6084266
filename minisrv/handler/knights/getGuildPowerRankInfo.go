package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 获取联盟力量排行榜信息
func (_ KnightsHandlers) GetGuildPowerRankInfoHandler(ctx *wctx.Context, req *wrpc.GetGuildPowerRankInfoRequest) (*wrpc.GetGuildPowerRankInfoReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		ret := rank.GetGuildPowerRankInfoList(ctx, req.UID)
		replay := wrpc.GetGuildPowerRankInfoReply{
			Result: ret,
		}
		rawResp = &replay
		err = nil
	})
	return rawResp.(*wrpc.GetGuildPowerRankInfoReply), err
}
