package model

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	cfg_mgr "bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"context"
	"github.com/sasha-s/go-deadlock"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/locker"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/timestamp"
	_ "gitlab-ee.funplus.io/backend-platform/zplus-go/timestamp"
	"sync"
	"time"
	_ "time"
)

func init() {
	orm.RegisterModel[*Hero](nil)
}

type Hero struct {
	mu     deadlock.RWMutex
	locker sync.RWMutex
	minirpc.HeroInfoRecord
}

func (h *Hero) Locker() locker.RWLocker {
	return nil
}

func (h *Hero) LockPriority() int {
	return int(h.Uid())
}

func GetHero(ctx context.Context, uid int64, heroId int32) (*Hero, error) {
	h, err := orm.Get[*Hero](ctx, uid, heroId)
	if err != nil {
		return nil, kdmerr.SysDBError.CastErrorf("db error")
	}
	return h, err
}

func GetAllHeros(ctx context.Context, uid int64) ([]*Hero, error) {
	heros, err := orm.GetAll[*Hero](ctx, uid)
	if err != nil {
		return nil, kdmerr.SysDBError.CastErrorf("db error")
	}
	return heros, err
}

func NewHero(ctx context.Context, uid int64, heroId int32) (*Hero, error) {
	h, err := orm.Create[*Hero](ctx, &minirpc.HeroInfo{
		Uid:           uid,
		ConfigId:      heroId,
		Exp:           0,
		HeroEquipment: make(map[int32]*minirpc.HeroEquipment),
		Ctime:         timestamp.NewUTCSeconds(time.Now()),
		Mtime:         timestamp.NewUTCSeconds(time.Now()),
	})
	h.InitHero(ctx)
	return h, err
}

func (h *Hero) InitHero(ctx context.Context) error {
	heroLine := cfg_mgr.Cfg.HeroTable.Get(h.ConfigId())

	//设置等级
	levelLines := cfg_mgr.Cfg.HeroLevelTable.FilterSlice(func(v *servercfg.HeroLevelTableCfg) bool {
		return v.HeroLevel == 1 && v.PlanID == heroLine.LevelPlanID
	})
	if levelLines == nil || len(levelLines) > 1 {
		panic("can not find level")
	}
	h.SetLevelId(ctx, levelLines[0].Id)

	//设置星级
	starLines := cfg_mgr.Cfg.HeroStarTable.FilterSlice(func(v *servercfg.HeroStarTableCfg) bool {
		return v.HeroStarLevel == 0 && v.PlanID == heroLine.StarPlanID
	})
	if starLines == nil || len(starLines) > 1 {
		return kdmerr.SysInvalidArguments.CastErrorf("can not find star")
	}
	h.SetStarId(ctx, starLines[0].Id)
	//设置基因
	heroGeneLines := cfg_mgr.Cfg.HeroGeneTable.FilterSlice(func(v *servercfg.HeroGeneTableCfg) bool {
		return v.HeroID == heroLine.Id && v.HeroGeneLevel == 1
	})
	if heroGeneLines == nil || len(heroGeneLines) > 1 {
		return kdmerr.SysInvalidArguments.CastErrorf("can not find gene")
	}
	h.SetGeneConfigId(ctx, heroGeneLines[0].Id)
	return nil
}

func ExportHeros(ctx context.Context, uid int64) ([]*minirpc.HeroInfo, error) {
	heros, err := orm.GetAll[*Hero](ctx, uid)
	var heroInfos []*minirpc.HeroInfo
	if heros != nil {
		for _, v := range heros {
			unlock := v.RLock()
			heroInfos = append(heroInfos, v.Snapshoot().(*minirpc.HeroInfo))
			unlock()
		}
	}
	return heroInfos, err
}
