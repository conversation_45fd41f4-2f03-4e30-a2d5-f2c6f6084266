package gmApi

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"context"
	"encoding/json"
	"fmt"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"net/http"
	"time"

	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// HuatuoBuildRequest 华佗构建请求
type HuatuoBuildRequest struct {
	Branch      string `json:"branch"`
	Type        string `json:"type"`
	Version     string `json:"version"`
	Description string `json:"description"`
}

// HuatuoBuildInfo 华佗构建信息
type HuatuoBuildInfo struct {
	BuildID     string     `json:"build_id"`
	Branch      string     `json:"branch"`
	Type        string     `json:"type"`
	Version     string     `json:"version"`
	Description string     `json:"description"`
	Status      string     `json:"status"`
	StartTime   time.Time  `json:"start_time"`
	EndTime     *time.Time `json:"end_time,omitempty"`
	Duration    int        `json:"duration,omitempty"`
	Log         string     `json:"log,omitempty"`
}

// StartHuatuoBuild 开始华佗构建
func StartHuatuoBuild(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	var req HuatuoBuildRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		jsonResponse(w, http.StatusBadRequest, "Invalid request body", nil)
		return
	}

	// 验证输入
	if req.Version == "" {
		jsonResponse(w, http.StatusBadRequest, "Version is required", nil)
		return
	}

	var buildInfo *HuatuoBuildInfo
	var err error

	tasklet.Invoke(context.Background(), "StartHuatuoBuild", func(ctx context.Context) {
		buildInfo, err = startHuatuoBuild(ctx, &req)
	})

	if err != nil {
		jsonResponse(w, http.StatusInternalServerError, "Failed to start build: "+err.Error(), nil)
		return
	}

	jsonResponse(w, http.StatusOK, "Build started successfully", buildInfo)
}

// GetHuattuoBuilds 获取华佗构建列表
func GetHuattuoBuilds(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	var builds []HuatuoBuildInfo
	var err error

	tasklet.Invoke(context.Background(), "GetHuattuoBuilds", func(ctx context.Context) {
		builds, err = getHuatuoBuilds(ctx)
	})

	if err != nil {
		jsonResponse(w, http.StatusInternalServerError, "Failed to get builds", nil)
		return
	}

	jsonResponse(w, http.StatusOK, "Success", builds)
}

// GetHuatuoBuildLog 获取华佗构建日志
func GetHuatuoBuildLog(w http.ResponseWriter, r *http.Request, ps httprouter.Params) {
	buildID := ps.ByName("buildId")
	if buildID == "" {
		jsonResponse(w, http.StatusBadRequest, "Build ID is required", nil)
		return
	}

	var logData map[string]string
	var err error

	tasklet.Invoke(context.Background(), "GetHuatuoBuildLog", func(ctx context.Context) {
		log, e := getHuatuoBuildLog(ctx, buildID)
		if e != nil {
			err = e
			return
		}
		logData = map[string]string{"log": log}
	})

	if err != nil {
		jsonResponse(w, http.StatusInternalServerError, "Failed to get build log", nil)
		return
	}

	jsonResponse(w, http.StatusOK, "Success", logData)
}

// DownloadHuatuoBuild 下载华佗构建产物
func DownloadHuatuoBuild(w http.ResponseWriter, r *http.Request, ps httprouter.Params) {
	buildID := ps.ByName("buildId")
	if buildID == "" {
		http.Error(w, "Build ID is required", http.StatusBadRequest)
		return
	}

	var filePath string
	var err error

	tasklet.Invoke(context.Background(), "DownloadHuatuoBuild", func(ctx context.Context) {
		filePath, err = getHuatuoBuildArtifact(ctx, buildID)
	})

	if err != nil {
		http.Error(w, "Failed to get build artifact"+filePath, http.StatusInternalServerError)
		return
	}

	// 设置下载头
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=huatuo-build-%s.zip", buildID))
	w.Header().Set("Content-Type", "application/zip")

	// 这里应该读取文件并写入响应
	// 简化实现，实际应该读取真实文件
	w.Write([]byte("Build artifact content"))
}

// AddHuatuoVersion 添加华佗版本
func AddHuatuoVersion(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	var req struct {
		Platform      string `json:"platform"`
		Version       string `json:"version"`
		TargetVersion string `json:"target_version"`
		JobId         string `json:"job_id"`
		Data          string `json:"data"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		jsonResponse(w, http.StatusBadRequest, "Invalid request body", nil)
		return
	}

	if req.Platform == "" || req.Version == "" {
		jsonResponse(w, http.StatusBadRequest, "Platform and version are required", nil)
		return
	}

	var err error
	tasklet.Invoke(context.Background(), "AddHuatuoVersion", func(ctx context.Context) {
		v, _ := model.GetHuatuoVersionModel(ctx, req.Platform, req.Version)
		if v != nil {
			v.SetData(ctx, req.Data)
			v.SetTargetVersion(ctx, req.TargetVersion)
			v.SetJobId(ctx, req.JobId)

		} else {
			orm.Create[*model.HuatuoVersionModel](ctx, &minirpc.HuatuoVersion{
				Platform:      req.Platform,
				ClientVersion: req.Version,
				TargetVersion: req.TargetVersion,
				JobId:         req.JobId,
				Data:          req.Data,
			})
		}

	})

	if err != nil {
		jsonResponse(w, http.StatusInternalServerError, "Failed to add version: "+err.Error(), nil)
		return
	}

	jsonResponse(w, http.StatusOK, "Version added successfully", nil)
}

// GetHuatuoVersions 获取华佗版本列表
func GetHuatuoVersions(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	platform := r.URL.Query().Get("platform")
	if platform == "" {
		platform = "ios"
	}

	var versions []map[string]interface{}
	var err error

	tasklet.Invoke(context.Background(), "GetHuatuoVersions", func(ctx context.Context) {
		clientVersions, _ := model.GetPlatformHuatuoVersion(ctx, platform)
		for _, v := range clientVersions {
			versions = append(versions, map[string]interface{}{
				"version":        v.ClientVersion(),
				"target_version": v.TargetVersion(),
				"data":           v.Data(),
				"job_id":         v.JobId(),
			})
		}
	})

	if err != nil {
		jsonResponse(w, http.StatusInternalServerError, "Failed to get versions", nil)
		return
	}

	jsonResponse(w, http.StatusOK, "Success", versions)
}

// AddHuatuoBundle 添加华佗Bundle
func AddHuatuoBundle(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	var req struct {
		Platform      string `json:"platform"`
		Version       string `json:"version"`
		BundlePathMc  string `json:"bundle_path_mc"`
		BundleCfgMc   string `json:"bundle_cfg_mc"`
		BundlePathGog string `json:"bundle_path_gog"`
		BundleCfgGog  string `json:"bundle_cfg_gog"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		jsonResponse(w, http.StatusBadRequest, "Invalid request body", nil)
		return
	}

	var err error
	tasklet.Invoke(context.Background(), "AddHuatuoBundle", func(ctx context.Context) {
		//err = saveHuatuoBundle(ctx, &req)
	})

	if err != nil {
		jsonResponse(w, http.StatusInternalServerError, "Failed to add bundle: "+err.Error(), nil)
		return
	}

	jsonResponse(w, http.StatusOK, "Bundle added successfully", nil)
}

// DeleteHuatuoVersion 删除华佗版本
func DeleteHuatuoVersion(w http.ResponseWriter, r *http.Request, ps httprouter.Params) {
	platform := ps.ByName("platform")
	version := ps.ByName("version")

	if platform == "" || version == "" {
		jsonResponse(w, http.StatusBadRequest, "Platform and version are required", nil)
		return
	}

	var err error
	tasklet.Invoke(context.Background(), "DeleteHuatuoVersion", func(ctx context.Context) {
		err = deleteHuatuoVersion(ctx, platform, version)
	})

	if err != nil {
		jsonResponse(w, http.StatusInternalServerError, "Failed to delete version: "+err.Error(), nil)
		return
	}

	jsonResponse(w, http.StatusOK, "Version deleted successfully", nil)
}

func deleteHuatuoVersion(ctx context.Context, platform, version string) error {
	huatuoModel, err := model.GetHuatuoVersionModel(ctx, platform, version)
	if err != nil {
		return err
	}
	if huatuoModel == nil {
		return fmt.Errorf("version not found")
	}
	huatuoModel.Delete(ctx)
	return nil
}

// UpdateHuatuoVersion 更新华佗版本
func UpdateHuatuoVersion(w http.ResponseWriter, r *http.Request, ps httprouter.Params) {
	platform := ps.ByName("platform")
	version := ps.ByName("version")

	if platform == "" || version == "" {
		jsonResponse(w, http.StatusBadRequest, "Platform and version are required", nil)
		return
	}

	var req struct {
		TargetVersion string `json:"target_version"`
		JobId         string `json:"job_id"`
		Data          string `json:"data"`
		BundlePathMc  string `json:"bundle_path_mc"`
		BundleCfgMc   string `json:"bundle_cfg_mc"`
		BundlePathGog string `json:"bundle_path_gog"`
		BundleCfgGog  string `json:"bundle_cfg_gog"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		jsonResponse(w, http.StatusBadRequest, "Invalid request body", nil)
		return
	}

	var err error
	tasklet.Invoke(context.Background(), "UpdateHuatuoVersion", func(ctx context.Context) {
		err = updateHuatuoVersion(ctx, platform, version, &req)
	})

	if err != nil {
		jsonResponse(w, http.StatusInternalServerError, "Failed to update version: "+err.Error(), nil)
		return
	}

	jsonResponse(w, http.StatusOK, "Version updated successfully", nil)
}

func updateHuatuoVersion(ctx context.Context, platform, version string, req interface{}) error {
	huatuoModel, err := model.GetHuatuoVersionModel(ctx, platform, version)
	if err != nil {
		return err
	}
	if huatuoModel == nil {
		return fmt.Errorf("version not found")
	}

	updateReq := req.(*struct {
		TargetVersion string `json:"target_version"`
		JobId         string `json:"job_id"`
		Data          string `json:"data"`
		BundlePathMc  string `json:"bundle_path_mc"`
		BundleCfgMc   string `json:"bundle_cfg_mc"`
		BundlePathGog string `json:"bundle_path_gog"`
		BundleCfgGog  string `json:"bundle_cfg_gog"`
	})

	if updateReq.TargetVersion != "" {
		huatuoModel.SetTargetVersion(ctx, updateReq.TargetVersion)
	}
	if updateReq.JobId != "" {
		huatuoModel.SetJobId(ctx, updateReq.JobId)
	}
	if updateReq.Data != "" {
		huatuoModel.SetData(ctx, updateReq.Data)
	}

	return nil
}

// 数据库操作函数
func startHuatuoBuild(ctx context.Context, req *HuatuoBuildRequest) (*HuatuoBuildInfo, error) {
	buildID := generateBuildID()

	buildInfo := &HuatuoBuildInfo{
		BuildID:     buildID,
		Branch:      req.Branch,
		Type:        req.Type,
		Version:     req.Version,
		Description: req.Description,
		Status:      "pending",
		StartTime:   time.Now(),
	}

	// 保存到数据库
	err := saveHuatuoBuild(ctx, buildInfo)
	if err != nil {
		return nil, err
	}

	// 启动异步构建任务
	go func() {
		executeHuatuoBuild(buildID)
	}()

	return buildInfo, nil
}

func getHuatuoBuilds(ctx context.Context) ([]HuatuoBuildInfo, error) {
	// 从数据库获取构建列表
	// 这里是模拟数据
	builds := []HuatuoBuildInfo{
		{
			BuildID:   "build-001",
			Branch:    "master",
			Type:      "release",
			Version:   "1.0.0",
			Status:    "success",
			StartTime: time.Now().Add(-time.Hour),
			Duration:  300,
		},
	}
	return builds, nil
}

func getHuatuoBuildLog(ctx context.Context, buildID string) (string, error) {
	// 从数据库或文件系统获取构建日志
	return fmt.Sprintf("Build log for %s\n[INFO] Starting build...\n[INFO] Build completed successfully", buildID), nil
}

func getHuatuoBuildArtifact(ctx context.Context, buildID string) (string, error) {
	// 返回构建产物文件路径
	return fmt.Sprintf("/builds/%s/artifact.zip", buildID), nil
}

func saveHuatuoBuild(ctx context.Context, build *HuatuoBuildInfo) error {
	// 保存构建信息到数据库
	return nil
}

func generateBuildID() string {
	return fmt.Sprintf("build-%d", time.Now().Unix())
}

func executeHuatuoBuild(buildID string) {
	// 模拟构建过程
	time.Sleep(5 * time.Second)

	// 更新构建状态为成功
	// 实际实现中应该调用真实的构建系统
}
