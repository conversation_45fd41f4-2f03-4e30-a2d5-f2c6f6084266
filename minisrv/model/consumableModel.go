package model

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"context"
	"github.com/sasha-s/go-deadlock"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/locker"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"sync"
	"time"
)

var AutoUse map[servercfg.ItemType]func(ctx context.Context, uid int64, itemId int32, amount int64, bi bi.ItemFlowReason, subReason string, sequence string) int64 //返回未使用的道具
var GetDropItem func(int32) map[int32]int64

func init() {
	orm.RegisterModel[*Consumable](nil)
	AutoUse = make(map[servercfg.ItemType]func(ctx context.Context, uid int64, itemId int32, amount int64, bi bi.ItemFlowReason, subReason string, sequence string) int64)
}

type Consumable struct {
	mu     deadlock.RWMutex
	locker sync.RWMutex
	minirpc.ConsumableRecord
}

func (u *Consumable) Locker() locker.RWLocker {
	return nil
}

func (u *Consumable) LockPriority() int {
	return int(u.Uid())
}

func GetConsumableQuantity(ctx context.Context, uid int64, itemId int32) int64 {
	c, err := orm.Get[*Consumable](ctx, uid, itemId)
	if err != nil {
		panic(err)
	}
	if c == nil {
		return 0
	}
	return c.Quantity()
}

func ExportAllConsumables(ctx context.Context, uid int64) ([]*minirpc.Consumable, error) {
	items, err := orm.GetAll[*Consumable](ctx, uid)
	var itemInfos []*minirpc.Consumable
	if items != nil {
		for _, v := range items {
			unlock := v.RLock()
			itemInfos = append(itemInfos, v.Snapshoot().(*minirpc.Consumable))
			unlock()
		}
	}
	return itemInfos, err
}

func AddItem(ctx context.Context, uid int64, itemId int32, amount int64, reason bi.ItemFlowReason, subReason string, sequence string) (bool, error) {
	if amount < 0 {
		panic("negative amount")
	}
	line := cfg_mgr.Cfg.ItemTable.Get(itemId)
	if line.AutoUse {
		f := AutoUse[line.Type]
		if f != nil {
			amount = f(ctx, uid, itemId, amount, reason, subReason, sequence)
		}
	}
	c, err := orm.Get[*Consumable](ctx, uid, itemId)
	if err != nil {
		panic(err)
	}
	if c == nil {
		c, err = orm.Create[*Consumable](ctx, &minirpc.Consumable{
			Uid:      uid,
			ConfigId: itemId,
			Quantity: 0,
		})
	}
	if err != nil {
		panic(err)
	}
	RefreshItem(ctx, uid, line, c)
	if amount == 0 {
		return true, nil
	}
	beforeAmount := c.Quantity()
	c.SetQuantity(ctx, c.Quantity()+amount)
	userModel, _ := GetUserModel(ctx, uid)
	stage, _ := GetMainLineStage(ctx, uid)
	stageLine := cfg_mgr.Cfg.MainLevelTable.Get(stage.UnlockStageId())
	curQuestId := ""
	if stageLine != nil {
		curQuestId = stageLine.StringId
	}
	logger.LogCustom(ctx, uid, "item_flow", logger.NewDetail().
		Put("action", "add").
		Put("item_id", itemId).Put("quantity", amount).
		Put("main_reason", reason).Put("item_type", line.Type).
		Put("change_before", beforeAmount).
		Put("change_after", c.Quantity()).
		Put("sub_reason", subReason).
		Put("sequence", sequence).
		Put("power", userModel.Power()).
		Put("item_name", line.StringId).
		Put("cur_quest_id", curQuestId))
	return true, err
}
func DeductItem(ctx context.Context, uid int64, itemId int32, amount int64, reason bi.ItemFlowReason, subReason string, sequence string) error {
	if amount <= 0 {
		return kdmerr.SysDBError.CastErrorf("amount cannot less than zero")
	}
	c, err := orm.Get[*Consumable](ctx, uid, itemId)
	if c == nil || err != nil {
		return kdmerr.SysDBError.CastErrorf("amount cannot less than zero")
	}
	line := cfg_mgr.Cfg.ItemTable.Get(itemId)
	RefreshItem(ctx, uid, line, c)
	if c.Quantity() < amount {
		panic("quantity less than amount")
	}
	beforeAmount := c.Quantity()
	c.SetQuantity(ctx, c.Quantity()-amount)
	userModel, _ := GetUserModel(ctx, uid)
	stage, _ := GetMainLineStage(ctx, uid)
	stageLine := cfg_mgr.Cfg.MainLevelTable.Get(stage.UnlockStageId())
	curQuestId := ""
	if stageLine != nil {
		curQuestId = stageLine.StringId
	}
	logger.LogCustom(ctx, uid, "item_flow", logger.NewDetail().Put("action", "cost").
		Put("item_id", itemId).Put("quantity", amount).Put("main_reason", reason).Put("item_type", line.Type).
		Put("change_before", beforeAmount).Put("change_after", c.Quantity()).Put("sub_reason", subReason).Put("sequence", sequence).
		Put("power", userModel.Power()).Put("item_name", line.StringId).Put("cur_quest_id", curQuestId))
	return nil
}

func RefreshItem(ctx context.Context, uid int64, itemIdLine *servercfg.ItemTableCfg, itemModel *Consumable) {
	//刷新体力
	if itemIdLine.Type == servercfg.ItemType_Energy {
		limitValue := cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_ENERGY_LIMIT)
		userDataModel, _ := GetUserData(ctx, uid, int32(minirpc.UserDataTypeEnergyUpdateTime))
		now := time.Now().Unix()
		if itemModel.Quantity() < limitValue {
			//计算增加的体力
			energyValue := cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_ENERGY_RESUMPTION)
			//计算增加的体力
			addEnergy := (now - userDataModel.Value()) / energyValue
			if addEnergy > 0 {
				preEnergy := itemModel.Quantity()
				itemModel.SetQuantity(ctx, itemModel.Quantity()+addEnergy)
				if itemModel.Quantity() > limitValue {
					itemModel.SetQuantity(ctx, limitValue)
				}
				fininalAdd := itemModel.Quantity() - preEnergy
				todayAddModel, _ := GetUserData(ctx, uid, int32(minirpc.UserDataTypeEnergyRecoveryValue))
				todayAddModel.SetValue(ctx, todayAddModel.Value()+fininalAdd)
				userDataModel.SetValue(ctx, now)
			}
		} else {
			userDataModel.SetValue(ctx, now)
		}
	}
}

//通用基因碎片自动转换

func ChangeItemByKvs(ctx context.Context, uid int64, rewards []*servercfg.RewardKVS) (map[int32]int64, map[int32]int64) {
	mapRewards := map[int32]int64{}
	for _, v := range rewards {
		mapRewards[v.RewardType] += int64(v.RewardValue)
	}
	return ChangeItem(ctx, uid, mapRewards)
}

func changeItemPer(ctx context.Context, uid int64, rewards map[int32]int64) (map[int32]int64, map[int32]int64) {
	if rewards == nil {
		return rewards, nil
	}
	gemRewards := map[int32]int64{}
	for itemId, itemValue := range rewards {
		itemLine := cfg_mgr.Cfg.ItemTable.Get(itemId)
		if itemLine == nil {
			continue
		}
		if itemLine.Type == servercfg.ItemType_HeroGeneralGeneFragment {
			heros := cfg_mgr.Cfg.HeroTable.GetAll()
			if heros == nil {
				continue
			}
			geneFragmentIdWeight := map[int32]int32{}
			for _, v := range heros {
				heroLine := v
				if heroLine == nil {
					continue
				}
				if heroLine.ItemGeneId != 0 {
					geneFragmentIdWeight[heroLine.ItemGeneId] = 1
				}
			}
			for i := int64(0); i < itemValue; i++ {
				newItemId := util.RandomByWeight(geneFragmentIdWeight)
				if newItemId == 0 {
					continue
				}
				rewards[newItemId]++
			}
			delete(rewards, itemId)
		} else if itemLine.Type == servercfg.ItemType_GemRandom /*|| itemLine.Type == servercfg.ItemType_GemQualityRandom*/ { //随机宝石
			cntIdweight := map[int32]int32{} //默认为 1 个
			dropQalityIdWeight := map[int32]int32{}
			equipTypeIdWeight := map[int32]int32{}
			equipTypeLines := cfg_mgr.Cfg.LordEquipTypeTable.GetAll()
			for _, v := range equipTypeLines {
				equipTypeIdWeight[v.Id] += v.Weight
			}

			if itemLine.Type == servercfg.ItemType_GemRandom {
				gemDropCntLines := cfg_mgr.Cfg.LordGemDropCntTable.FilterSlice(func(v *servercfg.LordGemDropCntTableCfg) bool {
					return v.LordGemRandom == itemId
				})

				if gemDropCntLines != nil {
					for _, v := range gemDropCntLines {
						cntIdweight[v.Id] += v.LordGemCntWeight
					}
				}

				dropQualityLines := cfg_mgr.Cfg.LordGemDropQualityTable.Filter(func(v *servercfg.LordGemDropQualityTableCfg) bool {
					return v.LordGemRandom == itemId
				})
				for _, v := range dropQualityLines {
					dropQalityIdWeight[int32(v.LordGemQuality)] += v.LordGemQualityWeight
				}
			} else {
				dropQualityLines := cfg_mgr.Cfg.LordGemDropQualityTable.Filter(func(v *servercfg.LordGemDropQualityTableCfg) bool {
					return int32(v.LordGemQuality) == itemId
				})
				for _, v := range dropQualityLines {
					dropQalityIdWeight[v.Id] += v.LordGemQualityWeight
				}
			}
			for i := int64(0); i < itemValue; i++ {
				cnt := int32(1)
				if itemLine.Type == servercfg.ItemType_GemRandom {
					cntLine := cfg_mgr.Cfg.LordGemDropCntTable.Get(util.RandomByWeight(cntIdweight))
					cnt = cntLine.LordGemCnt
				}
				for j := int32(0); j < cnt; j++ {
					typeId := util.RandomByWeight(equipTypeIdWeight)
					qualityId := util.RandomByWeight(dropQalityIdWeight)
					gemLines := cfg_mgr.Cfg.LordGemTable.Filter(func(v *servercfg.LordGemTableCfg) bool {
						return v.LordEquipType == typeId && v.GemQualityType == qualityId
					})
					gemIdWeight := map[int32]int32{}
					for _, v := range gemLines {
						gemIdWeight[v.Item] += v.Weight
					}
					//生成宝石
					rewards[util.RandomByWeight(gemIdWeight)]++
				}
			}
			delete(rewards, itemId)
		} else if itemLine.Type == servercfg.ItemType_LordEquipRandomManual {
			randomLines := cfg_mgr.Cfg.ItemTable.FilterSlice(func(v *servercfg.ItemTableCfg) bool {
				return v.Type == servercfg.ItemType_LordEquipManual
			})
			itemIdWeight := map[int32]int32{}
			for _, v := range randomLines {
				itemIdWeight[v.Id] += 1
			}
			delete(rewards, itemId)
			for i := 0; i < int(itemValue); i++ {
				newItemId := util.RandomByWeight(itemIdWeight)
				rewards[newItemId] += 1
			}
		} else if itemLine.Type == servercfg.ItemType_Chest {
			delete(rewards, itemId)
			for i := 0; i < int(itemValue); i++ {
				ret := GetDropItem(itemLine.ChestDropGroup)
				for k, v := range ret {
					rewards[k] += v
				}
			}

		}

	}
	return rewards, gemRewards
}

func ChangeItem(ctx context.Context, uid int64, rewards map[int32]int64) (map[int32]int64, map[int32]int64) { //道具，宝石
	ret, _ := changeItemPer(ctx, uid, rewards)
	return changeItemPer(ctx, uid, ret)
}
