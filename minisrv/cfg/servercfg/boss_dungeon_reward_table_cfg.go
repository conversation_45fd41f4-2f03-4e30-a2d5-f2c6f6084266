// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type BossDungeonRewardTableCfg struct {
	Id       int32  `json:"Id"`       // Id
	StringId string `json:"StringId"` // StringId
}

func NewBossDungeonRewardTableCfg() *BossDungeonRewardTableCfg {
	return &BossDungeonRewardTableCfg{
		Id:       0,
		StringId: "",
	}
}

type BossDungeonRewardTable struct {
	records  map[int32]*BossDungeonRewardTableCfg
	localIds map[int32]struct{}
}

func NewBossDungeonRewardTable() *BossDungeonRewardTable {
	return &BossDungeonRewardTable{
		records:  map[int32]*BossDungeonRewardTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *BossDungeonRewardTable) Get(key int32) *BossDungeonRewardTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *BossDungeonRewardTable) GetAll() map[int32]*BossDungeonRewardTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *BossDungeonRewardTable) put(key int32, value *BossDungeonRewardTableCfg, local bool) *BossDungeonRewardTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *BossDungeonRewardTable) Range(f func(v *BossDungeonRewardTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *BossDungeonRewardTable) Filter(filterFuncs ...func(v *BossDungeonRewardTableCfg) bool) map[int32]*BossDungeonRewardTableCfg {
	filtered := map[int32]*BossDungeonRewardTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *BossDungeonRewardTable) FilterSlice(filterFuncs ...func(v *BossDungeonRewardTableCfg) bool) []*BossDungeonRewardTableCfg {
	filtered := []*BossDungeonRewardTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *BossDungeonRewardTable) FilterKeys(filterFuncs ...func(v *BossDungeonRewardTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *BossDungeonRewardTable) satisfied(v *BossDungeonRewardTableCfg, filterFuncs ...func(v *BossDungeonRewardTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *BossDungeonRewardTable) setupIndexes() error {
	return nil
}

func (t *BossDungeonRewardTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *BossDungeonRewardTableCfg) bindRefs(c *Configs) {
}
