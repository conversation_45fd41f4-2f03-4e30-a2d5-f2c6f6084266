// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type GoToTableCfg struct {
	Id             int32      `json:"Id"`             // Id
	CanBreak       bool       `json:"CanBreak"`       // 是否可以打断
	CloseCurruntUi bool       `json:"CloseCurruntUi"` // 是否关闭当前页面
	GoToStep       []*GoToStr `json:"GoToStep"`       // 跳转步骤
}

func NewGoToTableCfg() *GoToTableCfg {
	return &GoToTableCfg{
		Id:             0,
		CanBreak:       false,
		CloseCurruntUi: false,
		GoToStep:       []*GoToStr{},
	}
}

func NewMockGoToTableCfg() *GoToTableCfg {
	return &GoToTableCfg{
		Id:             0,
		CanBreak:       false,
		CloseCurruntUi: false,
		GoToStep:       []*GoToStr{NewMockGoToStr()},
	}
}

type GoToTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*GoToTableCfg
	localIds         map[int32]struct{}
}

func NewGoToTable(configs *Configs) *GoToTable {
	return &GoToTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*GoToTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *GoToTable) Get(key int32) *GoToTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *GoToTable) GetAll() map[int32]*GoToTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *GoToTable) put(key int32, value *GoToTableCfg, local bool) *GoToTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *GoToTable) putFromInheritedTable(key int32, value *GoToTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[GoToTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *GoToTable) Put(key int32, value *GoToTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[GoToTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *GoToTable) PutAll(m map[int32]*GoToTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *GoToTable) Range(f func(v *GoToTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *GoToTable) Filter(filterFuncs ...func(v *GoToTableCfg) bool) map[int32]*GoToTableCfg {
	filtered := map[int32]*GoToTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *GoToTable) FilterSlice(filterFuncs ...func(v *GoToTableCfg) bool) []*GoToTableCfg {
	filtered := []*GoToTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *GoToTable) FilterKeys(filterFuncs ...func(v *GoToTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *GoToTable) satisfied(v *GoToTableCfg, filterFuncs ...func(v *GoToTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *GoToTable) setupIndexes() error {
	return nil
}

func (t *GoToTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *GoToTableCfg) bindRefs(c *Configs) {
	for _, e := range r.GoToStep {
		e.bindRefs(c)
	}
}

func (t *GoToTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[GoToTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewGoToTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [GoToTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[GoToTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// CanBreak
		{
			if record[t.getIndexInCsv("CanBreak")] == "" {
				recordCfg.CanBreak = false
			} else {
				var err error
				recordCfg.CanBreak, err = strconv.ParseBool(record[t.getIndexInCsv("CanBreak")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [GoToTable]unmarshal csv record failed, varName=CanBreak, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("CanBreak")], err)
					} else {
						return fmt.Errorf("[GoToTable]unmarshal csv record failed, varName=CanBreak, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("CanBreak")], err)
					}
				}
			}
		}
		// CloseCurruntUi
		{
			if record[t.getIndexInCsv("CloseCurruntUi")] == "" {
				recordCfg.CloseCurruntUi = false
			} else {
				var err error
				recordCfg.CloseCurruntUi, err = strconv.ParseBool(record[t.getIndexInCsv("CloseCurruntUi")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [GoToTable]unmarshal csv record failed, varName=CloseCurruntUi, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("CloseCurruntUi")], err)
					} else {
						return fmt.Errorf("[GoToTable]unmarshal csv record failed, varName=CloseCurruntUi, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("CloseCurruntUi")], err)
					}
				}
			}
		}
		// GoToStep
		{
			cfgoMeetNilForGoToStepOfRecordCfg := false
			// element 0 of GoToStep
			if !cfgoMeetNilForGoToStepOfRecordCfg {
				cfgoMeetNilForGoToStepOfRecordCfg = true
				var cfgoElemOfGoToStepOfRecordCfg *GoToStr = NewGoToStr()
				{
					if record[t.getIndexInCsv("GoToStep1Type")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseGoToType(record[t.getIndexInCsv("GoToStep1Type")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep1Type")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep1Type")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Type = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep1Hud")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseHudType(record[t.getIndexInCsv("GoToStep1Hud")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep1Hud")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep1Hud")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Hud = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep1Level")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Level, err = configs.MainLevelTable.getIdByRef(record[t.getIndexInCsv("GoToStep1Level")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep1Level")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep1Level")], err)
							}
						}
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("GoToStep1Path")]) != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoElemOfGoToStepOfRecordCfg.Path = strings.TrimSpace(record[t.getIndexInCsv("GoToStep1Path")])
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep1Size")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GoToStep1Size")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep1Size")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep1Size")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Size = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep1Dialogue")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Dialogue, err = configs.NpcDialogueTable.getIdByRef(record[t.getIndexInCsv("GoToStep1Dialogue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep1Dialogue")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep1Dialogue")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForGoToStepOfRecordCfg {
					recordCfg.GoToStep = append(recordCfg.GoToStep, cfgoElemOfGoToStepOfRecordCfg)
				}
			}
			// element 1 of GoToStep
			if !cfgoMeetNilForGoToStepOfRecordCfg {
				cfgoMeetNilForGoToStepOfRecordCfg = true
				var cfgoElemOfGoToStepOfRecordCfg *GoToStr = NewGoToStr()
				{
					if record[t.getIndexInCsv("GoToStep2Type")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseGoToType(record[t.getIndexInCsv("GoToStep2Type")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep2Type")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep2Type")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Type = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep2Hud")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseHudType(record[t.getIndexInCsv("GoToStep2Hud")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep2Hud")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep2Hud")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Hud = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep2Level")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Level, err = configs.MainLevelTable.getIdByRef(record[t.getIndexInCsv("GoToStep2Level")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep2Level")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep2Level")], err)
							}
						}
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("GoToStep2Path")]) != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoElemOfGoToStepOfRecordCfg.Path = strings.TrimSpace(record[t.getIndexInCsv("GoToStep2Path")])
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep2Size")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GoToStep2Size")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep2Size")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep2Size")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Size = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep2Dialogue")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Dialogue, err = configs.NpcDialogueTable.getIdByRef(record[t.getIndexInCsv("GoToStep2Dialogue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep2Dialogue")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep2Dialogue")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForGoToStepOfRecordCfg {
					recordCfg.GoToStep = append(recordCfg.GoToStep, cfgoElemOfGoToStepOfRecordCfg)
				}
			}
			// element 2 of GoToStep
			if !cfgoMeetNilForGoToStepOfRecordCfg {
				cfgoMeetNilForGoToStepOfRecordCfg = true
				var cfgoElemOfGoToStepOfRecordCfg *GoToStr = NewGoToStr()
				{
					if record[t.getIndexInCsv("GoToStep3Type")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseGoToType(record[t.getIndexInCsv("GoToStep3Type")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep3Type")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep3Type")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Type = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep3Hud")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseHudType(record[t.getIndexInCsv("GoToStep3Hud")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep3Hud")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep3Hud")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Hud = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep3Level")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Level, err = configs.MainLevelTable.getIdByRef(record[t.getIndexInCsv("GoToStep3Level")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep3Level")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep3Level")], err)
							}
						}
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("GoToStep3Path")]) != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoElemOfGoToStepOfRecordCfg.Path = strings.TrimSpace(record[t.getIndexInCsv("GoToStep3Path")])
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep3Size")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GoToStep3Size")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep3Size")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep3Size")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Size = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep3Dialogue")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Dialogue, err = configs.NpcDialogueTable.getIdByRef(record[t.getIndexInCsv("GoToStep3Dialogue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep3Dialogue")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep3Dialogue")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForGoToStepOfRecordCfg {
					recordCfg.GoToStep = append(recordCfg.GoToStep, cfgoElemOfGoToStepOfRecordCfg)
				}
			}
			// element 3 of GoToStep
			if !cfgoMeetNilForGoToStepOfRecordCfg {
				cfgoMeetNilForGoToStepOfRecordCfg = true
				var cfgoElemOfGoToStepOfRecordCfg *GoToStr = NewGoToStr()
				{
					if record[t.getIndexInCsv("GoToStep4Type")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseGoToType(record[t.getIndexInCsv("GoToStep4Type")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep4Type")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep4Type")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Type = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep4Hud")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseHudType(record[t.getIndexInCsv("GoToStep4Hud")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep4Hud")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep4Hud")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Hud = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep4Level")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Level, err = configs.MainLevelTable.getIdByRef(record[t.getIndexInCsv("GoToStep4Level")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep4Level")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep4Level")], err)
							}
						}
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("GoToStep4Path")]) != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoElemOfGoToStepOfRecordCfg.Path = strings.TrimSpace(record[t.getIndexInCsv("GoToStep4Path")])
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep4Size")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GoToStep4Size")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep4Size")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep4Size")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Size = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep4Dialogue")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Dialogue, err = configs.NpcDialogueTable.getIdByRef(record[t.getIndexInCsv("GoToStep4Dialogue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep4Dialogue")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep4Dialogue")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForGoToStepOfRecordCfg {
					recordCfg.GoToStep = append(recordCfg.GoToStep, cfgoElemOfGoToStepOfRecordCfg)
				}
			}
			// element 4 of GoToStep
			if !cfgoMeetNilForGoToStepOfRecordCfg {
				cfgoMeetNilForGoToStepOfRecordCfg = true
				var cfgoElemOfGoToStepOfRecordCfg *GoToStr = NewGoToStr()
				{
					if record[t.getIndexInCsv("GoToStep5Type")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseGoToType(record[t.getIndexInCsv("GoToStep5Type")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep5Type")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep5Type")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Type = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep5Hud")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseHudType(record[t.getIndexInCsv("GoToStep5Hud")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep5Hud")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep5Hud")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Hud = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep5Level")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Level, err = configs.MainLevelTable.getIdByRef(record[t.getIndexInCsv("GoToStep5Level")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep5Level")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep5Level")], err)
							}
						}
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("GoToStep5Path")]) != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoElemOfGoToStepOfRecordCfg.Path = strings.TrimSpace(record[t.getIndexInCsv("GoToStep5Path")])
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep5Size")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GoToStep5Size")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep5Size")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep5Size")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Size = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep5Dialogue")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Dialogue, err = configs.NpcDialogueTable.getIdByRef(record[t.getIndexInCsv("GoToStep5Dialogue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep5Dialogue")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep5Dialogue")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForGoToStepOfRecordCfg {
					recordCfg.GoToStep = append(recordCfg.GoToStep, cfgoElemOfGoToStepOfRecordCfg)
				}
			}
			// element 5 of GoToStep
			if !cfgoMeetNilForGoToStepOfRecordCfg {
				cfgoMeetNilForGoToStepOfRecordCfg = true
				var cfgoElemOfGoToStepOfRecordCfg *GoToStr = NewGoToStr()
				{
					if record[t.getIndexInCsv("GoToStep6Type")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseGoToType(record[t.getIndexInCsv("GoToStep6Type")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep6Type")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep6Type")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Type = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep6Hud")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseHudType(record[t.getIndexInCsv("GoToStep6Hud")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep6Hud")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep6Hud")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Hud = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep6Level")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Level, err = configs.MainLevelTable.getIdByRef(record[t.getIndexInCsv("GoToStep6Level")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep6Level")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep6Level")], err)
							}
						}
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("GoToStep6Path")]) != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoElemOfGoToStepOfRecordCfg.Path = strings.TrimSpace(record[t.getIndexInCsv("GoToStep6Path")])
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep6Size")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GoToStep6Size")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep6Size")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep6Size")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Size = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep6Dialogue")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Dialogue, err = configs.NpcDialogueTable.getIdByRef(record[t.getIndexInCsv("GoToStep6Dialogue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep6Dialogue")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep6Dialogue")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForGoToStepOfRecordCfg {
					recordCfg.GoToStep = append(recordCfg.GoToStep, cfgoElemOfGoToStepOfRecordCfg)
				}
			}
			// element 6 of GoToStep
			if !cfgoMeetNilForGoToStepOfRecordCfg {
				cfgoMeetNilForGoToStepOfRecordCfg = true
				var cfgoElemOfGoToStepOfRecordCfg *GoToStr = NewGoToStr()
				{
					if record[t.getIndexInCsv("GoToStep7Type")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseGoToType(record[t.getIndexInCsv("GoToStep7Type")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep7Type")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep7Type")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Type = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep7Hud")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseHudType(record[t.getIndexInCsv("GoToStep7Hud")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep7Hud")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep7Hud")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Hud = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep7Level")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Level, err = configs.MainLevelTable.getIdByRef(record[t.getIndexInCsv("GoToStep7Level")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep7Level")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep7Level")], err)
							}
						}
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("GoToStep7Path")]) != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoElemOfGoToStepOfRecordCfg.Path = strings.TrimSpace(record[t.getIndexInCsv("GoToStep7Path")])
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep7Size")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GoToStep7Size")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep7Size")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep7Size")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Size = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep7Dialogue")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Dialogue, err = configs.NpcDialogueTable.getIdByRef(record[t.getIndexInCsv("GoToStep7Dialogue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep7Dialogue")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep7Dialogue")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForGoToStepOfRecordCfg {
					recordCfg.GoToStep = append(recordCfg.GoToStep, cfgoElemOfGoToStepOfRecordCfg)
				}
			}
			// element 7 of GoToStep
			if !cfgoMeetNilForGoToStepOfRecordCfg {
				cfgoMeetNilForGoToStepOfRecordCfg = true
				var cfgoElemOfGoToStepOfRecordCfg *GoToStr = NewGoToStr()
				{
					if record[t.getIndexInCsv("GoToStep8Type")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseGoToType(record[t.getIndexInCsv("GoToStep8Type")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep8Type")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep8Type")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Type = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep8Hud")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseHudType(record[t.getIndexInCsv("GoToStep8Hud")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep8Hud")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep8Hud")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Hud = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep8Level")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Level, err = configs.MainLevelTable.getIdByRef(record[t.getIndexInCsv("GoToStep8Level")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep8Level")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep8Level")], err)
							}
						}
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("GoToStep8Path")]) != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoElemOfGoToStepOfRecordCfg.Path = strings.TrimSpace(record[t.getIndexInCsv("GoToStep8Path")])
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep8Size")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GoToStep8Size")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep8Size")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep8Size")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Size = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep8Dialogue")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Dialogue, err = configs.NpcDialogueTable.getIdByRef(record[t.getIndexInCsv("GoToStep8Dialogue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep8Dialogue")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep8Dialogue")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForGoToStepOfRecordCfg {
					recordCfg.GoToStep = append(recordCfg.GoToStep, cfgoElemOfGoToStepOfRecordCfg)
				}
			}
			// element 8 of GoToStep
			if !cfgoMeetNilForGoToStepOfRecordCfg {
				cfgoMeetNilForGoToStepOfRecordCfg = true
				var cfgoElemOfGoToStepOfRecordCfg *GoToStr = NewGoToStr()
				{
					if record[t.getIndexInCsv("GoToStep9Type")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseGoToType(record[t.getIndexInCsv("GoToStep9Type")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep9Type")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep9Type")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Type = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep9Hud")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseHudType(record[t.getIndexInCsv("GoToStep9Hud")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep9Hud")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep9Hud")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Hud = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep9Level")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Level, err = configs.MainLevelTable.getIdByRef(record[t.getIndexInCsv("GoToStep9Level")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep9Level")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep9Level")], err)
							}
						}
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("GoToStep9Path")]) != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoElemOfGoToStepOfRecordCfg.Path = strings.TrimSpace(record[t.getIndexInCsv("GoToStep9Path")])
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep9Size")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GoToStep9Size")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep9Size")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep9Size")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Size = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep9Dialogue")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Dialogue, err = configs.NpcDialogueTable.getIdByRef(record[t.getIndexInCsv("GoToStep9Dialogue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep9Dialogue")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep9Dialogue")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForGoToStepOfRecordCfg {
					recordCfg.GoToStep = append(recordCfg.GoToStep, cfgoElemOfGoToStepOfRecordCfg)
				}
			}
			// element 9 of GoToStep
			if !cfgoMeetNilForGoToStepOfRecordCfg {
				cfgoMeetNilForGoToStepOfRecordCfg = true
				var cfgoElemOfGoToStepOfRecordCfg *GoToStr = NewGoToStr()
				{
					if record[t.getIndexInCsv("GoToStep10Type")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseGoToType(record[t.getIndexInCsv("GoToStep10Type")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep10Type")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@GoToType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Type, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep10Type")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Type = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep10Hud")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoEnum, err := parseHudType(record[t.getIndexInCsv("GoToStep10Hud")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep10Hud")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse enum@HudType in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Hud, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep10Hud")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Hud = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep10Level")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Level, err = configs.MainLevelTable.getIdByRef(record[t.getIndexInCsv("GoToStep10Level")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep10Level")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@MainLevelTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Level, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep10Level")], err)
							}
						}
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("GoToStep10Path")]) != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoElemOfGoToStepOfRecordCfg.Path = strings.TrimSpace(record[t.getIndexInCsv("GoToStep10Path")])
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep10Size")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("GoToStep10Size")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep10Size")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Size, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep10Size")], err)
							}
						}
						cfgoElemOfGoToStepOfRecordCfg.Size = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("GoToStep10Dialogue")] != "" {
						cfgoMeetNilForGoToStepOfRecordCfg = false
						var err error
						cfgoElemOfGoToStepOfRecordCfg.Dialogue, err = configs.NpcDialogueTable.getIdByRef(record[t.getIndexInCsv("GoToStep10Dialogue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoToStep10Dialogue")], err)
							} else {
								return fmt.Errorf("[GoToTable]unmarshal record failed, cannot parse ref@NpcDialogueTable in collection, elemVarName=cfgoElemOfGoToStepOfRecordCfg.Dialogue, value=%s, err:[%s]", record[t.getIndexInCsv("GoToStep10Dialogue")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForGoToStepOfRecordCfg {
					recordCfg.GoToStep = append(recordCfg.GoToStep, cfgoElemOfGoToStepOfRecordCfg)
				}
			}

		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [GoToTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[GoToTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *GoToTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "GoToTable.csv") && (!strings.HasPrefix(fileName, "GoToTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for GoToTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[GoToTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[GoToTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[GoToTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[GoToTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[GoToTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[GoToTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[GoToTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[GoToTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [GoToTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *GoToTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[GoToTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [GoToTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *GoToTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[GoToTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *GoToTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[GoToTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[GoToTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
