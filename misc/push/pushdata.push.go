package push

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"github.com/gogo/protobuf/proto"
)

func buildDataRow(m proto.Message) *minirpc.DataRow {
	switch m.(type) {
	case *minirpc.AchievementTask:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_AchievementTask,
			Row:   &minirpc.DataRow_AchievementTask{AchievementTask: m.(*minirpc.AchievementTask)},
		}
	case *minirpc.ActivityTask:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_ActivityTask,
			Row:   &minirpc.DataRow_ActivityTask{ActivityTask: m.(*minirpc.ActivityTask)},
		}
	case *minirpc.Alliance:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_Alliance,
			Row:   &minirpc.DataRow_Alliance{Alliance: m.(*minirpc.Alliance)},
		}
	case *minirpc.AllianceMember:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_AllianceMember,
			Row:   &minirpc.DataRow_AllianceMember{AllianceMember: m.(*minirpc.AllianceMember)},
		}
	case *minirpc.AllianceShopBuy:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_AllianceShopBuy,
			Row:   &minirpc.DataRow_AllianceShopBuy{AllianceShopBuy: m.(*minirpc.AllianceShopBuy)},
		}
	case *minirpc.AllianceTask:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_AllianceTask,
			Row:   &minirpc.DataRow_AllianceTask{AllianceTask: m.(*minirpc.AllianceTask)},
		}
	case *minirpc.Animal:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_Animal,
			Row:   &minirpc.DataRow_Animal{Animal: m.(*minirpc.Animal)},
		}
	case *minirpc.BuildingInfo:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_BuildingInfo,
			Row:   &minirpc.DataRow_BuildingInfo{BuildingInfo: m.(*minirpc.BuildingInfo)},
		}
	case *minirpc.ClientVersion:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_ClientVersion,
			Row:   &minirpc.DataRow_ClientVersion{ClientVersion: m.(*minirpc.ClientVersion)},
		}
	case *minirpc.Consumable:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_Consumable,
			Row:   &minirpc.DataRow_Consumable{Consumable: m.(*minirpc.Consumable)},
		}
	case *minirpc.DailyTask:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_DailyTask,
			Row:   &minirpc.DataRow_DailyTask{DailyTask: m.(*minirpc.DailyTask)},
		}
	case *minirpc.Dave:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_Dave,
			Row:   &minirpc.DataRow_Dave{Dave: m.(*minirpc.Dave)},
		}
	case *minirpc.Dungeon:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_Dungeon,
			Row:   &minirpc.DataRow_Dungeon{Dungeon: m.(*minirpc.Dungeon)},
		}
	case *minirpc.FirstCharge:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_FirstCharge,
			Row:   &minirpc.DataRow_FirstCharge{FirstCharge: m.(*minirpc.FirstCharge)},
		}
	case *minirpc.FunctionOpen:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_FunctionOpen,
			Row:   &minirpc.DataRow_FunctionOpen{FunctionOpen: m.(*minirpc.FunctionOpen)},
		}
	case *minirpc.GlobalStageLevel:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_GlobalStageLevel,
			Row:   &minirpc.DataRow_GlobalStageLevel{GlobalStageLevel: m.(*minirpc.GlobalStageLevel)},
		}
	case *minirpc.GrowthFund:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_GrowthFund,
			Row:   &minirpc.DataRow_GrowthFund{GrowthFund: m.(*minirpc.GrowthFund)},
		}
	case *minirpc.GrowthTask:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_GrowthTask,
			Row:   &minirpc.DataRow_GrowthTask{GrowthTask: m.(*minirpc.GrowthTask)},
		}
	case *minirpc.HeroInfo:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_HeroInfo,
			Row:   &minirpc.DataRow_HeroInfo{HeroInfo: m.(*minirpc.HeroInfo)},
		}
	case *minirpc.HeroLottery:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_HeroLottery,
			Row:   &minirpc.DataRow_HeroLottery{HeroLottery: m.(*minirpc.HeroLottery)},
		}
	case *minirpc.HeroSkill:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_HeroSkill,
			Row:   &minirpc.DataRow_HeroSkill{HeroSkill: m.(*minirpc.HeroSkill)},
		}
	case *minirpc.HuatuoVersion:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_HuatuoVersion,
			Row:   &minirpc.DataRow_HuatuoVersion{HuatuoVersion: m.(*minirpc.HuatuoVersion)},
		}
	case *minirpc.Lord:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_Lord,
			Row:   &minirpc.DataRow_Lord{Lord: m.(*minirpc.Lord)},
		}
	case *minirpc.LordEquip:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_LordEquip,
			Row:   &minirpc.DataRow_LordEquip{LordEquip: m.(*minirpc.LordEquip)},
		}
	case *minirpc.LordGem:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_LordGem,
			Row:   &minirpc.DataRow_LordGem{LordGem: m.(*minirpc.LordGem)},
		}
	case *minirpc.LordGemRandom:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_LordGemRandom,
			Row:   &minirpc.DataRow_LordGemRandom{LordGemRandom: m.(*minirpc.LordGemRandom)},
		}
	case *minirpc.MainLineStage:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_MainLineStage,
			Row:   &minirpc.DataRow_MainLineStage{MainLineStage: m.(*minirpc.MainLineStage)},
		}
	case *minirpc.MainTask:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_MainTask,
			Row:   &minirpc.DataRow_MainTask{MainTask: m.(*minirpc.MainTask)},
		}
	case *minirpc.MapEvent:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_MapEvent,
			Row:   &minirpc.DataRow_MapEvent{MapEvent: m.(*minirpc.MapEvent)},
		}
	case *minirpc.MonsterInfo:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_MonsterInfo,
			Row:   &minirpc.DataRow_MonsterInfo{MonsterInfo: m.(*minirpc.MonsterInfo)},
		}
	case *minirpc.MonthCard:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_MonthCard,
			Row:   &minirpc.DataRow_MonthCard{MonthCard: m.(*minirpc.MonthCard)},
		}
	case *minirpc.NewbieGuide:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_NewbieGuide,
			Row:   &minirpc.DataRow_NewbieGuide{NewbieGuide: m.(*minirpc.NewbieGuide)},
		}
	case *minirpc.OutConsumable:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_OutConsumable,
			Row:   &minirpc.DataRow_OutConsumable{OutConsumable: m.(*minirpc.OutConsumable)},
		}
	case *minirpc.PaymentOrder:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_PaymentOrder,
			Row:   &minirpc.DataRow_PaymentOrder{PaymentOrder: m.(*minirpc.PaymentOrder)},
		}
	case *minirpc.RegularPack:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_RegularPack,
			Row:   &minirpc.DataRow_RegularPack{RegularPack: m.(*minirpc.RegularPack)},
		}
	case *minirpc.RepeatTask:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_RepeatTask,
			Row:   &minirpc.DataRow_RepeatTask{RepeatTask: m.(*minirpc.RepeatTask)},
		}
	case *minirpc.Research:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_Research,
			Row:   &minirpc.DataRow_Research{Research: m.(*minirpc.Research)},
		}
	case *minirpc.TaskCounter:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_TaskCounter,
			Row:   &minirpc.DataRow_TaskCounter{TaskCounter: m.(*minirpc.TaskCounter)},
		}
	case *minirpc.UserBenefit:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_UserBenefit,
			Row:   &minirpc.DataRow_UserBenefit{UserBenefit: m.(*minirpc.UserBenefit)},
		}
	case *minirpc.UserData:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_UserData,
			Row:   &minirpc.DataRow_UserData{UserData: m.(*minirpc.UserData)},
		}
	case *minirpc.UserDevices:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_UserDevices,
			Row:   &minirpc.DataRow_UserDevices{UserDevices: m.(*minirpc.UserDevices)},
		}
	case *minirpc.UserIapBuyTimes:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_UserIapBuyTimes,
			Row:   &minirpc.DataRow_UserIapBuyTimes{UserIapBuyTimes: m.(*minirpc.UserIapBuyTimes)},
		}
	case *minirpc.UserInfo:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_UserInfo,
			Row:   &minirpc.DataRow_UserInfo{UserInfo: m.(*minirpc.UserInfo)},
		}
	case *minirpc.UserJob:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_UserJob,
			Row:   &minirpc.DataRow_UserJob{UserJob: m.(*minirpc.UserJob)},
		}
	case *minirpc.UserLookup:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_UserLookup,
			Row:   &minirpc.DataRow_UserLookup{UserLookup: m.(*minirpc.UserLookup)},
		}
	case *minirpc.UserMail:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_UserMail,
			Row:   &minirpc.DataRow_UserMail{UserMail: m.(*minirpc.UserMail)},
		}
	case *minirpc.UserTroop:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_UserTroop,
			Row:   &minirpc.DataRow_UserTroop{UserTroop: m.(*minirpc.UserTroop)},
		}
	case *minirpc.Villager:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_Villager,
			Row:   &minirpc.DataRow_Villager{Villager: m.(*minirpc.Villager)},
		}
	case *minirpc.WeeklyTask:
		return &minirpc.DataRow{
			Table: minirpc.ClientTable_WeeklyTask,
			Row:   &minirpc.DataRow_WeeklyTask{WeeklyTask: m.(*minirpc.WeeklyTask)},
		}
	}

	panic("data row error")
}

func buildPayload(m proto.Message) *minirpc.PayloadRow {
	switch m.(type) {

	case *minirpc.AddFriendInfo:
		return &minirpc.PayloadRow{
			AddFriendInfo: m.(*minirpc.AddFriendInfo),
		}
	case *minirpc.AgreeJoinAlliance:
		return &minirpc.PayloadRow{
			AgreeJoinAlliance: m.(*minirpc.AgreeJoinAlliance),
		}
	case *minirpc.OrderResult:
		return &minirpc.PayloadRow{
			OrderResult: m.(*minirpc.OrderResult),
		}
	case *minirpc.UserPush:
		return &minirpc.PayloadRow{
			UserPush: m.(*minirpc.UserPush),
		}
	}

	panic("payload row error")
}
