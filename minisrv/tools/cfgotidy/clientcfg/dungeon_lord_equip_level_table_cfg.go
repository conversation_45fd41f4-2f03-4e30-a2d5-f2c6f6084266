// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type DungeonLordEquipLevelTableCfg struct {
	Id                      int32                         `json:"Id"`                   // Id
	LevelGrade              string                        `json:"LevelGrade"`           // 关卡等级
	MonsterPreview          []int32                       `json:"MonsterPreview"`       // 怪物预览
	MonsterPreviewRef       []*MonsterTypeTableCfg        `json:"-"`                    // 怪物预览
	MonsterPreviewPos       []int32                       `json:"MonsterPreviewPos"`    // 预览位置组
	MonsterPreviewScheme    int32                         `json:"MonsterPreviewScheme"` // 预览方案
	MonsterPreviewSchemeRef *MonsterPreviewSchemeTableCfg `json:"-"`                    // 预览方案
	Dungeon                 int32                         `json:"Dungeon"`              // 副本类型
	DungeonRef              *DungeonTypeTableCfg          `json:"-"`                    // 副本类型
	Level                   int32                         `json:"Level"`                // 等级
	ChooseNum               int32                         `json:"ChooseNum"`            // 开场选卡次数
	IsMax                   bool                          `json:"IsMax"`                // 是否最大关卡
	FirstReward             *RewardKVS                    `json:"FirstReward"`          // 首通奖励
	Reward                  []*RewardKVS                  `json:"Reward"`               // 奖励
	BuffType                []int32                       `json:"BuffType"`             // Buff类型
	BuffTypeRef             []*HeroSkillBuffTableCfg      `json:"-"`                    // Buff类型
	Buff                    []*IDPStr                     `json:"Buff"`                 // Buff图-文-参
}

func NewDungeonLordEquipLevelTableCfg() *DungeonLordEquipLevelTableCfg {
	return &DungeonLordEquipLevelTableCfg{
		Id:                      0,
		LevelGrade:              "",
		MonsterPreview:          []int32{},
		MonsterPreviewRef:       []*MonsterTypeTableCfg{},
		MonsterPreviewPos:       []int32{},
		MonsterPreviewScheme:    0,
		MonsterPreviewSchemeRef: nil,
		Dungeon:                 0,
		DungeonRef:              nil,
		Level:                   0,
		ChooseNum:               0,
		IsMax:                   false,
		FirstReward:             NewRewardKVS(),
		Reward:                  []*RewardKVS{},
		BuffType:                []int32{},
		BuffTypeRef:             []*HeroSkillBuffTableCfg{},
		Buff:                    []*IDPStr{},
	}
}

func NewMockDungeonLordEquipLevelTableCfg() *DungeonLordEquipLevelTableCfg {
	return &DungeonLordEquipLevelTableCfg{
		Id:                      0,
		LevelGrade:              "",
		MonsterPreview:          []int32{0},
		MonsterPreviewRef:       []*MonsterTypeTableCfg{},
		MonsterPreviewPos:       []int32{0},
		MonsterPreviewScheme:    0,
		MonsterPreviewSchemeRef: nil,
		Dungeon:                 0,
		DungeonRef:              nil,
		Level:                   0,
		ChooseNum:               0,
		IsMax:                   false,
		FirstReward:             NewMockRewardKVS(),
		Reward:                  []*RewardKVS{NewMockRewardKVS()},
		BuffType:                []int32{0},
		BuffTypeRef:             []*HeroSkillBuffTableCfg{},
		Buff:                    []*IDPStr{NewMockIDPStr()},
	}
}

type DungeonLordEquipLevelTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*DungeonLordEquipLevelTableCfg
	localIds         map[int32]struct{}
}

func NewDungeonLordEquipLevelTable(configs *Configs) *DungeonLordEquipLevelTable {
	return &DungeonLordEquipLevelTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*DungeonLordEquipLevelTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *DungeonLordEquipLevelTable) Get(key int32) *DungeonLordEquipLevelTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *DungeonLordEquipLevelTable) GetAll() map[int32]*DungeonLordEquipLevelTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *DungeonLordEquipLevelTable) put(key int32, value *DungeonLordEquipLevelTableCfg, local bool) *DungeonLordEquipLevelTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *DungeonLordEquipLevelTable) putFromInheritedTable(key int32, value *DungeonLordEquipLevelTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[DungeonLordEquipLevelTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *DungeonLordEquipLevelTable) Put(key int32, value *DungeonLordEquipLevelTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[DungeonLordEquipLevelTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *DungeonLordEquipLevelTable) PutAll(m map[int32]*DungeonLordEquipLevelTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *DungeonLordEquipLevelTable) Range(f func(v *DungeonLordEquipLevelTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *DungeonLordEquipLevelTable) Filter(filterFuncs ...func(v *DungeonLordEquipLevelTableCfg) bool) map[int32]*DungeonLordEquipLevelTableCfg {
	filtered := map[int32]*DungeonLordEquipLevelTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *DungeonLordEquipLevelTable) FilterSlice(filterFuncs ...func(v *DungeonLordEquipLevelTableCfg) bool) []*DungeonLordEquipLevelTableCfg {
	filtered := []*DungeonLordEquipLevelTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *DungeonLordEquipLevelTable) FilterKeys(filterFuncs ...func(v *DungeonLordEquipLevelTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *DungeonLordEquipLevelTable) satisfied(v *DungeonLordEquipLevelTableCfg, filterFuncs ...func(v *DungeonLordEquipLevelTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *DungeonLordEquipLevelTable) setupIndexes() error {
	return nil
}

func (t *DungeonLordEquipLevelTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *DungeonLordEquipLevelTableCfg) bindRefs(c *Configs) {
	for _, e := range r.MonsterPreview {
		cfgoRefRecord := c.MonsterTypeTable.Get(e)
		r.MonsterPreviewRef = append(r.MonsterPreviewRef, cfgoRefRecord)
	}
	r.MonsterPreviewSchemeRef = c.MonsterPreviewSchemeTable.Get(r.MonsterPreviewScheme)
	r.DungeonRef = c.DungeonTypeTable.Get(r.Dungeon)
	r.FirstReward.bindRefs(c)
	for _, e := range r.Reward {
		e.bindRefs(c)
	}
	for _, e := range r.BuffType {
		cfgoRefRecord := c.HeroSkillBuffTable.Get(e)
		r.BuffTypeRef = append(r.BuffTypeRef, cfgoRefRecord)
	}
	for _, e := range r.Buff {
		e.bindRefs(c)
	}
}

func (t *DungeonLordEquipLevelTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewDungeonLordEquipLevelTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// LevelGrade
		{
			recordCfg.LevelGrade = strings.TrimSpace(record[t.getIndexInCsv("LevelGrade")])
		}
		// MonsterPreview
		{
			cfgoMeetNilForMonsterPreviewOfRecordCfg := false
			// element 0 of MonsterPreview
			if !cfgoMeetNilForMonsterPreviewOfRecordCfg {
				cfgoMeetNilForMonsterPreviewOfRecordCfg = true
				var cfgoElemOfMonsterPreviewOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("MonsterPreview1")] != "" {
					cfgoMeetNilForMonsterPreviewOfRecordCfg = false
					var err error
					cfgoElemOfMonsterPreviewOfRecordCfg, err = configs.MonsterTypeTable.getIdByRef(record[t.getIndexInCsv("MonsterPreview1")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@MonsterTypeTable in collection, elemVarName=cfgoElemOfMonsterPreviewOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("MonsterPreview1")], err)
						} else {
							return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@MonsterTypeTable in collection, elemVarName=cfgoElemOfMonsterPreviewOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("MonsterPreview1")], err)
						}
					}
				}

				if !cfgoMeetNilForMonsterPreviewOfRecordCfg {
					recordCfg.MonsterPreview = append(recordCfg.MonsterPreview, cfgoElemOfMonsterPreviewOfRecordCfg)
				}
			}
			// element 1 of MonsterPreview
			if !cfgoMeetNilForMonsterPreviewOfRecordCfg {
				cfgoMeetNilForMonsterPreviewOfRecordCfg = true
				var cfgoElemOfMonsterPreviewOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("MonsterPreview2")] != "" {
					cfgoMeetNilForMonsterPreviewOfRecordCfg = false
					var err error
					cfgoElemOfMonsterPreviewOfRecordCfg, err = configs.MonsterTypeTable.getIdByRef(record[t.getIndexInCsv("MonsterPreview2")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@MonsterTypeTable in collection, elemVarName=cfgoElemOfMonsterPreviewOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("MonsterPreview2")], err)
						} else {
							return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@MonsterTypeTable in collection, elemVarName=cfgoElemOfMonsterPreviewOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("MonsterPreview2")], err)
						}
					}
				}

				if !cfgoMeetNilForMonsterPreviewOfRecordCfg {
					recordCfg.MonsterPreview = append(recordCfg.MonsterPreview, cfgoElemOfMonsterPreviewOfRecordCfg)
				}
			}
			// element 2 of MonsterPreview
			if !cfgoMeetNilForMonsterPreviewOfRecordCfg {
				cfgoMeetNilForMonsterPreviewOfRecordCfg = true
				var cfgoElemOfMonsterPreviewOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("MonsterPreview3")] != "" {
					cfgoMeetNilForMonsterPreviewOfRecordCfg = false
					var err error
					cfgoElemOfMonsterPreviewOfRecordCfg, err = configs.MonsterTypeTable.getIdByRef(record[t.getIndexInCsv("MonsterPreview3")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@MonsterTypeTable in collection, elemVarName=cfgoElemOfMonsterPreviewOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("MonsterPreview3")], err)
						} else {
							return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@MonsterTypeTable in collection, elemVarName=cfgoElemOfMonsterPreviewOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("MonsterPreview3")], err)
						}
					}
				}

				if !cfgoMeetNilForMonsterPreviewOfRecordCfg {
					recordCfg.MonsterPreview = append(recordCfg.MonsterPreview, cfgoElemOfMonsterPreviewOfRecordCfg)
				}
			}
			// element 3 of MonsterPreview
			if !cfgoMeetNilForMonsterPreviewOfRecordCfg {
				cfgoMeetNilForMonsterPreviewOfRecordCfg = true
				var cfgoElemOfMonsterPreviewOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("MonsterPreview4")] != "" {
					cfgoMeetNilForMonsterPreviewOfRecordCfg = false
					var err error
					cfgoElemOfMonsterPreviewOfRecordCfg, err = configs.MonsterTypeTable.getIdByRef(record[t.getIndexInCsv("MonsterPreview4")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@MonsterTypeTable in collection, elemVarName=cfgoElemOfMonsterPreviewOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("MonsterPreview4")], err)
						} else {
							return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@MonsterTypeTable in collection, elemVarName=cfgoElemOfMonsterPreviewOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("MonsterPreview4")], err)
						}
					}
				}

				if !cfgoMeetNilForMonsterPreviewOfRecordCfg {
					recordCfg.MonsterPreview = append(recordCfg.MonsterPreview, cfgoElemOfMonsterPreviewOfRecordCfg)
				}
			}
			// element 4 of MonsterPreview
			if !cfgoMeetNilForMonsterPreviewOfRecordCfg {
				cfgoMeetNilForMonsterPreviewOfRecordCfg = true
				var cfgoElemOfMonsterPreviewOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("MonsterPreview5")] != "" {
					cfgoMeetNilForMonsterPreviewOfRecordCfg = false
					var err error
					cfgoElemOfMonsterPreviewOfRecordCfg, err = configs.MonsterTypeTable.getIdByRef(record[t.getIndexInCsv("MonsterPreview5")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@MonsterTypeTable in collection, elemVarName=cfgoElemOfMonsterPreviewOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("MonsterPreview5")], err)
						} else {
							return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@MonsterTypeTable in collection, elemVarName=cfgoElemOfMonsterPreviewOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("MonsterPreview5")], err)
						}
					}
				}

				if !cfgoMeetNilForMonsterPreviewOfRecordCfg {
					recordCfg.MonsterPreview = append(recordCfg.MonsterPreview, cfgoElemOfMonsterPreviewOfRecordCfg)
				}
			}

		}
		// MonsterPreviewPos
		{
			if record[t.getIndexInCsv("MonsterPreviewPos")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("MonsterPreviewPos")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfMonsterPreviewPos int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfMonsterPreviewPos = 0
					} else {
						cfgoInt, err := strconv.Atoi(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse int in vector, varName=MonsterPreviewPos, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse int in vector, varName=MonsterPreviewPos, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfMonsterPreviewPos = int32(cfgoInt)
					}

					recordCfg.MonsterPreviewPos = append(recordCfg.MonsterPreviewPos, cfgoElemOfMonsterPreviewPos)
				}
			}
		}
		// MonsterPreviewScheme
		if record[t.getIndexInCsv("MonsterPreviewScheme")] == "" {
			recordCfg.MonsterPreviewScheme = 0
		} else {
			var err error
			recordCfg.MonsterPreviewScheme, err = configs.MonsterPreviewSchemeTable.getIdByRef(record[t.getIndexInCsv("MonsterPreviewScheme")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal csv record failed, varName=MonsterPreviewScheme, type=ref@MonsterPreviewSchemeTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("MonsterPreviewScheme")], err)
				} else {
					return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal csv record failed, varName=MonsterPreviewScheme, type=ref@MonsterPreviewSchemeTable, value=%s, err:[%s]", record[t.getIndexInCsv("MonsterPreviewScheme")], err)
				}
			}
		}
		// Dungeon
		if record[t.getIndexInCsv("Dungeon")] == "" {
			recordCfg.Dungeon = 0
		} else {
			var err error
			recordCfg.Dungeon, err = configs.DungeonTypeTable.getIdByRef(record[t.getIndexInCsv("Dungeon")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal csv record failed, varName=Dungeon, type=ref@DungeonTypeTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Dungeon")], err)
				} else {
					return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal csv record failed, varName=Dungeon, type=ref@DungeonTypeTable, value=%s, err:[%s]", record[t.getIndexInCsv("Dungeon")], err)
				}
			}
		}
		// Level
		{
			if record[t.getIndexInCsv("Level")] == "" {
				recordCfg.Level = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Level")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal csv record failed, varName=Level, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Level")], err)
					} else {
						return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal csv record failed, varName=Level, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Level")], err)
					}
				}
				recordCfg.Level = int32(cfgoInt)
			}
		}
		// ChooseNum
		{
			if record[t.getIndexInCsv("ChooseNum")] == "" {
				recordCfg.ChooseNum = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("ChooseNum")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal csv record failed, varName=ChooseNum, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("ChooseNum")], err)
					} else {
						return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal csv record failed, varName=ChooseNum, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("ChooseNum")], err)
					}
				}
				recordCfg.ChooseNum = int32(cfgoInt)
			}
		}
		// IsMax
		{
			if record[t.getIndexInCsv("IsMax")] == "" {
				recordCfg.IsMax = false
			} else {
				var err error
				recordCfg.IsMax, err = strconv.ParseBool(record[t.getIndexInCsv("IsMax")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal csv record failed, varName=IsMax, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("IsMax")], err)
					} else {
						return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal csv record failed, varName=IsMax, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("IsMax")], err)
					}
				}
			}
		}
		// FirstReward
		{
			// RewardType
			if record[t.getIndexInCsv("FirstRewardRewardType")] == "" {
				recordCfg.FirstReward.RewardType = 0
			} else {
				var err error
				recordCfg.FirstReward.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("FirstRewardRewardType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal csv record failed, varName=RewardType, type=ref@ItemTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("FirstRewardRewardType")], err)
					} else {
						return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal csv record failed, varName=RewardType, type=ref@ItemTable, value=%s, err:[%s]", record[t.getIndexInCsv("FirstRewardRewardType")], err)
					}
				}
			}
			// RewardValue
			{
				if record[t.getIndexInCsv("FirstRewardRewardValue")] == "" {
					recordCfg.FirstReward.RewardValue = 0
				} else {
					cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("FirstRewardRewardValue")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal csv record failed, varName=RewardValue, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("FirstRewardRewardValue")], err)
						} else {
							return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal csv record failed, varName=RewardValue, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("FirstRewardRewardValue")], err)
						}
					}
					recordCfg.FirstReward.RewardValue = int32(cfgoInt)
				}
			}
		}
		// Reward
		{
			cfgoMeetNilForRewardOfRecordCfg := false
			// element 0 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("Reward1RewardType")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						var err error
						cfgoElemOfRewardOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward1RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward1RewardType")], err)
							} else {
								return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("Reward1RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("Reward1RewardValue")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Reward1RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward1RewardValue")], err)
							} else {
								return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("Reward1RewardValue")], err)
							}
						}
						cfgoElemOfRewardOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}
			// element 1 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("Reward2RewardType")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						var err error
						cfgoElemOfRewardOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward2RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward2RewardType")], err)
							} else {
								return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("Reward2RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("Reward2RewardValue")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Reward2RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward2RewardValue")], err)
							} else {
								return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("Reward2RewardValue")], err)
							}
						}
						cfgoElemOfRewardOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}

		}
		// BuffType
		{
			cfgoMeetNilForBuffTypeOfRecordCfg := false
			// element 0 of BuffType
			if !cfgoMeetNilForBuffTypeOfRecordCfg {
				cfgoMeetNilForBuffTypeOfRecordCfg = true
				var cfgoElemOfBuffTypeOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("BuffType1")] != "" {
					cfgoMeetNilForBuffTypeOfRecordCfg = false
					var err error
					cfgoElemOfBuffTypeOfRecordCfg, err = configs.HeroSkillBuffTable.getIdByRef(record[t.getIndexInCsv("BuffType1")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfBuffTypeOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("BuffType1")], err)
						} else {
							return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfBuffTypeOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("BuffType1")], err)
						}
					}
				}

				if !cfgoMeetNilForBuffTypeOfRecordCfg {
					recordCfg.BuffType = append(recordCfg.BuffType, cfgoElemOfBuffTypeOfRecordCfg)
				}
			}
			// element 1 of BuffType
			if !cfgoMeetNilForBuffTypeOfRecordCfg {
				cfgoMeetNilForBuffTypeOfRecordCfg = true
				var cfgoElemOfBuffTypeOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("BuffType2")] != "" {
					cfgoMeetNilForBuffTypeOfRecordCfg = false
					var err error
					cfgoElemOfBuffTypeOfRecordCfg, err = configs.HeroSkillBuffTable.getIdByRef(record[t.getIndexInCsv("BuffType2")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfBuffTypeOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("BuffType2")], err)
						} else {
							return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfBuffTypeOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("BuffType2")], err)
						}
					}
				}

				if !cfgoMeetNilForBuffTypeOfRecordCfg {
					recordCfg.BuffType = append(recordCfg.BuffType, cfgoElemOfBuffTypeOfRecordCfg)
				}
			}
			// element 2 of BuffType
			if !cfgoMeetNilForBuffTypeOfRecordCfg {
				cfgoMeetNilForBuffTypeOfRecordCfg = true
				var cfgoElemOfBuffTypeOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("BuffType3")] != "" {
					cfgoMeetNilForBuffTypeOfRecordCfg = false
					var err error
					cfgoElemOfBuffTypeOfRecordCfg, err = configs.HeroSkillBuffTable.getIdByRef(record[t.getIndexInCsv("BuffType3")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfBuffTypeOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("BuffType3")], err)
						} else {
							return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfBuffTypeOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("BuffType3")], err)
						}
					}
				}

				if !cfgoMeetNilForBuffTypeOfRecordCfg {
					recordCfg.BuffType = append(recordCfg.BuffType, cfgoElemOfBuffTypeOfRecordCfg)
				}
			}

		}
		// Buff
		{
			cfgoMeetNilForBuffOfRecordCfg := false
			// element 0 of Buff
			if !cfgoMeetNilForBuffOfRecordCfg {
				cfgoMeetNilForBuffOfRecordCfg = true
				var cfgoElemOfBuffOfRecordCfg *IDPStr = NewIDPStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Buff1Image")]) != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoElemOfBuffOfRecordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Buff1Image")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Buff1Desc")]) != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoElemOfBuffOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Buff1Desc")])
					}
				}
				{
					if record[t.getIndexInCsv("Buff1Param")] != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("Buff1Param")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBuffOfRecordCfg.Param, value=%s, err:[%s]\n", record[t.getIndexInCsv("Buff1Param")], err)
							} else {
								return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBuffOfRecordCfg.Param, value=%s, err:[%s]", record[t.getIndexInCsv("Buff1Param")], err)
							}
						}
						cfgoElemOfBuffOfRecordCfg.Param = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForBuffOfRecordCfg {
					recordCfg.Buff = append(recordCfg.Buff, cfgoElemOfBuffOfRecordCfg)
				}
			}
			// element 1 of Buff
			if !cfgoMeetNilForBuffOfRecordCfg {
				cfgoMeetNilForBuffOfRecordCfg = true
				var cfgoElemOfBuffOfRecordCfg *IDPStr = NewIDPStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Buff2Image")]) != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoElemOfBuffOfRecordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Buff2Image")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Buff2Desc")]) != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoElemOfBuffOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Buff2Desc")])
					}
				}
				{
					if record[t.getIndexInCsv("Buff2Param")] != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("Buff2Param")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBuffOfRecordCfg.Param, value=%s, err:[%s]\n", record[t.getIndexInCsv("Buff2Param")], err)
							} else {
								return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBuffOfRecordCfg.Param, value=%s, err:[%s]", record[t.getIndexInCsv("Buff2Param")], err)
							}
						}
						cfgoElemOfBuffOfRecordCfg.Param = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForBuffOfRecordCfg {
					recordCfg.Buff = append(recordCfg.Buff, cfgoElemOfBuffOfRecordCfg)
				}
			}
			// element 2 of Buff
			if !cfgoMeetNilForBuffOfRecordCfg {
				cfgoMeetNilForBuffOfRecordCfg = true
				var cfgoElemOfBuffOfRecordCfg *IDPStr = NewIDPStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Buff3Image")]) != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoElemOfBuffOfRecordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Buff3Image")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Buff3Desc")]) != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoElemOfBuffOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Buff3Desc")])
					}
				}
				{
					if record[t.getIndexInCsv("Buff3Param")] != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("Buff3Param")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBuffOfRecordCfg.Param, value=%s, err:[%s]\n", record[t.getIndexInCsv("Buff3Param")], err)
							} else {
								return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBuffOfRecordCfg.Param, value=%s, err:[%s]", record[t.getIndexInCsv("Buff3Param")], err)
							}
						}
						cfgoElemOfBuffOfRecordCfg.Param = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForBuffOfRecordCfg {
					recordCfg.Buff = append(recordCfg.Buff, cfgoElemOfBuffOfRecordCfg)
				}
			}

		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [DungeonLordEquipLevelTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *DungeonLordEquipLevelTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "DungeonLordEquipLevelTable.csv") && (!strings.HasPrefix(fileName, "DungeonLordEquipLevelTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for DungeonLordEquipLevelTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[DungeonLordEquipLevelTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[DungeonLordEquipLevelTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[DungeonLordEquipLevelTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[DungeonLordEquipLevelTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[DungeonLordEquipLevelTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[DungeonLordEquipLevelTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[DungeonLordEquipLevelTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[DungeonLordEquipLevelTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [DungeonLordEquipLevelTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *DungeonLordEquipLevelTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[DungeonLordEquipLevelTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [DungeonLordEquipLevelTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *DungeonLordEquipLevelTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[DungeonLordEquipLevelTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *DungeonLordEquipLevelTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[DungeonLordEquipLevelTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[DungeonLordEquipLevelTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
