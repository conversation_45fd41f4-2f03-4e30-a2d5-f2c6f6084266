// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type DailyTasksTableCfg struct {
	Id              int32           `json:"Id"`              // Id
	TaskType        TaskType        `json:"TaskType"`        // 任务类型
	TaskCounterType TaskCounterType `json:"TaskCounterType"` // 任务计数类型
	Formula         string          `json:"Formula"`         // 达成条件
	Value           int32           `json:"Value"`           // 值
	RewardType      []int32         `json:"RewardType"`      // 奖励类型
	RewardTypeRef   []*ItemTableCfg `json:"-"`               // 奖励类型
	RewardValue     []int32         `json:"RewardValue"`     // 奖励数量
	Score           int32           `json:"Score"`           // 积分数量
}

func NewDailyTasksTableCfg() *DailyTasksTableCfg {
	return &DailyTasksTableCfg{
		Id:              0,
		TaskType:        TaskType(enumDefaultValue),
		TaskCounterType: TaskCounterType(enumDefaultValue),
		Formula:         "",
		Value:           0,
		RewardType:      []int32{},
		RewardTypeRef:   []*ItemTableCfg{},
		RewardValue:     []int32{},
		Score:           0,
	}
}

type DailyTasksTable struct {
	records  map[int32]*DailyTasksTableCfg
	localIds map[int32]struct{}
}

func NewDailyTasksTable() *DailyTasksTable {
	return &DailyTasksTable{
		records:  map[int32]*DailyTasksTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *DailyTasksTable) Get(key int32) *DailyTasksTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *DailyTasksTable) GetAll() map[int32]*DailyTasksTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *DailyTasksTable) put(key int32, value *DailyTasksTableCfg, local bool) *DailyTasksTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *DailyTasksTable) Range(f func(v *DailyTasksTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *DailyTasksTable) Filter(filterFuncs ...func(v *DailyTasksTableCfg) bool) map[int32]*DailyTasksTableCfg {
	filtered := map[int32]*DailyTasksTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *DailyTasksTable) FilterSlice(filterFuncs ...func(v *DailyTasksTableCfg) bool) []*DailyTasksTableCfg {
	filtered := []*DailyTasksTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *DailyTasksTable) FilterKeys(filterFuncs ...func(v *DailyTasksTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *DailyTasksTable) satisfied(v *DailyTasksTableCfg, filterFuncs ...func(v *DailyTasksTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *DailyTasksTable) setupIndexes() error {
	return nil
}

func (t *DailyTasksTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *DailyTasksTableCfg) bindRefs(c *Configs) {
	for _, e := range r.RewardType {
		cfgoRefRecord := c.ItemTable.Get(e)
		r.RewardTypeRef = append(r.RewardTypeRef, cfgoRefRecord)
	}
}
