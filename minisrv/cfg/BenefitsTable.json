{"1": {"Id": 1, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "2": {"Id": 2, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "3": {"Id": 3, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "4": {"Id": 4, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "5": {"Id": 5, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "6": {"Id": 6, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "7": {"Id": 7, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "8": {"Id": 8, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "9": {"Id": 9, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "10": {"Id": 10, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "11": {"Id": 11, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "12": {"Id": 12, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "13": {"Id": 13, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "14": {"Id": 14, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "15": {"Id": 15, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "16": {"Id": 16, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "17": {"Id": 17, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "18": {"Id": 18, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "19": {"Id": 19, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "20": {"Id": 20, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "21": {"Id": 21, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "22": {"Id": 22, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "23": {"Id": 23, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "24": {"Id": 24, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "25": {"Id": 25, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "26": {"Id": 26, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "27": {"Id": 27, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "28": {"Id": 28, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "29": {"Id": 29, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "30": {"Id": 30, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "31": {"Id": 31, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "32": {"Id": 32, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "33": {"Id": 33, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "34": {"Id": 34, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "35": {"Id": 35, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "36": {"Id": 36, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "37": {"Id": 37, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "38": {"Id": 38, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "39": {"Id": 39, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "40": {"Id": 40, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "41": {"Id": 41, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "42": {"Id": 42, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "43": {"Id": 43, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "44": {"Id": 44, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "45": {"Id": 45, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "46": {"Id": 46, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "47": {"Id": 47, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "48": {"Id": 48, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "49": {"Id": 49, "Type": "integer", "Category": "battle", "HeroCareer": 0, "Negative": false}, "50": {"Id": 50, "Type": "integer", "Category": "battle", "HeroCareer": 0, "Negative": false}, "51": {"Id": 51, "Type": "integer", "Category": "battle", "HeroCareer": 0, "Negative": false}, "52": {"Id": 52, "Type": "integer", "Category": "battle", "HeroCareer": 0, "Negative": true}, "53": {"Id": 53, "Type": "integer", "Category": "battle", "HeroCareer": 0, "Negative": true}, "54": {"Id": 54, "Type": "integer", "Category": "battle", "HeroCareer": 0, "Negative": true}, "55": {"Id": 55, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "56": {"Id": 56, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "57": {"Id": 57, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "58": {"Id": 58, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "59": {"Id": 59, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "60": {"Id": 60, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "61": {"Id": 61, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "62": {"Id": 62, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "63": {"Id": 63, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "64": {"Id": 64, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "65": {"Id": 65, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "66": {"Id": 66, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "67": {"Id": 67, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "68": {"Id": 68, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": true}, "69": {"Id": 69, "Type": "boolean", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "70": {"Id": 70, "Type": "boolean", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "71": {"Id": 71, "Type": "boolean", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "72": {"Id": 72, "Type": "integer", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "73": {"Id": 73, "Type": "percent", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "74": {"Id": 74, "Type": "boolean", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "75": {"Id": 75, "Type": "percent", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "76": {"Id": 76, "Type": "integer", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "77": {"Id": 77, "Type": "integer", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "78": {"Id": 78, "Type": "integer", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "79": {"Id": 79, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "80": {"Id": 80, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "81": {"Id": 81, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "82": {"Id": 82, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "83": {"Id": 83, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "84": {"Id": 84, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "85": {"Id": 85, "Type": "boolean", "Category": "battle", "HeroCareer": 0, "Negative": false}, "86": {"Id": 86, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "87": {"Id": 87, "Type": "percent", "Category": "battle", "HeroCareer": 1, "Negative": false}, "88": {"Id": 88, "Type": "percent", "Category": "battle", "HeroCareer": 2, "Negative": false}, "89": {"Id": 89, "Type": "percent", "Category": "battle", "HeroCareer": 3, "Negative": false}, "90": {"Id": 90, "Type": "percent", "Category": "battle", "HeroCareer": 1, "Negative": false}, "91": {"Id": 91, "Type": "percent", "Category": "battle", "HeroCareer": 2, "Negative": false}, "92": {"Id": 92, "Type": "percent", "Category": "battle", "HeroCareer": 3, "Negative": false}, "93": {"Id": 93, "Type": "percent", "Category": "battle", "HeroCareer": 1, "Negative": false}, "94": {"Id": 94, "Type": "percent", "Category": "battle", "HeroCareer": 2, "Negative": false}, "95": {"Id": 95, "Type": "percent", "Category": "battle", "HeroCareer": 3, "Negative": false}, "96": {"Id": 96, "Type": "integer", "Category": "battle", "HeroCareer": 1, "Negative": false}, "97": {"Id": 97, "Type": "integer", "Category": "battle", "HeroCareer": 2, "Negative": false}, "98": {"Id": 98, "Type": "integer", "Category": "battle", "HeroCareer": 3, "Negative": false}, "99": {"Id": 99, "Type": "integer", "Category": "battle", "HeroCareer": 1, "Negative": false}, "100": {"Id": 100, "Type": "integer", "Category": "battle", "HeroCareer": 2, "Negative": false}, "101": {"Id": 101, "Type": "integer", "Category": "battle", "HeroCareer": 3, "Negative": false}, "102": {"Id": 102, "Type": "integer", "Category": "battle", "HeroCareer": 1, "Negative": false}, "103": {"Id": 103, "Type": "integer", "Category": "battle", "HeroCareer": 2, "Negative": false}, "104": {"Id": 104, "Type": "integer", "Category": "battle", "HeroCareer": 3, "Negative": false}, "105": {"Id": 105, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "106": {"Id": 106, "Type": "boolean", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "107": {"Id": 107, "Type": "boolean", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "108": {"Id": 108, "Type": "integer", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}, "109": {"Id": 109, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "110": {"Id": 110, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "111": {"Id": 111, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "112": {"Id": 112, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "113": {"Id": 113, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "114": {"Id": 114, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "115": {"Id": 115, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "116": {"Id": 116, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "117": {"Id": 117, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "118": {"Id": 118, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "119": {"Id": 119, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "120": {"Id": 120, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "121": {"Id": 121, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "122": {"Id": 122, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "123": {"Id": 123, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "124": {"Id": 124, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "125": {"Id": 125, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "126": {"Id": 126, "Type": "percent", "Category": "battle", "HeroCareer": 0, "Negative": false}, "127": {"Id": 127, "Type": "integer", "Category": "nonbattle", "HeroCareer": 0, "Negative": false}}