package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
)

func init() {
	http_post.AddPostHandler("clearRank", clearRank)
}
func clearRank(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		values := r.URL.Query()
		//通过uid删除
		with := values.Get("with") // 获取查询参数q的值
		if with == "db" {
			globalModel := model.GetGlobalStageLevelModel(ctx, 1)
			globalModel.Delete(ctx)
		}
		rdb := rank.GetRankClient()
		rdb.Del(rank.RANK_STAGE_LEVEL)
		fmt.Fprintf(w, "success")
	})

}
