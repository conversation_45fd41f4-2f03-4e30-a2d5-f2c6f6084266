// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type EliteDungeonRewardTableCfg struct {
	Id       int32  `json:"Id"`       // Id
	StringId string `json:"StringId"` // StringId
	Star     int32  `json:"Star"`     // 总星数
}

func NewEliteDungeonRewardTableCfg() *EliteDungeonRewardTableCfg {
	return &EliteDungeonRewardTableCfg{
		Id:       0,
		StringId: "",
		Star:     0,
	}
}

type EliteDungeonRewardTable struct {
	records  map[int32]*EliteDungeonRewardTableCfg
	localIds map[int32]struct{}
}

func NewEliteDungeonRewardTable() *EliteDungeonRewardTable {
	return &EliteDungeonRewardTable{
		records:  map[int32]*EliteDungeonRewardTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *EliteDungeonRewardTable) Get(key int32) *EliteDungeonRewardTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *EliteDungeonRewardTable) GetAll() map[int32]*EliteDungeonRewardTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *EliteDungeonRewardTable) put(key int32, value *EliteDungeonRewardTableCfg, local bool) *EliteDungeonRewardTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *EliteDungeonRewardTable) Range(f func(v *EliteDungeonRewardTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *EliteDungeonRewardTable) Filter(filterFuncs ...func(v *EliteDungeonRewardTableCfg) bool) map[int32]*EliteDungeonRewardTableCfg {
	filtered := map[int32]*EliteDungeonRewardTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *EliteDungeonRewardTable) FilterSlice(filterFuncs ...func(v *EliteDungeonRewardTableCfg) bool) []*EliteDungeonRewardTableCfg {
	filtered := []*EliteDungeonRewardTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *EliteDungeonRewardTable) FilterKeys(filterFuncs ...func(v *EliteDungeonRewardTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *EliteDungeonRewardTable) satisfied(v *EliteDungeonRewardTableCfg, filterFuncs ...func(v *EliteDungeonRewardTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *EliteDungeonRewardTable) setupIndexes() error {
	return nil
}

func (t *EliteDungeonRewardTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *EliteDungeonRewardTableCfg) bindRefs(c *Configs) {
}
