// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type VipFreeExpTableCfg struct {
	Id       int32  `json:"Id"`       // Id
	StringId string `json:"StringId"` // StringId
	Day      int32  `json:"Day"`      // 连续登录天数
	Exp      int32  `json:"Exp"`      // 每日免费经验
	IsMax    bool   `json:"IsMax"`    // 是否最大
}

func NewVipFreeExpTableCfg() *VipFreeExpTableCfg {
	return &VipFreeExpTableCfg{
		Id:       0,
		StringId: "",
		Day:      0,
		Exp:      0,
		IsMax:    false,
	}
}

type VipFreeExpTable struct {
	records  map[int32]*VipFreeExpTableCfg
	localIds map[int32]struct{}
}

func NewVipFreeExpTable() *VipFreeExpTable {
	return &VipFreeExpTable{
		records:  map[int32]*VipFreeExpTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *VipFreeExpTable) Get(key int32) *VipFreeExpTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *VipFreeExpTable) GetAll() map[int32]*VipFreeExpTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *VipFreeExpTable) put(key int32, value *VipFreeExpTableCfg, local bool) *VipFreeExpTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *VipFreeExpTable) Range(f func(v *VipFreeExpTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *VipFreeExpTable) Filter(filterFuncs ...func(v *VipFreeExpTableCfg) bool) map[int32]*VipFreeExpTableCfg {
	filtered := map[int32]*VipFreeExpTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *VipFreeExpTable) FilterSlice(filterFuncs ...func(v *VipFreeExpTableCfg) bool) []*VipFreeExpTableCfg {
	filtered := []*VipFreeExpTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *VipFreeExpTable) FilterKeys(filterFuncs ...func(v *VipFreeExpTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *VipFreeExpTable) satisfied(v *VipFreeExpTableCfg, filterFuncs ...func(v *VipFreeExpTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *VipFreeExpTable) setupIndexes() error {
	return nil
}

func (t *VipFreeExpTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *VipFreeExpTableCfg) bindRefs(c *Configs) {
}
