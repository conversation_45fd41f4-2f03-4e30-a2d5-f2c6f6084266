// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"text/template"
	"unsafe"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (c *Configs) SaveJson(dir string) error {
	if err := c.AchievementTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ActivityTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ArenaBotTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ArenaChallengeRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ArenaDailyRankRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ArenaExtraChallengeCntTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ArenaMatchTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ArenaRefreshTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ArenaScoreTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ArenaShopTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ArenaWeeklyRankRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.AttributeHierarchyTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.AvatarFrameTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.AvatarTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.BattleAttributeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.BattleModelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.BenefitsCalcJustShowTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.BenefitsCalcTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.BenefitsTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.BlackShopTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.BossDungeonRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.BossDungeonTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ChapterLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ChapterTaskMainTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ChapterTaskTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.DailyTasksScoreTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.DailyTasksTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.DaveLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.DropGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.DropMainTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.DungeonChapterLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.DungeonCoinLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.DungeonGeneLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.DungeonLordEquipLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.DungeonRefreshTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.DungeonSunshineLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.DungeonTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.EliteDungeonRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.EliteDungeonTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.FunctionPreviewTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.FunctionTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.GameConfigs.saveJson(dir); err != nil {
		return err
	}
	if err := c.GemAffixQualityTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.GemQualityTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.GoToTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.GuildFlagTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.GuildHaggleTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.GuildLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.GuildPermissionTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.GuildRankTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.GuildShopTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.GuildTaskTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.GuildTasksScoreTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HelpInfoContentTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HelpInfoMainTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroBondsTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroCareerTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroConfigTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroElementTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroFragmentTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroGeneFragmentTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroGeneTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroLotteryGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroLotteryMustTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroLotteryRandomGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroLotteryRandomTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroQualityTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroRestrainTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroSkillAttrTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroSkillAwakeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroSkillBuffTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroSkillBuffTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroSkillEffectTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroSkillGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroSkillTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroStarTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.HeroTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.Iap1stTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.Iap2XTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapAdFreeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapBPTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapBpRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapDailySaleFreeRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapDailySaleRewardGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapDailySaleRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapDailySaleTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapDealTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapDungeonFundRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapDungeonFundTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapLevelFundRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapLevelFundTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapLifeCardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapMonthCardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapOrder.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapPackageDiamondShopTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapPackageRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapPackageTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapPriceTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapRegularPackGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapRegularPackTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapShopMallTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapSignRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapSignTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapTriggerPackGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapTriggerPackTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IapTurnPackTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IdleMonsterTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IdleRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.IdleRewardTime.saveJson(dir); err != nil {
		return err
	}
	if err := c.ItemQualityTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ItemSourceTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ItemTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LTCRechargeScoreTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LTCRechargeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LanguageCnTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LevelShopTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LoginOpenTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordEquipGradeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordEquipGradeTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordEquipSlotsTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordEquipTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordEquipTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordGemCraftTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordGemDropCntTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordGemDropQualityTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordGemRandomGroupChanceTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordGemRandomGroupMustTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordGemRandomGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordGemRandomRewardGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordGemReforgeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.LordGemTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MailTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MainChapterLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MainChapterTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MainLevelPassRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MainLevelRangeDmgTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MainLevelRewardRatioTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MainLevelRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MainLevelRogueRewardWeightTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MainLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MainLineTasksTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MapEventBuffTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MapEventMonsterGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MapEventMonsterTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MapEventObstacleTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MapEventPropTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MapEventRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MapEventSkillTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MapEventTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MapRefreshMonsterEventTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ModifierTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MonsterCareerTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MonsterGradeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MonsterPosTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MonsterPreviewSchemeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MonsterSkillTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MonsterTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.MonsterTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.NewbieTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.NpcDialogueTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.PhotovoltaicTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.PresetsTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.RankMainTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.RankRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.RougeNameCn.saveJson(dir); err != nil {
		return err
	}
	if err := c.RougeRefreshTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.RougeTabEffectTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.RougeTabGroupRandomTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.RougeTabGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.RougeTabNewbieTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.RougeTabTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.RougeWeightCoef.saveJson(dir); err != nil {
		return err
	}
	if err := c.SelectChestGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.SevenDayTasksScoreTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.SevenDayTasksTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.ShopTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.Sign7Table.saveJson(dir); err != nil {
		return err
	}
	if err := c.SkillDmgTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.TowerAILevelTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.TowerAITable.saveJson(dir); err != nil {
		return err
	}
	if err := c.TowerTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.TurnRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.TurnScoreRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.TurnTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.VehicleTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.VipFreeExpTable.saveJson(dir); err != nil {
		return err
	}
	if err := c.VipTable.saveJson(dir); err != nil {
		return err
	}
	return nil
}

func (c *Configs) SaveMeta(dir string) error {
	fbPlus := false
	/*
		if err := createDirIfNotExist(dir); err != nil {
			return fmt.Errorf("[client]create dir failed, path=%s, err=[%w]", dir, err)
		}
		// 删除client目录中所有.fbs后缀文件
		if err := removeDirFiles(dir, ".fbs"); err != nil {
			return err
		}
		// 删除client目录中所有.lua后缀文件
		if err := removeDirFiles(dir, ".lua"); err != nil {
			return err
		}
	*/

	fbd := &FlatBufferData{}
	if err := json.Unmarshal(string2Bytes(jsonContent), fbd); err != nil {
		return fmt.Errorf("[client]unmarshal json failed, err=[%w]", err)
	}
	{
		path := filepath.Join(dir, fbd.EnumsData.FileName)
		f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
		if err != nil {
			return fmt.Errorf("[client]generate enums schema failed, path: %s, err:[%w]", path, err)
		}
		defer f.Close()

		tpl, err := template.New("enums_schema").Parse(FlatBufferEnumsTpl)
		if err != nil {
			return fmt.Errorf("[client]generate enums schema failed, path: %s, err:[%w]", path, err)
		}

		if err = tpl.Execute(f, fbd.EnumsData); err != nil {
			return fmt.Errorf("[client]generate enums schema failed, path: %s, err:[%w]", path, err)
		}
	}
	{
		path := filepath.Join(dir, fbd.StructsData.FileName)
		f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
		if err != nil {
			return fmt.Errorf("[client]generate structs schema failed, path: %s, err:[%w]", path, err)
		}
		defer f.Close()

		tpl, err := template.New("structs_schema").Parse(FlatBufferStructsTpl)
		if err != nil {
			return fmt.Errorf("[client]generate structs schema failed, path: %s, err:[%w]", path, err)
		}

		if err = tpl.Execute(f, fbd.StructsData); err != nil {
			return fmt.Errorf("[client]generate structs schema failed, path: %s, err:[%w]", path, err)
		}
	}
	/*
		for _, td := range fbd.TableDataList {
			path := filepath.Join(dir, td.FileName)
			f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
			if err != nil {
				return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
			}
			defer f.Close()

			t := FlatBufferTableTpl
			if fbPlus {
				t = FlatBufferPlusTableTpl
			}
			tpl, err := template.New("table_schema").Parse(t)
			if err != nil {
				return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
			}

			if err = tpl.Execute(f, td); err != nil {
				return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
			}
		}
	*/
	for _, ctd := range fbd.ConstTableDataList {
		path := filepath.Join(dir, ctd.FileName)
		f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
		if err != nil {
			return fmt.Errorf("[client]generate const table schema failed, path: %s, table_name: %s, err:[%w]", path, ctd.TableName, err)
		}
		defer f.Close()

		t := FlatBufferConstTableTpl
		if fbPlus {
			t = FlatBufferPlusConstTableTpl
		}
		tpl, err := template.New("const_table_schema").Parse(t)
		if err != nil {
			return fmt.Errorf("[client]generate const table schema failed, path: %s, table_name: %s, err:[%w]", path, ctd.TableName, err)
		}

		if err = tpl.Execute(f, ctd); err != nil {
			return fmt.Errorf("[client]generate const table schema failed, path: %s, table_name: %s, err:[%w]", path, ctd.TableName, err)
		}
	}
	if !fbPlus {
		path := filepath.Join(dir, fbd.ConstTableList.FileName)
		f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
		if err != nil {
			return fmt.Errorf("[client]generate const table list lua failed, path: %s, err:[%w]", path, err)
		}
		defer f.Close()

		tpl, err := template.New("const_table_list_lua").Parse(LuaConstTableListTpl)
		if err != nil {
			return fmt.Errorf("[client]generate const table list lua failed, path: %s, err:[%w]", path, err)
		}

		if err = tpl.Execute(f, fbd.ConstTableList); err != nil {
			return fmt.Errorf("[client]generate const table list lua failed, path: %s, err:[%w]", path, err)
		}
	}
	if !fbPlus {
		path := filepath.Join(dir, "enums.txt")
		f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
		if err != nil {
			return fmt.Errorf("[client]generate enums txt failed, path: %s, err:[%w]", path, err)
		}
		defer f.Close()

		tpl, err := template.New("enums_txt").Parse(TxtEnumListTpl)
		if err != nil {
			return fmt.Errorf("[client]generate enums txt failed, path: %s, err:[%w]", path, err)
		}

		if err = tpl.Execute(f, fbd.EnumsData); err != nil {
			return fmt.Errorf("[client]generate enums txt failed, path: %s, err:[%w]", path, err)
		}
	}
	if !fbPlus {
		path := filepath.Join(dir, "structs.txt")
		f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
		if err != nil {
			return fmt.Errorf("[client]generate structs txt failed, path: %s, err:[%w]", path, err)
		}
		defer f.Close()

		tpl, err := template.New("structs.txt").Parse(TxtStructListTpl)
		if err != nil {
			return fmt.Errorf("[client]generate structs txt failed, path: %s, err:[%w]", path, err)
		}

		if err = tpl.Execute(f, fbd.StructsData); err != nil {
			return fmt.Errorf("[client]generate structs txt failed, path: %s, err:[%w]", path, err)
		}
	}
	if err := c.AchievementTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ActivityTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ArenaBotTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ArenaChallengeRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ArenaDailyRankRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ArenaExtraChallengeCntTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ArenaMatchTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ArenaRefreshTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ArenaScoreTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ArenaShopTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ArenaWeeklyRankRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.AttributeHierarchyTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.AvatarFrameTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.AvatarTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.BattleAttributeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.BattleModelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.BenefitsCalcJustShowTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.BenefitsCalcTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.BenefitsTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.BlackShopTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.BossDungeonRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.BossDungeonTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ChapterLevelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ChapterTaskMainTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ChapterTaskTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.DailyTasksScoreTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.DailyTasksTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.DaveLevelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.DropGroupTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.DropMainTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.DungeonChapterLevelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.DungeonCoinLevelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.DungeonGeneLevelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.DungeonLordEquipLevelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.DungeonRefreshTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.DungeonSunshineLevelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.DungeonTypeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.EliteDungeonRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.EliteDungeonTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.FunctionPreviewTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.FunctionTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.GameConfigs.saveMeta(dir); err != nil {
		return err
	}
	if err := c.GemAffixQualityTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.GemQualityTypeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.GoToTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.GuildFlagTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.GuildHaggleTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.GuildLevelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.GuildPermissionTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.GuildRankTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.GuildShopTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.GuildTaskTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.GuildTasksScoreTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HelpInfoContentTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HelpInfoMainTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroBondsTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroCareerTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroConfigTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroElementTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroFragmentTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroGeneFragmentTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroGeneTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroLevelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroLotteryGroupTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroLotteryMustTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroLotteryRandomGroupTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroLotteryRandomTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroQualityTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroRestrainTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroSkillAttrTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroSkillAwakeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroSkillBuffTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroSkillBuffTypeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroSkillEffectTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroSkillGroupTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroSkillTypeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroStarTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.HeroTypeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.Iap1stTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.Iap2XTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapAdFreeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapBPTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapBpRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapDailySaleFreeRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapDailySaleRewardGroupTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapDailySaleRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapDailySaleTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapDealTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapDungeonFundRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapDungeonFundTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapLevelFundRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapLevelFundTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapLifeCardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapMonthCardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapOrder.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapPackageDiamondShopTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapPackageRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapPackageTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapPriceTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapRegularPackGroupTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapRegularPackTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapShopMallTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapSignRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapSignTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapTriggerPackGroupTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapTriggerPackTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IapTurnPackTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IdleMonsterTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IdleRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.IdleRewardTime.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ItemQualityTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ItemSourceTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ItemTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LTCRechargeScoreTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LTCRechargeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LanguageCnTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LevelShopTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LoginOpenTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordEquipGradeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordEquipGradeTypeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordEquipSlotsTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordEquipTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordEquipTypeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordGemCraftTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordGemDropCntTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordGemDropQualityTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordGemRandomGroupChanceTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordGemRandomGroupMustTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordGemRandomGroupTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordGemRandomRewardGroupTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordGemReforgeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.LordGemTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MailTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MainChapterLevelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MainChapterTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MainLevelPassRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MainLevelRangeDmgTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MainLevelRewardRatioTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MainLevelRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MainLevelRogueRewardWeightTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MainLevelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MainLineTasksTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MapEventBuffTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MapEventMonsterGroupTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MapEventMonsterTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MapEventObstacleTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MapEventPropTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MapEventRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MapEventSkillTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MapEventTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MapRefreshMonsterEventTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ModifierTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MonsterCareerTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MonsterGradeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MonsterPosTypeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MonsterPreviewSchemeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MonsterSkillTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MonsterTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.MonsterTypeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.NewbieTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.NpcDialogueTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.PhotovoltaicTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.PresetsTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.RankMainTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.RankRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.RougeNameCn.saveMeta(dir); err != nil {
		return err
	}
	if err := c.RougeRefreshTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.RougeTabEffectTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.RougeTabGroupRandomTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.RougeTabGroupTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.RougeTabNewbieTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.RougeTabTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.RougeWeightCoef.saveMeta(dir); err != nil {
		return err
	}
	if err := c.SelectChestGroupTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.SevenDayTasksScoreTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.SevenDayTasksTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.ShopTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.Sign7Table.saveMeta(dir); err != nil {
		return err
	}
	if err := c.SkillDmgTypeTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.TowerAILevelTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.TowerAITable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.TowerTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.TurnRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.TurnScoreRewardTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.TurnTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.VehicleTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.VipFreeExpTable.saveMeta(dir); err != nil {
		return err
	}
	if err := c.VipTable.saveMeta(dir); err != nil {
		return err
	}
	return nil
}

func string2Bytes(s string) []byte {
	sh := (*reflect.StringHeader)(unsafe.Pointer(&s))
	bh := reflect.SliceHeader{
		Data: sh.Data,
		Len:  sh.Len,
		Cap:  sh.Len,
	}
	return *(*[]byte)(unsafe.Pointer(&bh))
}

type (
	EnumCaseData struct {
		CaseName  string
		CaseValue int32
	}

	EnumData struct {
		EnumName string
		Cases    []*EnumCaseData
	}

	EnumsData struct {
		FileName        string
		TitleForCodeGen string
		Enums           []*EnumData
	}

	StructFieldData struct {
		FieldName string
		FieldType string
	}

	StructData struct {
		StructName string
		Fields     []*StructFieldData
	}

	StructsData struct {
		FileName        string
		TitleForCodeGen string
		Structs         []*StructData
	}

	TableFieldData struct {
		FieldName string
		FieldType string
	}

	TableData struct {
		FileName        string
		ConstTable      bool
		TitleForCodeGen string
		TableName       string
		Key             *TableFieldData
		Fields          []*TableFieldData
	}

	ConstTableList struct {
		FileName    string
		ConstTables []string
	}

	FlatBufferData struct {
		EnumsData          *EnumsData
		StructsData        *StructsData
		TableDataList      []*TableData
		ConstTableDataList []*TableData
		ConstTableList     *ConstTableList
	}
)

var jsonContent string = `{
		"EnumsData": {
			"FileName": "enums.fbs",
			"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
			"Enums": [
				{
					"EnumName": "AttrDefaultType",
					"Cases": [
						{
							"CaseName": "Number",
							"CaseValue": 1
						},
						{
							"CaseName": "Model",
							"CaseValue": 2
						},
						{
							"CaseName": "Buff",
							"CaseValue": 3
						},
						{
							"CaseName": "Effect",
							"CaseValue": 4
						}
					]
				},
				{
					"EnumName": "Attribute",
					"Cases": [
						{
							"CaseName": "Hp",
							"CaseValue": 1
						},
						{
							"CaseName": "Atk",
							"CaseValue": 2
						},
						{
							"CaseName": "Def",
							"CaseValue": 3
						},
						{
							"CaseName": "CritChance",
							"CaseValue": 4
						},
						{
							"CaseName": "CritDmgUpPer",
							"CaseValue": 5
						},
						{
							"CaseName": "CdRate",
							"CaseValue": 6
						},
						{
							"CaseName": "AtkUpPer",
							"CaseValue": 7
						},
						{
							"CaseName": "DmgUpPer",
							"CaseValue": 8
						},
						{
							"CaseName": "CritResistChance",
							"CaseValue": 9
						},
						{
							"CaseName": "BeCritDmgDownPer",
							"CaseValue": 10
						},
						{
							"CaseName": "BeDmgDownPer",
							"CaseValue": 11
						},
						{
							"CaseName": "DefUpPer",
							"CaseValue": 12
						},
						{
							"CaseName": "HpUpPer",
							"CaseValue": 13
						},
						{
							"CaseName": "HpRecovery",
							"CaseValue": 14
						},
						{
							"CaseName": "CDTime",
							"CaseValue": 15
						},
						{
							"CaseName": "FireDmgUpPer",
							"CaseValue": 16
						},
						{
							"CaseName": "ElectricDmgUpPer",
							"CaseValue": 17
						},
						{
							"CaseName": "WindDmgUpPer",
							"CaseValue": 18
						},
						{
							"CaseName": "LightDmgUpPer",
							"CaseValue": 19
						},
						{
							"CaseName": "IceDmgUpPer",
							"CaseValue": 20
						},
						{
							"CaseName": "PhysicalDmgUpPer",
							"CaseValue": 21
						},
						{
							"CaseName": "BallisticDmgUpPer",
							"CaseValue": 22
						},
						{
							"CaseName": "MonsterDmgUpPer",
							"CaseValue": 23
						},
						{
							"CaseName": "FireDmgResistUpPer",
							"CaseValue": 24
						},
						{
							"CaseName": "ElectricDmgResistUpPer",
							"CaseValue": 25
						},
						{
							"CaseName": "WindDmgResistUpPer",
							"CaseValue": 26
						},
						{
							"CaseName": "LightDmgResistUpPer",
							"CaseValue": 27
						},
						{
							"CaseName": "IceDmgResistUpPer",
							"CaseValue": 28
						},
						{
							"CaseName": "PhysicalDmgResistUpPer",
							"CaseValue": 29
						},
						{
							"CaseName": "BallisticDmgResistUpPer",
							"CaseValue": 30
						},
						{
							"CaseName": "MonsterDmgResistUpPer",
							"CaseValue": 31
						}
					]
				},
				{
					"EnumName": "AttributeHierarchy",
					"Cases": [
						{
							"CaseName": "BaseAttribute",
							"CaseValue": 1
						},
						{
							"CaseName": "AtkAttribute",
							"CaseValue": 2
						},
						{
							"CaseName": "DefAttribute",
							"CaseValue": 3
						},
						{
							"CaseName": "AdvanceAttribute",
							"CaseValue": 4
						}
					]
				},
				{
					"EnumName": "BagType",
					"Cases": [
						{
							"CaseName": "Plants",
							"CaseValue": 1
						},
						{
							"CaseName": "Consumption",
							"CaseValue": 2
						},
						{
							"CaseName": "Materials",
							"CaseValue": 3
						}
					]
				},
				{
					"EnumName": "BenefitCalcFormula",
					"Cases": [
						{
							"CaseName": "Formula1",
							"CaseValue": 1
						},
						{
							"CaseName": "Formula2",
							"CaseValue": 2
						},
						{
							"CaseName": "Formula3",
							"CaseValue": 3
						},
						{
							"CaseName": "Formula4",
							"CaseValue": 4
						},
						{
							"CaseName": "Formula5",
							"CaseValue": 5
						},
						{
							"CaseName": "Formula6",
							"CaseValue": 6
						},
						{
							"CaseName": "Formula7",
							"CaseValue": 7
						},
						{
							"CaseName": "Formula8",
							"CaseValue": 8
						},
						{
							"CaseName": "Formula9",
							"CaseValue": 9
						}
					]
				},
				{
					"EnumName": "BuffOverlyingType",
					"Cases": [
						{
							"CaseName": "NoOverlying",
							"CaseValue": 1
						},
						{
							"CaseName": "TimeOverlying",
							"CaseValue": 2
						},
						{
							"CaseName": "EffectOverlying",
							"CaseValue": 3
						},
						{
							"CaseName": "EffectCover",
							"CaseValue": 4
						},
						{
							"CaseName": "EffectReplace",
							"CaseValue": 5
						},
						{
							"CaseName": "EffectMultiple",
							"CaseValue": 6
						}
					]
				},
				{
					"EnumName": "BuffTarget",
					"Cases": [
						{
							"CaseName": "Own",
							"CaseValue": 1
						},
						{
							"CaseName": "OwnSide",
							"CaseValue": 2
						},
						{
							"CaseName": "OwnSideHeroDefense",
							"CaseValue": 3
						},
						{
							"CaseName": "OwnSideHeroRanged",
							"CaseValue": 4
						},
						{
							"CaseName": "OwnSideHeroSupport",
							"CaseValue": 5
						},
						{
							"CaseName": "OwnSideHeroFront",
							"CaseValue": 6
						},
						{
							"CaseName": "OwnSideHeroBehind",
							"CaseValue": 7
						},
						{
							"CaseName": "OwnSideHeroHPLowest",
							"CaseValue": 8
						},
						{
							"CaseName": "Opposite",
							"CaseValue": 9
						},
						{
							"CaseName": "OppositeSide",
							"CaseValue": 10
						},
						{
							"CaseName": "OppositeSideHeroDefense",
							"CaseValue": 11
						},
						{
							"CaseName": "OppositeSideHeroRanged",
							"CaseValue": 12
						},
						{
							"CaseName": "OppositeSideHeroSupport",
							"CaseValue": 13
						},
						{
							"CaseName": "OppoSideHeroFront",
							"CaseValue": 14
						},
						{
							"CaseName": "OppoSideHeroBehind",
							"CaseValue": 15
						},
						{
							"CaseName": "OppositeSideHeroHpLowest",
							"CaseValue": 16
						},
						{
							"CaseName": "PreSkillEffectTarget",
							"CaseValue": 17
						},
						{
							"CaseName": "PreSkillEffectTargetElseBoss",
							"CaseValue": 18
						},
						{
							"CaseName": "Boss",
							"CaseValue": 19
						},
						{
							"CaseName": "Vehicle",
							"CaseValue": 20
						},
						{
							"CaseName": "OwnForward",
							"CaseValue": 21
						},
						{
							"CaseName": "OwnSideHeroMagic",
							"CaseValue": 22
						},
						{
							"CaseName": "OwnSideHeroSuperPowers",
							"CaseValue": 23
						},
						{
							"CaseName": "OwnSideHeroTech",
							"CaseValue": 24
						},
						{
							"CaseName": "OppositeSideHeroMagic",
							"CaseValue": 25
						},
						{
							"CaseName": "OppositeSideHeroSuperPowers",
							"CaseValue": 26
						},
						{
							"CaseName": "OppositeSideHeroTech",
							"CaseValue": 27
						},
						{
							"CaseName": "OwnSideHeroAtkHighest",
							"CaseValue": 28
						},
						{
							"CaseName": "OwnSideHeroBehindMagic",
							"CaseValue": 29
						},
						{
							"CaseName": "OwnSideHeroBehindTech",
							"CaseValue": 30
						},
						{
							"CaseName": "OwnSideHeroFrontDefenseRandom",
							"CaseValue": 31
						}
					]
				},
				{
					"EnumName": "BuffType",
					"Cases": [
						{
							"CaseName": "Buff",
							"CaseValue": 1
						},
						{
							"CaseName": "Debuff",
							"CaseValue": 2
						}
					]
				},
				{
					"EnumName": "CorrectType",
					"Cases": [
						{
							"CaseName": "Overlying",
							"CaseValue": 1
						},
						{
							"CaseName": "Cover",
							"CaseValue": 2
						}
					]
				},
				{
					"EnumName": "DailyOrWeekly",
					"Cases": [
						{
							"CaseName": "Daily",
							"CaseValue": 1
						},
						{
							"CaseName": "Weekly",
							"CaseValue": 2
						}
					]
				},
				{
					"EnumName": "DungeonType",
					"Cases": [
						{
							"CaseName": "CoinDungeon",
							"CaseValue": 1
						},
						{
							"CaseName": "GeneDungeon",
							"CaseValue": 2
						},
						{
							"CaseName": "LordEquipDungeon",
							"CaseValue": 3
						},
						{
							"CaseName": "SunshineDungeon",
							"CaseValue": 4
						}
					]
				},
				{
					"EnumName": "GemAffixQuality",
					"Cases": [
						{
							"CaseName": "GemAffixQuality1",
							"CaseValue": 1
						},
						{
							"CaseName": "GemAffixQuality2",
							"CaseValue": 2
						},
						{
							"CaseName": "GemAffixQuality3",
							"CaseValue": 3
						}
					]
				},
				{
					"EnumName": "GemQualityType",
					"Cases": [
						{
							"CaseName": "GemQualityType1",
							"CaseValue": 1
						},
						{
							"CaseName": "GemQualityType2",
							"CaseValue": 2
						},
						{
							"CaseName": "GemQualityType3",
							"CaseValue": 3
						},
						{
							"CaseName": "GemQualityType4",
							"CaseValue": 4
						},
						{
							"CaseName": "GemQualityType5",
							"CaseValue": 5
						},
						{
							"CaseName": "GemQualityType6",
							"CaseValue": 6
						},
						{
							"CaseName": "GemQualityType7",
							"CaseValue": 7
						}
					]
				},
				{
					"EnumName": "GoToType",
					"Cases": [
						{
							"CaseName": "Open",
							"CaseValue": 1
						},
						{
							"CaseName": "Guide",
							"CaseValue": 2
						},
						{
							"CaseName": "Main",
							"CaseValue": 3
						},
						{
							"CaseName": "Wait",
							"CaseValue": 4
						},
						{
							"CaseName": "Level",
							"CaseValue": 5
						},
						{
							"CaseName": "Daive",
							"CaseValue": 6
						},
						{
							"CaseName": "Force",
							"CaseValue": 7
						},
						{
							"CaseName": "WaitClose",
							"CaseValue": 8
						},
						{
							"CaseName": "BackHome",
							"CaseValue": 9
						},
						{
							"CaseName": "Tag",
							"CaseValue": 10
						}
					]
				},
				{
					"EnumName": "GuildFlagType",
					"Cases": [
						{
							"CaseName": "Base",
							"CaseValue": 1
						},
						{
							"CaseName": "Badge",
							"CaseValue": 2
						}
					]
				},
				{
					"EnumName": "GuildPermission",
					"Cases": [
						{
							"CaseName": "ChangeGuildFlag",
							"CaseValue": 1
						},
						{
							"CaseName": "ChangeGuildShortName",
							"CaseValue": 2
						},
						{
							"CaseName": "ChangeGuildName",
							"CaseValue": 3
						},
						{
							"CaseName": "EditNotice",
							"CaseValue": 4
						},
						{
							"CaseName": "ChangeRecruitSetting",
							"CaseValue": 5
						},
						{
							"CaseName": "ManageJoinApplication",
							"CaseValue": 6
						},
						{
							"CaseName": "DisbandGuild",
							"CaseValue": 7
						},
						{
							"CaseName": "TransferPresident",
							"CaseValue": 8
						},
						{
							"CaseName": "RemoveMember",
							"CaseValue": 9
						},
						{
							"CaseName": "ChangeMemberRank",
							"CaseValue": 10
						},
						{
							"CaseName": "ViewMemberInfo",
							"CaseValue": 11
						},
						{
							"CaseName": "ChangeRankTitle",
							"CaseValue": 12
						},
						{
							"CaseName": "ChangeLanguage",
							"CaseValue": 13
						},
						{
							"CaseName": "ExitGuild",
							"CaseValue": 14
						}
					]
				},
				{
					"EnumName": "GuildRank",
					"Cases": [
						{
							"CaseName": "Rank5",
							"CaseValue": 1
						},
						{
							"CaseName": "Rank4",
							"CaseValue": 2
						},
						{
							"CaseName": "Rank3",
							"CaseValue": 3
						},
						{
							"CaseName": "Rank2",
							"CaseValue": 4
						},
						{
							"CaseName": "Rank1",
							"CaseValue": 5
						}
					]
				},
				{
					"EnumName": "HeroCareer",
					"Cases": [
						{
							"CaseName": "HeroDefense",
							"CaseValue": 1
						},
						{
							"CaseName": "HeroRanged",
							"CaseValue": 2
						},
						{
							"CaseName": "HeroSupport",
							"CaseValue": 3
						}
					]
				},
				{
					"EnumName": "HeroConfig",
					"Cases": [
						{
							"CaseName": "HeroConfig1",
							"CaseValue": 1
						},
						{
							"CaseName": "HeroConfig2",
							"CaseValue": 2
						},
						{
							"CaseName": "HeroConfig3",
							"CaseValue": 3
						},
						{
							"CaseName": "HeroConfig4",
							"CaseValue": 4
						},
						{
							"CaseName": "HeroConfig5",
							"CaseValue": 5
						}
					]
				},
				{
					"EnumName": "HeroElement",
					"Cases": [
						{
							"CaseName": "ElementalFire",
							"CaseValue": 1
						},
						{
							"CaseName": "ElementalIce",
							"CaseValue": 2
						},
						{
							"CaseName": "ElementalElectricity",
							"CaseValue": 3
						},
						{
							"CaseName": "ElementalLight",
							"CaseValue": 4
						},
						{
							"CaseName": "ElementalPhysics",
							"CaseValue": 5
						},
						{
							"CaseName": "ElementalWind",
							"CaseValue": 6
						}
					]
				},
				{
					"EnumName": "HeroLevelUpPlan",
					"Cases": [
						{
							"CaseName": "HeroLevelPlanLegendaryDefense",
							"CaseValue": 1
						},
						{
							"CaseName": "HeroLevelPlanLegendaryRanged",
							"CaseValue": 2
						},
						{
							"CaseName": "HeroLevelPlanLegendarySupport",
							"CaseValue": 3
						},
						{
							"CaseName": "HeroLevelPlanEpicDefense",
							"CaseValue": 4
						},
						{
							"CaseName": "HeroLevelPlanEpicRanged",
							"CaseValue": 5
						},
						{
							"CaseName": "HeroLevelPlanEpicSupport",
							"CaseValue": 6
						},
						{
							"CaseName": "HeroLevelPlanRareDefense",
							"CaseValue": 7
						},
						{
							"CaseName": "HeroLevelPlanRareRanged",
							"CaseValue": 8
						},
						{
							"CaseName": "HeroLevelPlanRareSupport",
							"CaseValue": 9
						}
					]
				},
				{
					"EnumName": "HeroQuality",
					"Cases": [
						{
							"CaseName": "HeroLegendary",
							"CaseValue": 1
						},
						{
							"CaseName": "HeroEpic",
							"CaseValue": 2
						},
						{
							"CaseName": "HeroRare",
							"CaseValue": 3
						}
					]
				},
				{
					"EnumName": "HeroSkillBuffType",
					"Cases": [
						{
							"CaseName": "Benefit",
							"CaseValue": 1
						},
						{
							"CaseName": "Silent",
							"CaseValue": 2
						},
						{
							"CaseName": "Stun",
							"CaseValue": 3
						},
						{
							"CaseName": "Paralysis",
							"CaseValue": 4
						},
						{
							"CaseName": "Sleep",
							"CaseValue": 5
						},
						{
							"CaseName": "Bind",
							"CaseValue": 6
						},
						{
							"CaseName": "Immortal",
							"CaseValue": 7
						},
						{
							"CaseName": "Veil",
							"CaseValue": 8
						},
						{
							"CaseName": "Stealth",
							"CaseValue": 9
						},
						{
							"CaseName": "Curse",
							"CaseValue": 10
						},
						{
							"CaseName": "Dot_bleed",
							"CaseValue": 11
						},
						{
							"CaseName": "Dot_poison",
							"CaseValue": 12
						},
						{
							"CaseName": "Dot_frostbite",
							"CaseValue": 13
						},
						{
							"CaseName": "Dot_burn",
							"CaseValue": 14
						},
						{
							"CaseName": "Block",
							"CaseValue": 15
						},
						{
							"CaseName": "Unrevive",
							"CaseValue": 16
						},
						{
							"CaseName": "EternalSlumber",
							"CaseValue": 17
						},
						{
							"CaseName": "Tense",
							"CaseValue": 18
						},
						{
							"CaseName": "Immunity",
							"CaseValue": 19
						},
						{
							"CaseName": "Shield",
							"CaseValue": 20
						},
						{
							"CaseName": "HalfAsleep",
							"CaseValue": 21
						},
						{
							"CaseName": "Nightmare",
							"CaseValue": 22
						},
						{
							"CaseName": "LifeSteal",
							"CaseValue": 23
						},
						{
							"CaseName": "Revive",
							"CaseValue": 24
						},
						{
							"CaseName": "HpRecovery",
							"CaseValue": 25
						},
						{
							"CaseName": "SkillSwitch",
							"CaseValue": 26
						},
						{
							"CaseName": "Taunted",
							"CaseValue": 27
						},
						{
							"CaseName": "Dmg",
							"CaseValue": 28
						},
						{
							"CaseName": "RemoveBuff",
							"CaseValue": 29
						},
						{
							"CaseName": "OnSideExplosion",
							"CaseValue": 30
						},
						{
							"CaseName": "Frozen",
							"CaseValue": 31
						},
						{
							"CaseName": "Repel",
							"CaseValue": 32
						},
						{
							"CaseName": "Pull",
							"CaseValue": 33
						},
						{
							"CaseName": "LightingStruck",
							"CaseValue": 34
						},
						{
							"CaseName": "Vulnerability",
							"CaseValue": 35
						},
						{
							"CaseName": "Lame",
							"CaseValue": 36
						},
						{
							"CaseName": "Rampage",
							"CaseValue": 37
						},
						{
							"CaseName": "BuffDelay",
							"CaseValue": 38
						},
						{
							"CaseName": "CoolingOff",
							"CaseValue": 39
						},
						{
							"CaseName": "RecoveryDown",
							"CaseValue": 40
						},
						{
							"CaseName": "Armour",
							"CaseValue": 41
						},
						{
							"CaseName": "FrozenHpRecovery",
							"CaseValue": 42
						},
						{
							"CaseName": "ElectrostaticSputtering",
							"CaseValue": 43
						},
						{
							"CaseName": "InjuryHealing",
							"CaseValue": 44
						},
						{
							"CaseName": "Summon",
							"CaseValue": 45
						},
						{
							"CaseName": "CrabWalk",
							"CaseValue": 46
						},
						{
							"CaseName": "Invulnerable",
							"CaseValue": 47
						},
						{
							"CaseName": "Split",
							"CaseValue": 48
						},
						{
							"CaseName": "Trigger",
							"CaseValue": 49
						},
						{
							"CaseName": "Excavation",
							"CaseValue": 50
						},
						{
							"CaseName": "TombStone",
							"CaseValue": 51
						},
						{
							"CaseName": "InstantDeath",
							"CaseValue": 52
						},
						{
							"CaseName": "RangeRampage",
							"CaseValue": 53
						},
						{
							"CaseName": "Dot_wind",
							"CaseValue": 54
						},
						{
							"CaseName": "ConditionTrigger",
							"CaseValue": 55
						},
						{
							"CaseName": "Immolate",
							"CaseValue": 56
						},
						{
							"CaseName": "HpSwitch",
							"CaseValue": 57
						},
						{
							"CaseName": "StepingStone",
							"CaseValue": 58
						},
						{
							"CaseName": "EDER",
							"CaseValue": 59
						},
						{
							"CaseName": "Shippuden",
							"CaseValue": 60
						}
					]
				},
				{
					"EnumName": "HeroSkillDmgType",
					"Cases": [
						{
							"CaseName": "DmgValue",
							"CaseValue": 1
						},
						{
							"CaseName": "DmgRatio",
							"CaseValue": 2
						},
						{
							"CaseName": "MaxHpPerDmg",
							"CaseValue": 3
						},
						{
							"CaseName": "CurHpPerDmg",
							"CaseValue": 4
						},
						{
							"CaseName": "LossHpPerDmg",
							"CaseValue": 5
						},
						{
							"CaseName": "InheritedSkillRatio",
							"CaseValue": 6
						}
					]
				},
				{
					"EnumName": "HeroSkillEffectType",
					"Cases": [
						{
							"CaseName": "Buff",
							"CaseValue": 1
						},
						{
							"CaseName": "GuideLaser",
							"CaseValue": 2
						},
						{
							"CaseName": "BulletFire",
							"CaseValue": 3
						},
						{
							"CaseName": "IceRose",
							"CaseValue": 4
						},
						{
							"CaseName": "BulletWind",
							"CaseValue": 5
						},
						{
							"CaseName": "Laser",
							"CaseValue": 6
						},
						{
							"CaseName": "ElectricFierce",
							"CaseValue": 7
						},
						{
							"CaseName": "Airdrop",
							"CaseValue": 8
						},
						{
							"CaseName": "ElectricArc",
							"CaseValue": 9
						},
						{
							"CaseName": "BulletIce",
							"CaseValue": 10
						},
						{
							"CaseName": "DragonFlame",
							"CaseValue": 11
						},
						{
							"CaseName": "Car",
							"CaseValue": 12
						},
						{
							"CaseName": "Shrapnel",
							"CaseValue": 13
						},
						{
							"CaseName": "Cyclone",
							"CaseValue": 14
						},
						{
							"CaseName": "BulletPea",
							"CaseValue": 15
						},
						{
							"CaseName": "HandSword",
							"CaseValue": 16
						},
						{
							"CaseName": "Missile",
							"CaseValue": 17
						},
						{
							"CaseName": "MeleeAtk",
							"CaseValue": 18
						},
						{
							"CaseName": "SummonMonster",
							"CaseValue": 19
						},
						{
							"CaseName": "Aoe",
							"CaseValue": 20
						},
						{
							"CaseName": "UnlockTabLevelLimit",
							"CaseValue": 21
						},
						{
							"CaseName": "GetTab",
							"CaseValue": 22
						},
						{
							"CaseName": "UnlockTab",
							"CaseValue": 23
						},
						{
							"CaseName": "HomelanderLaser",
							"CaseValue": 24
						},
						{
							"CaseName": "ReplacedTab",
							"CaseValue": 25
						},
						{
							"CaseName": "BossEarLaser",
							"CaseValue": 26
						},
						{
							"CaseName": "BossMouseLaser",
							"CaseValue": 27
						},
						{
							"CaseName": "Boss2",
							"CaseValue": 28
						},
						{
							"CaseName": "MultipleMeleeAtk",
							"CaseValue": 29
						},
						{
							"CaseName": "SuicideBombing",
							"CaseValue": 30
						},
						{
							"CaseName": "Phase2Boss1",
							"CaseValue": 31
						},
						{
							"CaseName": "Phase2Boss6",
							"CaseValue": 32
						}
					]
				},
				{
					"EnumName": "HeroSkillHpRecoveryType",
					"Cases": [
						{
							"CaseName": "HpRecoveryValue",
							"CaseValue": 1
						},
						{
							"CaseName": "MaxHpPerRecovery",
							"CaseValue": 2
						},
						{
							"CaseName": "CurHpPerRecovery",
							"CaseValue": 3
						},
						{
							"CaseName": "LossHpPerRecovery",
							"CaseValue": 4
						},
						{
							"CaseName": "DmgPerRecovery",
							"CaseValue": 5
						}
					]
				},
				{
					"EnumName": "HeroSkillRangePolygon",
					"Cases": [
						{
							"CaseName": "Rectangle",
							"CaseValue": 1
						},
						{
							"CaseName": "Circular",
							"CaseValue": 2
						},
						{
							"CaseName": "Sector",
							"CaseValue": 3
						}
					]
				},
				{
					"EnumName": "HeroSkillType",
					"Cases": [
						{
							"CaseName": "HitSkill",
							"CaseValue": 1
						},
						{
							"CaseName": "NegativeSkill",
							"CaseValue": 2
						},
						{
							"CaseName": "GiftSkill",
							"CaseValue": 3
						}
					]
				},
				{
					"EnumName": "HeroStarUpPlan",
					"Cases": [
						{
							"CaseName": "HeroStarPlanLegendary",
							"CaseValue": 1
						},
						{
							"CaseName": "HeroStarPlanEpic",
							"CaseValue": 2
						},
						{
							"CaseName": "HeroStarPlanRare",
							"CaseValue": 3
						}
					]
				},
				{
					"EnumName": "HeroType",
					"Cases": [
						{
							"CaseName": "Magic",
							"CaseValue": 1
						},
						{
							"CaseName": "SuperPowers",
							"CaseValue": 2
						},
						{
							"CaseName": "Tech",
							"CaseValue": 3
						}
					]
				},
				{
					"EnumName": "HudType",
					"Cases": [
						{
							"CaseName": "Hero",
							"CaseValue": 1
						},
						{
							"CaseName": "Summon",
							"CaseValue": 2
						},
						{
							"CaseName": "Fight",
							"CaseValue": 3
						},
						{
							"CaseName": "Lab",
							"CaseValue": 4
						},
						{
							"CaseName": "Guild",
							"CaseValue": 5
						}
					]
				},
				{
					"EnumName": "IapBoothType",
					"Cases": [
						{
							"CaseName": "DiamondShop",
							"CaseValue": 1
						},
						{
							"CaseName": "DailySale",
							"CaseValue": 2
						},
						{
							"CaseName": "RegularPack",
							"CaseValue": 3
						},
						{
							"CaseName": "RegularBp",
							"CaseValue": 4
						},
						{
							"CaseName": "NoAds",
							"CaseValue": 5
						},
						{
							"CaseName": "2X",
							"CaseValue": 6
						},
						{
							"CaseName": "MonthCard",
							"CaseValue": 7
						},
						{
							"CaseName": "Fund",
							"CaseValue": 8
						},
						{
							"CaseName": "Sign",
							"CaseValue": 9
						},
						{
							"CaseName": "TurnTable",
							"CaseValue": 10
						},
						{
							"CaseName": "Sign7",
							"CaseValue": 11
						},
						{
							"CaseName": "Life",
							"CaseValue": 12
						}
					]
				},
				{
					"EnumName": "IapPackageType",
					"Cases": [
						{
							"CaseName": "Diamond",
							"CaseValue": 1
						},
						{
							"CaseName": "First",
							"CaseValue": 2
						},
						{
							"CaseName": "MonthCard",
							"CaseValue": 3
						},
						{
							"CaseName": "Fund",
							"CaseValue": 4
						},
						{
							"CaseName": "Regular",
							"CaseValue": 5
						},
						{
							"CaseName": "DailySale",
							"CaseValue": 6
						},
						{
							"CaseName": "AdFree",
							"CaseValue": 7
						},
						{
							"CaseName": "Life",
							"CaseValue": 8
						},
						{
							"CaseName": "SignBp",
							"CaseValue": 9
						},
						{
							"CaseName": "TurnActivity",
							"CaseValue": 10
						},
						{
							"CaseName": "Trigger",
							"CaseValue": 11
						},
						{
							"CaseName": "CoinFund",
							"CaseValue": 12
						},
						{
							"CaseName": "GeneFund",
							"CaseValue": 13
						},
						{
							"CaseName": "LordEquipFund",
							"CaseValue": 14
						},
						{
							"CaseName": "SunshineFund",
							"CaseValue": 15
						},
						{
							"CaseName": "Vip",
							"CaseValue": 16
						}
					]
				},
				{
					"EnumName": "ItemQuality",
					"Cases": [
						{
							"CaseName": "ItemDamaged",
							"CaseValue": 1
						},
						{
							"CaseName": "ItemCommon",
							"CaseValue": 2
						},
						{
							"CaseName": "ItemRare",
							"CaseValue": 3
						},
						{
							"CaseName": "ItemEpic",
							"CaseValue": 4
						},
						{
							"CaseName": "ItemLegendary",
							"CaseValue": 5
						},
						{
							"CaseName": "ItemMyth",
							"CaseValue": 6
						},
						{
							"CaseName": "ItemSupreme",
							"CaseValue": 7
						}
					]
				},
				{
					"EnumName": "ItemType",
					"Cases": [
						{
							"CaseName": "Chest",
							"CaseValue": 1
						},
						{
							"CaseName": "ChestSelfSelect",
							"CaseValue": 2
						},
						{
							"CaseName": "Diamond",
							"CaseValue": 3
						},
						{
							"CaseName": "Doughnut",
							"CaseValue": 4
						},
						{
							"CaseName": "SunShine",
							"CaseValue": 5
						},
						{
							"CaseName": "SummonCard",
							"CaseValue": 6
						},
						{
							"CaseName": "SeedBagCommon",
							"CaseValue": 7
						},
						{
							"CaseName": "SeedBagRare",
							"CaseValue": 8
						},
						{
							"CaseName": "SeedBagEpic",
							"CaseValue": 9
						},
						{
							"CaseName": "SeedBagLegendary",
							"CaseValue": 10
						},
						{
							"CaseName": "SeedBagMyth",
							"CaseValue": 11
						},
						{
							"CaseName": "LegendarySkillBook",
							"CaseValue": 12
						},
						{
							"CaseName": "EpicSkillBook",
							"CaseValue": 13
						},
						{
							"CaseName": "RareSkillBook",
							"CaseValue": 14
						},
						{
							"CaseName": "UniversalLegendaryHeroFragment",
							"CaseValue": 15
						},
						{
							"CaseName": "UniversalEpicHeroFragment",
							"CaseValue": 16
						},
						{
							"CaseName": "UniversalRareHeroFragment",
							"CaseValue": 17
						},
						{
							"CaseName": "RandomLegendaryHeroFragment",
							"CaseValue": 18
						},
						{
							"CaseName": "RandomEpicHeroFragment",
							"CaseValue": 19
						},
						{
							"CaseName": "RandomRareHeroFragment",
							"CaseValue": 20
						},
						{
							"CaseName": "Hero",
							"CaseValue": 21
						},
						{
							"CaseName": "HeroFragment",
							"CaseValue": 22
						},
						{
							"CaseName": "Avatar",
							"CaseValue": 23
						},
						{
							"CaseName": "AvatarFrame",
							"CaseValue": 24
						},
						{
							"CaseName": "RougeExp",
							"CaseValue": 25
						},
						{
							"CaseName": "SkillBook",
							"CaseValue": 26
						},
						{
							"CaseName": "Energy",
							"CaseValue": 27
						},
						{
							"CaseName": "HeroGeneFragment",
							"CaseValue": 28
						},
						{
							"CaseName": "Coin",
							"CaseValue": 29
						},
						{
							"CaseName": "HeroGeneralGeneFragment",
							"CaseValue": 30
						},
						{
							"CaseName": "GemRandom",
							"CaseValue": 31
						},
						{
							"CaseName": "Gem",
							"CaseValue": 32
						},
						{
							"CaseName": "GemReforge",
							"CaseValue": 33
						},
						{
							"CaseName": "LordEquipManual",
							"CaseValue": 34
						},
						{
							"CaseName": "LordEquipRandomManual",
							"CaseValue": 35
						},
						{
							"CaseName": "GemDraw",
							"CaseValue": 36
						},
						{
							"CaseName": "GuildExp",
							"CaseValue": 37
						},
						{
							"CaseName": "GuildCoin",
							"CaseValue": 38
						},
						{
							"CaseName": "ArenaCoin",
							"CaseValue": 39
						},
						{
							"CaseName": "TowerKey",
							"CaseValue": 40
						},
						{
							"CaseName": "CoinDungeonKey",
							"CaseValue": 41
						},
						{
							"CaseName": "GeneDungeonKey",
							"CaseValue": 42
						},
						{
							"CaseName": "LordEquipDungenKey",
							"CaseValue": 43
						},
						{
							"CaseName": "LordEquipGradeUpManual",
							"CaseValue": 44
						},
						{
							"CaseName": "RegularBpKey1",
							"CaseValue": 45
						},
						{
							"CaseName": "FreeAd",
							"CaseValue": 46
						},
						{
							"CaseName": "2X",
							"CaseValue": 47
						},
						{
							"CaseName": "SignKey1",
							"CaseValue": 48
						},
						{
							"CaseName": "FundKey1",
							"CaseValue": 49
						},
						{
							"CaseName": "MonthCard1",
							"CaseValue": 50
						},
						{
							"CaseName": "MonthCard2",
							"CaseValue": 51
						},
						{
							"CaseName": "turntablecoin",
							"CaseValue": 52
						},
						{
							"CaseName": "LordEquipMaterial",
							"CaseValue": 53
						},
						{
							"CaseName": "HeroQualityUp",
							"CaseValue": 54
						},
						{
							"CaseName": "SevenDayTasksScore",
							"CaseValue": 55
						},
						{
							"CaseName": "StarUpCommonItem",
							"CaseValue": 56
						},
						{
							"CaseName": "SunShineDungenKey",
							"CaseValue": 57
						},
						{
							"CaseName": "Vip",
							"CaseValue": 58
						}
					]
				},
				{
					"EnumName": "LaserTarget",
					"Cases": [
						{
							"CaseName": "Opposite",
							"CaseValue": 1
						},
						{
							"CaseName": "OppositeSideHeroDefense",
							"CaseValue": 2
						},
						{
							"CaseName": "OppositeSideHeroRanged",
							"CaseValue": 3
						},
						{
							"CaseName": "OppositeSideHeroSupport",
							"CaseValue": 4
						},
						{
							"CaseName": "OppoSideHeroFront",
							"CaseValue": 5
						},
						{
							"CaseName": "OppoSideHeroBehind",
							"CaseValue": 6
						},
						{
							"CaseName": "OppositeSideHeroHpLowest",
							"CaseValue": 7
						},
						{
							"CaseName": "PreSkillEffectTarget",
							"CaseValue": 8
						},
						{
							"CaseName": "PreSkillEffectTargetElseBoss",
							"CaseValue": 9
						},
						{
							"CaseName": "Boss",
							"CaseValue": 10
						},
						{
							"CaseName": "OwnForward",
							"CaseValue": 11
						}
					]
				},
				{
					"EnumName": "LevelType",
					"Cases": [
						{
							"CaseName": "TowerDefense",
							"CaseValue": 1
						},
						{
							"CaseName": "ParkOur",
							"CaseValue": 2
						}
					]
				},
				{
					"EnumName": "LogicType",
					"Cases": [
						{
							"CaseName": "And",
							"CaseValue": 1
						},
						{
							"CaseName": "Or",
							"CaseValue": 2
						},
						{
							"CaseName": "Invert",
							"CaseValue": 3
						}
					]
				},
				{
					"EnumName": "LordEquipGradeType",
					"Cases": [
						{
							"CaseName": "LordEquipGrade1",
							"CaseValue": 1
						},
						{
							"CaseName": "LordEquipGrade2",
							"CaseValue": 2
						},
						{
							"CaseName": "LordEquipGrade3",
							"CaseValue": 3
						},
						{
							"CaseName": "LordEquipGrade4",
							"CaseValue": 4
						},
						{
							"CaseName": "LordEquipGrade5",
							"CaseValue": 5
						},
						{
							"CaseName": "LordEquipGrade6",
							"CaseValue": 6
						},
						{
							"CaseName": "LordEquipGrade7",
							"CaseValue": 7
						}
					]
				},
				{
					"EnumName": "LordEquipType",
					"Cases": [
						{
							"CaseName": "LordEquipType1",
							"CaseValue": 1
						},
						{
							"CaseName": "LordEquipType2",
							"CaseValue": 2
						},
						{
							"CaseName": "LordEquipType3",
							"CaseValue": 3
						},
						{
							"CaseName": "LordEquipType4",
							"CaseValue": 4
						},
						{
							"CaseName": "LordEquipType5",
							"CaseValue": 5
						},
						{
							"CaseName": "LordEquipType6",
							"CaseValue": 6
						}
					]
				},
				{
					"EnumName": "MapEventType",
					"Cases": [
						{
							"CaseName": "Monster",
							"CaseValue": 1
						},
						{
							"CaseName": "Prop",
							"CaseValue": 2
						},
						{
							"CaseName": "Buff",
							"CaseValue": 3
						},
						{
							"CaseName": "Obstacle",
							"CaseValue": 4
						},
						{
							"CaseName": "Reward",
							"CaseValue": 5
						}
					]
				},
				{
					"EnumName": "MissileTarget",
					"Cases": [
						{
							"CaseName": "Opposite",
							"CaseValue": 1
						},
						{
							"CaseName": "OppositeSideHeroDefense",
							"CaseValue": 2
						},
						{
							"CaseName": "OppositeSideHeroRanged",
							"CaseValue": 3
						},
						{
							"CaseName": "OppositeSideHeroSupport",
							"CaseValue": 4
						},
						{
							"CaseName": "OppoSideHeroFront",
							"CaseValue": 5
						},
						{
							"CaseName": "OppoSideHeroBehind",
							"CaseValue": 6
						},
						{
							"CaseName": "OppositeSideHeroHpLowest",
							"CaseValue": 7
						},
						{
							"CaseName": "PreSkillEffectTarget",
							"CaseValue": 8
						},
						{
							"CaseName": "PreSkillEffectTargetElseBoss",
							"CaseValue": 9
						},
						{
							"CaseName": "Boss",
							"CaseValue": 10
						}
					]
				},
				{
					"EnumName": "MonsterCareerType",
					"Cases": [
						{
							"CaseName": "Melee",
							"CaseValue": 1
						},
						{
							"CaseName": "Ranger",
							"CaseValue": 2
						},
						{
							"CaseName": "Tank",
							"CaseValue": 3
						},
						{
							"CaseName": "Assassin",
							"CaseValue": 4
						},
						{
							"CaseName": "AirMelee",
							"CaseValue": 5
						},
						{
							"CaseName": "AirRanger",
							"CaseValue": 6
						},
						{
							"CaseName": "Suicide",
							"CaseValue": 7
						},
						{
							"CaseName": "LongRanger",
							"CaseValue": 8
						}
					]
				},
				{
					"EnumName": "MonsterGrade",
					"Cases": [
						{
							"CaseName": "Common",
							"CaseValue": 1
						},
						{
							"CaseName": "Elite",
							"CaseValue": 2
						},
						{
							"CaseName": "Boss",
							"CaseValue": 3
						}
					]
				},
				{
					"EnumName": "MonsterPosType",
					"Cases": [
						{
							"CaseName": "Ground",
							"CaseValue": 1
						},
						{
							"CaseName": "Air",
							"CaseValue": 2
						}
					]
				},
				{
					"EnumName": "MonsterRefreshType",
					"Cases": [
						{
							"CaseName": "InitialRefresh",
							"CaseValue": 1
						},
						{
							"CaseName": "DelayRefresh",
							"CaseValue": 2
						},
						{
							"CaseName": "UpstreamDeathCntRefresh",
							"CaseValue": 3
						},
						{
							"CaseName": "PassPlotsRefresh",
							"CaseValue": 4
						},
						{
							"CaseName": "AfterRefreshing",
							"CaseValue": 5
						},
						{
							"CaseName": "AfterTheGameStarts",
							"CaseValue": 6
						}
					]
				},
				{
					"EnumName": "PassType",
					"Cases": [
						{
							"CaseName": "OnlyFans",
							"CaseValue": 1
						},
						{
							"CaseName": "Element",
							"CaseValue": 2
						},
						{
							"CaseName": "Quality",
							"CaseValue": 3
						},
						{
							"CaseName": "Specify",
							"CaseValue": 4
						},
						{
							"CaseName": "BloodPer",
							"CaseValue": 5
						},
						{
							"CaseName": "Survive",
							"CaseValue": 6
						},
						{
							"CaseName": "RougeLevel",
							"CaseValue": 7
						},
						{
							"CaseName": "BtCard",
							"CaseValue": 8
						}
					]
				},
				{
					"EnumName": "PurchaseLimitType",
					"Cases": [
						{
							"CaseName": "DailyLimit",
							"CaseValue": 1
						},
						{
							"CaseName": "WeeklyLimit",
							"CaseValue": 2
						},
						{
							"CaseName": "MonthlyLimit",
							"CaseValue": 3
						},
						{
							"CaseName": "LifeLimit",
							"CaseValue": 4
						},
						{
							"CaseName": "UnLimit",
							"CaseValue": 5
						}
					]
				},
				{
					"EnumName": "RougeTabType",
					"Cases": [
						{
							"CaseName": "EffectTab",
							"CaseValue": 1
						},
						{
							"CaseName": "UnlockTab",
							"CaseValue": 2
						},
						{
							"CaseName": "ConfigTab",
							"CaseValue": 3
						}
					]
				},
				{
					"EnumName": "ShopType",
					"Cases": [
						{
							"CaseName": "GuildShop",
							"CaseValue": 1
						},
						{
							"CaseName": "ArenaShop",
							"CaseValue": 2
						},
						{
							"CaseName": "BlackShop",
							"CaseValue": 3
						},
						{
							"CaseName": "LevelShop",
							"CaseValue": 4
						}
					]
				},
				{
					"EnumName": "SkillAttrOverlyingType",
					"Cases": [
						{
							"CaseName": "AddOverlying",
							"CaseValue": 1
						},
						{
							"CaseName": "MulOverlying",
							"CaseValue": 2
						},
						{
							"CaseName": "EnumOverlying",
							"CaseValue": 3
						},
						{
							"CaseName": "NoOverlying",
							"CaseValue": 4
						}
					]
				},
				{
					"EnumName": "SkillDmgType",
					"Cases": [
						{
							"CaseName": "Electrical",
							"CaseValue": 1
						},
						{
							"CaseName": "Wind",
							"CaseValue": 2
						},
						{
							"CaseName": "Light",
							"CaseValue": 3
						},
						{
							"CaseName": "Fire",
							"CaseValue": 4
						},
						{
							"CaseName": "Ice",
							"CaseValue": 5
						},
						{
							"CaseName": "Physical",
							"CaseValue": 6
						}
					]
				},
				{
					"EnumName": "SkillType",
					"Cases": [
						{
							"CaseName": "ActiveSkill",
							"CaseValue": 1
						},
						{
							"CaseName": "PassiveSkill",
							"CaseValue": 2
						},
						{
							"CaseName": "AuraSkill",
							"CaseValue": 3
						}
					]
				},
				{
					"EnumName": "TaskCounterType",
					"Cases": [
						{
							"CaseName": "Reset",
							"CaseValue": 1
						},
						{
							"CaseName": "Total",
							"CaseValue": 2
						}
					]
				},
				{
					"EnumName": "TaskType",
					"Cases": [
						{
							"CaseName": "Login",
							"CaseValue": 1
						},
						{
							"CaseName": "TotalLogin",
							"CaseValue": 2
						},
						{
							"CaseName": "LevelBegin",
							"CaseValue": 3
						},
						{
							"CaseName": "LevelPass",
							"CaseValue": 4
						},
						{
							"CaseName": "LevelPassTo",
							"CaseValue": 5
						},
						{
							"CaseName": "ItemBurn",
							"CaseValue": 6
						},
						{
							"CaseName": "TotalItemBurn",
							"CaseValue": 7
						},
						{
							"CaseName": "HeroLevelUp",
							"CaseValue": 8
						},
						{
							"CaseName": "HeroLevelUpTo",
							"CaseValue": 9
						},
						{
							"CaseName": "HeroStarUp",
							"CaseValue": 10
						},
						{
							"CaseName": "HeroStarUpTo",
							"CaseValue": 11
						},
						{
							"CaseName": "HeroSkillUp",
							"CaseValue": 12
						},
						{
							"CaseName": "HeroSkillUpTo",
							"CaseValue": 13
						},
						{
							"CaseName": "HeroGeneUp",
							"CaseValue": 14
						},
						{
							"CaseName": "HeroGeneUpTo",
							"CaseValue": 15
						},
						{
							"CaseName": "KillMonster",
							"CaseValue": 16
						},
						{
							"CaseName": "TotalKillMonster",
							"CaseValue": 17
						},
						{
							"CaseName": "claim_idle_reward",
							"CaseValue": 18
						},
						{
							"CaseName": "claim_pass_level_reward",
							"CaseValue": 19
						},
						{
							"CaseName": "Chat",
							"CaseValue": 20
						},
						{
							"CaseName": "Nigger",
							"CaseValue": 21
						},
						{
							"CaseName": "Rename",
							"CaseValue": 22
						},
						{
							"CaseName": "Avatar",
							"CaseValue": 23
						},
						{
							"CaseName": "JoinGuild",
							"CaseValue": 24
						},
						{
							"CaseName": "Sweep",
							"CaseValue": 25
						},
						{
							"CaseName": "HeroSummon",
							"CaseValue": 26
						},
						{
							"CaseName": "HeroConfig",
							"CaseValue": 27
						},
						{
							"CaseName": "LordEquipLvlUp",
							"CaseValue": 28
						},
						{
							"CaseName": "LordEquipLvlUpTo",
							"CaseValue": 29
						},
						{
							"CaseName": "GemCraft",
							"CaseValue": 30
						},
						{
							"CaseName": "GemSummon",
							"CaseValue": 31
						},
						{
							"CaseName": "Shopping",
							"CaseValue": 32
						},
						{
							"CaseName": "DungeonChallenge",
							"CaseValue": 33
						},
						{
							"CaseName": "DungeonSweep",
							"CaseValue": 34
						},
						{
							"CaseName": "ArenaChallenge",
							"CaseValue": 35
						},
						{
							"CaseName": "TotalActivateHero",
							"CaseValue": 36
						},
						{
							"CaseName": "TotalQualityHero",
							"CaseValue": 37
						},
						{
							"CaseName": "TotalMainStar",
							"CaseValue": 38
						},
						{
							"CaseName": "DailyScore",
							"CaseValue": 39
						},
						{
							"CaseName": "WeeklyScore",
							"CaseValue": 40
						},
						{
							"CaseName": "DungeonLevel",
							"CaseValue": 41
						},
						{
							"CaseName": "GemQuality",
							"CaseValue": 42
						},
						{
							"CaseName": "LordEquipGrade",
							"CaseValue": 43
						},
						{
							"CaseName": "LordEquipGradeTo",
							"CaseValue": 44
						},
						{
							"CaseName": "EnergyFactory",
							"CaseValue": 45
						},
						{
							"CaseName": "TotalHeroLevelUp",
							"CaseValue": 46
						},
						{
							"CaseName": "TotalHeroStarUp",
							"CaseValue": 47
						},
						{
							"CaseName": "TotalHeroSkillUp",
							"CaseValue": 48
						},
						{
							"CaseName": "TotalHeroGeneUp",
							"CaseValue": 49
						},
						{
							"CaseName": "TotalHeroSummon",
							"CaseValue": 50
						},
						{
							"CaseName": "TotalGemSummon",
							"CaseValue": 51
						},
						{
							"CaseName": "TotalGemCraft",
							"CaseValue": 52
						},
						{
							"CaseName": "TotalLordEquipLvlUp",
							"CaseValue": 53
						},
						{
							"CaseName": "TotalLordEquipGrade",
							"CaseValue": 54
						},
						{
							"CaseName": "CompleteTask",
							"CaseValue": 55
						},
						{
							"CaseName": "TotalGemSummon_1",
							"CaseValue": 56
						},
						{
							"CaseName": "TotalGemSummon_2",
							"CaseValue": 57
						},
						{
							"CaseName": "ChapterTaskComplete",
							"CaseValue": 58
						}
					]
				},
				{
					"EnumName": "TriggerPackType",
					"Cases": [
						{
							"CaseName": "LevelPass",
							"CaseValue": 1
						},
						{
							"CaseName": "GemDraw",
							"CaseValue": 2
						},
						{
							"CaseName": "HeroSummon",
							"CaseValue": 3
						}
					]
				}
			]
		},
		"StructsData": {
			"FileName": "structs.fbs",
			"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
			"Structs": [
				{
					"StructName": "AoeParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "BuffTarget = Own"
						},
						{
							"FieldName": "Offset",
							"FieldType": "OffsetStr"
						},
						{
							"FieldName": "Delay",
							"FieldType": "int32"
						},
						{
							"FieldName": "Model",
							"FieldType": "int32"
						},
						{
							"FieldName": "Range",
							"FieldType": "RangeStr"
						},
						{
							"FieldName": "Time",
							"FieldType": "int32"
						},
						{
							"FieldName": "DmgInterval",
							"FieldType": "int32"
						},
						{
							"FieldName": "DmgType",
							"FieldType": "HeroSkillDmgType = DmgValue"
						},
						{
							"FieldName": "DmgValue",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "ArmourParamStr",
					"Fields": [
						{
							"FieldName": "Cnt",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "AttrStr",
					"Fields": [
						{
							"FieldName": "Atk",
							"FieldType": "int32"
						},
						{
							"FieldName": "Def",
							"FieldType": "int32"
						},
						{
							"FieldName": "Hp",
							"FieldType": "int32"
						},
						{
							"FieldName": "CritChance",
							"FieldType": "float"
						},
						{
							"FieldName": "CritDmgUpPer",
							"FieldType": "float"
						},
						{
							"FieldName": "DmgUpPer",
							"FieldType": "float"
						},
						{
							"FieldName": "CritResistChance",
							"FieldType": "float"
						},
						{
							"FieldName": "BeCritDmgDownPer",
							"FieldType": "float"
						},
						{
							"FieldName": "BeDmgDownPer",
							"FieldType": "float"
						},
						{
							"FieldName": "CdRate",
							"FieldType": "float"
						},
						{
							"FieldName": "Benefits",
							"FieldType": "[BenefitsKVS]"
						}
					]
				},
				{
					"StructName": "BenefitsKVS",
					"Fields": [
						{
							"FieldName": "BenefitsID",
							"FieldType": "int32"
						},
						{
							"FieldName": "BenefitsValue",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "Boss2ParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "MissileTarget = Opposite"
						},
						{
							"FieldName": "DmgRatio",
							"FieldType": "float"
						},
						{
							"FieldName": "IsParabola",
							"FieldType": "bool"
						},
						{
							"FieldName": "FlySpeed",
							"FieldType": "float"
						},
						{
							"FieldName": "Model",
							"FieldType": "int32"
						},
						{
							"FieldName": "SEAffected",
							"FieldType": "string"
						},
						{
							"FieldName": "SESelf",
							"FieldType": "string"
						}
					]
				},
				{
					"StructName": "BossEarLaserParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "LaserTarget = Opposite"
						},
						{
							"FieldName": "DmgRatio",
							"FieldType": "float"
						},
						{
							"FieldName": "Time",
							"FieldType": "float"
						},
						{
							"FieldName": "Times",
							"FieldType": "int32"
						},
						{
							"FieldName": "CastTime",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "BossMouseLaserParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "LaserTarget = Opposite"
						},
						{
							"FieldName": "DmgRatio",
							"FieldType": "float"
						},
						{
							"FieldName": "Time",
							"FieldType": "float"
						},
						{
							"FieldName": "Times",
							"FieldType": "int32"
						},
						{
							"FieldName": "CastTime",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "BuffDelayParamStr",
					"Fields": [
						{
							"FieldName": "AllDebuff",
							"FieldType": "bool"
						},
						{
							"FieldName": "BuffType",
							"FieldType": "[int32]"
						},
						{
							"FieldName": "DelayTimeAddRatio",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "BuffParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "BuffTarget = Own"
						},
						{
							"FieldName": "Chance",
							"FieldType": "float"
						},
						{
							"FieldName": "BuffType",
							"FieldType": "int32"
						},
						{
							"FieldName": "BuffTime",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "CircularRangeStr",
					"Fields": [
						{
							"FieldName": "CircularRadius",
							"FieldType": "float"
						},
						{
							"FieldName": "CircularRadiusAdditionRatio",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "ConditionTriggerParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "BuffTarget = Own"
						},
						{
							"FieldName": "Hp",
							"FieldType": "float"
						},
						{
							"FieldName": "Buff",
							"FieldType": "int32"
						},
						{
							"FieldName": "Time",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "CoolingOffParamStr",
					"Fields": [
						{
							"FieldName": "Hp",
							"FieldType": "float"
						},
						{
							"FieldName": "Stop",
							"FieldType": "bool"
						},
						{
							"FieldName": "Time",
							"FieldType": "float"
						},
						{
							"FieldName": "Buff",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "CordinateStr",
					"Fields": [
						{
							"FieldName": "XY",
							"FieldType": "[float]"
						}
					]
				},
				{
					"StructName": "CostKVS",
					"Fields": [
						{
							"FieldName": "CostType",
							"FieldType": "int32"
						},
						{
							"FieldName": "CostValue",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "CrabWalkParamStr",
					"Fields": [
						{
							"FieldName": "Ratio",
							"FieldType": "float"
						},
						{
							"FieldName": "HorizontalMoveTimeMin",
							"FieldType": "float"
						},
						{
							"FieldName": "HorizontalMoveTimeMax",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "DescStr",
					"Fields": [
						{
							"FieldName": "Desc",
							"FieldType": "string"
						},
						{
							"FieldName": "Param",
							"FieldType": "string"
						}
					]
				},
				{
					"StructName": "DotParamStr",
					"Fields": [
						{
							"FieldName": "DotDmgType",
							"FieldType": "HeroSkillDmgType = DmgValue"
						},
						{
							"FieldName": "DotDmgValue",
							"FieldType": "float"
						},
						{
							"FieldName": "DotDmgInterval",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "EDERParamStr",
					"Fields": [
						{
							"FieldName": "Hp",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "ElectrostaticSputteringParamStr",
					"Fields": [
						{
							"FieldName": "Range",
							"FieldType": "CircularRangeStr"
						},
						{
							"FieldName": "DmgRatio",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "ExtraEffectKVS",
					"Fields": [
						{
							"FieldName": "UnlockExtraEffect",
							"FieldType": "bool"
						},
						{
							"FieldName": "HeroSkillEffect",
							"FieldType": "[int32]"
						},
						{
							"FieldName": "Desc",
							"FieldType": "string"
						}
					]
				},
				{
					"StructName": "FrozenHpRecoveryParamStr",
					"Fields": [
						{
							"FieldName": "Cd",
							"FieldType": "float"
						},
						{
							"FieldName": "Limit",
							"FieldType": "int32"
						},
						{
							"FieldName": "Time",
							"FieldType": "float"
						},
						{
							"FieldName": "Buff",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "GoToStr",
					"Fields": [
						{
							"FieldName": "Type",
							"FieldType": "GoToType = Open"
						},
						{
							"FieldName": "Hud",
							"FieldType": "HudType = Hero"
						},
						{
							"FieldName": "Level",
							"FieldType": "int32"
						},
						{
							"FieldName": "Path",
							"FieldType": "string"
						},
						{
							"FieldName": "Size",
							"FieldType": "float"
						},
						{
							"FieldName": "Dialogue",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "HeroBotStr",
					"Fields": [
						{
							"FieldName": "Hero",
							"FieldType": "int32"
						},
						{
							"FieldName": "Level",
							"FieldType": "int32"
						},
						{
							"FieldName": "Star",
							"FieldType": "int32"
						},
						{
							"FieldName": "Skill",
							"FieldType": "[int32]"
						},
						{
							"FieldName": "Gene",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "HeroLotterySSR",
					"Fields": [
						{
							"FieldName": "Hero",
							"FieldType": "[int32]"
						},
						{
							"FieldName": "Weight",
							"FieldType": "[int32]"
						}
					]
				},
				{
					"StructName": "HomelanderLaserParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "LaserTarget = Opposite"
						},
						{
							"FieldName": "DmgRatio",
							"FieldType": "float"
						},
						{
							"FieldName": "Time",
							"FieldType": "float"
						},
						{
							"FieldName": "Interval",
							"FieldType": "float"
						},
						{
							"FieldName": "CastTime",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "HpRecoveryParamStr",
					"Fields": [
						{
							"FieldName": "HpRecoveryIsNegative",
							"FieldType": "bool"
						},
						{
							"FieldName": "HpRecoveryType",
							"FieldType": "HeroSkillHpRecoveryType = HpRecoveryValue"
						},
						{
							"FieldName": "HpRecoveryInterval",
							"FieldType": "float"
						},
						{
							"FieldName": "HpRecoveryValue",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "HpSwitchParamStr",
					"Fields": [
						{
							"FieldName": "LostHpPer",
							"FieldType": "float"
						},
						{
							"FieldName": "Buff",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "IDPStr",
					"Fields": [
						{
							"FieldName": "Image",
							"FieldType": "string"
						},
						{
							"FieldName": "Desc",
							"FieldType": "string"
						},
						{
							"FieldName": "Param",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "ImmolateParamStr",
					"Fields": [
						{
							"FieldName": "Sacrifice",
							"FieldType": "BuffTarget = Own"
						},
						{
							"FieldName": "Recipient",
							"FieldType": "BuffTarget = Own"
						},
						{
							"FieldName": "Buff",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "InjuryHealingParamStr",
					"Fields": [
						{
							"FieldName": "Cd",
							"FieldType": "float"
						},
						{
							"FieldName": "Limit",
							"FieldType": "int32"
						},
						{
							"FieldName": "Time",
							"FieldType": "float"
						},
						{
							"FieldName": "Buff",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "InstantDeathParamStr",
					"Fields": [
						{
							"FieldName": "Time",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "IntervalStr",
					"Fields": [
						{
							"FieldName": "Min",
							"FieldType": "int32"
						},
						{
							"FieldName": "Max",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "InvulnerableParamStr",
					"Fields": [
						{
							"FieldName": "HpPer",
							"FieldType": "[float]"
						},
						{
							"FieldName": "Time",
							"FieldType": "[float]"
						}
					]
				},
				{
					"StructName": "LifeStealParamStr",
					"Fields": [
						{
							"FieldName": "LifeStealType",
							"FieldType": "HeroSkillHpRecoveryType = HpRecoveryValue"
						},
						{
							"FieldName": "LifeStealValue",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "MeleeParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "MissileTarget = Opposite"
						},
						{
							"FieldName": "DmgRatio",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "MissileParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "MissileTarget = Opposite"
						},
						{
							"FieldName": "DmgRatio",
							"FieldType": "float"
						},
						{
							"FieldName": "IsParabola",
							"FieldType": "bool"
						},
						{
							"FieldName": "FlySpeed",
							"FieldType": "float"
						},
						{
							"FieldName": "Model",
							"FieldType": "int32"
						},
						{
							"FieldName": "SEAffected",
							"FieldType": "string"
						},
						{
							"FieldName": "SESelf",
							"FieldType": "string"
						}
					]
				},
				{
					"StructName": "MonsterKVS",
					"Fields": [
						{
							"FieldName": "MonsterId",
							"FieldType": "[int32]"
						},
						{
							"FieldName": "MonsterCnt",
							"FieldType": "[int32]"
						}
					]
				},
				{
					"StructName": "MultipleMeleeParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "MissileTarget = Opposite"
						},
						{
							"FieldName": "DmgRatio",
							"FieldType": "float"
						},
						{
							"FieldName": "Cnt",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "OffsetStr",
					"Fields": [
						{
							"FieldName": "OffSetDirection",
							"FieldType": "int32"
						},
						{
							"FieldName": "OffSetDistance",
							"FieldType": "float"
						},
						{
							"FieldName": "OffSetCenter",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "PassConditionStr",
					"Fields": [
						{
							"FieldName": "PassType",
							"FieldType": "PassType = OnlyFans"
						},
						{
							"FieldName": "Formula",
							"FieldType": "string"
						},
						{
							"FieldName": "Value",
							"FieldType": "int32"
						},
						{
							"FieldName": "Desc",
							"FieldType": "string"
						},
						{
							"FieldName": "Param",
							"FieldType": "string"
						}
					]
				},
				{
					"StructName": "Phase2Boss1ParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "MissileTarget = Opposite"
						},
						{
							"FieldName": "DmgRatio",
							"FieldType": "float"
						},
						{
							"FieldName": "IsParabola",
							"FieldType": "bool"
						},
						{
							"FieldName": "FlySpeed",
							"FieldType": "float"
						},
						{
							"FieldName": "Model",
							"FieldType": "int32"
						},
						{
							"FieldName": "SEAffected",
							"FieldType": "string"
						},
						{
							"FieldName": "SESelfLeft",
							"FieldType": "string"
						},
						{
							"FieldName": "SESelfRight",
							"FieldType": "string"
						}
					]
				},
				{
					"StructName": "Phase2Boss4ParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "MissileTarget = Opposite"
						},
						{
							"FieldName": "DmgRatio",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "RampageParamStr",
					"Fields": [
						{
							"FieldName": "Range",
							"FieldType": "float"
						},
						{
							"FieldName": "HpPer",
							"FieldType": "float"
						},
						{
							"FieldName": "Chance",
							"FieldType": "float"
						},
						{
							"FieldName": "Buff",
							"FieldType": "[int32]"
						},
						{
							"FieldName": "Time",
							"FieldType": "[float]"
						}
					]
				},
				{
					"StructName": "RangeRampageParamStr",
					"Fields": [
						{
							"FieldName": "Range",
							"FieldType": "CircularRangeStr"
						},
						{
							"FieldName": "Buff",
							"FieldType": "[int32]"
						},
						{
							"FieldName": "Time",
							"FieldType": "[float]"
						}
					]
				},
				{
					"StructName": "RangeStr",
					"Fields": [
						{
							"FieldName": "Polygon",
							"FieldType": "HeroSkillRangePolygon = Rectangle"
						},
						{
							"FieldName": "RectangleParam",
							"FieldType": "[float]"
						},
						{
							"FieldName": "RectangleVertex",
							"FieldType": "int32"
						},
						{
							"FieldName": "CircularParam",
							"FieldType": "float"
						},
						{
							"FieldName": "RectorParam",
							"FieldType": "[float]"
						}
					]
				},
				{
					"StructName": "RefreshParamStr",
					"Fields": [
						{
							"FieldName": "DelayTime",
							"FieldType": "float"
						},
						{
							"FieldName": "DeathCnt",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "ReviveParamStr",
					"Fields": [
						{
							"FieldName": "LostHpPer",
							"FieldType": "float"
						},
						{
							"FieldName": "Cnt",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "RewardKVS",
					"Fields": [
						{
							"FieldName": "RewardType",
							"FieldType": "int32"
						},
						{
							"FieldName": "RewardValue",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "RougeCorrectBenefitsStr",
					"Fields": [
						{
							"FieldName": "CorrectType",
							"FieldType": "CorrectType = Overlying"
						},
						{
							"FieldName": "CorrectBenefits",
							"FieldType": "int32"
						},
						{
							"FieldName": "CorrectSkillValue",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "RougeCorrectPlayerBenefitsStr",
					"Fields": [
						{
							"FieldName": "CorrectType",
							"FieldType": "CorrectType = Overlying"
						},
						{
							"FieldName": "CorrectBenefits",
							"FieldType": "int32"
						},
						{
							"FieldName": "CorrectSkillValue",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "RougeCorrectStr",
					"Fields": [
						{
							"FieldName": "CorrectType",
							"FieldType": "CorrectType = Overlying"
						},
						{
							"FieldName": "CorrectSkillAttr",
							"FieldType": "int32"
						},
						{
							"FieldName": "CorrectSkillValue",
							"FieldType": "float"
						},
						{
							"FieldName": "CorrectSkillModel",
							"FieldType": "int32"
						},
						{
							"FieldName": "CorrectSkillBuff",
							"FieldType": "int32"
						},
						{
							"FieldName": "CorrectSkillEffect",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "ShieldParamStr",
					"Fields": [
						{
							"FieldName": "ShiledHpRatio",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "SkillAttrStr",
					"Fields": [
						{
							"FieldName": "SkillAttr",
							"FieldType": "int32"
						},
						{
							"FieldName": "SkillAttrParam",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "SplitParamStr",
					"Fields": [
						{
							"FieldName": "MonsterType",
							"FieldType": "[int32]"
						},
						{
							"FieldName": "MonsterCnt",
							"FieldType": "[int32]"
						},
						{
							"FieldName": "Ratio",
							"FieldType": "[float]"
						}
					]
				},
				{
					"StructName": "StepingStoneParamStr",
					"Fields": [
						{
							"FieldName": "Giver",
							"FieldType": "BuffTarget = Own"
						},
						{
							"FieldName": "GiverDmgRatio",
							"FieldType": "float"
						},
						{
							"FieldName": "Recipient",
							"FieldType": "BuffTarget = Own"
						},
						{
							"FieldName": "Buff",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "SuicideBombingParamStr",
					"Fields": [
						{
							"FieldName": "Target",
							"FieldType": "MissileTarget = Opposite"
						},
						{
							"FieldName": "DmgRatio",
							"FieldType": "float"
						},
						{
							"FieldName": "RangeParam",
							"FieldType": "CircularRangeStr"
						}
					]
				},
				{
					"StructName": "SummonParamStr",
					"Fields": [
						{
							"FieldName": "IsAtkedTrigger",
							"FieldType": "bool"
						},
						{
							"FieldName": "Cd",
							"FieldType": "float"
						},
						{
							"FieldName": "Limit",
							"FieldType": "int32"
						},
						{
							"FieldName": "Ratio",
							"FieldType": "float"
						},
						{
							"FieldName": "MonsterId",
							"FieldType": "[int32]"
						},
						{
							"FieldName": "MonsterCnt",
							"FieldType": "[int32]"
						}
					]
				},
				{
					"StructName": "TombStoneParamStr",
					"Fields": [
						{
							"FieldName": "Ratio",
							"FieldType": "float"
						},
						{
							"FieldName": "MonsterId",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "TowerAIStr",
					"Fields": [
						{
							"FieldName": "AI",
							"FieldType": "int32"
						},
						{
							"FieldName": "Level",
							"FieldType": "int32"
						},
						{
							"FieldName": "Star",
							"FieldType": "int32"
						},
						{
							"FieldName": "Skill",
							"FieldType": "[int32]"
						},
						{
							"FieldName": "Gene",
							"FieldType": "int32"
						}
					]
				},
				{
					"StructName": "TriggerParamStr",
					"Fields": [
						{
							"FieldName": "IsAtkedTrigger",
							"FieldType": "bool"
						},
						{
							"FieldName": "Cd",
							"FieldType": "float"
						},
						{
							"FieldName": "Limit",
							"FieldType": "int32"
						},
						{
							"FieldName": "Buff",
							"FieldType": "int32"
						},
						{
							"FieldName": "Time",
							"FieldType": "float"
						}
					]
				},
				{
					"StructName": "UnixTime",
					"Fields": [
						{
							"FieldName": "Timestamp",
							"FieldType": "int64"
						}
					]
				}
			]
		},
		"TableDataList": [
			{
				"FileName": "AchievementTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "AchievementTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "ReqFunction",
						"FieldType": "int32"
					},
					{
						"FieldName": "Order",
						"FieldType": "int32"
					},
					{
						"FieldName": "Group",
						"FieldType": "int32"
					},
					{
						"FieldName": "Sub",
						"FieldType": "int32"
					},
					{
						"FieldName": "TaskType",
						"FieldType": "TaskType = Login"
					},
					{
						"FieldName": "TaskCounterType",
						"FieldType": "TaskCounterType = Reset"
					},
					{
						"FieldName": "Formula",
						"FieldType": "string"
					},
					{
						"FieldName": "Value",
						"FieldType": "int32"
					},
					{
						"FieldName": "GoTo",
						"FieldType": "int32"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Param",
						"FieldType": "[string]"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "ActivityTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ActivityTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Type",
						"FieldType": "IapBoothType = DiamondShop"
					},
					{
						"FieldName": "Tag",
						"FieldType": "string"
					},
					{
						"FieldName": "Order",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "ArenaBotTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ArenaBotTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Avatar",
						"FieldType": "int32"
					},
					{
						"FieldName": "Score",
						"FieldType": "int32"
					},
					{
						"FieldName": "Hero",
						"FieldType": "[HeroBotStr]"
					},
					{
						"FieldName": "Equip",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Gem",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "ArenaChallengeRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ArenaChallengeRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Win",
						"FieldType": "bool"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "ArenaDailyRankRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ArenaDailyRankRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Rank",
						"FieldType": "IntervalStr"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "ArenaExtraChallengeCntTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ArenaExtraChallengeCntTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Cnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "Price",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					}
				]
			},
			{
				"FileName": "ArenaMatchTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ArenaMatchTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "OwnRank",
						"FieldType": "IntervalStr"
					},
					{
						"FieldName": "OppositeRank",
						"FieldType": "[IntervalStr]"
					},
					{
						"FieldName": "OppositeWeight",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "ArenaRefreshTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ArenaRefreshTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "RefreshCnt",
						"FieldType": "IntervalStr"
					},
					{
						"FieldName": "Price",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "ArenaScoreTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ArenaScoreTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Score",
						"FieldType": "IntervalStr"
					},
					{
						"FieldName": "Win",
						"FieldType": "int32"
					},
					{
						"FieldName": "Fail",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "ArenaShopTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ArenaShopTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Period",
						"FieldType": "int32"
					},
					{
						"FieldName": "Shop",
						"FieldType": "int32"
					},
					{
						"FieldName": "Score",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "int32"
					},
					{
						"FieldName": "Currency",
						"FieldType": "int32"
					},
					{
						"FieldName": "Price",
						"FieldType": "int32"
					},
					{
						"FieldName": "TimesLimit",
						"FieldType": "int32"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "ArenaWeeklyRankRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ArenaWeeklyRankRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Rank",
						"FieldType": "IntervalStr"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "AttributeHierarchyTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "AttributeHierarchyTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "AttributeHierarchy",
						"FieldType": "AttributeHierarchy = BaseAttribute"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "BattleAttribute",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "AvatarFrameTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "AvatarFrameTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Item",
						"FieldType": "int32"
					},
					{
						"FieldName": "Benefits",
						"FieldType": "[BenefitsKVS]"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "AvatarTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "AvatarTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Default",
						"FieldType": "bool"
					},
					{
						"FieldName": "Hero",
						"FieldType": "int32"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "BattleAttributeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "BattleAttributeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Attribute",
						"FieldType": "Attribute = Hp"
					},
					{
						"FieldName": "AttributeHierarchy",
						"FieldType": "AttributeHierarchy = BaseAttribute"
					},
					{
						"FieldName": "IsShow",
						"FieldType": "bool"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Calc",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "BattleModelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "BattleModelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "SE",
						"FieldType": "string"
					},
					{
						"FieldName": "Bone",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "BenefitsCalcJustShowTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "BenefitsCalcJustShowTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Formula",
						"FieldType": "BenefitCalcFormula = Formula1"
					},
					{
						"FieldName": "Param1",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Param2",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Param3",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "BenefitsCalcTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "BenefitsCalcTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Formula",
						"FieldType": "BenefitCalcFormula = Formula1"
					},
					{
						"FieldName": "Param1",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Param2",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Param3",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "BenefitsTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "BenefitsTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Type",
						"FieldType": "string"
					},
					{
						"FieldName": "Category",
						"FieldType": "string"
					},
					{
						"FieldName": "HeroCareer",
						"FieldType": "int32"
					},
					{
						"FieldName": "Negative",
						"FieldType": "bool"
					},
					{
						"FieldName": "ShowHud",
						"FieldType": "bool"
					},
					{
						"FieldName": "BenefitsName",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "EquipBenefitsName",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "BlackShopTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "BlackShopTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Shop",
						"FieldType": "int32"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "int32"
					},
					{
						"FieldName": "Currency",
						"FieldType": "int32"
					},
					{
						"FieldName": "Price",
						"FieldType": "int32"
					},
					{
						"FieldName": "TimesLimit",
						"FieldType": "int32"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "BossDungeonRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "BossDungeonRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "BossDungeonTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "BossDungeonTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Day",
						"FieldType": "int32"
					},
					{
						"FieldName": "Boss",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "ChapterLevelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ChapterLevelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Star_Desc_1",
						"FieldType": "string"
					},
					{
						"FieldName": "Star_Desc_2",
						"FieldType": "string"
					},
					{
						"FieldName": "Star_Desc_3",
						"FieldType": "string"
					},
					{
						"FieldName": "ChapterLevelImage",
						"FieldType": "string"
					},
					{
						"FieldName": "Unlock",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "ChapterTaskMainTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ChapterTaskMainTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "ChapterTaskTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ChapterTaskTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "ReqFunction",
						"FieldType": "int32"
					},
					{
						"FieldName": "Chapter",
						"FieldType": "int32"
					},
					{
						"FieldName": "TaskType",
						"FieldType": "TaskType = Login"
					},
					{
						"FieldName": "TaskCounterType",
						"FieldType": "TaskCounterType = Reset"
					},
					{
						"FieldName": "Formula",
						"FieldType": "string"
					},
					{
						"FieldName": "Value",
						"FieldType": "int32"
					},
					{
						"FieldName": "GoTo",
						"FieldType": "int32"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Param",
						"FieldType": "string"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "DailyTasksScoreTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "DailyTasksScoreTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Score",
						"FieldType": "int32"
					},
					{
						"FieldName": "DailyOrWeekly",
						"FieldType": "DailyOrWeekly = Daily"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "DailyTasksTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "DailyTasksTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "ReqFunction",
						"FieldType": "int32"
					},
					{
						"FieldName": "TaskType",
						"FieldType": "TaskType = Login"
					},
					{
						"FieldName": "TaskCounterType",
						"FieldType": "TaskCounterType = Reset"
					},
					{
						"FieldName": "Formula",
						"FieldType": "string"
					},
					{
						"FieldName": "Value",
						"FieldType": "int32"
					},
					{
						"FieldName": "GoTo",
						"FieldType": "int32"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Param",
						"FieldType": "string"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Score",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "DaveLevelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "DaveLevelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "LevelUp",
						"FieldType": "CostKVS"
					},
					{
						"FieldName": "Atk",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "DropGroupTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "DropGroupTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "DropType",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "DropMainTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "DropMainTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "DropGroupId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Chance",
						"FieldType": "int32"
					},
					{
						"FieldName": "Weight",
						"FieldType": "int32"
					},
					{
						"FieldName": "ItemId",
						"FieldType": "int32"
					},
					{
						"FieldName": "MinValue",
						"FieldType": "int32"
					},
					{
						"FieldName": "MaxValue",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "DungeonChapterLevelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "DungeonChapterLevelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Dungeon",
						"FieldType": "int32"
					},
					{
						"FieldName": "Levels",
						"FieldType": "int32"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "LevelUpCost",
						"FieldType": "CostKVS"
					}
				]
			},
			{
				"FileName": "DungeonCoinLevelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "DungeonCoinLevelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LevelGrade",
						"FieldType": "string"
					},
					{
						"FieldName": "MonsterPreview",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MonsterPreviewPos",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MonsterPreviewScheme",
						"FieldType": "int32"
					},
					{
						"FieldName": "Dungeon",
						"FieldType": "int32"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "ChooseNum",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "FirstReward",
						"FieldType": "RewardKVS"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "BuffType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Buff",
						"FieldType": "[IDPStr]"
					}
				]
			},
			{
				"FileName": "DungeonGeneLevelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "DungeonGeneLevelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LevelGrade",
						"FieldType": "string"
					},
					{
						"FieldName": "MonsterPreview",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MonsterPreviewPos",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MonsterPreviewScheme",
						"FieldType": "int32"
					},
					{
						"FieldName": "Dungeon",
						"FieldType": "int32"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "ChooseNum",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "FirstReward",
						"FieldType": "RewardKVS"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "BuffType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Buff",
						"FieldType": "[IDPStr]"
					}
				]
			},
			{
				"FileName": "DungeonLordEquipLevelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "DungeonLordEquipLevelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LevelGrade",
						"FieldType": "string"
					},
					{
						"FieldName": "MonsterPreview",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MonsterPreviewPos",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MonsterPreviewScheme",
						"FieldType": "int32"
					},
					{
						"FieldName": "Dungeon",
						"FieldType": "int32"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "ChooseNum",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "FirstReward",
						"FieldType": "RewardKVS"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "BuffType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Buff",
						"FieldType": "[IDPStr]"
					}
				]
			},
			{
				"FileName": "DungeonRefreshTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "DungeonRefreshTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Dungeon",
						"FieldType": "int32"
					},
					{
						"FieldName": "Levels",
						"FieldType": "int32"
					},
					{
						"FieldName": "Wave",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reversionary",
						"FieldType": "int32"
					},
					{
						"FieldName": "RefreshType",
						"FieldType": "MonsterRefreshType = InitialRefresh"
					},
					{
						"FieldName": "RefreshParamDelayTime",
						"FieldType": "[float]"
					},
					{
						"FieldName": "RefreshParamDeathCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "MonsterId",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MonsterCnt",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Weights",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Intervals",
						"FieldType": "[float]"
					}
				]
			},
			{
				"FileName": "DungeonSunshineLevelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "DungeonSunshineLevelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LevelGrade",
						"FieldType": "string"
					},
					{
						"FieldName": "MonsterPreview",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MonsterPreviewPos",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MonsterPreviewScheme",
						"FieldType": "int32"
					},
					{
						"FieldName": "Dungeon",
						"FieldType": "int32"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "ChooseNum",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "FirstReward",
						"FieldType": "RewardKVS"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "BuffType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Buff",
						"FieldType": "[IDPStr]"
					}
				]
			},
			{
				"FileName": "DungeonTypeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "DungeonTypeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "DungeonType",
						"FieldType": "DungeonType = CoinDungeon"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Buff",
						"FieldType": "[IDPStr]"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "LevelScene",
						"FieldType": "string"
					},
					{
						"FieldName": "ShowReward",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "EliteDungeonRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "EliteDungeonRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Star",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "EliteDungeonTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "EliteDungeonTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Inherit",
						"FieldType": "int32"
					},
					{
						"FieldName": "DirectWin",
						"FieldType": "int32"
					},
					{
						"FieldName": "EliteMonsterAtkRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "EliteMonsterDefRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "EliteMonsterHpRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "StarCondition",
						"FieldType": "[PassConditionStr]"
					}
				]
			},
			{
				"FileName": "FunctionPreviewTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "FunctionPreviewTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Priority",
						"FieldType": "int32"
					},
					{
						"FieldName": "Function",
						"FieldType": "int32"
					},
					{
						"FieldName": "UnlockDesc",
						"FieldType": "DescStr"
					},
					{
						"FieldName": "Reward",
						"FieldType": "RewardKVS"
					}
				]
			},
			{
				"FileName": "FunctionTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "FunctionTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "Day",
						"FieldType": "int32"
					},
					{
						"FieldName": "Logic",
						"FieldType": "LogicType = And"
					},
					{
						"FieldName": "Priority",
						"FieldType": "int32"
					},
					{
						"FieldName": "Open",
						"FieldType": "bool"
					},
					{
						"FieldName": "OpenEffect",
						"FieldType": "bool"
					},
					{
						"FieldName": "EffectFly",
						"FieldType": "string"
					},
					{
						"FieldName": "FunctionName",
						"FieldType": "string"
					},
					{
						"FieldName": "FunctionDesc",
						"FieldType": "string"
					},
					{
						"FieldName": "FunctionImage",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "GameConfigs.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "GameConfigs",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Value",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "GemAffixQualityTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "GemAffixQualityTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "GemAffixQuality",
						"FieldType": "GemAffixQuality = GemAffixQuality1"
					},
					{
						"FieldName": "Weight",
						"FieldType": "int32"
					},
					{
						"FieldName": "Show",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "GemQualityTypeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "GemQualityTypeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "GemQualityType",
						"FieldType": "GemQualityType = GemQualityType1"
					},
					{
						"FieldName": "Power",
						"FieldType": "int32"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Tag",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Image2",
						"FieldType": "string"
					},
					{
						"FieldName": "Image3",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "GoToTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "GoToTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "CanBreak",
						"FieldType": "bool"
					},
					{
						"FieldName": "CloseCurruntUi",
						"FieldType": "bool"
					},
					{
						"FieldName": "GoToStep",
						"FieldType": "[GoToStr]"
					}
				]
			},
			{
				"FileName": "GuildFlagTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "GuildFlagTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Type",
						"FieldType": "GuildFlagType = Base"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Unlock",
						"FieldType": "int32"
					},
					{
						"FieldName": "Order",
						"FieldType": "int32"
					},
					{
						"FieldName": "Default",
						"FieldType": "bool"
					}
				]
			},
			{
				"FileName": "GuildHaggleTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "GuildHaggleTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "RewardCost",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "HaggleLeast",
						"FieldType": "int32"
					},
					{
						"FieldName": "HaggleMag",
						"FieldType": "int32"
					},
					{
						"FieldName": "HaggleRate",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "GuildLevelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "GuildLevelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "IsMaxLevel",
						"FieldType": "bool"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "Exp",
						"FieldType": "int32"
					},
					{
						"FieldName": "Member",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "GuildPermissionTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "GuildPermissionTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Permission",
						"FieldType": "GuildPermission = ChangeGuildFlag"
					},
					{
						"FieldName": "CnBan",
						"FieldType": "bool"
					},
					{
						"FieldName": "GuildRank",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "GuildRankTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "GuildRankTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Rank",
						"FieldType": "GuildRank = Rank5"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "GuildShopTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "GuildShopTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Period",
						"FieldType": "int32"
					},
					{
						"FieldName": "Shop",
						"FieldType": "int32"
					},
					{
						"FieldName": "GuildLevelLimit",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "int32"
					},
					{
						"FieldName": "Currency",
						"FieldType": "int32"
					},
					{
						"FieldName": "Price",
						"FieldType": "int32"
					},
					{
						"FieldName": "TimesLimit",
						"FieldType": "int32"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "GuildTaskTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "GuildTaskTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "TaskType",
						"FieldType": "TaskType = Login"
					},
					{
						"FieldName": "TaskCounterType",
						"FieldType": "TaskCounterType = Reset"
					},
					{
						"FieldName": "Formula",
						"FieldType": "string"
					},
					{
						"FieldName": "Value",
						"FieldType": "int32"
					},
					{
						"FieldName": "GoTo",
						"FieldType": "int32"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Param",
						"FieldType": "string"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Score",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "GuildTasksScoreTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "GuildTasksScoreTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Score",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "HelpInfoContentTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HelpInfoContentTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "BelongTo",
						"FieldType": "int32"
					},
					{
						"FieldName": "ShowTag",
						"FieldType": "bool"
					},
					{
						"FieldName": "Order",
						"FieldType": "int32"
					},
					{
						"FieldName": "Tag",
						"FieldType": "string"
					},
					{
						"FieldName": "Content",
						"FieldType": "[string]"
					}
				]
			},
			{
				"FileName": "HelpInfoMainTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HelpInfoMainTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "HeroBondsTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroBondsTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Remark",
						"FieldType": "string"
					},
					{
						"FieldName": "BondsBenefits",
						"FieldType": "[BenefitsKVS]"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Param",
						"FieldType": "[string]"
					}
				]
			},
			{
				"FileName": "HeroCareerTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroCareerTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroCareerType",
						"FieldType": "HeroCareer = HeroDefense"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "BgImage",
						"FieldType": "string"
					},
					{
						"FieldName": "SelectImage",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "HeroConfigTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroConfigTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroConfig",
						"FieldType": "HeroConfig = HeroConfig1"
					},
					{
						"FieldName": "Unlock",
						"FieldType": "int32"
					},
					{
						"FieldName": "UnlockDesc",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "HeroElementTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroElementTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroElementType",
						"FieldType": "HeroElement = ElementalFire"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "HeroFragmentTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroFragmentTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroFragment",
						"FieldType": "int32"
					},
					{
						"FieldName": "Hero",
						"FieldType": "int32"
					},
					{
						"FieldName": "ComposeCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "DivisionCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "CanUseUniversalFragmentExchange",
						"FieldType": "bool"
					},
					{
						"FieldName": "UniversalFragmentType",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "HeroGeneFragmentTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroGeneFragmentTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Hero",
						"FieldType": "int32"
					},
					{
						"FieldName": "HeroGeneralGeneFragment",
						"FieldType": "int32"
					},
					{
						"FieldName": "HeroGeneFragment",
						"FieldType": "int32"
					},
					{
						"FieldName": "Weight",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "HeroGeneTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroGeneTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroID",
						"FieldType": "int32"
					},
					{
						"FieldName": "HeroGeneLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "LevelUp",
						"FieldType": "[CostKVS]"
					},
					{
						"FieldName": "Power",
						"FieldType": "int32"
					},
					{
						"FieldName": "Attr",
						"FieldType": "AttrStr"
					},
					{
						"FieldName": "AllHeroCritDmgUp",
						"FieldType": "float"
					},
					{
						"FieldName": "IsUnlockExtraSkillEffect",
						"FieldType": "bool"
					},
					{
						"FieldName": "RefHeroStarLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "SkillTitle",
						"FieldType": "string"
					},
					{
						"FieldName": "SkillDesc",
						"FieldType": "string"
					},
					{
						"FieldName": "SkillDescParam",
						"FieldType": "[string]"
					},
					{
						"FieldName": "DetailSkillDesc",
						"FieldType": "string"
					},
					{
						"FieldName": "DetailSkillDescParam",
						"FieldType": "[string]"
					},
					{
						"FieldName": "Icon",
						"FieldType": "string"
					},
					{
						"FieldName": "IconLocked",
						"FieldType": "string"
					},
					{
						"FieldName": "IsSkillPreview",
						"FieldType": "bool"
					},
					{
						"FieldName": "SkillPreview",
						"FieldType": "string"
					},
					{
						"FieldName": "IsPopUp",
						"FieldType": "bool"
					},
					{
						"FieldName": "PopupName",
						"FieldType": "string"
					},
					{
						"FieldName": "PopupDesc",
						"FieldType": "string"
					},
					{
						"FieldName": "PopupDescParam",
						"FieldType": "[string]"
					},
					{
						"FieldName": "PVEPassiveSkillEffect",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "HeroLevelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroLevelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "PlanID",
						"FieldType": "HeroLevelUpPlan = HeroLevelPlanLegendaryDefense"
					},
					{
						"FieldName": "ReqDesc",
						"FieldType": "string"
					},
					{
						"FieldName": "HeroLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "LevelUp",
						"FieldType": "[CostKVS]"
					},
					{
						"FieldName": "Power",
						"FieldType": "int32"
					},
					{
						"FieldName": "Attr",
						"FieldType": "AttrStr"
					}
				]
			},
			{
				"FileName": "HeroLotteryGroupTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroLotteryGroupTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "FreeCD",
						"FieldType": "int32"
					},
					{
						"FieldName": "DailyFreeTimesLimit",
						"FieldType": "int32"
					},
					{
						"FieldName": "ImageBig",
						"FieldType": "string"
					},
					{
						"FieldName": "ImageTag",
						"FieldType": "string"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "BgImageDown",
						"FieldType": "string"
					},
					{
						"FieldName": "BgImageUp",
						"FieldType": "string"
					},
					{
						"FieldName": "Model",
						"FieldType": "string"
					},
					{
						"FieldName": "Bet",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "SingleDrawCostType",
						"FieldType": "int32"
					},
					{
						"FieldName": "SingleDrawCostValue",
						"FieldType": "int32"
					},
					{
						"FieldName": "SingleDrawCostDiamdondCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "ChanceDesc",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "HeroLotteryMustTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroLotteryMustTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroLotteryGroup",
						"FieldType": "int32"
					},
					{
						"FieldName": "MustCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "IncreaseCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "Increment",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "IsRefresh",
						"FieldType": "bool"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Must11",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MustSSR",
						"FieldType": "HeroLotterySSR"
					},
					{
						"FieldName": "FirstHero",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "FirstHeroWeight",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "TenthHero",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "TenthHeroWeight",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "HeroLotteryRandomGroupTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroLotteryRandomGroupTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "ChapterUnlockMin",
						"FieldType": "int32"
					},
					{
						"FieldName": "ChapterUnlockMax",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "HeroLotteryRandomTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroLotteryRandomTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "IsMust",
						"FieldType": "bool"
					},
					{
						"FieldName": "RandomGroup",
						"FieldType": "int32"
					},
					{
						"FieldName": "HeroLotteryGroup",
						"FieldType": "int32"
					},
					{
						"FieldName": "ItemType",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardQuality",
						"FieldType": "int32"
					},
					{
						"FieldName": "Order",
						"FieldType": "int32"
					},
					{
						"FieldName": "ItemValue",
						"FieldType": "int32"
					},
					{
						"FieldName": "Weight",
						"FieldType": "int32"
					},
					{
						"FieldName": "DisplayChance",
						"FieldType": "float"
					}
				]
			},
			{
				"FileName": "HeroQualityTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroQualityTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroQuality",
						"FieldType": "HeroQuality = HeroLegendary"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Tag",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "HeroRestrainTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroRestrainTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroTypeRestrainSide",
						"FieldType": "int32"
					},
					{
						"FieldName": "HeroTypeRestrainedSide",
						"FieldType": "int32"
					},
					{
						"FieldName": "RestrainDmgAddRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Param",
						"FieldType": "[string]"
					}
				]
			},
			{
				"FileName": "HeroSkillAttrTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroSkillAttrTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Remark",
						"FieldType": "string"
					},
					{
						"FieldName": "SkillEffectType",
						"FieldType": "HeroSkillEffectType = Buff"
					},
					{
						"FieldName": "AttrDefaultType",
						"FieldType": "AttrDefaultType = Number"
					},
					{
						"FieldName": "OverlyingType",
						"FieldType": "SkillAttrOverlyingType = AddOverlying"
					},
					{
						"FieldName": "DefaultValue",
						"FieldType": "float"
					},
					{
						"FieldName": "BattleModel",
						"FieldType": "int32"
					},
					{
						"FieldName": "BuffType",
						"FieldType": "int32"
					},
					{
						"FieldName": "Effect",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "HeroSkillAwakeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroSkillAwakeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "SkillGroupId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "Power",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "UnlockHeroStar",
						"FieldType": "int32"
					},
					{
						"FieldName": "LevelUp",
						"FieldType": "[CostKVS]"
					},
					{
						"FieldName": "Action",
						"FieldType": "string"
					},
					{
						"FieldName": "TimeCharge",
						"FieldType": "int32"
					},
					{
						"FieldName": "Audio",
						"FieldType": "string"
					},
					{
						"FieldName": "SkillCoolDown",
						"FieldType": "float"
					},
					{
						"FieldName": "SkillRange",
						"FieldType": "float"
					},
					{
						"FieldName": "SkillType",
						"FieldType": "SkillType = ActiveSkill"
					},
					{
						"FieldName": "SkillDesc",
						"FieldType": "string"
					},
					{
						"FieldName": "IsUnlockExtraSkillEffect",
						"FieldType": "bool"
					},
					{
						"FieldName": "IsSkillPreview",
						"FieldType": "bool"
					},
					{
						"FieldName": "SkillPreview",
						"FieldType": "string"
					},
					{
						"FieldName": "ExtraEffect",
						"FieldType": "ExtraEffectKVS"
					},
					{
						"FieldName": "PVEPassiveSkillEffect",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "PassiveSkillEffect",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "ActiveSkillEffect",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "HeroSkillBuffTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroSkillBuffTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "BuffType",
						"FieldType": "BuffType = Buff"
					},
					{
						"FieldName": "BuffEffect",
						"FieldType": "string"
					},
					{
						"FieldName": "IsRemove",
						"FieldType": "bool"
					},
					{
						"FieldName": "StackingType",
						"FieldType": "BuffOverlyingType = NoOverlying"
					},
					{
						"FieldName": "StackingTimes",
						"FieldType": "int32"
					},
					{
						"FieldName": "BuffEffectType",
						"FieldType": "HeroSkillBuffType = Benefit"
					},
					{
						"FieldName": "BenefitsParam",
						"FieldType": "[BenefitsKVS]"
					},
					{
						"FieldName": "LameParam",
						"FieldType": "float"
					},
					{
						"FieldName": "DotParam",
						"FieldType": "DotParamStr"
					},
					{
						"FieldName": "ImmunityParam",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "HpRecovery",
						"FieldType": "HpRecoveryParamStr"
					},
					{
						"FieldName": "ShieldParam",
						"FieldType": "ShieldParamStr"
					},
					{
						"FieldName": "RampageParam",
						"FieldType": "RampageParamStr"
					},
					{
						"FieldName": "BuffDelayParam",
						"FieldType": "BuffDelayParamStr"
					},
					{
						"FieldName": "CoolingOffParam",
						"FieldType": "CoolingOffParamStr"
					},
					{
						"FieldName": "ArmourParam",
						"FieldType": "ArmourParamStr"
					},
					{
						"FieldName": "FrozenHpRecoveryParam",
						"FieldType": "FrozenHpRecoveryParamStr"
					},
					{
						"FieldName": "ElectrostaticSputteringParam",
						"FieldType": "ElectrostaticSputteringParamStr"
					},
					{
						"FieldName": "InjuryHealingParam",
						"FieldType": "InjuryHealingParamStr"
					},
					{
						"FieldName": "SummonParam",
						"FieldType": "SummonParamStr"
					},
					{
						"FieldName": "CrabWalkParam",
						"FieldType": "CrabWalkParamStr"
					},
					{
						"FieldName": "InvulnerableParam",
						"FieldType": "InvulnerableParamStr"
					},
					{
						"FieldName": "SplitParam",
						"FieldType": "SplitParamStr"
					},
					{
						"FieldName": "LifeStealParam",
						"FieldType": "LifeStealParamStr"
					},
					{
						"FieldName": "InstantDeathParam",
						"FieldType": "InstantDeathParamStr"
					},
					{
						"FieldName": "TombStoneParam",
						"FieldType": "TombStoneParamStr"
					},
					{
						"FieldName": "ReviveParam",
						"FieldType": "ReviveParamStr"
					},
					{
						"FieldName": "RangeRampageParam",
						"FieldType": "RangeRampageParamStr"
					},
					{
						"FieldName": "TriggerParam",
						"FieldType": "TriggerParamStr"
					},
					{
						"FieldName": "ConditionTriggerParam",
						"FieldType": "ConditionTriggerParamStr"
					},
					{
						"FieldName": "ImmolateParam",
						"FieldType": "ImmolateParamStr"
					},
					{
						"FieldName": "HpSwitchParam",
						"FieldType": "HpSwitchParamStr"
					},
					{
						"FieldName": "StepingStoneParam",
						"FieldType": "StepingStoneParamStr"
					},
					{
						"FieldName": "EDERParam",
						"FieldType": "EDERParamStr"
					},
					{
						"FieldName": "ShippudenParam",
						"FieldType": "float"
					}
				]
			},
			{
				"FileName": "HeroSkillBuffTypeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroSkillBuffTypeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "BuffType",
						"FieldType": "HeroSkillBuffType = Benefit"
					}
				]
			},
			{
				"FileName": "HeroSkillEffectTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroSkillEffectTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "SkillEffectType",
						"FieldType": "HeroSkillEffectType = Buff"
					},
					{
						"FieldName": "SkillAttr",
						"FieldType": "[SkillAttrStr]"
					},
					{
						"FieldName": "BuffParam",
						"FieldType": "BuffParamStr"
					},
					{
						"FieldName": "AoeParam",
						"FieldType": "AoeParamStr"
					},
					{
						"FieldName": "RougeTab",
						"FieldType": "int32"
					},
					{
						"FieldName": "RougeTabReplace",
						"FieldType": "int32"
					},
					{
						"FieldName": "RougeTabReplaced",
						"FieldType": "int32"
					},
					{
						"FieldName": "RougeTabUnlock",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "BossEarLaserParam",
						"FieldType": "BossEarLaserParamStr"
					},
					{
						"FieldName": "BossMouseLaserParam",
						"FieldType": "BossMouseLaserParamStr"
					},
					{
						"FieldName": "HomelanderLaserParam",
						"FieldType": "HomelanderLaserParamStr"
					},
					{
						"FieldName": "MissileParam",
						"FieldType": "MissileParamStr"
					},
					{
						"FieldName": "MissileSpecialEffects",
						"FieldType": "string"
					},
					{
						"FieldName": "MeleeParam",
						"FieldType": "MeleeParamStr"
					},
					{
						"FieldName": "Boss2Param",
						"FieldType": "Boss2ParamStr"
					},
					{
						"FieldName": "MultipleMeleeParam",
						"FieldType": "MultipleMeleeParamStr"
					},
					{
						"FieldName": "SuicideBombingParam",
						"FieldType": "SuicideBombingParamStr"
					},
					{
						"FieldName": "Phase2Boss1Param",
						"FieldType": "Phase2Boss1ParamStr"
					},
					{
						"FieldName": "Phase2Boss4Param",
						"FieldType": "Phase2Boss4ParamStr"
					}
				]
			},
			{
				"FileName": "HeroSkillGroupTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroSkillGroupTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroId",
						"FieldType": "int32"
					},
					{
						"FieldName": "HitSkillType",
						"FieldType": "SkillDmgType = Electrical"
					},
					{
						"FieldName": "MonsterPosType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "IsBallistic",
						"FieldType": "bool"
					},
					{
						"FieldName": "UnlockHeroLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "UnlockHeroStar",
						"FieldType": "int32"
					},
					{
						"FieldName": "HeroSkillType",
						"FieldType": "HeroSkillType = HitSkill"
					},
					{
						"FieldName": "HeroSkillName",
						"FieldType": "string"
					},
					{
						"FieldName": "HeroSkillIcon",
						"FieldType": "string"
					},
					{
						"FieldName": "HeroSkillLevel",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MaxSkillLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "RougeTab",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "HeroSkillTypeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroSkillTypeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroSkillType",
						"FieldType": "HeroSkillType = HitSkill"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "HeroStarTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroStarTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "PlanID",
						"FieldType": "HeroStarUpPlan = HeroStarPlanLegendary"
					},
					{
						"FieldName": "HeroStarLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "HeroSkillLevelLimit",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "StarUpCostValue",
						"FieldType": "int32"
					},
					{
						"FieldName": "StarUpDifference",
						"FieldType": "int32"
					},
					{
						"FieldName": "StarUpCommonItem",
						"FieldType": "string"
					},
					{
						"FieldName": "StarUpCommonItemNum",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsGradeUp",
						"FieldType": "bool"
					},
					{
						"FieldName": "GradeUpCostValue",
						"FieldType": "int32"
					},
					{
						"FieldName": "HeroQuality",
						"FieldType": "int32"
					},
					{
						"FieldName": "Power",
						"FieldType": "int32"
					},
					{
						"FieldName": "Attr",
						"FieldType": "AttrStr"
					}
				]
			},
			{
				"FileName": "HeroTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "ItemId",
						"FieldType": "int32"
					},
					{
						"FieldName": "ItemGeneId",
						"FieldType": "int32"
					},
					{
						"FieldName": "HeroName",
						"FieldType": "string"
					},
					{
						"FieldName": "StarUpCostItem",
						"FieldType": "int32"
					},
					{
						"FieldName": "GradeUpCostItem",
						"FieldType": "int32"
					},
					{
						"FieldName": "StarPlanID",
						"FieldType": "HeroStarUpPlan = HeroStarPlanLegendary"
					},
					{
						"FieldName": "LevelPlanID",
						"FieldType": "HeroLevelUpPlan = HeroLevelPlanLegendaryDefense"
					},
					{
						"FieldName": "ShowPriority",
						"FieldType": "int32"
					},
					{
						"FieldName": "HeroCareer",
						"FieldType": "HeroCareer = HeroDefense"
					},
					{
						"FieldName": "HeroElement",
						"FieldType": "int32"
					},
					{
						"FieldName": "HeroQuality",
						"FieldType": "HeroQuality = HeroLegendary"
					},
					{
						"FieldName": "HeroType",
						"FieldType": "HeroType = Magic"
					},
					{
						"FieldName": "HasAtkFirstCareer",
						"FieldType": "bool"
					},
					{
						"FieldName": "AtkFirstCareer",
						"FieldType": "HeroCareer = HeroDefense"
					},
					{
						"FieldName": "SkillRectangle",
						"FieldType": "HeroSkillRangePolygon = Rectangle"
					},
					{
						"FieldName": "CollisionRadius",
						"FieldType": "float"
					},
					{
						"FieldName": "HitSkill",
						"FieldType": "int32"
					},
					{
						"FieldName": "NegativeSkill",
						"FieldType": "int32"
					},
					{
						"FieldName": "GiftSkill",
						"FieldType": "int32"
					},
					{
						"FieldName": "GiftSkillBenefits",
						"FieldType": "[BenefitsKVS]"
					},
					{
						"FieldName": "HeroImage",
						"FieldType": "string"
					},
					{
						"FieldName": "HeroTabImage",
						"FieldType": "string"
					},
					{
						"FieldName": "HeroTabBgImage",
						"FieldType": "string"
					},
					{
						"FieldName": "SettlementHeroTabImage",
						"FieldType": "string"
					},
					{
						"FieldName": "SettlementHeroTabBgImage",
						"FieldType": "string"
					},
					{
						"FieldName": "HeroSpine",
						"FieldType": "string"
					},
					{
						"FieldName": "HeroFullSpine",
						"FieldType": "string"
					},
					{
						"FieldName": "HeroFullPrefab",
						"FieldType": "string"
					},
					{
						"FieldName": "HeroFullPrefabExtra",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "HeroTypeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "HeroTypeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroType",
						"FieldType": "HeroType = Magic"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "Iap1stTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "Iap1stTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Priority",
						"FieldType": "int32"
					},
					{
						"FieldName": "Limit",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "D2",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "D3",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "View",
						"FieldType": "string"
					},
					{
						"FieldName": "ImageTitle",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Thumbnail",
						"FieldType": "string"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "Iap2XTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "Iap2XTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Limit",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Help",
						"FieldType": "int32"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapAdFreeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapAdFreeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Limit",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Help",
						"FieldType": "int32"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapBPTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapBPTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Help",
						"FieldType": "int32"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapBpRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapBpRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Bp",
						"FieldType": "int32"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "Exp",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardFree",
						"FieldType": "RewardKVS"
					},
					{
						"FieldName": "RewardVip",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "IapDailySaleFreeRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapDailySaleFreeRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Reset",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "IapDailySaleRewardGroupTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapDailySaleRewardGroupTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Hero",
						"FieldType": "int32"
					},
					{
						"FieldName": "Gift",
						"FieldType": "int32"
					},
					{
						"FieldName": "Unlock",
						"FieldType": "int32"
					},
					{
						"FieldName": "Default",
						"FieldType": "bool"
					},
					{
						"FieldName": "HeroImage",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapDailySaleRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapDailySaleRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Group",
						"FieldType": "int32"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "IapPackageOrder",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsPack",
						"FieldType": "bool"
					},
					{
						"FieldName": "Limit",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapDailySaleTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapDailySaleTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Help",
						"FieldType": "int32"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Image2",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapDealTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapDealTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Type",
						"FieldType": "IapBoothType = DiamondShop"
					},
					{
						"FieldName": "Tag",
						"FieldType": "string"
					},
					{
						"FieldName": "Order",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "IapDungeonFundRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapDungeonFundRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Fund",
						"FieldType": "int32"
					},
					{
						"FieldName": "Type",
						"FieldType": "DungeonType = CoinDungeon"
					},
					{
						"FieldName": "CoinLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "GeneLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "LordEquipLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "SunshineLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardFree",
						"FieldType": "RewardKVS"
					},
					{
						"FieldName": "RewardVip",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "IapDungeonFundTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapDungeonFundTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Unlock",
						"FieldType": "int32"
					},
					{
						"FieldName": "Type",
						"FieldType": "DungeonType = CoinDungeon"
					},
					{
						"FieldName": "Stage",
						"FieldType": "int32"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Limit",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "Tag",
						"FieldType": "string"
					},
					{
						"FieldName": "TagParam",
						"FieldType": "string"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Help",
						"FieldType": "int32"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Image2",
						"FieldType": "string"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapLevelFundRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapLevelFundRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Fund",
						"FieldType": "int32"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardFree",
						"FieldType": "RewardKVS"
					},
					{
						"FieldName": "RewardVip",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "IapLevelFundTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapLevelFundTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Unlock",
						"FieldType": "int32"
					},
					{
						"FieldName": "Stage",
						"FieldType": "int32"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Limit",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "Tag",
						"FieldType": "string"
					},
					{
						"FieldName": "TagParam",
						"FieldType": "string"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Help",
						"FieldType": "int32"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Image2",
						"FieldType": "string"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapLifeCardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapLifeCardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Benefits",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "BenefitsValue",
						"FieldType": "[float]"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Limit",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardDaily",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "[DescStr]"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapMonthCardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapMonthCardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Benefits",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "BenefitsValue",
						"FieldType": "[float]"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Limit",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardDaily",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "[DescStr]"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapOrder.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapOrder",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Order",
						"FieldType": "int32"
					},
					{
						"FieldName": "Funciton",
						"FieldType": "int32"
					},
					{
						"FieldName": "Goto",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "IapPackageDiamondShopTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapPackageDiamondShopTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "BgImage",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Tag",
						"FieldType": "string"
					},
					{
						"FieldName": "FirstDoubleReward",
						"FieldType": "bool"
					}
				]
			},
			{
				"FileName": "IapPackageRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapPackageRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "RewardType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "IapPackageTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapPackageTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Remark",
						"FieldType": "string"
					},
					{
						"FieldName": "IapPackageType",
						"FieldType": "IapPackageType = Diamond"
					},
					{
						"FieldName": "PayID",
						"FieldType": "int32"
					},
					{
						"FieldName": "IapPackageRewardId",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "IapPriceTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapPriceTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Price",
						"FieldType": "string"
					},
					{
						"FieldName": "Cny",
						"FieldType": "int32"
					},
					{
						"FieldName": "Krw",
						"FieldType": "int32"
					},
					{
						"FieldName": "Diamond",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "IapRegularPackGroupTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapRegularPackGroupTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Reset",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Order",
						"FieldType": "int32"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Hot",
						"FieldType": "bool"
					},
					{
						"FieldName": "FrontImage",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapRegularPackTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapRegularPackTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Group",
						"FieldType": "int32"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "Order",
						"FieldType": "int32"
					},
					{
						"FieldName": "End",
						"FieldType": "bool"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapShopMallTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapShopMallTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Type",
						"FieldType": "IapBoothType = DiamondShop"
					},
					{
						"FieldName": "Tag",
						"FieldType": "string"
					},
					{
						"FieldName": "Order",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "IapSignRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapSignRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Sign",
						"FieldType": "int32"
					},
					{
						"FieldName": "Day",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardFree",
						"FieldType": "RewardKVS"
					},
					{
						"FieldName": "RewardVip",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "IapSignTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapSignTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Limit",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapTriggerPackGroupTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapTriggerPackGroupTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "TriggerId",
						"FieldType": "int32"
					},
					{
						"FieldName": "TriggerInterval",
						"FieldType": "int32"
					},
					{
						"FieldName": "TriggerPackType",
						"FieldType": "TriggerPackType = LevelPass"
					},
					{
						"FieldName": "Formula",
						"FieldType": "string"
					},
					{
						"FieldName": "RechargeDay",
						"FieldType": "int32"
					},
					{
						"FieldName": "RechargeFormula",
						"FieldType": "string"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "ImageTitle",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Thumbnail",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapTriggerPackTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapTriggerPackTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Group",
						"FieldType": "int32"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IapTurnPackTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IapTurnPackTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "IsFree",
						"FieldType": "bool"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Limit",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					},
					{
						"FieldName": "FreeReward",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "IdleMonsterTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IdleMonsterTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Chapters",
						"FieldType": "int32"
					},
					{
						"FieldName": "Wave",
						"FieldType": "int32"
					},
					{
						"FieldName": "Control",
						"FieldType": "string"
					},
					{
						"FieldName": "RefreshType",
						"FieldType": "MonsterRefreshType = InitialRefresh"
					},
					{
						"FieldName": "RefreshParam",
						"FieldType": "RefreshParamStr"
					},
					{
						"FieldName": "Monster",
						"FieldType": "MonsterKVS"
					}
				]
			},
			{
				"FileName": "IdleRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IdleRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "ExtraRewardDrop",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "IdleRewardTime.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "IdleRewardTime",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "MinTime",
						"FieldType": "int32"
					},
					{
						"FieldName": "MaxTime",
						"FieldType": "int32"
					},
					{
						"FieldName": "DefaultReward1RefreshTime",
						"FieldType": "int32"
					},
					{
						"FieldName": "DefaultReward2RefreshTime",
						"FieldType": "int32"
					},
					{
						"FieldName": "DefaultReward3RefreshTime",
						"FieldType": "int32"
					},
					{
						"FieldName": "DefaultReward4RefreshTime",
						"FieldType": "int32"
					},
					{
						"FieldName": "DefaultReward5RefreshTime",
						"FieldType": "int32"
					},
					{
						"FieldName": "DefaultReward6RefreshTime",
						"FieldType": "int32"
					},
					{
						"FieldName": "ExtraRewardRefreshTime",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "ItemQualityTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ItemQualityTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "ItemQuality",
						"FieldType": "ItemQuality = ItemDamaged"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Tag",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "ItemSourceTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ItemSourceTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Goto",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "ItemTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ItemTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Priority",
						"FieldType": "int32"
					},
					{
						"FieldName": "Type",
						"FieldType": "ItemType = Chest"
					},
					{
						"FieldName": "Value",
						"FieldType": "int32"
					},
					{
						"FieldName": "Benefits",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "BenefitsValue",
						"FieldType": "[float]"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Param",
						"FieldType": "[string]"
					},
					{
						"FieldName": "Quality",
						"FieldType": "int32"
					},
					{
						"FieldName": "AutoUse",
						"FieldType": "bool"
					},
					{
						"FieldName": "RepeatAutoTransform",
						"FieldType": "bool"
					},
					{
						"FieldName": "ChestDropGroup",
						"FieldType": "int32"
					},
					{
						"FieldName": "ChestSelect",
						"FieldType": "int32"
					},
					{
						"FieldName": "MaxUseCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsDiamond",
						"FieldType": "bool"
					},
					{
						"FieldName": "DiamondExchange",
						"FieldType": "bool"
					},
					{
						"FieldName": "DiamondCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "ShowSource",
						"FieldType": "bool"
					},
					{
						"FieldName": "ItemSource",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "InBag",
						"FieldType": "bool"
					},
					{
						"FieldName": "BagType",
						"FieldType": "BagType = Plants"
					},
					{
						"FieldName": "BagStack",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "LTCRechargeScoreTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LTCRechargeScoreTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Score",
						"FieldType": "int32"
					},
					{
						"FieldName": "Usd",
						"FieldType": "float"
					},
					{
						"FieldName": "Cny",
						"FieldType": "float"
					},
					{
						"FieldName": "Krw",
						"FieldType": "float"
					}
				]
			},
			{
				"FileName": "LTCRechargeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LTCRechargeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Score",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "LanguageCnTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LanguageCnTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Key",
						"FieldType": "string"
					},
					{
						"FieldName": "CnName",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "LevelShopTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LevelShopTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Shop",
						"FieldType": "int32"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsFree",
						"FieldType": "bool"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Currency",
						"FieldType": "int32"
					},
					{
						"FieldName": "Price",
						"FieldType": "int32"
					},
					{
						"FieldName": "TimesLimit",
						"FieldType": "int32"
					},
					{
						"FieldName": "Discount",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "LoginOpenTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LoginOpenTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Order",
						"FieldType": "int32"
					},
					{
						"FieldName": "Function",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "LordEquipGradeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordEquipGradeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LordEquipType",
						"FieldType": "int32"
					},
					{
						"FieldName": "Grade",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "BasePower",
						"FieldType": "int32"
					},
					{
						"FieldName": "ExtraPower",
						"FieldType": "int32"
					},
					{
						"FieldName": "LevelUp",
						"FieldType": "[CostKVS]"
					},
					{
						"FieldName": "UnlockLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "Attr",
						"FieldType": "AttrStr"
					},
					{
						"FieldName": "ExtraAttr",
						"FieldType": "AttrStr"
					}
				]
			},
			{
				"FileName": "LordEquipGradeTypeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordEquipGradeTypeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LordEquipGrade",
						"FieldType": "LordEquipGradeType = LordEquipGrade1"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Color",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "LordEquipSlotsTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordEquipSlotsTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "Desc",
						"FieldType": "DescStr"
					}
				]
			},
			{
				"FileName": "LordEquipTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordEquipTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LordEquipType",
						"FieldType": "int32"
					},
					{
						"FieldName": "RefGrade",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "LevelUp",
						"FieldType": "[CostKVS]"
					},
					{
						"FieldName": "Attr",
						"FieldType": "AttrStr"
					}
				]
			},
			{
				"FileName": "LordEquipTypeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordEquipTypeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LordEquipType",
						"FieldType": "LordEquipType = LordEquipType1"
					},
					{
						"FieldName": "Weight",
						"FieldType": "int32"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Icon",
						"FieldType": "string"
					},
					{
						"FieldName": "Icon2",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "LordGemCraftTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordGemCraftTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "PreCraftQuality",
						"FieldType": "int32"
					},
					{
						"FieldName": "PostCraftQuality",
						"FieldType": "int32"
					},
					{
						"FieldName": "GemCnt",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "LordGemDropCntTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordGemDropCntTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LordGemRandom",
						"FieldType": "int32"
					},
					{
						"FieldName": "LordGemCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "LordGemCntWeight",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "LordGemDropQualityTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordGemDropQualityTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LordGemRandom",
						"FieldType": "int32"
					},
					{
						"FieldName": "LordGemQuality",
						"FieldType": "GemQualityType = GemQualityType1"
					},
					{
						"FieldName": "LordGemQualityWeight",
						"FieldType": "int32"
					},
					{
						"FieldName": "Show",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "LordGemRandomGroupChanceTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordGemRandomGroupChanceTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LordGemRandomGroup",
						"FieldType": "int32"
					},
					{
						"FieldName": "LordGemRandomRewardGroup",
						"FieldType": "int32"
					},
					{
						"FieldName": "GemQualityType",
						"FieldType": "int32"
					},
					{
						"FieldName": "Weight",
						"FieldType": "int32"
					},
					{
						"FieldName": "Cnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "Show",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "LordGemRandomGroupMustTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordGemRandomGroupMustTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LordGemRandomGroup",
						"FieldType": "int32"
					},
					{
						"FieldName": "MustCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "GemQualityType",
						"FieldType": "int32"
					},
					{
						"FieldName": "Cnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsRefresh",
						"FieldType": "bool"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "LordGemRandomGroupTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordGemRandomGroupTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "FreeCD",
						"FieldType": "int32"
					},
					{
						"FieldName": "DailyFreeTimesLimit",
						"FieldType": "int32"
					},
					{
						"FieldName": "ImageBig",
						"FieldType": "string"
					},
					{
						"FieldName": "ImageTag",
						"FieldType": "string"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Bet",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "SingleDrawCostType",
						"FieldType": "int32"
					},
					{
						"FieldName": "SingleDrawCostValue",
						"FieldType": "int32"
					},
					{
						"FieldName": "SingleDrawCostDiamdondCnt",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "LordGemRandomRewardGroupTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordGemRandomRewardGroupTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "LordGemRandomGroup",
						"FieldType": "int32"
					},
					{
						"FieldName": "MinLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "MaxLevel",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "LordGemReforgeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordGemReforgeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "GemQualityType",
						"FieldType": "int32"
					},
					{
						"FieldName": "CanReforge",
						"FieldType": "bool"
					},
					{
						"FieldName": "Reforge",
						"FieldType": "CostKVS"
					}
				]
			},
			{
				"FileName": "LordGemTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "LordGemTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Item",
						"FieldType": "int32"
					},
					{
						"FieldName": "Hero",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "GemAffixId",
						"FieldType": "int32"
					},
					{
						"FieldName": "LordEquipType",
						"FieldType": "int32"
					},
					{
						"FieldName": "GemQualityType",
						"FieldType": "int32"
					},
					{
						"FieldName": "GemAffixQuality",
						"FieldType": "int32"
					},
					{
						"FieldName": "Modifier",
						"FieldType": "int32"
					},
					{
						"FieldName": "Weight",
						"FieldType": "int32"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "DescParam",
						"FieldType": "[string]"
					}
				]
			},
			{
				"FileName": "MailTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MailTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Subject",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "MainChapterLevelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MainChapterLevelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Chapter",
						"FieldType": "int32"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					},
					{
						"FieldName": "LevelUpCost",
						"FieldType": "CostKVS"
					}
				]
			},
			{
				"FileName": "MainChapterTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MainChapterTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Level",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "MainLevelPassRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MainLevelPassRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "MainLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "Star",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "MainLevelRangeDmgTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MainLevelRangeDmgTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Range",
						"FieldType": "[float]"
					},
					{
						"FieldName": "DmgRatio",
						"FieldType": "float"
					}
				]
			},
			{
				"FileName": "MainLevelRewardRatioTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MainLevelRewardRatioTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "MaxRougeLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardRatio",
						"FieldType": "float"
					}
				]
			},
			{
				"FileName": "MainLevelRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MainLevelRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "MainLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsElite",
						"FieldType": "bool"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "MainLevelRogueRewardWeightTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MainLevelRogueRewardWeightTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "KillRewardRougeTabCnt",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "KillRewardRougeTabCntWeight",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "MainLevelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MainLevelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "IsMaxLevel",
						"FieldType": "bool"
					},
					{
						"FieldName": "IsRewardLevel",
						"FieldType": "bool"
					},
					{
						"FieldName": "LevelGrade",
						"FieldType": "string"
					},
					{
						"FieldName": "ChapterLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "Chapter",
						"FieldType": "int32"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "EliteMonsterAtkRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "EliteMonsterDefRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "EliteMonsterHpRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "LevelScene",
						"FieldType": "string"
					},
					{
						"FieldName": "MapImage",
						"FieldType": "string"
					},
					{
						"FieldName": "LevelType",
						"FieldType": "LevelType = TowerDefense"
					},
					{
						"FieldName": "LevelPrefab",
						"FieldType": "string"
					},
					{
						"FieldName": "MonsterPreview",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MonsterPreviewPos",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MonsterPreviewScheme",
						"FieldType": "int32"
					},
					{
						"FieldName": "KillRewardRougeTabCntScheme",
						"FieldType": "int32"
					},
					{
						"FieldName": "CommonChallengeReward",
						"FieldType": "int32"
					},
					{
						"FieldName": "EliteChallengeReward",
						"FieldType": "int32"
					},
					{
						"FieldName": "OneStarReward",
						"FieldType": "int32"
					},
					{
						"FieldName": "TwoStarReward",
						"FieldType": "int32"
					},
					{
						"FieldName": "ThreeStarReward",
						"FieldType": "int32"
					},
					{
						"FieldName": "ExtraReward",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "MainLineTasksTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MainLineTasksTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "ReqFunction",
						"FieldType": "int32"
					},
					{
						"FieldName": "ReqMainLineTasks",
						"FieldType": "int32"
					},
					{
						"FieldName": "TaskType",
						"FieldType": "TaskType = Login"
					},
					{
						"FieldName": "TaskCounterType",
						"FieldType": "TaskCounterType = Reset"
					},
					{
						"FieldName": "Formula",
						"FieldType": "string"
					},
					{
						"FieldName": "Value",
						"FieldType": "int32"
					},
					{
						"FieldName": "GoTo",
						"FieldType": "int32"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Param",
						"FieldType": "string"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "int32"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "int32"
					},
					{
						"FieldName": "GuideTrigger",
						"FieldType": "bool"
					}
				]
			},
			{
				"FileName": "MapEventBuffTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MapEventBuffTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "BuffType",
						"FieldType": "int32"
					},
					{
						"FieldName": "BuffTime",
						"FieldType": "int32"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "TriggerDisappear",
						"FieldType": "bool"
					},
					{
						"FieldName": "ContinuedSE",
						"FieldType": "string"
					},
					{
						"FieldName": "ContinuedShow",
						"FieldType": "string"
					},
					{
						"FieldName": "TriggerSE",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "MapEventMonsterGroupTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MapEventMonsterGroupTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "MonsterWaveGroup",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "MapEventMonsterTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MapEventMonsterTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Chapters",
						"FieldType": "int32"
					},
					{
						"FieldName": "Levels",
						"FieldType": "int32"
					},
					{
						"FieldName": "Wave",
						"FieldType": "int32"
					},
					{
						"FieldName": "RefreshType",
						"FieldType": "MonsterRefreshType = InitialRefresh"
					},
					{
						"FieldName": "RefreshParam",
						"FieldType": "RefreshParamStr"
					},
					{
						"FieldName": "Monster",
						"FieldType": "MonsterKVS"
					}
				]
			},
			{
				"FileName": "MapEventObstacleTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MapEventObstacleTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Effect",
						"FieldType": "int32"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Spine",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "TriggerSE",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "MapEventPropTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MapEventPropTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Effect",
						"FieldType": "int32"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Spine",
						"FieldType": "string"
					},
					{
						"FieldName": "TriggerSE",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "MapEventRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MapEventRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Reward",
						"FieldType": "RewardKVS"
					}
				]
			},
			{
				"FileName": "MapEventSkillTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MapEventSkillTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "MapEventType",
						"FieldType": "MapEventType = Monster"
					},
					{
						"FieldName": "SkillDescription",
						"FieldType": "string"
					},
					{
						"FieldName": "SkillCoolDown",
						"FieldType": "float"
					},
					{
						"FieldName": "SkillRange",
						"FieldType": "float"
					},
					{
						"FieldName": "Action",
						"FieldType": "string"
					},
					{
						"FieldName": "TimeCharge",
						"FieldType": "int32"
					},
					{
						"FieldName": "Audio",
						"FieldType": "string"
					},
					{
						"FieldName": "IsDeath",
						"FieldType": "bool"
					},
					{
						"FieldName": "PassiveSkillEffect",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "ActiveSkillEffect",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "MapEventTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MapEventTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "MapEventType",
						"FieldType": "MapEventType = Monster"
					},
					{
						"FieldName": "Monster",
						"FieldType": "int32"
					},
					{
						"FieldName": "Prop",
						"FieldType": "int32"
					},
					{
						"FieldName": "Buff",
						"FieldType": "int32"
					},
					{
						"FieldName": "Obstacle",
						"FieldType": "int32"
					},
					{
						"FieldName": "Hero",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "MapRefreshMonsterEventTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MapRefreshMonsterEventTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Chapters",
						"FieldType": "int32"
					},
					{
						"FieldName": "Levels",
						"FieldType": "int32"
					},
					{
						"FieldName": "Wave",
						"FieldType": "int32"
					},
					{
						"FieldName": "SpeedRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "HpRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "AtkRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "DefRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "Reversionary",
						"FieldType": "int32"
					},
					{
						"FieldName": "RefreshType",
						"FieldType": "MonsterRefreshType = InitialRefresh"
					},
					{
						"FieldName": "RefreshParamDelayTime",
						"FieldType": "[float]"
					},
					{
						"FieldName": "RefreshParamDeathCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "MonsterId",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "MonsterCnt",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Weights",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Intervals",
						"FieldType": "[float]"
					},
					{
						"FieldName": "Habit1",
						"FieldType": "[float]"
					},
					{
						"FieldName": "Habit2",
						"FieldType": "[float]"
					},
					{
						"FieldName": "Habit3",
						"FieldType": "[float]"
					},
					{
						"FieldName": "Habit4",
						"FieldType": "[float]"
					},
					{
						"FieldName": "Habit5",
						"FieldType": "[float]"
					}
				]
			},
			{
				"FileName": "ModifierTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ModifierTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Hero",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsAllHero",
						"FieldType": "bool"
					},
					{
						"FieldName": "AddBuff",
						"FieldType": "int32"
					},
					{
						"FieldName": "RougeCorrect",
						"FieldType": "[RougeCorrectStr]"
					},
					{
						"FieldName": "RougeCorrectBenefits",
						"FieldType": "[RougeCorrectBenefitsStr]"
					},
					{
						"FieldName": "RougeCorrectPlayerBenefits",
						"FieldType": "RougeCorrectPlayerBenefitsStr"
					}
				]
			},
			{
				"FileName": "MonsterCareerTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MonsterCareerTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "MonsterGrade",
						"FieldType": "MonsterCareerType = Melee"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "MonsterGradeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MonsterGradeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "MonsterGrade",
						"FieldType": "MonsterGrade = Common"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "MonsterPosTypeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MonsterPosTypeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "MonsterPosType",
						"FieldType": "MonsterPosType = Ground"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "MonsterPreviewSchemeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MonsterPreviewSchemeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "MonsterSmallCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "MonsterBigCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "MonsterSmallCordinate",
						"FieldType": "[CordinateStr]"
					},
					{
						"FieldName": "MonsterBigCordinate",
						"FieldType": "[CordinateStr]"
					}
				]
			},
			{
				"FileName": "MonsterSkillTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MonsterSkillTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "SkillCoolDown",
						"FieldType": "float"
					},
					{
						"FieldName": "SkillRange",
						"FieldType": "float"
					},
					{
						"FieldName": "SkillType",
						"FieldType": "SkillType = ActiveSkill"
					},
					{
						"FieldName": "Action",
						"FieldType": "string"
					},
					{
						"FieldName": "TimeCharge",
						"FieldType": "int32"
					},
					{
						"FieldName": "Audio",
						"FieldType": "string"
					},
					{
						"FieldName": "PassiveSkillEffect",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "ActiveSkillEffect",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "MonsterTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MonsterTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "MonsterType",
						"FieldType": "int32"
					},
					{
						"FieldName": "MonsterLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "DropGroupId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Power",
						"FieldType": "int32"
					},
					{
						"FieldName": "Hp",
						"FieldType": "int32"
					},
					{
						"FieldName": "Atk",
						"FieldType": "int32"
					},
					{
						"FieldName": "Def",
						"FieldType": "int32"
					},
					{
						"FieldName": "CritChance",
						"FieldType": "float"
					},
					{
						"FieldName": "CritDmgUpPer",
						"FieldType": "float"
					},
					{
						"FieldName": "CdRate",
						"FieldType": "float"
					},
					{
						"FieldName": "DmgUpPer",
						"FieldType": "float"
					},
					{
						"FieldName": "CritResistChance",
						"FieldType": "float"
					},
					{
						"FieldName": "BeCritDmgDownPer",
						"FieldType": "float"
					},
					{
						"FieldName": "BeDmgDownPer",
						"FieldType": "float"
					}
				]
			},
			{
				"FileName": "MonsterTypeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "MonsterTypeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "MonsterGrade",
						"FieldType": "int32"
					},
					{
						"FieldName": "Pos",
						"FieldType": "int32"
					},
					{
						"FieldName": "Career",
						"FieldType": "int32"
					},
					{
						"FieldName": "Immunity",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Weak",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "WeakParam",
						"FieldType": "[string]"
					},
					{
						"FieldName": "Resist",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "ResistParam",
						"FieldType": "[string]"
					},
					{
						"FieldName": "Feat",
						"FieldType": "[string]"
					},
					{
						"FieldName": "FeatParam",
						"FieldType": "[string]"
					},
					{
						"FieldName": "Skill",
						"FieldType": "string"
					},
					{
						"FieldName": "SkillParam",
						"FieldType": "[string]"
					},
					{
						"FieldName": "MonsterSkill",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "BallisticInterception",
						"FieldType": "bool"
					},
					{
						"FieldName": "BallisticDeviation",
						"FieldType": "bool"
					},
					{
						"FieldName": "BallisticEvasionChance",
						"FieldType": "float"
					},
					{
						"FieldName": "RepelRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "ClaimingDistance",
						"FieldType": "float"
					},
					{
						"FieldName": "HeroRec",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Habit",
						"FieldType": "[float]"
					},
					{
						"FieldName": "MoveSpeedTD",
						"FieldType": "float"
					},
					{
						"FieldName": "MoveSpeedPK",
						"FieldType": "float"
					},
					{
						"FieldName": "CollisionRadius",
						"FieldType": "float"
					},
					{
						"FieldName": "InManual",
						"FieldType": "bool"
					},
					{
						"FieldName": "InManualOrder",
						"FieldType": "int32"
					},
					{
						"FieldName": "UnlockReward",
						"FieldType": "RewardKVS"
					},
					{
						"FieldName": "PrefabPass",
						"FieldType": "string"
					},
					{
						"FieldName": "MonsterSpine",
						"FieldType": "string"
					},
					{
						"FieldName": "MonsterImage",
						"FieldType": "string"
					},
					{
						"FieldName": "MonsterImageBottom",
						"FieldType": "string"
					},
					{
						"FieldName": "ModelHeight",
						"FieldType": "float"
					},
					{
						"FieldName": "ManualScaleRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "PreviewScaleRatio",
						"FieldType": "float"
					},
					{
						"FieldName": "MonsterDeathAudio",
						"FieldType": "string"
					},
					{
						"FieldName": "MoveSlowlyAction",
						"FieldType": "string"
					},
					{
						"FieldName": "MoveSlowlySpeedRange",
						"FieldType": "[float]"
					},
					{
						"FieldName": "MoveSlowlySpeed",
						"FieldType": "float"
					},
					{
						"FieldName": "MoveFastAction",
						"FieldType": "string"
					},
					{
						"FieldName": "MoveFastSpeedRange",
						"FieldType": "[float]"
					},
					{
						"FieldName": "MoveFastSpeed",
						"FieldType": "float"
					}
				]
			},
			{
				"FileName": "NewbieTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "NewbieTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Valid",
						"FieldType": "bool"
					},
					{
						"FieldName": "Goto",
						"FieldType": "int32"
					},
					{
						"FieldName": "Force",
						"FieldType": "bool"
					},
					{
						"FieldName": "BIName",
						"FieldType": "string"
					},
					{
						"FieldName": "Track",
						"FieldType": "int32"
					},
					{
						"FieldName": "TrackStep",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "NpcDialogueTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "NpcDialogueTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "NpcName",
						"FieldType": "string"
					},
					{
						"FieldName": "NpcImage",
						"FieldType": "string"
					},
					{
						"FieldName": "NpcSpine",
						"FieldType": "string"
					},
					{
						"FieldName": "NpcPosition",
						"FieldType": "int32"
					},
					{
						"FieldName": "NpcDialogue",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "PhotovoltaicTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "PhotovoltaicTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "EnergyType",
						"FieldType": "int32"
					},
					{
						"FieldName": "EnergyTime",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "EnergyCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "EnergyLimit",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "PresetsTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "PresetsTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "ItemId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Cnt",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "RankMainTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "RankMainTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "RankName",
						"FieldType": "string"
					},
					{
						"FieldName": "RankLen",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "RankRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "RankRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reward",
						"FieldType": "RewardKVS"
					}
				]
			},
			{
				"FileName": "RougeNameCn.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "RougeNameCn",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "RougeRefreshTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "RougeRefreshTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "AdFreeRefreshCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "DiamondRefreshCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "DiamondRefreshCost",
						"FieldType": "CostKVS"
					}
				]
			},
			{
				"FileName": "RougeTabEffectTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "RougeTabEffectTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Hero",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsAllHero",
						"FieldType": "bool"
					},
					{
						"FieldName": "RougeCorrect",
						"FieldType": "[RougeCorrectStr]"
					},
					{
						"FieldName": "RougeCorrectBenefits",
						"FieldType": "[RougeCorrectBenefitsStr]"
					}
				]
			},
			{
				"FileName": "RougeTabGroupRandomTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "RougeTabGroupRandomTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroDeployedCnt",
						"FieldType": "int32"
					},
					{
						"FieldName": "Chance",
						"FieldType": "float"
					}
				]
			},
			{
				"FileName": "RougeTabGroupTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "RougeTabGroupTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "HeroSkillGroup",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsShowCd",
						"FieldType": "bool"
					},
					{
						"FieldName": "IsShowDuration",
						"FieldType": "bool"
					},
					{
						"FieldName": "Hero",
						"FieldType": "int32"
					},
					{
						"FieldName": "Weight",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "RougeTabNewbieTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "RougeTabNewbieTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Chapter",
						"FieldType": "int32"
					},
					{
						"FieldName": "Count",
						"FieldType": "int32"
					},
					{
						"FieldName": "RougeTabGroup",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RecommendPos",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "RougeTabTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "RougeTabTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "IsSenior",
						"FieldType": "bool"
					},
					{
						"FieldName": "IsPassive",
						"FieldType": "bool"
					},
					{
						"FieldName": "IsSurplus",
						"FieldType": "bool"
					},
					{
						"FieldName": "IsRecovery",
						"FieldType": "bool"
					},
					{
						"FieldName": "IsTree",
						"FieldType": "bool"
					},
					{
						"FieldName": "ShowGroup",
						"FieldType": "int32"
					},
					{
						"FieldName": "RougeTabGroup",
						"FieldType": "int32"
					},
					{
						"FieldName": "ReqHeroGeneLevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "ReqRougeTab",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "ReqRougeTabCnt",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RougeTabType",
						"FieldType": "RougeTabType = EffectTab"
					},
					{
						"FieldName": "RougeTabUnlock",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RougeTabEffect",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Weight",
						"FieldType": "int32"
					},
					{
						"FieldName": "WeightCoef",
						"FieldType": "int32"
					},
					{
						"FieldName": "Limit",
						"FieldType": "int32"
					},
					{
						"FieldName": "MutexGroup",
						"FieldType": "int32"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "ShortDesc",
						"FieldType": "string"
					},
					{
						"FieldName": "DescParam",
						"FieldType": "[string]"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "RougeWeightCoef.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "RougeWeightCoef",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": null
			},
			{
				"FileName": "SelectChestGroupTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "SelectChestGroupTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Item",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "SevenDayTasksScoreTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "SevenDayTasksScoreTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "ScoreType",
						"FieldType": "int32"
					},
					{
						"FieldName": "ScoreValue",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "Day7Reward",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "SevenDayTasksTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "SevenDayTasksTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "ReqFunction",
						"FieldType": "int32"
					},
					{
						"FieldName": "Condition",
						"FieldType": "int32"
					},
					{
						"FieldName": "Day",
						"FieldType": "int32"
					},
					{
						"FieldName": "TaskId",
						"FieldType": "int32"
					},
					{
						"FieldName": "TaskType",
						"FieldType": "TaskType = Login"
					},
					{
						"FieldName": "Formula",
						"FieldType": "string"
					},
					{
						"FieldName": "Value",
						"FieldType": "int32"
					},
					{
						"FieldName": "GoTo",
						"FieldType": "int32"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Param",
						"FieldType": "string"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "ShopTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "ShopTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Type",
						"FieldType": "ShopType = GuildShop"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "PurchaseLimit",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "CurrencyShow",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "Sign7Table.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "Sign7Table",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Turn",
						"FieldType": "int32"
					},
					{
						"FieldName": "Day",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "SkillDmgTypeTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "SkillDmgTypeTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "SkillDmgType",
						"FieldType": "SkillDmgType = Electrical"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "NameColor",
						"FieldType": "string"
					},
					{
						"FieldName": "Desc",
						"FieldType": "string"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "WeakDesc",
						"FieldType": "string"
					},
					{
						"FieldName": "WeakImage",
						"FieldType": "string"
					},
					{
						"FieldName": "ResistDesc",
						"FieldType": "string"
					},
					{
						"FieldName": "ResistImage",
						"FieldType": "string"
					},
					{
						"FieldName": "ImmuneDesc",
						"FieldType": "string"
					},
					{
						"FieldName": "ImmuneImage",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "TowerAILevelTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "TowerAILevelTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "AI",
						"FieldType": "int32"
					},
					{
						"FieldName": "AILevel",
						"FieldType": "int32"
					},
					{
						"FieldName": "Power",
						"FieldType": "int32"
					},
					{
						"FieldName": "Hp",
						"FieldType": "int32"
					},
					{
						"FieldName": "Atk",
						"FieldType": "int32"
					},
					{
						"FieldName": "Def",
						"FieldType": "int32"
					},
					{
						"FieldName": "MoveSpeed",
						"FieldType": "float"
					},
					{
						"FieldName": "CritChance",
						"FieldType": "float"
					},
					{
						"FieldName": "CritDmgUpPer",
						"FieldType": "float"
					},
					{
						"FieldName": "AtkUpPer",
						"FieldType": "float"
					},
					{
						"FieldName": "DmgUpPer",
						"FieldType": "float"
					},
					{
						"FieldName": "CritResistChance",
						"FieldType": "float"
					},
					{
						"FieldName": "BeCritDmgDownPer",
						"FieldType": "float"
					},
					{
						"FieldName": "BeDmgDownPer",
						"FieldType": "float"
					},
					{
						"FieldName": "DefUpPer",
						"FieldType": "float"
					},
					{
						"FieldName": "HpUpPer",
						"FieldType": "float"
					},
					{
						"FieldName": "CdRate",
						"FieldType": "float"
					}
				]
			},
			{
				"FileName": "TowerAITable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "TowerAITable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "AI",
						"FieldType": "int32"
					}
				]
			},
			{
				"FileName": "TowerTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "TowerTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Shelter",
						"FieldType": "int32"
					},
					{
						"FieldName": "Name",
						"FieldType": "string"
					},
					{
						"FieldName": "Layer",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsReward",
						"FieldType": "bool"
					},
					{
						"FieldName": "AI",
						"FieldType": "[TowerAIStr]"
					},
					{
						"FieldName": "RewardType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "RewardValue",
						"FieldType": "[int32]"
					}
				]
			},
			{
				"FileName": "TurnRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "TurnRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Turn",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reward",
						"FieldType": "RewardKVS"
					},
					{
						"FieldName": "Quality",
						"FieldType": "int32"
					},
					{
						"FieldName": "Order",
						"FieldType": "int32"
					},
					{
						"FieldName": "Weight",
						"FieldType": "float"
					},
					{
						"FieldName": "DisplayChance",
						"FieldType": "float"
					}
				]
			},
			{
				"FileName": "TurnScoreRewardTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "TurnScoreRewardTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Turn",
						"FieldType": "int32"
					},
					{
						"FieldName": "Reward",
						"FieldType": "[RewardKVS]"
					}
				]
			},
			{
				"FileName": "TurnTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "TurnTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "DailyFreeTimesLimit",
						"FieldType": "int32"
					},
					{
						"FieldName": "Bet",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "DrawCostType",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "DrawCostValue",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "DrawCostDiamdondCnt",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Help",
						"FieldType": "int32"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Limit",
						"FieldType": "int32"
					},
					{
						"FieldName": "LimitDesc",
						"FieldType": "string"
					}
				]
			},
			{
				"FileName": "VehicleTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "VehicleTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "Power",
						"FieldType": "int32"
					},
					{
						"FieldName": "Hp",
						"FieldType": "int32"
					},
					{
						"FieldName": "Atk",
						"FieldType": "int32"
					},
					{
						"FieldName": "Def",
						"FieldType": "int32"
					},
					{
						"FieldName": "VerticalMoveSpeed",
						"FieldType": "float"
					},
					{
						"FieldName": "CritChance",
						"FieldType": "float"
					},
					{
						"FieldName": "CritDmgUpPer",
						"FieldType": "float"
					},
					{
						"FieldName": "CdRate",
						"FieldType": "float"
					},
					{
						"FieldName": "DmgUpPer",
						"FieldType": "float"
					},
					{
						"FieldName": "CritResistChance",
						"FieldType": "float"
					},
					{
						"FieldName": "BeCritDmgDownPer",
						"FieldType": "float"
					},
					{
						"FieldName": "BeDmgDownPer",
						"FieldType": "float"
					}
				]
			},
			{
				"FileName": "VipFreeExpTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "VipFreeExpTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Day",
						"FieldType": "int32"
					},
					{
						"FieldName": "Exp",
						"FieldType": "int32"
					},
					{
						"FieldName": "IsMax",
						"FieldType": "bool"
					}
				]
			},
			{
				"FileName": "VipTable.fbs",
				"ConstTable": false,
				"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
				"TableName": "VipTable",
				"Key": {
					"FieldName": "Id",
					"FieldType": "int32"
				},
				"Fields": [
					{
						"FieldName": "StringId",
						"FieldType": "string"
					},
					{
						"FieldName": "Level",
						"FieldType": "int32"
					},
					{
						"FieldName": "Exp",
						"FieldType": "int32"
					},
					{
						"FieldName": "Image",
						"FieldType": "string"
					},
					{
						"FieldName": "Title",
						"FieldType": "string"
					},
					{
						"FieldName": "Benefits",
						"FieldType": "[int32]"
					},
					{
						"FieldName": "BenefitsValue",
						"FieldType": "[float]"
					},
					{
						"FieldName": "Desc",
						"FieldType": "[DescStr]"
					},
					{
						"FieldName": "FreeGift",
						"FieldType": "[RewardKVS]"
					},
					{
						"FieldName": "FreeBg",
						"FieldType": "string"
					},
					{
						"FieldName": "FreeIcon",
						"FieldType": "string"
					},
					{
						"FieldName": "IapPackageId",
						"FieldType": "int32"
					},
					{
						"FieldName": "Limit",
						"FieldType": "PurchaseLimitType = DailyLimit"
					},
					{
						"FieldName": "Times",
						"FieldType": "int32"
					}
				]
			}
		],
		"ConstTableDataList": null,
		"ConstTableList": {
			"FileName": "consts.lua",
			"ConstTables": null
		}
	}`
var (
	FlatBufferConstTableTpl = `{{.TitleForCodeGen}}
include "enums.fbs";
include "structs.fbs";

namespace cfg.code.test;

table {{.TableName}} {
{{- range .Fields }}
	{{.FieldName}}:{{.FieldType}};
{{- end }}
}

root_type {{.TableName}};
`
	FlatBufferEnumsTpl = `{{.TitleForCodeGen}}
namespace cfg.code.test;
{{ range .Enums }}
enum {{.EnumName}}:int32 {
{{- range .Cases }}
	{{.CaseName}} = {{.CaseValue}},
{{- end }}
}
{{ end }}
`
	FlatBufferPlusConstTableTpl = `{{.TitleForCodeGen}}
include "enums.fbs";
include "structs.fbs";

namespace cfg.code.test;

table {{.TableName}}(consts) {
{{- range .Fields }}
	{{.FieldName}}:{{.FieldType}};
{{- end }}
}

root_type {{.TableName}};
`
	FlatBufferPlusTableTpl = `{{.TitleForCodeGen}}
include "enums.fbs";
include "structs.fbs";

namespace cfg.code.test;

table {{.TableName}}Cfg {
	{{.Key.FieldName}}:{{.Key.FieldType}}(key);
{{- range .Fields }}
	{{.FieldName}}:{{.FieldType}};
{{- end }}
}

table {{.TableName}}Cfgs {
	Cfgs:[{{.TableName}}Cfg];
}

root_type {{.TableName}}Cfgs;
`
	FlatBufferStructsTpl = `{{.TitleForCodeGen}}
include "enums.fbs";

namespace cfg.code.test;
{{ range .Structs }}
table {{.StructName}} {
{{- range .Fields }}
	{{.FieldName}}:{{.FieldType}};
{{- end }}
}
{{- end }}
`
	FlatBufferTableTpl = `{{.TitleForCodeGen}}
include "enums.fbs";
include "structs.fbs";

namespace cfg.code.test;

table {{.TableName}}ConfigCell {
	{{.Key.FieldName}}:{{.Key.FieldType}};
{{- range .Fields }}
	{{.FieldName}}:{{.FieldType}};
{{- end }}
}

table {{.TableName}}Config {
	Cells:[{{.TableName}}ConfigCell];
}

root_type {{.TableName}}Config;
`
	LuaConstTableListTpl = `-- Code generated by Cfgo. DO NOT EDIT.
local CfgConst = {
{{- range .ConstTables }}
	"{{.}}",
{{ end -}}
}
return CfgConst
`
	TxtEnumListTpl = `{{range .Enums}}{{.EnumName}}
{{end -}}`
	TxtStructListTpl = `{{range .Structs}}{{.StructName}}
{{end -}}`
)
