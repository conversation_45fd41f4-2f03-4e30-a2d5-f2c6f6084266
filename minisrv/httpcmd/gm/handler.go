package gm

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/g"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/handler/gmApi"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"github.com/julienschmidt/httprouter"
	"net/http"
	"sync"
	"time"
)

var (
	// 简单的内存存储管理员账号，实际应该使用数据库
	adminUsers = map[string]string{
		"admin": "admin123", // 用户名: 密码
	}

	// 会话存储
	sessions = struct {
		sync.RWMutex
		data map[string]sessionData
	}{
		data: make(map[string]sessionData),
	}
)

type sessionData struct {
	Username string
	ExpireAt time.Time
}

type loginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type loginResponse struct {
	Success bool   `json:"success"`
	Token   string `json:"token,omitempty"`
	Message string `json:"message,omitempty"`
}

// RegisterRoutes 注册GM相关的路由
func RegisterRoutes(router *httprouter.Router) {
	router.GET("/gm/", handleGMPage)
	router.GET("/gm/login", handleGMPage)
	router.POST("/gm/login", handleLogin)
	router.GET("/gm/api/verify-token", handleVerifyToken) // 新增
	router.GET("/gm/country", handleCountry)
	router.GET("/gm/users", handleUsersPage) // 新增用户管理页面
	router.GET("/gm/items", handleItemsPage) // 新增道具管理页面
	//活动
	router.GET("/gm/activity", handleActivity)
	//客户端版本管理
	router.GET("/gm/version", handleVersionPage)
	router.GET("/gm/huatuo_release", handleHuatuoRelease)
	gmApi.RegisterRoutes(router)
}

func handleVersionPage(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	http.ServeFile(w, r, g.RootDir+"/static/gm/version.html")
}

func handleActivity(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	http.ServeFile(w, r, g.RootDir+"/static/gm/activity.html")
}

func handleCountry(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	http.ServeFile(w, r, g.RootDir+"/static/gm/country.html")
}

func handleUsersPage(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	http.ServeFile(w, r, g.RootDir+"/static/gm/user.html")
}

func handleItemsPage(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	http.ServeFile(w, r, g.RootDir+"/static/gm/items.html")
}

func handleHuatuoRelease(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	http.ServeFile(w, r, g.RootDir+"/static/gm/huatuo_release.html")
}

// handleGMPage 处理GM页面请求
func handleGMPage(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	// 根据请求路径返回对应页面
	if r.URL.Path == "/gm/login" {
		http.ServeFile(w, r, g.RootDir+"/static/gm/login.html")
		return
	}

	// 对于主页面，直接返回，让前端 JS 处理认证逻辑
	http.ServeFile(w, r, g.RootDir+"/static/gm/index.html")
}

// handleLogin 处理登录请求
func handleLogin(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	var req loginRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}

	// 验证用户名和密码
	if storedPassword, exists := adminUsers[req.Username]; !exists || storedPassword != req.Password {
		json.NewEncoder(w).Encode(loginResponse{
			Success: false,
			Message: "Invalid username or password",
		})
		return
	}

	// 生成会话token
	token := generateToken()
	sessions.Lock()
	sessions.data[token] = sessionData{
		Username: req.Username,
		ExpireAt: time.Now().Add(24 * time.Hour),
	}
	sessions.Unlock()

	json.NewEncoder(w).Encode(loginResponse{
		Success: true,
		Token:   token,
	})
}

// 验证会话
func validateSession(token string) bool {
	sessions.RLock()
	defer sessions.RUnlock()

	if session, exists := sessions.data[token]; exists {
		if time.Now().Before(session.ExpireAt) {
			return true
		}
		// 清理过期会话
		delete(sessions.data, token)
	}
	return false
}

// 生成随机token
func generateToken() string {
	// 使用随机字节生成 token
	b := make([]byte, 32)
	if _, err := rand.Read(b); err != nil {
		return ""
	}
	return hex.EncodeToString(b)
}

// handleVerifyToken 验证token
func handleVerifyToken(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	token := r.Header.Get("X-GM-Token")
	if token == "" || !validateSession(token) {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}
	w.WriteHeader(http.StatusOK)
}
