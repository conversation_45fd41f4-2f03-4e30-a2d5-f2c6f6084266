// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type RougeTabEffectTableCfg struct {
	Id                   int32                      `json:"Id"`                   // Id
	Hero                 int32                      `json:"Hero"`                 // 对应英雄
	HeroRef              *HeroTableCfg              `json:"-"`                    // 对应英雄
	IsAllHero            bool                       `json:"IsAllHero"`            // 针对所有英雄生效
	RougeCorrect         []*RougeCorrectStr         `json:"RougeCorrect"`         // 修改属性
	RougeCorrectBenefits []*RougeCorrectBenefitsStr `json:"RougeCorrectBenefits"` // 修改Benefits
}

func NewRougeTabEffectTableCfg() *RougeTabEffectTableCfg {
	return &RougeTabEffectTableCfg{
		Id:                   0,
		Hero:                 0,
		HeroRef:              nil,
		IsAllHero:            false,
		RougeCorrect:         []*RougeCorrectStr{},
		RougeCorrectBenefits: []*RougeCorrectBenefitsStr{},
	}
}

func NewMockRougeTabEffectTableCfg() *RougeTabEffectTableCfg {
	return &RougeTabEffectTableCfg{
		Id:                   0,
		Hero:                 0,
		HeroRef:              nil,
		IsAllHero:            false,
		RougeCorrect:         []*RougeCorrectStr{NewMockRougeCorrectStr()},
		RougeCorrectBenefits: []*RougeCorrectBenefitsStr{NewMockRougeCorrectBenefitsStr()},
	}
}

type RougeTabEffectTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*RougeTabEffectTableCfg
	localIds         map[int32]struct{}
}

func NewRougeTabEffectTable(configs *Configs) *RougeTabEffectTable {
	return &RougeTabEffectTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*RougeTabEffectTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *RougeTabEffectTable) Get(key int32) *RougeTabEffectTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *RougeTabEffectTable) GetAll() map[int32]*RougeTabEffectTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *RougeTabEffectTable) put(key int32, value *RougeTabEffectTableCfg, local bool) *RougeTabEffectTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *RougeTabEffectTable) putFromInheritedTable(key int32, value *RougeTabEffectTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[RougeTabEffectTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *RougeTabEffectTable) Put(key int32, value *RougeTabEffectTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[RougeTabEffectTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *RougeTabEffectTable) PutAll(m map[int32]*RougeTabEffectTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *RougeTabEffectTable) Range(f func(v *RougeTabEffectTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *RougeTabEffectTable) Filter(filterFuncs ...func(v *RougeTabEffectTableCfg) bool) map[int32]*RougeTabEffectTableCfg {
	filtered := map[int32]*RougeTabEffectTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *RougeTabEffectTable) FilterSlice(filterFuncs ...func(v *RougeTabEffectTableCfg) bool) []*RougeTabEffectTableCfg {
	filtered := []*RougeTabEffectTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *RougeTabEffectTable) FilterKeys(filterFuncs ...func(v *RougeTabEffectTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *RougeTabEffectTable) satisfied(v *RougeTabEffectTableCfg, filterFuncs ...func(v *RougeTabEffectTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *RougeTabEffectTable) setupIndexes() error {
	return nil
}

func (t *RougeTabEffectTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *RougeTabEffectTableCfg) bindRefs(c *Configs) {
	r.HeroRef = c.HeroTable.Get(r.Hero)
	for _, e := range r.RougeCorrect {
		e.bindRefs(c)
	}
	for _, e := range r.RougeCorrectBenefits {
		e.bindRefs(c)
	}
}

func (t *RougeTabEffectTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[RougeTabEffectTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewRougeTabEffectTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [RougeTabEffectTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[RougeTabEffectTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// Hero
		if record[t.getIndexInCsv("Hero")] == "" {
			recordCfg.Hero = 0
		} else {
			var err error
			recordCfg.Hero, err = configs.HeroTable.getIdByRef(record[t.getIndexInCsv("Hero")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [RougeTabEffectTable]unmarshal csv record failed, varName=Hero, type=ref@HeroTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Hero")], err)
				} else {
					return fmt.Errorf("[RougeTabEffectTable]unmarshal csv record failed, varName=Hero, type=ref@HeroTable, value=%s, err:[%s]", record[t.getIndexInCsv("Hero")], err)
				}
			}
		}
		// IsAllHero
		{
			if record[t.getIndexInCsv("IsAllHero")] == "" {
				recordCfg.IsAllHero = false
			} else {
				var err error
				recordCfg.IsAllHero, err = strconv.ParseBool(record[t.getIndexInCsv("IsAllHero")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [RougeTabEffectTable]unmarshal csv record failed, varName=IsAllHero, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("IsAllHero")], err)
					} else {
						return fmt.Errorf("[RougeTabEffectTable]unmarshal csv record failed, varName=IsAllHero, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("IsAllHero")], err)
					}
				}
			}
		}
		// RougeCorrect
		{
			cfgoMeetNilForRougeCorrectOfRecordCfg := false
			// element 0 of RougeCorrect
			if !cfgoMeetNilForRougeCorrectOfRecordCfg {
				cfgoMeetNilForRougeCorrectOfRecordCfg = true
				var cfgoElemOfRougeCorrectOfRecordCfg *RougeCorrectStr = NewRougeCorrectStr()
				{
					if record[t.getIndexInCsv("RougeCorrect1CorrectType")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						cfgoEnum, err := parseCorrectType(record[t.getIndexInCsv("RougeCorrect1CorrectType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect1CorrectType")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectType, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect1CorrectType")], err)
							}
						}
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectType = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect1CorrectSkillAttr")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, err = configs.HeroSkillAttrTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect1CorrectSkillAttr")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillAttrTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect1CorrectSkillAttr")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillAttrTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect1CorrectSkillAttr")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect1CorrectSkillValue")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("RougeCorrect1CorrectSkillValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect1CorrectSkillValue")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect1CorrectSkillValue")], err)
							}
						}
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect1CorrectSkillModel")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, err = configs.BattleModelTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect1CorrectSkillModel")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@BattleModelTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect1CorrectSkillModel")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@BattleModelTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect1CorrectSkillModel")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect1CorrectSkillBuff")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, err = configs.HeroSkillBuffTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect1CorrectSkillBuff")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect1CorrectSkillBuff")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect1CorrectSkillBuff")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect1CorrectSkillEffect")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, err = configs.HeroSkillEffectTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect1CorrectSkillEffect")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect1CorrectSkillEffect")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect1CorrectSkillEffect")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForRougeCorrectOfRecordCfg {
					recordCfg.RougeCorrect = append(recordCfg.RougeCorrect, cfgoElemOfRougeCorrectOfRecordCfg)
				}
			}
			// element 1 of RougeCorrect
			if !cfgoMeetNilForRougeCorrectOfRecordCfg {
				cfgoMeetNilForRougeCorrectOfRecordCfg = true
				var cfgoElemOfRougeCorrectOfRecordCfg *RougeCorrectStr = NewRougeCorrectStr()
				{
					if record[t.getIndexInCsv("RougeCorrect2CorrectType")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						cfgoEnum, err := parseCorrectType(record[t.getIndexInCsv("RougeCorrect2CorrectType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect2CorrectType")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectType, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect2CorrectType")], err)
							}
						}
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectType = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect2CorrectSkillAttr")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, err = configs.HeroSkillAttrTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect2CorrectSkillAttr")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillAttrTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect2CorrectSkillAttr")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillAttrTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect2CorrectSkillAttr")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect2CorrectSkillValue")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("RougeCorrect2CorrectSkillValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect2CorrectSkillValue")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect2CorrectSkillValue")], err)
							}
						}
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect2CorrectSkillModel")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, err = configs.BattleModelTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect2CorrectSkillModel")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@BattleModelTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect2CorrectSkillModel")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@BattleModelTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect2CorrectSkillModel")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect2CorrectSkillBuff")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, err = configs.HeroSkillBuffTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect2CorrectSkillBuff")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect2CorrectSkillBuff")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect2CorrectSkillBuff")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect2CorrectSkillEffect")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, err = configs.HeroSkillEffectTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect2CorrectSkillEffect")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect2CorrectSkillEffect")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect2CorrectSkillEffect")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForRougeCorrectOfRecordCfg {
					recordCfg.RougeCorrect = append(recordCfg.RougeCorrect, cfgoElemOfRougeCorrectOfRecordCfg)
				}
			}
			// element 2 of RougeCorrect
			if !cfgoMeetNilForRougeCorrectOfRecordCfg {
				cfgoMeetNilForRougeCorrectOfRecordCfg = true
				var cfgoElemOfRougeCorrectOfRecordCfg *RougeCorrectStr = NewRougeCorrectStr()
				{
					if record[t.getIndexInCsv("RougeCorrect3CorrectType")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						cfgoEnum, err := parseCorrectType(record[t.getIndexInCsv("RougeCorrect3CorrectType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect3CorrectType")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectType, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect3CorrectType")], err)
							}
						}
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectType = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect3CorrectSkillAttr")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, err = configs.HeroSkillAttrTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect3CorrectSkillAttr")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillAttrTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect3CorrectSkillAttr")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillAttrTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect3CorrectSkillAttr")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect3CorrectSkillValue")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("RougeCorrect3CorrectSkillValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect3CorrectSkillValue")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect3CorrectSkillValue")], err)
							}
						}
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect3CorrectSkillModel")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, err = configs.BattleModelTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect3CorrectSkillModel")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@BattleModelTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect3CorrectSkillModel")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@BattleModelTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect3CorrectSkillModel")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect3CorrectSkillBuff")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, err = configs.HeroSkillBuffTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect3CorrectSkillBuff")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect3CorrectSkillBuff")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect3CorrectSkillBuff")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect3CorrectSkillEffect")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, err = configs.HeroSkillEffectTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect3CorrectSkillEffect")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect3CorrectSkillEffect")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect3CorrectSkillEffect")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForRougeCorrectOfRecordCfg {
					recordCfg.RougeCorrect = append(recordCfg.RougeCorrect, cfgoElemOfRougeCorrectOfRecordCfg)
				}
			}
			// element 3 of RougeCorrect
			if !cfgoMeetNilForRougeCorrectOfRecordCfg {
				cfgoMeetNilForRougeCorrectOfRecordCfg = true
				var cfgoElemOfRougeCorrectOfRecordCfg *RougeCorrectStr = NewRougeCorrectStr()
				{
					if record[t.getIndexInCsv("RougeCorrect4CorrectType")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						cfgoEnum, err := parseCorrectType(record[t.getIndexInCsv("RougeCorrect4CorrectType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect4CorrectType")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectType, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect4CorrectType")], err)
							}
						}
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectType = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect4CorrectSkillAttr")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, err = configs.HeroSkillAttrTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect4CorrectSkillAttr")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillAttrTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect4CorrectSkillAttr")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillAttrTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect4CorrectSkillAttr")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect4CorrectSkillValue")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("RougeCorrect4CorrectSkillValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect4CorrectSkillValue")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect4CorrectSkillValue")], err)
							}
						}
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect4CorrectSkillModel")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, err = configs.BattleModelTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect4CorrectSkillModel")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@BattleModelTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect4CorrectSkillModel")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@BattleModelTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect4CorrectSkillModel")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect4CorrectSkillBuff")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, err = configs.HeroSkillBuffTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect4CorrectSkillBuff")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect4CorrectSkillBuff")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect4CorrectSkillBuff")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect4CorrectSkillEffect")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, err = configs.HeroSkillEffectTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect4CorrectSkillEffect")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect4CorrectSkillEffect")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect4CorrectSkillEffect")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForRougeCorrectOfRecordCfg {
					recordCfg.RougeCorrect = append(recordCfg.RougeCorrect, cfgoElemOfRougeCorrectOfRecordCfg)
				}
			}
			// element 4 of RougeCorrect
			if !cfgoMeetNilForRougeCorrectOfRecordCfg {
				cfgoMeetNilForRougeCorrectOfRecordCfg = true
				var cfgoElemOfRougeCorrectOfRecordCfg *RougeCorrectStr = NewRougeCorrectStr()
				{
					if record[t.getIndexInCsv("RougeCorrect5CorrectType")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						cfgoEnum, err := parseCorrectType(record[t.getIndexInCsv("RougeCorrect5CorrectType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect5CorrectType")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectType, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect5CorrectType")], err)
							}
						}
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectType = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect5CorrectSkillAttr")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, err = configs.HeroSkillAttrTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect5CorrectSkillAttr")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillAttrTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect5CorrectSkillAttr")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillAttrTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect5CorrectSkillAttr")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect5CorrectSkillValue")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("RougeCorrect5CorrectSkillValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect5CorrectSkillValue")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect5CorrectSkillValue")], err)
							}
						}
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect5CorrectSkillModel")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, err = configs.BattleModelTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect5CorrectSkillModel")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@BattleModelTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect5CorrectSkillModel")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@BattleModelTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect5CorrectSkillModel")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect5CorrectSkillBuff")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, err = configs.HeroSkillBuffTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect5CorrectSkillBuff")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect5CorrectSkillBuff")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect5CorrectSkillBuff")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect5CorrectSkillEffect")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, err = configs.HeroSkillEffectTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect5CorrectSkillEffect")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect5CorrectSkillEffect")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect5CorrectSkillEffect")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForRougeCorrectOfRecordCfg {
					recordCfg.RougeCorrect = append(recordCfg.RougeCorrect, cfgoElemOfRougeCorrectOfRecordCfg)
				}
			}
			// element 5 of RougeCorrect
			if !cfgoMeetNilForRougeCorrectOfRecordCfg {
				cfgoMeetNilForRougeCorrectOfRecordCfg = true
				var cfgoElemOfRougeCorrectOfRecordCfg *RougeCorrectStr = NewRougeCorrectStr()
				{
					if record[t.getIndexInCsv("RougeCorrect6CorrectType")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						cfgoEnum, err := parseCorrectType(record[t.getIndexInCsv("RougeCorrect6CorrectType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect6CorrectType")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectType, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect6CorrectType")], err)
							}
						}
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectType = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect6CorrectSkillAttr")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, err = configs.HeroSkillAttrTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect6CorrectSkillAttr")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillAttrTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect6CorrectSkillAttr")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillAttrTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillAttr, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect6CorrectSkillAttr")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect6CorrectSkillValue")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("RougeCorrect6CorrectSkillValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect6CorrectSkillValue")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect6CorrectSkillValue")], err)
							}
						}
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillValue = float32(cfgoFloat)
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect6CorrectSkillModel")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, err = configs.BattleModelTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect6CorrectSkillModel")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@BattleModelTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect6CorrectSkillModel")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@BattleModelTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillModel, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect6CorrectSkillModel")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect6CorrectSkillBuff")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, err = configs.HeroSkillBuffTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect6CorrectSkillBuff")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect6CorrectSkillBuff")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillBuffTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillBuff, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect6CorrectSkillBuff")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrect6CorrectSkillEffect")] != "" {
						cfgoMeetNilForRougeCorrectOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, err = configs.HeroSkillEffectTable.getIdByRef(record[t.getIndexInCsv("RougeCorrect6CorrectSkillEffect")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrect6CorrectSkillEffect")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in collection, elemVarName=cfgoElemOfRougeCorrectOfRecordCfg.CorrectSkillEffect, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrect6CorrectSkillEffect")], err)
							}
						}
					}
				}

				if !cfgoMeetNilForRougeCorrectOfRecordCfg {
					recordCfg.RougeCorrect = append(recordCfg.RougeCorrect, cfgoElemOfRougeCorrectOfRecordCfg)
				}
			}

		}
		// RougeCorrectBenefits
		{
			cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg := false
			// element 0 of RougeCorrectBenefits
			if !cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg {
				cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg = true
				var cfgoElemOfRougeCorrectBenefitsOfRecordCfg *RougeCorrectBenefitsStr = NewRougeCorrectBenefitsStr()
				{
					if record[t.getIndexInCsv("RougeCorrectBenefits1CorrectType")] != "" {
						cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg = false
						cfgoEnum, err := parseCorrectType(record[t.getIndexInCsv("RougeCorrectBenefits1CorrectType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrectBenefits1CorrectType")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectType, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrectBenefits1CorrectType")], err)
							}
						}
						cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectType = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrectBenefits1CorrectBenefits")] != "" {
						cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectBenefits, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("RougeCorrectBenefits1CorrectBenefits")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectBenefits, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrectBenefits1CorrectBenefits")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectBenefits, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrectBenefits1CorrectBenefits")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrectBenefits1CorrectSkillValue")] != "" {
						cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("RougeCorrectBenefits1CorrectSkillValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrectBenefits1CorrectSkillValue")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrectBenefits1CorrectSkillValue")], err)
							}
						}
						cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectSkillValue = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg {
					recordCfg.RougeCorrectBenefits = append(recordCfg.RougeCorrectBenefits, cfgoElemOfRougeCorrectBenefitsOfRecordCfg)
				}
			}
			// element 1 of RougeCorrectBenefits
			if !cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg {
				cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg = true
				var cfgoElemOfRougeCorrectBenefitsOfRecordCfg *RougeCorrectBenefitsStr = NewRougeCorrectBenefitsStr()
				{
					if record[t.getIndexInCsv("RougeCorrectBenefits2CorrectType")] != "" {
						cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg = false
						cfgoEnum, err := parseCorrectType(record[t.getIndexInCsv("RougeCorrectBenefits2CorrectType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrectBenefits2CorrectType")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse enum@CorrectType in collection, elemVarName=cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectType, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrectBenefits2CorrectType")], err)
							}
						}
						cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectType = cfgoEnum
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrectBenefits2CorrectBenefits")] != "" {
						cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg = false
						var err error
						cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectBenefits, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("RougeCorrectBenefits2CorrectBenefits")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectBenefits, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrectBenefits2CorrectBenefits")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectBenefits, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrectBenefits2CorrectBenefits")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RougeCorrectBenefits2CorrectSkillValue")] != "" {
						cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("RougeCorrectBenefits2CorrectSkillValue")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RougeCorrectBenefits2CorrectSkillValue")], err)
							} else {
								return fmt.Errorf("[RougeTabEffectTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectSkillValue, value=%s, err:[%s]", record[t.getIndexInCsv("RougeCorrectBenefits2CorrectSkillValue")], err)
							}
						}
						cfgoElemOfRougeCorrectBenefitsOfRecordCfg.CorrectSkillValue = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForRougeCorrectBenefitsOfRecordCfg {
					recordCfg.RougeCorrectBenefits = append(recordCfg.RougeCorrectBenefits, cfgoElemOfRougeCorrectBenefitsOfRecordCfg)
				}
			}

		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [RougeTabEffectTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[RougeTabEffectTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *RougeTabEffectTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "RougeTabEffectTable.csv") && (!strings.HasPrefix(fileName, "RougeTabEffectTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for RougeTabEffectTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[RougeTabEffectTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[RougeTabEffectTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[RougeTabEffectTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[RougeTabEffectTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[RougeTabEffectTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[RougeTabEffectTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[RougeTabEffectTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[RougeTabEffectTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [RougeTabEffectTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *RougeTabEffectTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[RougeTabEffectTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [RougeTabEffectTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *RougeTabEffectTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[RougeTabEffectTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *RougeTabEffectTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[RougeTabEffectTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[RougeTabEffectTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
