{"1": {"Id": 1, "Dungeon": 1, "Levels": 0, "Level": 1, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 11}}, "2": {"Id": 2, "Dungeon": 1, "Levels": 0, "Level": 2, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 15}}, "3": {"Id": 3, "Dungeon": 1, "Levels": 0, "Level": 3, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "4": {"Id": 4, "Dungeon": 1, "Levels": 0, "Level": 4, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 25}}, "5": {"Id": 5, "Dungeon": 1, "Levels": 0, "Level": 5, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 30}}, "6": {"Id": 6, "Dungeon": 1, "Levels": 0, "Level": 6, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 35}}, "7": {"Id": 7, "Dungeon": 1, "Levels": 0, "Level": 7, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 40}}, "8": {"Id": 8, "Dungeon": 1, "Levels": 0, "Level": 8, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 45}}, "9": {"Id": 9, "Dungeon": 1, "Levels": 0, "Level": 9, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 50}}, "10": {"Id": 10, "Dungeon": 1, "Levels": 0, "Level": 10, "IsMax": true, "LevelUpCost": {"CostType": 5, "CostValue": 55}}, "11": {"Id": 11, "Dungeon": 2, "Levels": 0, "Level": 1, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 15}}, "12": {"Id": 12, "Dungeon": 2, "Levels": 0, "Level": 2, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "13": {"Id": 13, "Dungeon": 2, "Levels": 0, "Level": 3, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 25}}, "14": {"Id": 14, "Dungeon": 2, "Levels": 0, "Level": 4, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 30}}, "15": {"Id": 15, "Dungeon": 2, "Levels": 0, "Level": 5, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 35}}, "16": {"Id": 16, "Dungeon": 2, "Levels": 0, "Level": 6, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 40}}, "17": {"Id": 17, "Dungeon": 2, "Levels": 0, "Level": 7, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 46}}, "18": {"Id": 18, "Dungeon": 2, "Levels": 0, "Level": 8, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 50}}, "19": {"Id": 19, "Dungeon": 2, "Levels": 0, "Level": 9, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 55}}, "20": {"Id": 20, "Dungeon": 2, "Levels": 0, "Level": 10, "IsMax": true, "LevelUpCost": {"CostType": 5, "CostValue": 60}}, "21": {"Id": 21, "Dungeon": 3, "Levels": 0, "Level": 1, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 15}}, "22": {"Id": 22, "Dungeon": 3, "Levels": 0, "Level": 2, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "23": {"Id": 23, "Dungeon": 3, "Levels": 0, "Level": 3, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "24": {"Id": 24, "Dungeon": 3, "Levels": 0, "Level": 4, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "25": {"Id": 25, "Dungeon": 3, "Levels": 0, "Level": 5, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "26": {"Id": 26, "Dungeon": 3, "Levels": 0, "Level": 6, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 21}}, "27": {"Id": 27, "Dungeon": 3, "Levels": 0, "Level": 7, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "28": {"Id": 28, "Dungeon": 3, "Levels": 0, "Level": 8, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "29": {"Id": 29, "Dungeon": 3, "Levels": 0, "Level": 9, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "30": {"Id": 30, "Dungeon": 3, "Levels": 0, "Level": 10, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "31": {"Id": 31, "Dungeon": 3, "Levels": 0, "Level": 11, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "32": {"Id": 32, "Dungeon": 3, "Levels": 0, "Level": 12, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "33": {"Id": 33, "Dungeon": 3, "Levels": 0, "Level": 13, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "34": {"Id": 34, "Dungeon": 3, "Levels": 0, "Level": 14, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "35": {"Id": 35, "Dungeon": 3, "Levels": 0, "Level": 15, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "36": {"Id": 36, "Dungeon": 3, "Levels": 0, "Level": 16, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "37": {"Id": 37, "Dungeon": 3, "Levels": 0, "Level": 17, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "38": {"Id": 38, "Dungeon": 3, "Levels": 0, "Level": 18, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "39": {"Id": 39, "Dungeon": 3, "Levels": 0, "Level": 19, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "40": {"Id": 40, "Dungeon": 3, "Levels": 0, "Level": 20, "IsMax": true, "LevelUpCost": {"CostType": 5, "CostValue": 20}}, "10001": {"Id": 10001, "Dungeon": 4, "Levels": 1, "Level": 1, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "10002": {"Id": 10002, "Dungeon": 4, "Levels": 1, "Level": 2, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "10003": {"Id": 10003, "Dungeon": 4, "Levels": 1, "Level": 3, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "10004": {"Id": 10004, "Dungeon": 4, "Levels": 1, "Level": 4, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "10005": {"Id": 10005, "Dungeon": 4, "Levels": 1, "Level": 5, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "20001": {"Id": 20001, "Dungeon": 2, "Levels": 1, "Level": 1, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "20002": {"Id": 20002, "Dungeon": 2, "Levels": 1, "Level": 2, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "20003": {"Id": 20003, "Dungeon": 2, "Levels": 1, "Level": 3, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "20004": {"Id": 20004, "Dungeon": 2, "Levels": 1, "Level": 4, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "20005": {"Id": 20005, "Dungeon": 2, "Levels": 1, "Level": 5, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "30001": {"Id": 30001, "Dungeon": 3, "Levels": 1, "Level": 1, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "30002": {"Id": 30002, "Dungeon": 3, "Levels": 1, "Level": 2, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "30003": {"Id": 30003, "Dungeon": 3, "Levels": 1, "Level": 3, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "30004": {"Id": 30004, "Dungeon": 3, "Levels": 1, "Level": 4, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "30005": {"Id": 30005, "Dungeon": 3, "Levels": 1, "Level": 5, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "40001": {"Id": 40001, "Dungeon": 1, "Levels": 1, "Level": 1, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "40002": {"Id": 40002, "Dungeon": 1, "Levels": 1, "Level": 2, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "40003": {"Id": 40003, "Dungeon": 1, "Levels": 1, "Level": 3, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "40004": {"Id": 40004, "Dungeon": 1, "Levels": 1, "Level": 4, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}, "40005": {"Id": 40005, "Dungeon": 1, "Levels": 1, "Level": 5, "IsMax": false, "LevelUpCost": {"CostType": 5, "CostValue": 10}}}