// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type SummonParamStr struct {
	IsAtkedTrigger bool               `json:"IsAtkedTrigger"` // 被攻击触发
	Cd             float32            `json:"Cd"`             // 冷却，秒
	Limit          int32              `json:"Limit"`          // 次数上限
	Ratio          float32            `json:"Ratio"`          // 攻防血比例
	MonsterId      []int32            `json:"MonsterId"`      // 怪物Id
	MonsterIdRef   []*MonsterTableCfg `json:"-"`              // 怪物Id
	MonsterCnt     []int32            `json:"MonsterCnt"`     // 怪物数量
}

func NewSummonParamStr() *SummonParamStr {
	return &SummonParamStr{
		IsAtkedTrigger: false,
		Cd:             0.0,
		Limit:          0,
		Ratio:          0.0,
		MonsterId:      []int32{},
		MonsterIdRef:   []*MonsterTableCfg{},
		MonsterCnt:     []int32{},
	}
}

func (s *SummonParamStr) bindRefs(c *Configs) {
	for _, e := range s.MonsterId {
		cfgoRefRecord := c.MonsterTable.Get(e)
		s.MonsterIdRef = append(s.MonsterIdRef, cfgoRefRecord)
	}
}
