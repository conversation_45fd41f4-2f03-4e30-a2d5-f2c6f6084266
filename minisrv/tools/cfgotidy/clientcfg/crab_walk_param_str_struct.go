// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

type CrabWalkParamStr struct {
	Ratio                 float32 `json:"Ratio"`                 // 横向速度占比
	HorizontalMoveTimeMin float32 `json:"HorizontalMoveTimeMin"` // 横向移动时间下限，秒
	HorizontalMoveTimeMax float32 `json:"HorizontalMoveTimeMax"` // 横向移动时间上限，秒
}

func NewCrabWalkParamStr() *CrabWalkParamStr {
	return &CrabWalkParamStr{
		Ratio:                 0.0,
		HorizontalMoveTimeMin: 0.0,
		HorizontalMoveTimeMax: 0.0,
	}
}

func NewMockCrabWalkParamStr() *CrabWalkParamStr {
	return &CrabWalkParamStr{
		Ratio:                 0.0,
		HorizontalMoveTimeMin: 0.0,
		HorizontalMoveTimeMax: 0.0,
	}
}

func (s *CrabWalkParamStr) bindRefs(c *Configs) {
}
