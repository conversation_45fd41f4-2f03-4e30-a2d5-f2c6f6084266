package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/srvconf"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 获取manifest信息
func (_ KnightsHandlers) GetManifestHandler(ctx *wctx.Context, req *wrpc.GetManifestRequest) (*wrpc.GetManifestReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		if req.Os == "android" {
			req.Os = "android"
		} else {
			req.Os = "ios"
		}
		if req.ClientVersion == "" {
			req.ClientVersion = "1.0.0"
		}
		kdmSrvConfig := srvconf.GetKdmSrvConfig()
		clientVersion, _ := model.GetClientVersionModel(ctx, req.Os, req.ClientVersion)
		if clientVersion == nil {
			err = kdmerr.SysInvalidArguments.CastErrorf("client version not found")
			return
		}
		cdn := ""
		if kdmSrvConfig != nil {
			if kdmSrvConfig.KdmsrvConf.ClientConfig != nil {
				cdn = kdmSrvConfig.KdmsrvConf.ClientConfig.Cdn
			}
		}
		rawResp = &wrpc.GetManifestReply{
			Result: &wrpc.ManiFest{
				LangUrl:        "http://gog-global-cdn.akamaized.net/gogx/language/guardian/1.0.0/1/language_zh-CN.json",
				AppId:          " gogx.global.prod",
				BundleVersionR: clientVersion.BundleVersionR(),
				BundleVersionG: clientVersion.BundleVersionG(),
				Cdn:            cdn,
			},
		}
	})
	return rawResp.(*wrpc.GetManifestReply), err
}
