// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: datarow.proto

package minirpc

import (
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type ClientTable int32

const (
	ClientTable_None             ClientTable = 0
	ClientTable_AchievementTask  ClientTable = 47
	ClientTable_ActivityTask     ClientTable = 42
	ClientTable_Alliance         ClientTable = 35
	ClientTable_AllianceMember   ClientTable = 36
	ClientTable_AllianceShopBuy  ClientTable = 37
	ClientTable_AllianceTask     ClientTable = 38
	ClientTable_Animal           ClientTable = 21
	ClientTable_BuildingInfo     ClientTable = 7
	ClientTable_ClientVersion    ClientTable = 48
	ClientTable_Consumable       ClientTable = 6
	ClientTable_DailyTask        ClientTable = 16
	ClientTable_Dave             ClientTable = 25
	ClientTable_Dungeon          ClientTable = 22
	ClientTable_FirstCharge      ClientTable = 45
	ClientTable_FunctionOpen     ClientTable = 40
	ClientTable_GlobalStageLevel ClientTable = 31
	ClientTable_GrowthFund       ClientTable = 44
	ClientTable_GrowthTask       ClientTable = 15
	ClientTable_HeroInfo         ClientTable = 3
	ClientTable_HeroLottery      ClientTable = 11
	ClientTable_HeroSkill        ClientTable = 24
	ClientTable_HuatuoVersion    ClientTable = 49
	ClientTable_Lord             ClientTable = 13
	ClientTable_LordEquip        ClientTable = 32
	ClientTable_LordGem          ClientTable = 33
	ClientTable_LordGemRandom    ClientTable = 39
	ClientTable_MainLineStage    ClientTable = 27
	ClientTable_MainTask         ClientTable = 14
	ClientTable_MapEvent         ClientTable = 19
	ClientTable_MonsterInfo      ClientTable = 4
	ClientTable_MonthCard        ClientTable = 46
	ClientTable_NewbieGuide      ClientTable = 41
	ClientTable_OutConsumable    ClientTable = 23
	ClientTable_PaymentOrder     ClientTable = 29
	ClientTable_RegularPack      ClientTable = 43
	ClientTable_RepeatTask       ClientTable = 17
	ClientTable_Research         ClientTable = 12
	ClientTable_TaskCounter      ClientTable = 50
	ClientTable_UserBenefit      ClientTable = 8
	ClientTable_UserData         ClientTable = 26
	ClientTable_UserDevices      ClientTable = 28
	ClientTable_UserIapBuyTimes  ClientTable = 30
	ClientTable_UserInfo         ClientTable = 2
	ClientTable_UserJob          ClientTable = 9
	ClientTable_UserLookup       ClientTable = 5
	ClientTable_UserMail         ClientTable = 34
	ClientTable_UserTroop        ClientTable = 10
	ClientTable_Villager         ClientTable = 20
	ClientTable_WeeklyTask       ClientTable = 18
)

var ClientTable_name = map[int32]string{
	0:  "ClientTable_None",
	47: "ClientTable_AchievementTask",
	42: "ClientTable_ActivityTask",
	35: "ClientTable_Alliance",
	36: "ClientTable_AllianceMember",
	37: "ClientTable_AllianceShopBuy",
	38: "ClientTable_AllianceTask",
	21: "ClientTable_Animal",
	7:  "ClientTable_BuildingInfo",
	48: "ClientTable_ClientVersion",
	6:  "ClientTable_Consumable",
	16: "ClientTable_DailyTask",
	25: "ClientTable_Dave",
	22: "ClientTable_Dungeon",
	45: "ClientTable_FirstCharge",
	40: "ClientTable_FunctionOpen",
	31: "ClientTable_GlobalStageLevel",
	44: "ClientTable_GrowthFund",
	15: "ClientTable_GrowthTask",
	3:  "ClientTable_HeroInfo",
	11: "ClientTable_HeroLottery",
	24: "ClientTable_HeroSkill",
	49: "ClientTable_HuatuoVersion",
	13: "ClientTable_Lord",
	32: "ClientTable_LordEquip",
	33: "ClientTable_LordGem",
	39: "ClientTable_LordGemRandom",
	27: "ClientTable_MainLineStage",
	14: "ClientTable_MainTask",
	19: "ClientTable_MapEvent",
	4:  "ClientTable_MonsterInfo",
	46: "ClientTable_MonthCard",
	41: "ClientTable_NewbieGuide",
	23: "ClientTable_OutConsumable",
	29: "ClientTable_PaymentOrder",
	43: "ClientTable_RegularPack",
	17: "ClientTable_RepeatTask",
	12: "ClientTable_Research",
	50: "ClientTable_TaskCounter",
	8:  "ClientTable_UserBenefit",
	26: "ClientTable_UserData",
	28: "ClientTable_UserDevices",
	30: "ClientTable_UserIapBuyTimes",
	2:  "ClientTable_UserInfo",
	9:  "ClientTable_UserJob",
	5:  "ClientTable_UserLookup",
	34: "ClientTable_UserMail",
	10: "ClientTable_UserTroop",
	20: "ClientTable_Villager",
	18: "ClientTable_WeeklyTask",
}

var ClientTable_value = map[string]int32{
	"ClientTable_None":             0,
	"ClientTable_AchievementTask":  47,
	"ClientTable_ActivityTask":     42,
	"ClientTable_Alliance":         35,
	"ClientTable_AllianceMember":   36,
	"ClientTable_AllianceShopBuy":  37,
	"ClientTable_AllianceTask":     38,
	"ClientTable_Animal":           21,
	"ClientTable_BuildingInfo":     7,
	"ClientTable_ClientVersion":    48,
	"ClientTable_Consumable":       6,
	"ClientTable_DailyTask":        16,
	"ClientTable_Dave":             25,
	"ClientTable_Dungeon":          22,
	"ClientTable_FirstCharge":      45,
	"ClientTable_FunctionOpen":     40,
	"ClientTable_GlobalStageLevel": 31,
	"ClientTable_GrowthFund":       44,
	"ClientTable_GrowthTask":       15,
	"ClientTable_HeroInfo":         3,
	"ClientTable_HeroLottery":      11,
	"ClientTable_HeroSkill":        24,
	"ClientTable_HuatuoVersion":    49,
	"ClientTable_Lord":             13,
	"ClientTable_LordEquip":        32,
	"ClientTable_LordGem":          33,
	"ClientTable_LordGemRandom":    39,
	"ClientTable_MainLineStage":    27,
	"ClientTable_MainTask":         14,
	"ClientTable_MapEvent":         19,
	"ClientTable_MonsterInfo":      4,
	"ClientTable_MonthCard":        46,
	"ClientTable_NewbieGuide":      41,
	"ClientTable_OutConsumable":    23,
	"ClientTable_PaymentOrder":     29,
	"ClientTable_RegularPack":      43,
	"ClientTable_RepeatTask":       17,
	"ClientTable_Research":         12,
	"ClientTable_TaskCounter":      50,
	"ClientTable_UserBenefit":      8,
	"ClientTable_UserData":         26,
	"ClientTable_UserDevices":      28,
	"ClientTable_UserIapBuyTimes":  30,
	"ClientTable_UserInfo":         2,
	"ClientTable_UserJob":          9,
	"ClientTable_UserLookup":       5,
	"ClientTable_UserMail":         34,
	"ClientTable_UserTroop":        10,
	"ClientTable_Villager":         20,
	"ClientTable_WeeklyTask":       18,
}

func (x ClientTable) String() string {
	return proto.EnumName(ClientTable_name, int32(x))
}

func (ClientTable) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_8371a479f90527bb, []int{0}
}

type Deprecated struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Deprecated) Reset()         { *m = Deprecated{} }
func (m *Deprecated) String() string { return proto.CompactTextString(m) }
func (*Deprecated) ProtoMessage()    {}
func (*Deprecated) Descriptor() ([]byte, []int) {
	return fileDescriptor_8371a479f90527bb, []int{0}
}
func (m *Deprecated) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Deprecated) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Deprecated.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Deprecated) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Deprecated.Merge(m, src)
}
func (m *Deprecated) XXX_Size() int {
	return m.Size()
}
func (m *Deprecated) XXX_DiscardUnknown() {
	xxx_messageInfo_Deprecated.DiscardUnknown(m)
}

var xxx_messageInfo_Deprecated proto.InternalMessageInfo

type DataRow struct {
	Table ClientTable `protobuf:"varint,1,opt,name=table,proto3,enum=minirpc.ClientTable" json:"table,omitempty"`
	// Types that are valid to be assigned to Row:
	//	*DataRow_AchievementTask
	//	*DataRow_ActivityTask
	//	*DataRow_Alliance
	//	*DataRow_AllianceMember
	//	*DataRow_AllianceShopBuy
	//	*DataRow_AllianceTask
	//	*DataRow_Animal
	//	*DataRow_BuildingInfo
	//	*DataRow_ClientVersion
	//	*DataRow_Consumable
	//	*DataRow_DailyTask
	//	*DataRow_Dave
	//	*DataRow_Dungeon
	//	*DataRow_FirstCharge
	//	*DataRow_FunctionOpen
	//	*DataRow_GlobalStageLevel
	//	*DataRow_GrowthFund
	//	*DataRow_GrowthTask
	//	*DataRow_HeroInfo
	//	*DataRow_HeroLottery
	//	*DataRow_HeroSkill
	//	*DataRow_HuatuoVersion
	//	*DataRow_Lord
	//	*DataRow_LordEquip
	//	*DataRow_LordGem
	//	*DataRow_LordGemRandom
	//	*DataRow_MainLineStage
	//	*DataRow_MainTask
	//	*DataRow_MapEvent
	//	*DataRow_MonsterInfo
	//	*DataRow_MonthCard
	//	*DataRow_NewbieGuide
	//	*DataRow_OutConsumable
	//	*DataRow_PaymentOrder
	//	*DataRow_RegularPack
	//	*DataRow_RepeatTask
	//	*DataRow_Research
	//	*DataRow_TaskCounter
	//	*DataRow_UserBenefit
	//	*DataRow_UserData
	//	*DataRow_UserDevices
	//	*DataRow_UserIapBuyTimes
	//	*DataRow_UserInfo
	//	*DataRow_UserJob
	//	*DataRow_UserLookup
	//	*DataRow_UserMail
	//	*DataRow_UserTroop
	//	*DataRow_Villager
	//	*DataRow_WeeklyTask
	Row                  isDataRow_Row `protobuf_oneof:"row"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DataRow) Reset()         { *m = DataRow{} }
func (m *DataRow) String() string { return proto.CompactTextString(m) }
func (*DataRow) ProtoMessage()    {}
func (*DataRow) Descriptor() ([]byte, []int) {
	return fileDescriptor_8371a479f90527bb, []int{1}
}
func (m *DataRow) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DataRow) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DataRow.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DataRow) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataRow.Merge(m, src)
}
func (m *DataRow) XXX_Size() int {
	return m.Size()
}
func (m *DataRow) XXX_DiscardUnknown() {
	xxx_messageInfo_DataRow.DiscardUnknown(m)
}

var xxx_messageInfo_DataRow proto.InternalMessageInfo

type isDataRow_Row interface {
	isDataRow_Row()
	MarshalTo([]byte) (int, error)
	Size() int
}

type DataRow_AchievementTask struct {
	AchievementTask *AchievementTask `protobuf:"bytes,47,opt,name=achievement_task,json=achievementTask,proto3,oneof" json:"achievement_task,omitempty"`
}
type DataRow_ActivityTask struct {
	ActivityTask *ActivityTask `protobuf:"bytes,42,opt,name=activity_task,json=activityTask,proto3,oneof" json:"activity_task,omitempty"`
}
type DataRow_Alliance struct {
	Alliance *Alliance `protobuf:"bytes,35,opt,name=alliance,proto3,oneof" json:"alliance,omitempty"`
}
type DataRow_AllianceMember struct {
	AllianceMember *AllianceMember `protobuf:"bytes,36,opt,name=alliance_member,json=allianceMember,proto3,oneof" json:"alliance_member,omitempty"`
}
type DataRow_AllianceShopBuy struct {
	AllianceShopBuy *AllianceShopBuy `protobuf:"bytes,37,opt,name=alliance_shop_buy,json=allianceShopBuy,proto3,oneof" json:"alliance_shop_buy,omitempty"`
}
type DataRow_AllianceTask struct {
	AllianceTask *AllianceTask `protobuf:"bytes,38,opt,name=alliance_task,json=allianceTask,proto3,oneof" json:"alliance_task,omitempty"`
}
type DataRow_Animal struct {
	Animal *Animal `protobuf:"bytes,21,opt,name=animal,proto3,oneof" json:"animal,omitempty"`
}
type DataRow_BuildingInfo struct {
	BuildingInfo *BuildingInfo `protobuf:"bytes,7,opt,name=building_info,json=buildingInfo,proto3,oneof" json:"building_info,omitempty"`
}
type DataRow_ClientVersion struct {
	ClientVersion *ClientVersion `protobuf:"bytes,48,opt,name=client_version,json=clientVersion,proto3,oneof" json:"client_version,omitempty"`
}
type DataRow_Consumable struct {
	Consumable *Consumable `protobuf:"bytes,6,opt,name=consumable,proto3,oneof" json:"consumable,omitempty"`
}
type DataRow_DailyTask struct {
	DailyTask *DailyTask `protobuf:"bytes,16,opt,name=daily_task,json=dailyTask,proto3,oneof" json:"daily_task,omitempty"`
}
type DataRow_Dave struct {
	Dave *Dave `protobuf:"bytes,25,opt,name=dave,proto3,oneof" json:"dave,omitempty"`
}
type DataRow_Dungeon struct {
	Dungeon *Dungeon `protobuf:"bytes,22,opt,name=dungeon,proto3,oneof" json:"dungeon,omitempty"`
}
type DataRow_FirstCharge struct {
	FirstCharge *FirstCharge `protobuf:"bytes,45,opt,name=first_charge,json=firstCharge,proto3,oneof" json:"first_charge,omitempty"`
}
type DataRow_FunctionOpen struct {
	FunctionOpen *FunctionOpen `protobuf:"bytes,40,opt,name=function_open,json=functionOpen,proto3,oneof" json:"function_open,omitempty"`
}
type DataRow_GlobalStageLevel struct {
	GlobalStageLevel *GlobalStageLevel `protobuf:"bytes,31,opt,name=global_stage_level,json=globalStageLevel,proto3,oneof" json:"global_stage_level,omitempty"`
}
type DataRow_GrowthFund struct {
	GrowthFund *GrowthFund `protobuf:"bytes,44,opt,name=growth_fund,json=growthFund,proto3,oneof" json:"growth_fund,omitempty"`
}
type DataRow_GrowthTask struct {
	GrowthTask *GrowthTask `protobuf:"bytes,15,opt,name=growth_task,json=growthTask,proto3,oneof" json:"growth_task,omitempty"`
}
type DataRow_HeroInfo struct {
	HeroInfo *HeroInfo `protobuf:"bytes,3,opt,name=hero_info,json=heroInfo,proto3,oneof" json:"hero_info,omitempty"`
}
type DataRow_HeroLottery struct {
	HeroLottery *HeroLottery `protobuf:"bytes,11,opt,name=hero_lottery,json=heroLottery,proto3,oneof" json:"hero_lottery,omitempty"`
}
type DataRow_HeroSkill struct {
	HeroSkill *HeroSkill `protobuf:"bytes,24,opt,name=hero_skill,json=heroSkill,proto3,oneof" json:"hero_skill,omitempty"`
}
type DataRow_HuatuoVersion struct {
	HuatuoVersion *HuatuoVersion `protobuf:"bytes,49,opt,name=huatuo_version,json=huatuoVersion,proto3,oneof" json:"huatuo_version,omitempty"`
}
type DataRow_Lord struct {
	Lord *Lord `protobuf:"bytes,13,opt,name=lord,proto3,oneof" json:"lord,omitempty"`
}
type DataRow_LordEquip struct {
	LordEquip *LordEquip `protobuf:"bytes,32,opt,name=lord_equip,json=lordEquip,proto3,oneof" json:"lord_equip,omitempty"`
}
type DataRow_LordGem struct {
	LordGem *LordGem `protobuf:"bytes,33,opt,name=lord_gem,json=lordGem,proto3,oneof" json:"lord_gem,omitempty"`
}
type DataRow_LordGemRandom struct {
	LordGemRandom *LordGemRandom `protobuf:"bytes,39,opt,name=lord_gem_random,json=lordGemRandom,proto3,oneof" json:"lord_gem_random,omitempty"`
}
type DataRow_MainLineStage struct {
	MainLineStage *MainLineStage `protobuf:"bytes,27,opt,name=main_line_stage,json=mainLineStage,proto3,oneof" json:"main_line_stage,omitempty"`
}
type DataRow_MainTask struct {
	MainTask *MainTask `protobuf:"bytes,14,opt,name=main_task,json=mainTask,proto3,oneof" json:"main_task,omitempty"`
}
type DataRow_MapEvent struct {
	MapEvent *MapEvent `protobuf:"bytes,19,opt,name=map_event,json=mapEvent,proto3,oneof" json:"map_event,omitempty"`
}
type DataRow_MonsterInfo struct {
	MonsterInfo *MonsterInfo `protobuf:"bytes,4,opt,name=monster_info,json=monsterInfo,proto3,oneof" json:"monster_info,omitempty"`
}
type DataRow_MonthCard struct {
	MonthCard *MonthCard `protobuf:"bytes,46,opt,name=month_card,json=monthCard,proto3,oneof" json:"month_card,omitempty"`
}
type DataRow_NewbieGuide struct {
	NewbieGuide *NewbieGuide `protobuf:"bytes,41,opt,name=newbie_guide,json=newbieGuide,proto3,oneof" json:"newbie_guide,omitempty"`
}
type DataRow_OutConsumable struct {
	OutConsumable *OutConsumable `protobuf:"bytes,23,opt,name=out_consumable,json=outConsumable,proto3,oneof" json:"out_consumable,omitempty"`
}
type DataRow_PaymentOrder struct {
	PaymentOrder *PaymentOrder `protobuf:"bytes,29,opt,name=payment_order,json=paymentOrder,proto3,oneof" json:"payment_order,omitempty"`
}
type DataRow_RegularPack struct {
	RegularPack *RegularPack `protobuf:"bytes,43,opt,name=regular_pack,json=regularPack,proto3,oneof" json:"regular_pack,omitempty"`
}
type DataRow_RepeatTask struct {
	RepeatTask *RepeatTask `protobuf:"bytes,17,opt,name=repeat_task,json=repeatTask,proto3,oneof" json:"repeat_task,omitempty"`
}
type DataRow_Research struct {
	Research *Research `protobuf:"bytes,12,opt,name=research,proto3,oneof" json:"research,omitempty"`
}
type DataRow_TaskCounter struct {
	TaskCounter *TaskCounter `protobuf:"bytes,50,opt,name=task_counter,json=taskCounter,proto3,oneof" json:"task_counter,omitempty"`
}
type DataRow_UserBenefit struct {
	UserBenefit *UserBenefit `protobuf:"bytes,8,opt,name=user_benefit,json=userBenefit,proto3,oneof" json:"user_benefit,omitempty"`
}
type DataRow_UserData struct {
	UserData *UserData `protobuf:"bytes,26,opt,name=user_data,json=userData,proto3,oneof" json:"user_data,omitempty"`
}
type DataRow_UserDevices struct {
	UserDevices *UserDevices `protobuf:"bytes,28,opt,name=user_devices,json=userDevices,proto3,oneof" json:"user_devices,omitempty"`
}
type DataRow_UserIapBuyTimes struct {
	UserIapBuyTimes *UserIapBuyTimes `protobuf:"bytes,30,opt,name=user_iap_buy_times,json=userIapBuyTimes,proto3,oneof" json:"user_iap_buy_times,omitempty"`
}
type DataRow_UserInfo struct {
	UserInfo *UserInfo `protobuf:"bytes,2,opt,name=user_info,json=userInfo,proto3,oneof" json:"user_info,omitempty"`
}
type DataRow_UserJob struct {
	UserJob *UserJob `protobuf:"bytes,9,opt,name=user_job,json=userJob,proto3,oneof" json:"user_job,omitempty"`
}
type DataRow_UserLookup struct {
	UserLookup *UserLookup `protobuf:"bytes,5,opt,name=user_lookup,json=userLookup,proto3,oneof" json:"user_lookup,omitempty"`
}
type DataRow_UserMail struct {
	UserMail *UserMail `protobuf:"bytes,34,opt,name=user_mail,json=userMail,proto3,oneof" json:"user_mail,omitempty"`
}
type DataRow_UserTroop struct {
	UserTroop *UserTroop `protobuf:"bytes,10,opt,name=user_troop,json=userTroop,proto3,oneof" json:"user_troop,omitempty"`
}
type DataRow_Villager struct {
	Villager *Villager `protobuf:"bytes,20,opt,name=villager,proto3,oneof" json:"villager,omitempty"`
}
type DataRow_WeeklyTask struct {
	WeeklyTask *WeeklyTask `protobuf:"bytes,18,opt,name=weekly_task,json=weeklyTask,proto3,oneof" json:"weekly_task,omitempty"`
}

func (*DataRow_AchievementTask) isDataRow_Row()  {}
func (*DataRow_ActivityTask) isDataRow_Row()     {}
func (*DataRow_Alliance) isDataRow_Row()         {}
func (*DataRow_AllianceMember) isDataRow_Row()   {}
func (*DataRow_AllianceShopBuy) isDataRow_Row()  {}
func (*DataRow_AllianceTask) isDataRow_Row()     {}
func (*DataRow_Animal) isDataRow_Row()           {}
func (*DataRow_BuildingInfo) isDataRow_Row()     {}
func (*DataRow_ClientVersion) isDataRow_Row()    {}
func (*DataRow_Consumable) isDataRow_Row()       {}
func (*DataRow_DailyTask) isDataRow_Row()        {}
func (*DataRow_Dave) isDataRow_Row()             {}
func (*DataRow_Dungeon) isDataRow_Row()          {}
func (*DataRow_FirstCharge) isDataRow_Row()      {}
func (*DataRow_FunctionOpen) isDataRow_Row()     {}
func (*DataRow_GlobalStageLevel) isDataRow_Row() {}
func (*DataRow_GrowthFund) isDataRow_Row()       {}
func (*DataRow_GrowthTask) isDataRow_Row()       {}
func (*DataRow_HeroInfo) isDataRow_Row()         {}
func (*DataRow_HeroLottery) isDataRow_Row()      {}
func (*DataRow_HeroSkill) isDataRow_Row()        {}
func (*DataRow_HuatuoVersion) isDataRow_Row()    {}
func (*DataRow_Lord) isDataRow_Row()             {}
func (*DataRow_LordEquip) isDataRow_Row()        {}
func (*DataRow_LordGem) isDataRow_Row()          {}
func (*DataRow_LordGemRandom) isDataRow_Row()    {}
func (*DataRow_MainLineStage) isDataRow_Row()    {}
func (*DataRow_MainTask) isDataRow_Row()         {}
func (*DataRow_MapEvent) isDataRow_Row()         {}
func (*DataRow_MonsterInfo) isDataRow_Row()      {}
func (*DataRow_MonthCard) isDataRow_Row()        {}
func (*DataRow_NewbieGuide) isDataRow_Row()      {}
func (*DataRow_OutConsumable) isDataRow_Row()    {}
func (*DataRow_PaymentOrder) isDataRow_Row()     {}
func (*DataRow_RegularPack) isDataRow_Row()      {}
func (*DataRow_RepeatTask) isDataRow_Row()       {}
func (*DataRow_Research) isDataRow_Row()         {}
func (*DataRow_TaskCounter) isDataRow_Row()      {}
func (*DataRow_UserBenefit) isDataRow_Row()      {}
func (*DataRow_UserData) isDataRow_Row()         {}
func (*DataRow_UserDevices) isDataRow_Row()      {}
func (*DataRow_UserIapBuyTimes) isDataRow_Row()  {}
func (*DataRow_UserInfo) isDataRow_Row()         {}
func (*DataRow_UserJob) isDataRow_Row()          {}
func (*DataRow_UserLookup) isDataRow_Row()       {}
func (*DataRow_UserMail) isDataRow_Row()         {}
func (*DataRow_UserTroop) isDataRow_Row()        {}
func (*DataRow_Villager) isDataRow_Row()         {}
func (*DataRow_WeeklyTask) isDataRow_Row()       {}

func (m *DataRow) GetRow() isDataRow_Row {
	if m != nil {
		return m.Row
	}
	return nil
}

func (m *DataRow) GetTable() ClientTable {
	if m != nil {
		return m.Table
	}
	return ClientTable_None
}

func (m *DataRow) GetAchievementTask() *AchievementTask {
	if x, ok := m.GetRow().(*DataRow_AchievementTask); ok {
		return x.AchievementTask
	}
	return nil
}

func (m *DataRow) GetActivityTask() *ActivityTask {
	if x, ok := m.GetRow().(*DataRow_ActivityTask); ok {
		return x.ActivityTask
	}
	return nil
}

func (m *DataRow) GetAlliance() *Alliance {
	if x, ok := m.GetRow().(*DataRow_Alliance); ok {
		return x.Alliance
	}
	return nil
}

func (m *DataRow) GetAllianceMember() *AllianceMember {
	if x, ok := m.GetRow().(*DataRow_AllianceMember); ok {
		return x.AllianceMember
	}
	return nil
}

func (m *DataRow) GetAllianceShopBuy() *AllianceShopBuy {
	if x, ok := m.GetRow().(*DataRow_AllianceShopBuy); ok {
		return x.AllianceShopBuy
	}
	return nil
}

func (m *DataRow) GetAllianceTask() *AllianceTask {
	if x, ok := m.GetRow().(*DataRow_AllianceTask); ok {
		return x.AllianceTask
	}
	return nil
}

func (m *DataRow) GetAnimal() *Animal {
	if x, ok := m.GetRow().(*DataRow_Animal); ok {
		return x.Animal
	}
	return nil
}

func (m *DataRow) GetBuildingInfo() *BuildingInfo {
	if x, ok := m.GetRow().(*DataRow_BuildingInfo); ok {
		return x.BuildingInfo
	}
	return nil
}

func (m *DataRow) GetClientVersion() *ClientVersion {
	if x, ok := m.GetRow().(*DataRow_ClientVersion); ok {
		return x.ClientVersion
	}
	return nil
}

func (m *DataRow) GetConsumable() *Consumable {
	if x, ok := m.GetRow().(*DataRow_Consumable); ok {
		return x.Consumable
	}
	return nil
}

func (m *DataRow) GetDailyTask() *DailyTask {
	if x, ok := m.GetRow().(*DataRow_DailyTask); ok {
		return x.DailyTask
	}
	return nil
}

func (m *DataRow) GetDave() *Dave {
	if x, ok := m.GetRow().(*DataRow_Dave); ok {
		return x.Dave
	}
	return nil
}

func (m *DataRow) GetDungeon() *Dungeon {
	if x, ok := m.GetRow().(*DataRow_Dungeon); ok {
		return x.Dungeon
	}
	return nil
}

func (m *DataRow) GetFirstCharge() *FirstCharge {
	if x, ok := m.GetRow().(*DataRow_FirstCharge); ok {
		return x.FirstCharge
	}
	return nil
}

func (m *DataRow) GetFunctionOpen() *FunctionOpen {
	if x, ok := m.GetRow().(*DataRow_FunctionOpen); ok {
		return x.FunctionOpen
	}
	return nil
}

func (m *DataRow) GetGlobalStageLevel() *GlobalStageLevel {
	if x, ok := m.GetRow().(*DataRow_GlobalStageLevel); ok {
		return x.GlobalStageLevel
	}
	return nil
}

func (m *DataRow) GetGrowthFund() *GrowthFund {
	if x, ok := m.GetRow().(*DataRow_GrowthFund); ok {
		return x.GrowthFund
	}
	return nil
}

func (m *DataRow) GetGrowthTask() *GrowthTask {
	if x, ok := m.GetRow().(*DataRow_GrowthTask); ok {
		return x.GrowthTask
	}
	return nil
}

func (m *DataRow) GetHeroInfo() *HeroInfo {
	if x, ok := m.GetRow().(*DataRow_HeroInfo); ok {
		return x.HeroInfo
	}
	return nil
}

func (m *DataRow) GetHeroLottery() *HeroLottery {
	if x, ok := m.GetRow().(*DataRow_HeroLottery); ok {
		return x.HeroLottery
	}
	return nil
}

func (m *DataRow) GetHeroSkill() *HeroSkill {
	if x, ok := m.GetRow().(*DataRow_HeroSkill); ok {
		return x.HeroSkill
	}
	return nil
}

func (m *DataRow) GetHuatuoVersion() *HuatuoVersion {
	if x, ok := m.GetRow().(*DataRow_HuatuoVersion); ok {
		return x.HuatuoVersion
	}
	return nil
}

func (m *DataRow) GetLord() *Lord {
	if x, ok := m.GetRow().(*DataRow_Lord); ok {
		return x.Lord
	}
	return nil
}

func (m *DataRow) GetLordEquip() *LordEquip {
	if x, ok := m.GetRow().(*DataRow_LordEquip); ok {
		return x.LordEquip
	}
	return nil
}

func (m *DataRow) GetLordGem() *LordGem {
	if x, ok := m.GetRow().(*DataRow_LordGem); ok {
		return x.LordGem
	}
	return nil
}

func (m *DataRow) GetLordGemRandom() *LordGemRandom {
	if x, ok := m.GetRow().(*DataRow_LordGemRandom); ok {
		return x.LordGemRandom
	}
	return nil
}

func (m *DataRow) GetMainLineStage() *MainLineStage {
	if x, ok := m.GetRow().(*DataRow_MainLineStage); ok {
		return x.MainLineStage
	}
	return nil
}

func (m *DataRow) GetMainTask() *MainTask {
	if x, ok := m.GetRow().(*DataRow_MainTask); ok {
		return x.MainTask
	}
	return nil
}

func (m *DataRow) GetMapEvent() *MapEvent {
	if x, ok := m.GetRow().(*DataRow_MapEvent); ok {
		return x.MapEvent
	}
	return nil
}

func (m *DataRow) GetMonsterInfo() *MonsterInfo {
	if x, ok := m.GetRow().(*DataRow_MonsterInfo); ok {
		return x.MonsterInfo
	}
	return nil
}

func (m *DataRow) GetMonthCard() *MonthCard {
	if x, ok := m.GetRow().(*DataRow_MonthCard); ok {
		return x.MonthCard
	}
	return nil
}

func (m *DataRow) GetNewbieGuide() *NewbieGuide {
	if x, ok := m.GetRow().(*DataRow_NewbieGuide); ok {
		return x.NewbieGuide
	}
	return nil
}

func (m *DataRow) GetOutConsumable() *OutConsumable {
	if x, ok := m.GetRow().(*DataRow_OutConsumable); ok {
		return x.OutConsumable
	}
	return nil
}

func (m *DataRow) GetPaymentOrder() *PaymentOrder {
	if x, ok := m.GetRow().(*DataRow_PaymentOrder); ok {
		return x.PaymentOrder
	}
	return nil
}

func (m *DataRow) GetRegularPack() *RegularPack {
	if x, ok := m.GetRow().(*DataRow_RegularPack); ok {
		return x.RegularPack
	}
	return nil
}

func (m *DataRow) GetRepeatTask() *RepeatTask {
	if x, ok := m.GetRow().(*DataRow_RepeatTask); ok {
		return x.RepeatTask
	}
	return nil
}

func (m *DataRow) GetResearch() *Research {
	if x, ok := m.GetRow().(*DataRow_Research); ok {
		return x.Research
	}
	return nil
}

func (m *DataRow) GetTaskCounter() *TaskCounter {
	if x, ok := m.GetRow().(*DataRow_TaskCounter); ok {
		return x.TaskCounter
	}
	return nil
}

func (m *DataRow) GetUserBenefit() *UserBenefit {
	if x, ok := m.GetRow().(*DataRow_UserBenefit); ok {
		return x.UserBenefit
	}
	return nil
}

func (m *DataRow) GetUserData() *UserData {
	if x, ok := m.GetRow().(*DataRow_UserData); ok {
		return x.UserData
	}
	return nil
}

func (m *DataRow) GetUserDevices() *UserDevices {
	if x, ok := m.GetRow().(*DataRow_UserDevices); ok {
		return x.UserDevices
	}
	return nil
}

func (m *DataRow) GetUserIapBuyTimes() *UserIapBuyTimes {
	if x, ok := m.GetRow().(*DataRow_UserIapBuyTimes); ok {
		return x.UserIapBuyTimes
	}
	return nil
}

func (m *DataRow) GetUserInfo() *UserInfo {
	if x, ok := m.GetRow().(*DataRow_UserInfo); ok {
		return x.UserInfo
	}
	return nil
}

func (m *DataRow) GetUserJob() *UserJob {
	if x, ok := m.GetRow().(*DataRow_UserJob); ok {
		return x.UserJob
	}
	return nil
}

func (m *DataRow) GetUserLookup() *UserLookup {
	if x, ok := m.GetRow().(*DataRow_UserLookup); ok {
		return x.UserLookup
	}
	return nil
}

func (m *DataRow) GetUserMail() *UserMail {
	if x, ok := m.GetRow().(*DataRow_UserMail); ok {
		return x.UserMail
	}
	return nil
}

func (m *DataRow) GetUserTroop() *UserTroop {
	if x, ok := m.GetRow().(*DataRow_UserTroop); ok {
		return x.UserTroop
	}
	return nil
}

func (m *DataRow) GetVillager() *Villager {
	if x, ok := m.GetRow().(*DataRow_Villager); ok {
		return x.Villager
	}
	return nil
}

func (m *DataRow) GetWeeklyTask() *WeeklyTask {
	if x, ok := m.GetRow().(*DataRow_WeeklyTask); ok {
		return x.WeeklyTask
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*DataRow) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*DataRow_AchievementTask)(nil),
		(*DataRow_ActivityTask)(nil),
		(*DataRow_Alliance)(nil),
		(*DataRow_AllianceMember)(nil),
		(*DataRow_AllianceShopBuy)(nil),
		(*DataRow_AllianceTask)(nil),
		(*DataRow_Animal)(nil),
		(*DataRow_BuildingInfo)(nil),
		(*DataRow_ClientVersion)(nil),
		(*DataRow_Consumable)(nil),
		(*DataRow_DailyTask)(nil),
		(*DataRow_Dave)(nil),
		(*DataRow_Dungeon)(nil),
		(*DataRow_FirstCharge)(nil),
		(*DataRow_FunctionOpen)(nil),
		(*DataRow_GlobalStageLevel)(nil),
		(*DataRow_GrowthFund)(nil),
		(*DataRow_GrowthTask)(nil),
		(*DataRow_HeroInfo)(nil),
		(*DataRow_HeroLottery)(nil),
		(*DataRow_HeroSkill)(nil),
		(*DataRow_HuatuoVersion)(nil),
		(*DataRow_Lord)(nil),
		(*DataRow_LordEquip)(nil),
		(*DataRow_LordGem)(nil),
		(*DataRow_LordGemRandom)(nil),
		(*DataRow_MainLineStage)(nil),
		(*DataRow_MainTask)(nil),
		(*DataRow_MapEvent)(nil),
		(*DataRow_MonsterInfo)(nil),
		(*DataRow_MonthCard)(nil),
		(*DataRow_NewbieGuide)(nil),
		(*DataRow_OutConsumable)(nil),
		(*DataRow_PaymentOrder)(nil),
		(*DataRow_RegularPack)(nil),
		(*DataRow_RepeatTask)(nil),
		(*DataRow_Research)(nil),
		(*DataRow_TaskCounter)(nil),
		(*DataRow_UserBenefit)(nil),
		(*DataRow_UserData)(nil),
		(*DataRow_UserDevices)(nil),
		(*DataRow_UserIapBuyTimes)(nil),
		(*DataRow_UserInfo)(nil),
		(*DataRow_UserJob)(nil),
		(*DataRow_UserLookup)(nil),
		(*DataRow_UserMail)(nil),
		(*DataRow_UserTroop)(nil),
		(*DataRow_Villager)(nil),
		(*DataRow_WeeklyTask)(nil),
	}
}

type PayloadRow struct {
	AddFriendInfo        *AddFriendInfo     `protobuf:"bytes,4,opt,name=add_friend_info,json=addFriendInfo,proto3" json:"add_friend_info,omitempty"`
	AgreeJoinAlliance    *AgreeJoinAlliance `protobuf:"bytes,3,opt,name=agree_join_alliance,json=agreeJoinAlliance,proto3" json:"agree_join_alliance,omitempty"`
	OrderResult          *OrderResult       `protobuf:"bytes,2,opt,name=order_result,json=orderResult,proto3" json:"order_result,omitempty"`
	UserPush             *UserPush          `protobuf:"bytes,5,opt,name=user_push,json=userPush,proto3" json:"user_push,omitempty"`
	PerPlayerScore       *Deprecated        `protobuf:"bytes,1,opt,name=per_player_score,json=perPlayerScore,proto3" json:"per_player_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PayloadRow) Reset()         { *m = PayloadRow{} }
func (m *PayloadRow) String() string { return proto.CompactTextString(m) }
func (*PayloadRow) ProtoMessage()    {}
func (*PayloadRow) Descriptor() ([]byte, []int) {
	return fileDescriptor_8371a479f90527bb, []int{2}
}
func (m *PayloadRow) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PayloadRow) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PayloadRow.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PayloadRow) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayloadRow.Merge(m, src)
}
func (m *PayloadRow) XXX_Size() int {
	return m.Size()
}
func (m *PayloadRow) XXX_DiscardUnknown() {
	xxx_messageInfo_PayloadRow.DiscardUnknown(m)
}

var xxx_messageInfo_PayloadRow proto.InternalMessageInfo

func (m *PayloadRow) GetAddFriendInfo() *AddFriendInfo {
	if m != nil {
		return m.AddFriendInfo
	}
	return nil
}

func (m *PayloadRow) GetAgreeJoinAlliance() *AgreeJoinAlliance {
	if m != nil {
		return m.AgreeJoinAlliance
	}
	return nil
}

func (m *PayloadRow) GetOrderResult() *OrderResult {
	if m != nil {
		return m.OrderResult
	}
	return nil
}

func (m *PayloadRow) GetUserPush() *UserPush {
	if m != nil {
		return m.UserPush
	}
	return nil
}

func (m *PayloadRow) GetPerPlayerScore() *Deprecated {
	if m != nil {
		return m.PerPlayerScore
	}
	return nil
}

func init() {
	proto.RegisterEnum("minirpc.ClientTable", ClientTable_name, ClientTable_value)
	proto.RegisterType((*Deprecated)(nil), "minirpc.Deprecated")
	proto.RegisterType((*DataRow)(nil), "minirpc.DataRow")
	proto.RegisterType((*PayloadRow)(nil), "minirpc.PayloadRow")
}

func init() { proto.RegisterFile("datarow.proto", fileDescriptor_8371a479f90527bb) }

var fileDescriptor_8371a479f90527bb = []byte{
	// 1765 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x58, 0xdb, 0x52, 0x1c, 0x39,
	0x12, 0x6d, 0x7c, 0x03, 0xd4, 0x5c, 0x84, 0xc0, 0x20, 0xb0, 0x8d, 0x19, 0x3c, 0x17, 0xdb, 0x63,
	0x83, 0xc7, 0x13, 0xbb, 0x1b, 0x1b, 0xb1, 0xb7, 0xc1, 0x36, 0xb4, 0x1d, 0x30, 0x26, 0xca, 0xde,
	0xd9, 0xc7, 0x0a, 0x75, 0x95, 0xba, 0x5b, 0x43, 0x55, 0xa9, 0x56, 0x55, 0xd5, 0x04, 0x9f, 0xb0,
	0x6f, 0xf3, 0x49, 0xfb, 0xb8, 0x8f, 0xf3, 0x09, 0x6b, 0xef, 0x0f, 0xec, 0x27, 0x4c, 0xa4, 0xea,
	0xa6, 0x4a, 0xfa, 0x09, 0xa5, 0x4e, 0x9e, 0x94, 0x94, 0x99, 0x25, 0x9d, 0x86, 0x2c, 0x87, 0x22,
	0x17, 0x46, 0x5f, 0x1e, 0xa4, 0x46, 0xe7, 0x9a, 0xcd, 0xc7, 0x2a, 0x51, 0x26, 0x0d, 0x76, 0x9e,
	0x8f, 0x55, 0x3e, 0x29, 0x86, 0x07, 0x81, 0x8e, 0x0f, 0xc7, 0x7a, 0xac, 0x0f, 0x2d, 0x3e, 0x2c,
	0x46, 0xd6, 0xb2, 0x86, 0x1d, 0x95, 0xbc, 0x9d, 0x8d, 0x6c, 0x22, 0x8c, 0xf4, 0x8d, 0x4c, 0x23,
	0x15, 0x88, 0xac, 0x9c, 0xdd, 0x5f, 0x22, 0xe4, 0xb5, 0x4c, 0x8d, 0x0c, 0x44, 0x2e, 0xc3, 0xfd,
	0x5f, 0x36, 0xc9, 0xfc, 0x6b, 0x91, 0x0b, 0x4f, 0x5f, 0xb2, 0xa7, 0xe4, 0x76, 0x2e, 0x86, 0x91,
	0xe4, 0x73, 0x7b, 0x73, 0x8f, 0x57, 0x5e, 0x6e, 0x1c, 0x54, 0xeb, 0x1e, 0xbc, 0x8a, 0x94, 0x4c,
	0xf2, 0x8f, 0x80, 0x79, 0xa5, 0x0b, 0x7b, 0x43, 0xa8, 0x08, 0x26, 0x4a, 0x4e, 0x65, 0x2c, 0x93,
	0xdc, 0xcf, 0x45, 0x76, 0xc1, 0x0f, 0xf7, 0xe6, 0x1e, 0xf7, 0x5f, 0xf2, 0x86, 0xf6, 0x43, 0xeb,
	0xf0, 0x51, 0x64, 0x17, 0x83, 0x9e, 0xb7, 0x2a, 0xba, 0x53, 0xec, 0x4f, 0x64, 0x59, 0x04, 0xb9,
	0x9a, 0xaa, 0xfc, 0xaa, 0x8c, 0xf1, 0xd4, 0xc6, 0xb8, 0xeb, 0xc4, 0x28, 0xd1, 0x2a, 0xc0, 0x92,
	0x70, 0x6c, 0x76, 0x48, 0x16, 0x44, 0x14, 0x29, 0x91, 0x04, 0x92, 0x3f, 0xb2, 0xc4, 0xb5, 0x96,
	0x58, 0x01, 0x83, 0x9e, 0xd7, 0x38, 0xb1, 0x23, 0xb2, 0x5a, 0x8f, 0xfd, 0x58, 0xc6, 0x43, 0x69,
	0xf8, 0x97, 0x96, 0xb7, 0x75, 0x8d, 0x77, 0x66, 0xe1, 0x41, 0xcf, 0x5b, 0x11, 0x9d, 0x19, 0x76,
	0x4c, 0xd6, 0x9a, 0x18, 0xd9, 0x44, 0xa7, 0xfe, 0xb0, 0xb8, 0xe2, 0x5f, 0xe1, 0xa3, 0x57, 0x1e,
	0x1f, 0x26, 0x3a, 0x3d, 0x2a, 0xae, 0xec, 0xd1, 0xbb, 0x53, 0xf6, 0xe8, 0x75, 0x1c, 0x7b, 0xf4,
	0xaf, 0xf1, 0xd1, 0x2b, 0xb4, 0x39, 0xba, 0x63, 0xb3, 0x27, 0xe4, 0x8e, 0x48, 0x54, 0x2c, 0x22,
	0x7e, 0xd7, 0xd2, 0x56, 0x5b, 0x9a, 0x9d, 0x1e, 0xf4, 0xbc, 0xca, 0x01, 0x16, 0x1a, 0x16, 0x2a,
	0x0a, 0x55, 0x32, 0xf6, 0x55, 0x32, 0xd2, 0x7c, 0x1e, 0x2d, 0x74, 0x54, 0xa1, 0x6f, 0x93, 0x91,
	0x86, 0x85, 0x86, 0x8e, 0xcd, 0xfe, 0x4a, 0x56, 0x02, 0x5b, 0x7e, 0x7f, 0x2a, 0x4d, 0xa6, 0x74,
	0xc2, 0x5f, 0x58, 0xfa, 0x26, 0xea, 0x8e, 0x9f, 0x4a, 0x74, 0xd0, 0xf3, 0x96, 0x03, 0x77, 0x82,
	0xfd, 0x8e, 0x90, 0x40, 0x27, 0x59, 0x11, 0xdb, 0xd6, 0xba, 0x63, 0xc9, 0xeb, 0x2d, 0xb9, 0x81,
	0x06, 0x3d, 0xcf, 0x71, 0x64, 0xdf, 0x13, 0x12, 0x0a, 0x15, 0x55, 0x6d, 0x41, 0x2d, 0x8d, 0x35,
	0xb4, 0xd7, 0x00, 0x55, 0x89, 0x59, 0x0c, 0x6b, 0x83, 0x3d, 0x22, 0xb7, 0x42, 0x31, 0x95, 0x7c,
	0xdb, 0xba, 0x2f, 0x3b, 0xee, 0x53, 0x88, 0x6f, 0x41, 0xf6, 0x8c, 0xcc, 0x87, 0x45, 0x32, 0x96,
	0x3a, 0xe1, 0x9b, 0xd6, 0x8f, 0xb6, 0x7e, 0xe5, 0xfc, 0xa0, 0xe7, 0xd5, 0x2e, 0xec, 0x8f, 0x64,
	0x69, 0xa4, 0x4c, 0x96, 0xfb, 0xc1, 0x44, 0x98, 0xb1, 0xe4, 0xcf, 0x2d, 0xa5, 0xfd, 0x36, 0x8e,
	0x01, 0x7c, 0x65, 0xb1, 0x41, 0xcf, 0xeb, 0x8f, 0x5a, 0x13, 0x12, 0x3f, 0x2a, 0x92, 0x20, 0x57,
	0x3a, 0xf1, 0x75, 0x2a, 0x13, 0xfe, 0x18, 0x25, 0xfe, 0xb8, 0x42, 0xdf, 0xa7, 0x12, 0xd6, 0x5c,
	0x1a, 0x39, 0x36, 0x7b, 0x4b, 0xd8, 0x38, 0xd2, 0x43, 0x11, 0xf9, 0x59, 0x2e, 0xc6, 0xd2, 0x8f,
	0xe4, 0x54, 0x46, 0xfc, 0xa1, 0x0d, 0xb1, 0xdd, 0x84, 0x38, 0xb1, 0x2e, 0x1f, 0xc0, 0xe3, 0x14,
	0x1c, 0x06, 0x3d, 0x8f, 0x8e, 0xd1, 0x1c, 0xfb, 0x3d, 0xe9, 0x8f, 0x8d, 0xbe, 0xcc, 0x27, 0xfe,
	0xa8, 0x48, 0x42, 0xfe, 0x0c, 0xd5, 0xe0, 0xc4, 0x62, 0xc7, 0x45, 0x12, 0x42, 0x0d, 0xc6, 0x8d,
	0xe5, 0xf0, 0x6c, 0x11, 0x56, 0x67, 0xf2, 0xaa, 0x2a, 0x54, 0x3c, 0x5b, 0x86, 0x17, 0x64, 0x71,
	0x22, 0x8d, 0x2e, 0xbb, 0xed, 0x26, 0xfa, 0x30, 0x07, 0xd2, 0xe8, 0xaa, 0xd3, 0x16, 0x26, 0xd5,
	0x18, 0xb2, 0x6c, 0x19, 0x91, 0xce, 0x73, 0x69, 0xae, 0x78, 0x1f, 0x65, 0x19, 0x48, 0xa7, 0x25,
	0x06, 0x59, 0x9e, 0xb4, 0x26, 0x34, 0x8a, 0xa5, 0x66, 0x17, 0x2a, 0x8a, 0x38, 0x47, 0x8d, 0x02,
	0xc4, 0x0f, 0x80, 0x40, 0xa3, 0x4c, 0x6a, 0x03, 0xba, 0x7a, 0x52, 0x88, 0xbc, 0xd0, 0x4d, 0x57,
	0x7f, 0x87, 0xba, 0x7a, 0x60, 0x61, 0xa7, 0xab, 0x27, 0xee, 0x04, 0x74, 0x5a, 0xa4, 0x4d, 0xc8,
	0x97, 0x51, 0xa7, 0x9d, 0x6a, 0x03, 0x59, 0xb4, 0x20, 0x6c, 0x0d, 0xfe, 0xfa, 0xf2, 0x9f, 0x85,
	0x4a, 0xf9, 0x1e, 0xda, 0x1a, 0xb8, 0xbe, 0x01, 0x04, 0xb6, 0x16, 0xd5, 0x06, 0x7b, 0x4e, 0x16,
	0x2c, 0x69, 0x2c, 0x63, 0xfe, 0x05, 0xea, 0x4f, 0xa0, 0x9c, 0xc8, 0x18, 0xfa, 0x33, 0x2a, 0x87,
	0xec, 0x6f, 0x64, 0xb5, 0x76, 0xf7, 0x8d, 0x48, 0x42, 0x1d, 0xf3, 0x6f, 0xd0, 0x51, 0x2a, 0x96,
	0x67, 0x51, 0x38, 0x4a, 0xe4, 0x4e, 0x40, 0x84, 0x58, 0xa8, 0xc4, 0x8f, 0x54, 0x22, 0xcb, 0x5e,
	0xe3, 0xf7, 0x50, 0x84, 0x33, 0xa1, 0x92, 0x53, 0x95, 0x48, 0xdb, 0x53, 0x10, 0x21, 0x76, 0x27,
	0xa0, 0xde, 0x36, 0x82, 0xed, 0x92, 0x15, 0x54, 0x6f, 0xe0, 0x56, 0x3d, 0xb2, 0x10, 0x57, 0xe3,
	0x92, 0x91, 0xfa, 0x72, 0x2a, 0x93, 0x9c, 0xaf, 0x5f, 0x63, 0xa4, 0x6f, 0x00, 0x28, 0x19, 0xe5,
	0x18, 0x3a, 0x24, 0xd6, 0x49, 0x96, 0x4b, 0x53, 0xb6, 0xd5, 0x2d, 0xd4, 0x21, 0x67, 0x25, 0x58,
	0x75, 0x56, 0x3f, 0x6e, 0x4d, 0x28, 0x43, 0xac, 0x93, 0x7c, 0xe2, 0x07, 0xc2, 0x84, 0xfc, 0x00,
	0x95, 0xe1, 0x0c, 0xa0, 0x57, 0xc2, 0x96, 0x6d, 0x31, 0xae, 0x0d, 0x58, 0x2f, 0x91, 0x97, 0x43,
	0x25, 0xfd, 0x71, 0xa1, 0x42, 0xc9, 0x9f, 0xa0, 0xf5, 0x7e, 0xb4, 0xe0, 0x09, 0x60, 0xb0, 0x5e,
	0xd2, 0x9a, 0xd0, 0x5c, 0xba, 0xc8, 0x7d, 0xe7, 0xd6, 0xdb, 0x42, 0xf9, 0x7c, 0x5f, 0xe4, 0x9d,
	0x8b, 0x6f, 0x59, 0xbb, 0x13, 0x70, 0x71, 0xa4, 0xe2, 0xca, 0x3e, 0xac, 0xda, 0x84, 0xd2, 0xf0,
	0x07, 0xe8, 0xe2, 0x38, 0x2f, 0xd1, 0xf7, 0x00, 0xc2, 0xc5, 0x91, 0x3a, 0x36, 0xec, 0xdc, 0xc8,
	0x71, 0x11, 0x09, 0xe3, 0xa7, 0x22, 0xb8, 0xe0, 0xdf, 0xa2, 0x9d, 0x7b, 0x25, 0x78, 0x2e, 0x02,
	0xa8, 0x49, 0xdf, 0xb4, 0x26, 0x7c, 0xf0, 0x46, 0xa6, 0x52, 0x54, 0x0f, 0xfa, 0x1a, 0xfa, 0xe0,
	0x3d, 0x8b, 0xd5, 0x1f, 0xbc, 0x69, 0x2c, 0x78, 0x88, 0x8d, 0xcc, 0xa4, 0x30, 0xc1, 0x84, 0x2f,
	0xa1, 0x6a, 0x7a, 0x15, 0x00, 0xd5, 0xac, 0x9d, 0x60, 0x8f, 0xb0, 0x82, 0x1f, 0xe8, 0x22, 0xc9,
	0xa5, 0xe1, 0x2f, 0xd1, 0x1e, 0x21, 0xea, 0xab, 0x12, 0x83, 0x3d, 0xe6, 0xad, 0x09, 0xd4, 0x22,
	0x93, 0xc6, 0x1f, 0xca, 0x44, 0x8e, 0x54, 0xce, 0x17, 0x10, 0xf5, 0xef, 0x99, 0x34, 0x47, 0x25,
	0x06, 0xd4, 0xa2, 0x35, 0xa1, 0xeb, 0x2c, 0x15, 0xe4, 0x15, 0xdf, 0x41, 0xfb, 0x04, 0x1e, 0x28,
	0x21, 0xd8, 0x67, 0x51, 0x8d, 0x9b, 0xc5, 0x42, 0x39, 0x55, 0x81, 0xcc, 0xf8, 0xfd, 0x19, 0x8b,
	0xbd, 0x2e, 0xb1, 0x7a, 0xb1, 0xca, 0x64, 0x27, 0x84, 0x59, 0xaa, 0x12, 0x56, 0x22, 0xf8, 0xb9,
	0x8a, 0x65, 0xc6, 0x77, 0x91, 0x50, 0x80, 0x00, 0x6f, 0x05, 0x08, 0x82, 0x8f, 0x80, 0x83, 0x50,
	0x28, 0xba, 0x53, 0xcd, 0xae, 0x6d, 0xdb, 0xdf, 0x98, 0xb1, 0xeb, 0xfa, 0x36, 0x2d, 0xaa, 0x31,
	0x5c, 0x21, 0x96, 0xf1, 0xb3, 0x1e, 0xf2, 0x45, 0x74, 0x85, 0x00, 0xe1, 0x9d, 0x1e, 0xc2, 0x15,
	0x52, 0x94, 0x43, 0xa8, 0xba, 0x75, 0x8f, 0xb4, 0xbe, 0x28, 0x52, 0x7e, 0x1b, 0x55, 0x1d, 0x18,
	0xa7, 0x16, 0x82, 0xaa, 0x17, 0x8d, 0xd5, 0x6c, 0x2c, 0x16, 0x2a, 0xe2, 0xfb, 0x33, 0x36, 0x76,
	0x26, 0x54, 0x54, 0x6f, 0x0c, 0xc6, 0xf0, 0x25, 0x5a, 0x46, 0x6e, 0xb4, 0x4e, 0x39, 0x41, 0x5f,
	0x22, 0x50, 0x3e, 0x02, 0x02, 0x5f, 0x62, 0x51, 0x1b, 0xd0, 0x5c, 0x53, 0x15, 0x45, 0x62, 0x2c,
	0x0d, 0xdf, 0x40, 0xab, 0xfc, 0x54, 0x01, 0xb0, 0x4a, 0xed, 0x04, 0xe7, 0xb9, 0x94, 0xf2, 0xa2,
	0xd6, 0x0e, 0x0c, 0x9d, 0xe7, 0x1f, 0x16, 0xab, 0xbb, 0xf8, 0xb2, 0xb1, 0x8e, 0x6e, 0x93, 0x9b,
	0x46, 0x5f, 0xee, 0xff, 0xfb, 0x06, 0x21, 0xe7, 0xe2, 0x2a, 0xd2, 0x22, 0x04, 0x55, 0xfc, 0x17,
	0xb2, 0x2a, 0xc2, 0xd0, 0x1f, 0x19, 0x25, 0x93, 0xd0, 0xbd, 0x7b, 0xda, 0xcf, 0xf9, 0x87, 0x30,
	0x3c, 0xb6, 0x30, 0x64, 0xdf, 0x5b, 0x16, 0xae, 0xc9, 0xde, 0x91, 0x75, 0x31, 0x36, 0x52, 0xfa,
	0x3f, 0x6b, 0x95, 0xf8, 0x8d, 0x5e, 0x2d, 0x9f, 0xc5, 0x9d, 0x36, 0x06, 0xf8, 0xbc, 0xd3, 0x2a,
	0xa9, 0x65, 0x9f, 0xb7, 0x26, 0xf0, 0x14, 0xfb, 0x03, 0x59, 0xb2, 0x17, 0x82, 0x6f, 0x64, 0x56,
	0x44, 0x79, 0xd5, 0x0d, 0x6d, 0x3b, 0xda, 0x0b, 0xc0, 0xb3, 0x98, 0xd7, 0xd7, 0xad, 0xc1, 0x0e,
	0xaa, 0x52, 0xa5, 0x45, 0x36, 0xa9, 0x0a, 0xdc, 0x2d, 0xd5, 0x79, 0x91, 0x4d, 0xca, 0x42, 0xc1,
	0x88, 0xfd, 0x99, 0xd0, 0x14, 0xdc, 0x23, 0x71, 0x25, 0x8d, 0x9f, 0x05, 0xda, 0x94, 0xbf, 0x0a,
	0xdc, 0x3c, 0xb6, 0xbf, 0x22, 0xbc, 0x95, 0x54, 0x9a, 0x73, 0xeb, 0xfb, 0x01, 0x5c, 0x9f, 0xfe,
	0xab, 0x4f, 0xfa, 0xce, 0x8f, 0x06, 0xb6, 0x41, 0xa8, 0x63, 0xfa, 0x3f, 0xea, 0x44, 0xd2, 0x1e,
	0x7b, 0x48, 0xee, 0xb9, 0xb3, 0xe8, 0xe7, 0x02, 0x3d, 0x64, 0xf7, 0x09, 0xef, 0x3a, 0xb4, 0xda,
	0x9f, 0x3e, 0x65, 0x9c, 0x6c, 0x74, 0xd0, 0x2a, 0x49, 0xf4, 0x11, 0xdb, 0x25, 0x3b, 0xb3, 0x90,
	0x52, 0xc0, 0xd3, 0x2f, 0xaf, 0x2d, 0xdc, 0x55, 0xe6, 0xf4, 0xab, 0x6b, 0x0b, 0x3b, 0xca, 0x9b,
	0x7e, 0xcd, 0x36, 0x09, 0xeb, 0xa0, 0x56, 0x66, 0xd3, 0xbb, 0x98, 0xe5, 0xca, 0x6a, 0x3a, 0xcf,
	0x1e, 0x90, 0x6d, 0x17, 0xed, 0xa8, 0x66, 0xfa, 0x82, 0xed, 0x90, 0xcd, 0x0e, 0xdc, 0xbc, 0x06,
	0xf4, 0x0e, 0xdb, 0x26, 0x77, 0x5d, 0xac, 0x11, 0xbf, 0x94, 0xe2, 0xcc, 0x82, 0xd0, 0xa5, 0xdb,
	0x6c, 0x8b, 0xac, 0x77, 0x66, 0x4b, 0x2d, 0x4b, 0x37, 0xd9, 0x3d, 0xb2, 0xe5, 0x02, 0x8e, 0x78,
	0xa5, 0xcf, 0xf1, 0xfe, 0x5d, 0x75, 0x4a, 0x1f, 0xb3, 0x3d, 0x72, 0xdf, 0x45, 0xb1, 0xf0, 0xa4,
	0x0f, 0xf1, 0x11, 0x5a, 0x59, 0x49, 0x9f, 0xcd, 0xc6, 0xec, 0x19, 0x56, 0x71, 0x21, 0x6b, 0x81,
	0x48, 0x6f, 0xe2, 0xed, 0x3a, 0x2a, 0x90, 0xf6, 0x71, 0x56, 0x1a, 0xa5, 0x47, 0x39, 0xce, 0x75,
	0x47, 0xcb, 0xd1, 0xef, 0x70, 0xd2, 0x40, 0x1f, 0xd1, 0x65, 0x1c, 0xaf, 0x91, 0x67, 0x74, 0x0f,
	0xe7, 0xb3, 0x12, 0x54, 0xf4, 0x0b, 0xbc, 0x50, 0x47, 0x69, 0xd1, 0x6f, 0x30, 0xdc, 0x91, 0x51,
	0xf4, 0x1e, 0x3e, 0x78, 0xad, 0x94, 0xe8, 0xca, 0x75, 0xa4, 0x54, 0x41, 0x74, 0x1d, 0xa7, 0xc4,
	0x91, 0x3d, 0xf4, 0x16, 0x3e, 0x42, 0x23, 0x6d, 0xe8, 0x01, 0xe6, 0x39, 0xf2, 0x85, 0x3e, 0xc1,
	0xfb, 0xec, 0xc8, 0x13, 0xba, 0x85, 0x1b, 0xc3, 0x55, 0x1f, 0xf4, 0x01, 0x8e, 0xec, 0xc8, 0x0b,
	0xfa, 0x2d, 0xae, 0x7b, 0xab, 0x20, 0xe8, 0x1a, 0x3e, 0x64, 0x2d, 0x14, 0xe8, 0x12, 0x0e, 0xe9,
	0xa8, 0x01, 0xfa, 0x12, 0x83, 0xce, 0x7b, 0x4f, 0x17, 0x70, 0xcc, 0xfa, 0x51, 0xa7, 0x3b, 0xb3,
	0x68, 0xd5, 0x53, 0x4d, 0xef, 0xe3, 0x1b, 0x01, 0xbd, 0xca, 0x74, 0x77, 0x56, 0x5c, 0x9b, 0xf3,
	0x1b, 0xb8, 0x37, 0xaa, 0xf7, 0x95, 0x2e, 0xe2, 0xa3, 0xb7, 0xcf, 0x28, 0xbd, 0x3d, 0x2b, 0x1c,
	0x3c, 0x90, 0x74, 0x1f, 0x97, 0xb0, 0x79, 0x13, 0x29, 0xc1, 0xa4, 0xfa, 0xed, 0xa3, 0x1b, 0x78,
	0xa9, 0xf6, 0x85, 0xa3, 0xec, 0x88, 0xff, 0xfa, 0x69, 0xb7, 0xf7, 0xff, 0x4f, 0xbb, 0x73, 0xff,
	0xf9, 0xbc, 0x3b, 0xf7, 0xeb, 0xe7, 0xdd, 0xb9, 0xff, 0x7e, 0xde, 0x9d, 0xfb, 0xe5, 0x7f, 0xbb,
	0xbd, 0xe1, 0x1d, 0xfb, 0x0f, 0xa1, 0xef, 0x7f, 0x0b, 0x00, 0x00, 0xff, 0xff, 0x15, 0x01, 0x81,
	0xe4, 0x6f, 0x12, 0x00, 0x00,
}

func (this *Deprecated) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&minirpc.Deprecated{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DataRow) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 54)
	s = append(s, "&minirpc.DataRow{")
	s = append(s, "Table: "+fmt.Sprintf("%#v", this.Table)+",\n")
	if this.Row != nil {
		s = append(s, "Row: "+fmt.Sprintf("%#v", this.Row)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DataRow_AchievementTask) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_AchievementTask{` +
		`AchievementTask:` + fmt.Sprintf("%#v", this.AchievementTask) + `}`}, ", ")
	return s
}
func (this *DataRow_ActivityTask) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_ActivityTask{` +
		`ActivityTask:` + fmt.Sprintf("%#v", this.ActivityTask) + `}`}, ", ")
	return s
}
func (this *DataRow_Alliance) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_Alliance{` +
		`Alliance:` + fmt.Sprintf("%#v", this.Alliance) + `}`}, ", ")
	return s
}
func (this *DataRow_AllianceMember) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_AllianceMember{` +
		`AllianceMember:` + fmt.Sprintf("%#v", this.AllianceMember) + `}`}, ", ")
	return s
}
func (this *DataRow_AllianceShopBuy) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_AllianceShopBuy{` +
		`AllianceShopBuy:` + fmt.Sprintf("%#v", this.AllianceShopBuy) + `}`}, ", ")
	return s
}
func (this *DataRow_AllianceTask) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_AllianceTask{` +
		`AllianceTask:` + fmt.Sprintf("%#v", this.AllianceTask) + `}`}, ", ")
	return s
}
func (this *DataRow_Animal) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_Animal{` +
		`Animal:` + fmt.Sprintf("%#v", this.Animal) + `}`}, ", ")
	return s
}
func (this *DataRow_BuildingInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_BuildingInfo{` +
		`BuildingInfo:` + fmt.Sprintf("%#v", this.BuildingInfo) + `}`}, ", ")
	return s
}
func (this *DataRow_ClientVersion) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_ClientVersion{` +
		`ClientVersion:` + fmt.Sprintf("%#v", this.ClientVersion) + `}`}, ", ")
	return s
}
func (this *DataRow_Consumable) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_Consumable{` +
		`Consumable:` + fmt.Sprintf("%#v", this.Consumable) + `}`}, ", ")
	return s
}
func (this *DataRow_DailyTask) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_DailyTask{` +
		`DailyTask:` + fmt.Sprintf("%#v", this.DailyTask) + `}`}, ", ")
	return s
}
func (this *DataRow_Dave) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_Dave{` +
		`Dave:` + fmt.Sprintf("%#v", this.Dave) + `}`}, ", ")
	return s
}
func (this *DataRow_Dungeon) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_Dungeon{` +
		`Dungeon:` + fmt.Sprintf("%#v", this.Dungeon) + `}`}, ", ")
	return s
}
func (this *DataRow_FirstCharge) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_FirstCharge{` +
		`FirstCharge:` + fmt.Sprintf("%#v", this.FirstCharge) + `}`}, ", ")
	return s
}
func (this *DataRow_FunctionOpen) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_FunctionOpen{` +
		`FunctionOpen:` + fmt.Sprintf("%#v", this.FunctionOpen) + `}`}, ", ")
	return s
}
func (this *DataRow_GlobalStageLevel) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_GlobalStageLevel{` +
		`GlobalStageLevel:` + fmt.Sprintf("%#v", this.GlobalStageLevel) + `}`}, ", ")
	return s
}
func (this *DataRow_GrowthFund) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_GrowthFund{` +
		`GrowthFund:` + fmt.Sprintf("%#v", this.GrowthFund) + `}`}, ", ")
	return s
}
func (this *DataRow_GrowthTask) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_GrowthTask{` +
		`GrowthTask:` + fmt.Sprintf("%#v", this.GrowthTask) + `}`}, ", ")
	return s
}
func (this *DataRow_HeroInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_HeroInfo{` +
		`HeroInfo:` + fmt.Sprintf("%#v", this.HeroInfo) + `}`}, ", ")
	return s
}
func (this *DataRow_HeroLottery) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_HeroLottery{` +
		`HeroLottery:` + fmt.Sprintf("%#v", this.HeroLottery) + `}`}, ", ")
	return s
}
func (this *DataRow_HeroSkill) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_HeroSkill{` +
		`HeroSkill:` + fmt.Sprintf("%#v", this.HeroSkill) + `}`}, ", ")
	return s
}
func (this *DataRow_HuatuoVersion) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_HuatuoVersion{` +
		`HuatuoVersion:` + fmt.Sprintf("%#v", this.HuatuoVersion) + `}`}, ", ")
	return s
}
func (this *DataRow_Lord) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_Lord{` +
		`Lord:` + fmt.Sprintf("%#v", this.Lord) + `}`}, ", ")
	return s
}
func (this *DataRow_LordEquip) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_LordEquip{` +
		`LordEquip:` + fmt.Sprintf("%#v", this.LordEquip) + `}`}, ", ")
	return s
}
func (this *DataRow_LordGem) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_LordGem{` +
		`LordGem:` + fmt.Sprintf("%#v", this.LordGem) + `}`}, ", ")
	return s
}
func (this *DataRow_LordGemRandom) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_LordGemRandom{` +
		`LordGemRandom:` + fmt.Sprintf("%#v", this.LordGemRandom) + `}`}, ", ")
	return s
}
func (this *DataRow_MainLineStage) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_MainLineStage{` +
		`MainLineStage:` + fmt.Sprintf("%#v", this.MainLineStage) + `}`}, ", ")
	return s
}
func (this *DataRow_MainTask) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_MainTask{` +
		`MainTask:` + fmt.Sprintf("%#v", this.MainTask) + `}`}, ", ")
	return s
}
func (this *DataRow_MapEvent) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_MapEvent{` +
		`MapEvent:` + fmt.Sprintf("%#v", this.MapEvent) + `}`}, ", ")
	return s
}
func (this *DataRow_MonsterInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_MonsterInfo{` +
		`MonsterInfo:` + fmt.Sprintf("%#v", this.MonsterInfo) + `}`}, ", ")
	return s
}
func (this *DataRow_MonthCard) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_MonthCard{` +
		`MonthCard:` + fmt.Sprintf("%#v", this.MonthCard) + `}`}, ", ")
	return s
}
func (this *DataRow_NewbieGuide) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_NewbieGuide{` +
		`NewbieGuide:` + fmt.Sprintf("%#v", this.NewbieGuide) + `}`}, ", ")
	return s
}
func (this *DataRow_OutConsumable) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_OutConsumable{` +
		`OutConsumable:` + fmt.Sprintf("%#v", this.OutConsumable) + `}`}, ", ")
	return s
}
func (this *DataRow_PaymentOrder) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_PaymentOrder{` +
		`PaymentOrder:` + fmt.Sprintf("%#v", this.PaymentOrder) + `}`}, ", ")
	return s
}
func (this *DataRow_RegularPack) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_RegularPack{` +
		`RegularPack:` + fmt.Sprintf("%#v", this.RegularPack) + `}`}, ", ")
	return s
}
func (this *DataRow_RepeatTask) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_RepeatTask{` +
		`RepeatTask:` + fmt.Sprintf("%#v", this.RepeatTask) + `}`}, ", ")
	return s
}
func (this *DataRow_Research) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_Research{` +
		`Research:` + fmt.Sprintf("%#v", this.Research) + `}`}, ", ")
	return s
}
func (this *DataRow_TaskCounter) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_TaskCounter{` +
		`TaskCounter:` + fmt.Sprintf("%#v", this.TaskCounter) + `}`}, ", ")
	return s
}
func (this *DataRow_UserBenefit) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_UserBenefit{` +
		`UserBenefit:` + fmt.Sprintf("%#v", this.UserBenefit) + `}`}, ", ")
	return s
}
func (this *DataRow_UserData) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_UserData{` +
		`UserData:` + fmt.Sprintf("%#v", this.UserData) + `}`}, ", ")
	return s
}
func (this *DataRow_UserDevices) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_UserDevices{` +
		`UserDevices:` + fmt.Sprintf("%#v", this.UserDevices) + `}`}, ", ")
	return s
}
func (this *DataRow_UserIapBuyTimes) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_UserIapBuyTimes{` +
		`UserIapBuyTimes:` + fmt.Sprintf("%#v", this.UserIapBuyTimes) + `}`}, ", ")
	return s
}
func (this *DataRow_UserInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_UserInfo{` +
		`UserInfo:` + fmt.Sprintf("%#v", this.UserInfo) + `}`}, ", ")
	return s
}
func (this *DataRow_UserJob) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_UserJob{` +
		`UserJob:` + fmt.Sprintf("%#v", this.UserJob) + `}`}, ", ")
	return s
}
func (this *DataRow_UserLookup) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_UserLookup{` +
		`UserLookup:` + fmt.Sprintf("%#v", this.UserLookup) + `}`}, ", ")
	return s
}
func (this *DataRow_UserMail) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_UserMail{` +
		`UserMail:` + fmt.Sprintf("%#v", this.UserMail) + `}`}, ", ")
	return s
}
func (this *DataRow_UserTroop) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_UserTroop{` +
		`UserTroop:` + fmt.Sprintf("%#v", this.UserTroop) + `}`}, ", ")
	return s
}
func (this *DataRow_Villager) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_Villager{` +
		`Villager:` + fmt.Sprintf("%#v", this.Villager) + `}`}, ", ")
	return s
}
func (this *DataRow_WeeklyTask) GoString() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&minirpc.DataRow_WeeklyTask{` +
		`WeeklyTask:` + fmt.Sprintf("%#v", this.WeeklyTask) + `}`}, ", ")
	return s
}
func (this *PayloadRow) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&minirpc.PayloadRow{")
	if this.AddFriendInfo != nil {
		s = append(s, "AddFriendInfo: "+fmt.Sprintf("%#v", this.AddFriendInfo)+",\n")
	}
	if this.AgreeJoinAlliance != nil {
		s = append(s, "AgreeJoinAlliance: "+fmt.Sprintf("%#v", this.AgreeJoinAlliance)+",\n")
	}
	if this.OrderResult != nil {
		s = append(s, "OrderResult: "+fmt.Sprintf("%#v", this.OrderResult)+",\n")
	}
	if this.UserPush != nil {
		s = append(s, "UserPush: "+fmt.Sprintf("%#v", this.UserPush)+",\n")
	}
	if this.PerPlayerScore != nil {
		s = append(s, "PerPlayerScore: "+fmt.Sprintf("%#v", this.PerPlayerScore)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringDatarow(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *Deprecated) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Deprecated) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Deprecated) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *DataRow) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DataRow) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Row != nil {
		{
			size := m.Row.Size()
			i -= size
			if _, err := m.Row.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	if m.Table != 0 {
		i = encodeVarintDatarow(dAtA, i, uint64(m.Table))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DataRow_UserInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_UserInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.UserInfo != nil {
		{
			size, err := m.UserInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_HeroInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_HeroInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.HeroInfo != nil {
		{
			size, err := m.HeroInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_MonsterInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_MonsterInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.MonsterInfo != nil {
		{
			size, err := m.MonsterInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_UserLookup) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_UserLookup) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.UserLookup != nil {
		{
			size, err := m.UserLookup.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_Consumable) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_Consumable) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Consumable != nil {
		{
			size, err := m.Consumable.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_BuildingInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_BuildingInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.BuildingInfo != nil {
		{
			size, err := m.BuildingInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_UserBenefit) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_UserBenefit) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.UserBenefit != nil {
		{
			size, err := m.UserBenefit.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_UserJob) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_UserJob) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.UserJob != nil {
		{
			size, err := m.UserJob.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_UserTroop) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_UserTroop) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.UserTroop != nil {
		{
			size, err := m.UserTroop.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_HeroLottery) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_HeroLottery) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.HeroLottery != nil {
		{
			size, err := m.HeroLottery.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_Research) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_Research) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Research != nil {
		{
			size, err := m.Research.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_Lord) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_Lord) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Lord != nil {
		{
			size, err := m.Lord.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_MainTask) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_MainTask) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.MainTask != nil {
		{
			size, err := m.MainTask.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x72
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_GrowthTask) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_GrowthTask) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.GrowthTask != nil {
		{
			size, err := m.GrowthTask.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x7a
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_DailyTask) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_DailyTask) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.DailyTask != nil {
		{
			size, err := m.DailyTask.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_RepeatTask) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_RepeatTask) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RepeatTask != nil {
		{
			size, err := m.RepeatTask.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_WeeklyTask) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_WeeklyTask) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.WeeklyTask != nil {
		{
			size, err := m.WeeklyTask.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x92
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_MapEvent) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_MapEvent) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.MapEvent != nil {
		{
			size, err := m.MapEvent.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x9a
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_Villager) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_Villager) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Villager != nil {
		{
			size, err := m.Villager.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa2
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_Animal) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_Animal) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Animal != nil {
		{
			size, err := m.Animal.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xaa
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_Dungeon) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_Dungeon) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Dungeon != nil {
		{
			size, err := m.Dungeon.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb2
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_OutConsumable) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_OutConsumable) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.OutConsumable != nil {
		{
			size, err := m.OutConsumable.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xba
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_HeroSkill) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_HeroSkill) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.HeroSkill != nil {
		{
			size, err := m.HeroSkill.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc2
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_Dave) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_Dave) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Dave != nil {
		{
			size, err := m.Dave.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xca
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_UserData) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_UserData) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.UserData != nil {
		{
			size, err := m.UserData.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xd2
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_MainLineStage) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_MainLineStage) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.MainLineStage != nil {
		{
			size, err := m.MainLineStage.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xda
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_UserDevices) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_UserDevices) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.UserDevices != nil {
		{
			size, err := m.UserDevices.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xe2
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_PaymentOrder) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_PaymentOrder) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.PaymentOrder != nil {
		{
			size, err := m.PaymentOrder.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xea
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_UserIapBuyTimes) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_UserIapBuyTimes) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.UserIapBuyTimes != nil {
		{
			size, err := m.UserIapBuyTimes.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xf2
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_GlobalStageLevel) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_GlobalStageLevel) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.GlobalStageLevel != nil {
		{
			size, err := m.GlobalStageLevel.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xfa
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_LordEquip) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_LordEquip) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.LordEquip != nil {
		{
			size, err := m.LordEquip.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x82
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_LordGem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_LordGem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.LordGem != nil {
		{
			size, err := m.LordGem.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x8a
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_UserMail) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_UserMail) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.UserMail != nil {
		{
			size, err := m.UserMail.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x92
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_Alliance) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_Alliance) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Alliance != nil {
		{
			size, err := m.Alliance.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x9a
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_AllianceMember) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_AllianceMember) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.AllianceMember != nil {
		{
			size, err := m.AllianceMember.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xa2
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_AllianceShopBuy) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_AllianceShopBuy) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.AllianceShopBuy != nil {
		{
			size, err := m.AllianceShopBuy.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xaa
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_AllianceTask) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_AllianceTask) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.AllianceTask != nil {
		{
			size, err := m.AllianceTask.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xb2
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_LordGemRandom) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_LordGemRandom) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.LordGemRandom != nil {
		{
			size, err := m.LordGemRandom.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xba
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_FunctionOpen) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_FunctionOpen) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.FunctionOpen != nil {
		{
			size, err := m.FunctionOpen.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xc2
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_NewbieGuide) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_NewbieGuide) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.NewbieGuide != nil {
		{
			size, err := m.NewbieGuide.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xca
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_ActivityTask) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_ActivityTask) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.ActivityTask != nil {
		{
			size, err := m.ActivityTask.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xd2
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_RegularPack) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_RegularPack) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RegularPack != nil {
		{
			size, err := m.RegularPack.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xda
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_GrowthFund) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_GrowthFund) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.GrowthFund != nil {
		{
			size, err := m.GrowthFund.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xe2
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_FirstCharge) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_FirstCharge) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.FirstCharge != nil {
		{
			size, err := m.FirstCharge.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xea
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_MonthCard) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_MonthCard) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.MonthCard != nil {
		{
			size, err := m.MonthCard.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xf2
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_AchievementTask) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_AchievementTask) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.AchievementTask != nil {
		{
			size, err := m.AchievementTask.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xfa
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_ClientVersion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_ClientVersion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.ClientVersion != nil {
		{
			size, err := m.ClientVersion.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3
		i--
		dAtA[i] = 0x82
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_HuatuoVersion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_HuatuoVersion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.HuatuoVersion != nil {
		{
			size, err := m.HuatuoVersion.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3
		i--
		dAtA[i] = 0x8a
	}
	return len(dAtA) - i, nil
}
func (m *DataRow_TaskCounter) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataRow_TaskCounter) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.TaskCounter != nil {
		{
			size, err := m.TaskCounter.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3
		i--
		dAtA[i] = 0x92
	}
	return len(dAtA) - i, nil
}
func (m *PayloadRow) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PayloadRow) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PayloadRow) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.UserPush != nil {
		{
			size, err := m.UserPush.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.AddFriendInfo != nil {
		{
			size, err := m.AddFriendInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.AgreeJoinAlliance != nil {
		{
			size, err := m.AgreeJoinAlliance.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.OrderResult != nil {
		{
			size, err := m.OrderResult.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.PerPlayerScore != nil {
		{
			size, err := m.PerPlayerScore.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDatarow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintDatarow(dAtA []byte, offset int, v uint64) int {
	offset -= sovDatarow(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Deprecated) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *DataRow) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Table != 0 {
		n += 1 + sovDatarow(uint64(m.Table))
	}
	if m.Row != nil {
		n += m.Row.Size()
	}
	return n
}

func (m *DataRow_UserInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.UserInfo != nil {
		l = m.UserInfo.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_HeroInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.HeroInfo != nil {
		l = m.HeroInfo.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_MonsterInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MonsterInfo != nil {
		l = m.MonsterInfo.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_UserLookup) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.UserLookup != nil {
		l = m.UserLookup.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_Consumable) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Consumable != nil {
		l = m.Consumable.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_BuildingInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BuildingInfo != nil {
		l = m.BuildingInfo.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_UserBenefit) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.UserBenefit != nil {
		l = m.UserBenefit.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_UserJob) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.UserJob != nil {
		l = m.UserJob.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_UserTroop) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.UserTroop != nil {
		l = m.UserTroop.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_HeroLottery) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.HeroLottery != nil {
		l = m.HeroLottery.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_Research) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Research != nil {
		l = m.Research.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_Lord) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Lord != nil {
		l = m.Lord.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_MainTask) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MainTask != nil {
		l = m.MainTask.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_GrowthTask) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.GrowthTask != nil {
		l = m.GrowthTask.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_DailyTask) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DailyTask != nil {
		l = m.DailyTask.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_RepeatTask) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RepeatTask != nil {
		l = m.RepeatTask.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_WeeklyTask) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.WeeklyTask != nil {
		l = m.WeeklyTask.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_MapEvent) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MapEvent != nil {
		l = m.MapEvent.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_Villager) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Villager != nil {
		l = m.Villager.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_Animal) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Animal != nil {
		l = m.Animal.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_Dungeon) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Dungeon != nil {
		l = m.Dungeon.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_OutConsumable) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.OutConsumable != nil {
		l = m.OutConsumable.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_HeroSkill) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.HeroSkill != nil {
		l = m.HeroSkill.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_Dave) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Dave != nil {
		l = m.Dave.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_UserData) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.UserData != nil {
		l = m.UserData.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_MainLineStage) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MainLineStage != nil {
		l = m.MainLineStage.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_UserDevices) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.UserDevices != nil {
		l = m.UserDevices.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_PaymentOrder) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PaymentOrder != nil {
		l = m.PaymentOrder.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_UserIapBuyTimes) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.UserIapBuyTimes != nil {
		l = m.UserIapBuyTimes.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_GlobalStageLevel) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.GlobalStageLevel != nil {
		l = m.GlobalStageLevel.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_LordEquip) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.LordEquip != nil {
		l = m.LordEquip.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_LordGem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.LordGem != nil {
		l = m.LordGem.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_UserMail) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.UserMail != nil {
		l = m.UserMail.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_Alliance) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Alliance != nil {
		l = m.Alliance.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_AllianceMember) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.AllianceMember != nil {
		l = m.AllianceMember.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_AllianceShopBuy) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.AllianceShopBuy != nil {
		l = m.AllianceShopBuy.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_AllianceTask) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.AllianceTask != nil {
		l = m.AllianceTask.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_LordGemRandom) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.LordGemRandom != nil {
		l = m.LordGemRandom.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_FunctionOpen) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FunctionOpen != nil {
		l = m.FunctionOpen.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_NewbieGuide) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.NewbieGuide != nil {
		l = m.NewbieGuide.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_ActivityTask) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ActivityTask != nil {
		l = m.ActivityTask.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_RegularPack) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegularPack != nil {
		l = m.RegularPack.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_GrowthFund) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.GrowthFund != nil {
		l = m.GrowthFund.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_FirstCharge) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FirstCharge != nil {
		l = m.FirstCharge.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_MonthCard) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MonthCard != nil {
		l = m.MonthCard.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_AchievementTask) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.AchievementTask != nil {
		l = m.AchievementTask.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_ClientVersion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ClientVersion != nil {
		l = m.ClientVersion.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_HuatuoVersion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.HuatuoVersion != nil {
		l = m.HuatuoVersion.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *DataRow_TaskCounter) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TaskCounter != nil {
		l = m.TaskCounter.Size()
		n += 2 + l + sovDatarow(uint64(l))
	}
	return n
}
func (m *PayloadRow) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PerPlayerScore != nil {
		l = m.PerPlayerScore.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	if m.OrderResult != nil {
		l = m.OrderResult.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	if m.AgreeJoinAlliance != nil {
		l = m.AgreeJoinAlliance.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	if m.AddFriendInfo != nil {
		l = m.AddFriendInfo.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	if m.UserPush != nil {
		l = m.UserPush.Size()
		n += 1 + l + sovDatarow(uint64(l))
	}
	return n
}

func sovDatarow(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozDatarow(x uint64) (n int) {
	return sovDatarow(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Deprecated) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDatarow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Deprecated: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Deprecated: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDatarow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDatarow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DataRow) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDatarow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DataRow: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DataRow: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Table", wireType)
			}
			m.Table = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Table |= ClientTable(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &UserInfo{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_UserInfo{v}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HeroInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &HeroInfo{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_HeroInfo{v}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MonsterInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &MonsterInfo{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_MonsterInfo{v}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserLookup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &UserLookup{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_UserLookup{v}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Consumable", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Consumable{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_Consumable{v}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BuildingInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &BuildingInfo{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_BuildingInfo{v}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserBenefit", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &UserBenefit{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_UserBenefit{v}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserJob", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &UserJob{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_UserJob{v}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserTroop", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &UserTroop{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_UserTroop{v}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HeroLottery", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &HeroLottery{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_HeroLottery{v}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Research", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Research{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_Research{v}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lord", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Lord{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_Lord{v}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MainTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &MainTask{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_MainTask{v}
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GrowthTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &GrowthTask{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_GrowthTask{v}
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DailyTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &DailyTask{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_DailyTask{v}
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RepeatTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &RepeatTask{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_RepeatTask{v}
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WeeklyTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &WeeklyTask{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_WeeklyTask{v}
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MapEvent", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &MapEvent{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_MapEvent{v}
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Villager", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Villager{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_Villager{v}
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Animal", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Animal{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_Animal{v}
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dungeon", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Dungeon{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_Dungeon{v}
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OutConsumable", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &OutConsumable{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_OutConsumable{v}
			iNdEx = postIndex
		case 24:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HeroSkill", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &HeroSkill{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_HeroSkill{v}
			iNdEx = postIndex
		case 25:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dave", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Dave{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_Dave{v}
			iNdEx = postIndex
		case 26:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserData", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &UserData{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_UserData{v}
			iNdEx = postIndex
		case 27:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MainLineStage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &MainLineStage{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_MainLineStage{v}
			iNdEx = postIndex
		case 28:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserDevices", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &UserDevices{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_UserDevices{v}
			iNdEx = postIndex
		case 29:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PaymentOrder", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &PaymentOrder{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_PaymentOrder{v}
			iNdEx = postIndex
		case 30:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserIapBuyTimes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &UserIapBuyTimes{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_UserIapBuyTimes{v}
			iNdEx = postIndex
		case 31:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GlobalStageLevel", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &GlobalStageLevel{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_GlobalStageLevel{v}
			iNdEx = postIndex
		case 32:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LordEquip", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &LordEquip{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_LordEquip{v}
			iNdEx = postIndex
		case 33:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LordGem", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &LordGem{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_LordGem{v}
			iNdEx = postIndex
		case 34:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserMail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &UserMail{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_UserMail{v}
			iNdEx = postIndex
		case 35:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Alliance", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Alliance{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_Alliance{v}
			iNdEx = postIndex
		case 36:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllianceMember", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &AllianceMember{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_AllianceMember{v}
			iNdEx = postIndex
		case 37:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllianceShopBuy", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &AllianceShopBuy{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_AllianceShopBuy{v}
			iNdEx = postIndex
		case 38:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllianceTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &AllianceTask{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_AllianceTask{v}
			iNdEx = postIndex
		case 39:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LordGemRandom", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &LordGemRandom{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_LordGemRandom{v}
			iNdEx = postIndex
		case 40:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FunctionOpen", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &FunctionOpen{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_FunctionOpen{v}
			iNdEx = postIndex
		case 41:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewbieGuide", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &NewbieGuide{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_NewbieGuide{v}
			iNdEx = postIndex
		case 42:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActivityTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ActivityTask{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_ActivityTask{v}
			iNdEx = postIndex
		case 43:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegularPack", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &RegularPack{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_RegularPack{v}
			iNdEx = postIndex
		case 44:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GrowthFund", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &GrowthFund{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_GrowthFund{v}
			iNdEx = postIndex
		case 45:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FirstCharge", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &FirstCharge{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_FirstCharge{v}
			iNdEx = postIndex
		case 46:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MonthCard", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &MonthCard{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_MonthCard{v}
			iNdEx = postIndex
		case 47:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AchievementTask", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &AchievementTask{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_AchievementTask{v}
			iNdEx = postIndex
		case 48:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientVersion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ClientVersion{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_ClientVersion{v}
			iNdEx = postIndex
		case 49:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HuatuoVersion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &HuatuoVersion{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_HuatuoVersion{v}
			iNdEx = postIndex
		case 50:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskCounter", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &TaskCounter{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Row = &DataRow_TaskCounter{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDatarow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDatarow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PayloadRow) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDatarow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PayloadRow: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PayloadRow: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PerPlayerScore", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.PerPlayerScore == nil {
				m.PerPlayerScore = &Deprecated{}
			}
			if err := m.PerPlayerScore.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrderResult", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.OrderResult == nil {
				m.OrderResult = &OrderResult{}
			}
			if err := m.OrderResult.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AgreeJoinAlliance", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AgreeJoinAlliance == nil {
				m.AgreeJoinAlliance = &AgreeJoinAlliance{}
			}
			if err := m.AgreeJoinAlliance.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AddFriendInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AddFriendInfo == nil {
				m.AddFriendInfo = &AddFriendInfo{}
			}
			if err := m.AddFriendInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserPush", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDatarow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDatarow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.UserPush == nil {
				m.UserPush = &UserPush{}
			}
			if err := m.UserPush.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDatarow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDatarow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipDatarow(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowDatarow
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDatarow
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthDatarow
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupDatarow
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthDatarow
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthDatarow        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowDatarow          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupDatarow = fmt.Errorf("proto: unexpected end of group")
)
