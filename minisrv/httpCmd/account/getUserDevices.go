package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
	"strconv"
)

func init() {
	http_post.AddPostHandler("getUserDevices", getUserDevices)
}
func getUserDevices(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid") // 获取查询参数q的值
		if uidStr != "" {
			uid, _ := strconv.ParseInt(uidStr, 10, 64)
			userDevice, _, _ := model.GetUserDevice(ctx, uid)
			if userDevice == nil {
				fmt.Fprintf(w, "can not find user devices")
				return
			}
			fmt.Fprintf(w, "player devices info  %s", userDevice.Snapshoot().String())
			return
		}
		//通过fpid删除
		fpid := values.Get("fpid") // 获取查询参数q的值
		if fpid != "" {
			userLookModel, e := orm.Get[*model.UserLookUp](ctx, fpid)
			if e != nil || userLookModel == nil {
				fmt.Fprintf(w, "can not find user")
				return
			}
			fmt.Fprintf(w, "delete user uid : %s, fpid : %s", userLookModel.Uid(), userLookModel.DeviceId())
			userLookModel.Delete(ctx)
			return
		}
		fmt.Fprintf(w, "no uid or fpid")
	})

}
