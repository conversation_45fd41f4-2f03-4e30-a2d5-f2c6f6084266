// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type IapDungeonFundRewardTableCfg struct {
	Id                int32                          `json:"Id"`             // Id
	StringId          string                         `json:"StringId"`       // StringId
	Fund              int32                          `json:"Fund"`           // 所属基金
	FundRef           *IapDungeonFundTableCfg        `json:"-"`              // 所属基金
	Type              DungeonType                    `json:"Type"`           // 副本类型
	CoinLevel         int32                          `json:"CoinLevel"`      // 金币关
	CoinLevelRef      *DungeonCoinLevelTableCfg      `json:"-"`              // 金币关
	GeneLevel         int32                          `json:"GeneLevel"`      // 技能书关
	GeneLevelRef      *DungeonGeneLevelTableCfg      `json:"-"`              // 技能书关
	LordEquipLevel    int32                          `json:"LordEquipLevel"` // 培养仓关
	LordEquipLevelRef *DungeonLordEquipLevelTableCfg `json:"-"`              // 培养仓关
	SunshineLevel     int32                          `json:"SunshineLevel"`  // 阳光关
	SunshineLevelRef  *DungeonSunshineLevelTableCfg  `json:"-"`              // 阳光关
	RewardFree        *RewardKVS                     `json:"RewardFree"`     // 免费奖励
	RewardVip         []*RewardKVS                   `json:"RewardVip"`      // 付费奖励
}

func NewIapDungeonFundRewardTableCfg() *IapDungeonFundRewardTableCfg {
	return &IapDungeonFundRewardTableCfg{
		Id:                0,
		StringId:          "",
		Fund:              0,
		FundRef:           nil,
		Type:              DungeonType(enumDefaultValue),
		CoinLevel:         0,
		CoinLevelRef:      nil,
		GeneLevel:         0,
		GeneLevelRef:      nil,
		LordEquipLevel:    0,
		LordEquipLevelRef: nil,
		SunshineLevel:     0,
		SunshineLevelRef:  nil,
		RewardFree:        NewRewardKVS(),
		RewardVip:         []*RewardKVS{},
	}
}

type IapDungeonFundRewardTable struct {
	records  map[int32]*IapDungeonFundRewardTableCfg
	localIds map[int32]struct{}
}

func NewIapDungeonFundRewardTable() *IapDungeonFundRewardTable {
	return &IapDungeonFundRewardTable{
		records:  map[int32]*IapDungeonFundRewardTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *IapDungeonFundRewardTable) Get(key int32) *IapDungeonFundRewardTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapDungeonFundRewardTable) GetAll() map[int32]*IapDungeonFundRewardTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapDungeonFundRewardTable) put(key int32, value *IapDungeonFundRewardTableCfg, local bool) *IapDungeonFundRewardTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapDungeonFundRewardTable) Range(f func(v *IapDungeonFundRewardTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapDungeonFundRewardTable) Filter(filterFuncs ...func(v *IapDungeonFundRewardTableCfg) bool) map[int32]*IapDungeonFundRewardTableCfg {
	filtered := map[int32]*IapDungeonFundRewardTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapDungeonFundRewardTable) FilterSlice(filterFuncs ...func(v *IapDungeonFundRewardTableCfg) bool) []*IapDungeonFundRewardTableCfg {
	filtered := []*IapDungeonFundRewardTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapDungeonFundRewardTable) FilterKeys(filterFuncs ...func(v *IapDungeonFundRewardTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapDungeonFundRewardTable) satisfied(v *IapDungeonFundRewardTableCfg, filterFuncs ...func(v *IapDungeonFundRewardTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapDungeonFundRewardTable) setupIndexes() error {
	return nil
}

func (t *IapDungeonFundRewardTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapDungeonFundRewardTableCfg) bindRefs(c *Configs) {
	r.FundRef = c.IapDungeonFundTable.Get(r.Fund)
	r.CoinLevelRef = c.DungeonCoinLevelTable.Get(r.CoinLevel)
	r.GeneLevelRef = c.DungeonGeneLevelTable.Get(r.GeneLevel)
	r.LordEquipLevelRef = c.DungeonLordEquipLevelTable.Get(r.LordEquipLevel)
	r.SunshineLevelRef = c.DungeonSunshineLevelTable.Get(r.SunshineLevel)
	r.RewardFree.bindRefs(c)
	for _, e := range r.RewardVip {
		e.bindRefs(c)
	}
}
