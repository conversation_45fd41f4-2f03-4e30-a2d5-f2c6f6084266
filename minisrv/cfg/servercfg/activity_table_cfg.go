// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type ActivityTableCfg struct {
	Id       int32        `json:"Id"`       // Id
	StringId string       `json:"StringId"` // StringId
	Type     IapBoothType `json:"Type"`     // 展位类型
}

func NewActivityTableCfg() *ActivityTableCfg {
	return &ActivityTableCfg{
		Id:       0,
		StringId: "",
		Type:     IapBoothType(enumDefaultValue),
	}
}

type ActivityTable struct {
	records  map[int32]*ActivityTableCfg
	localIds map[int32]struct{}
}

func NewActivityTable() *ActivityTable {
	return &ActivityTable{
		records:  map[int32]*ActivityTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *ActivityTable) Get(key int32) *ActivityTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *ActivityTable) GetAll() map[int32]*ActivityTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *ActivityTable) put(key int32, value *ActivityTableCfg, local bool) *ActivityTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *ActivityTable) Range(f func(v *ActivityTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *ActivityTable) Filter(filterFuncs ...func(v *ActivityTableCfg) bool) map[int32]*ActivityTableCfg {
	filtered := map[int32]*ActivityTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *ActivityTable) FilterSlice(filterFuncs ...func(v *ActivityTableCfg) bool) []*ActivityTableCfg {
	filtered := []*ActivityTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *ActivityTable) FilterKeys(filterFuncs ...func(v *ActivityTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *ActivityTable) satisfied(v *ActivityTableCfg, filterFuncs ...func(v *ActivityTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *ActivityTable) setupIndexes() error {
	return nil
}

func (t *ActivityTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *ActivityTableCfg) bindRefs(c *Configs) {
}
