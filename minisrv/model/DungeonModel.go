package model

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"context"
	"github.com/sasha-s/go-deadlock"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/locker"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"sync"
)

func init() {
	orm.RegisterModel[*Dungeon](nil)
}

type Dungeon struct {
	mu     deadlock.RWMutex
	locker sync.RWMutex
	minirpc.DungeonRecord
	uid int64
}

func (u *Dungeon) Locker() locker.RWLocker {
	return nil
}

func (u *Dungeon) LockPriority() int {
	return int(u.Uid())
}

func (u *Dungeon) IsGroup() bool {
	return false
}

func GetOrCreateDungeonModel(ctx context.Context, uid int64, dungeonType int32) *Dungeon {
	d, _ := orm.Get[*Dungeon](ctx, uid, dungeonType)
	if d != nil {
		return d
	}
	d, _ = orm.Create[*Dungeon](ctx, &minirpc.Dungeon{
		Uid:         uid,
		DungeonType: dungeonType,
		MaxLevel:    0,
	})
	return d
}

func ExportDungeon(ctx context.Context, uid int64) []*minirpc.Dungeon {
	ds, _ := orm.GetAll[*Dungeon](ctx, uid)
	var rets []*minirpc.Dungeon
	for _, d := range ds {
		rets = append(rets, d.Snapshoot().(*minirpc.Dungeon))
	}
	return rets

}

func GetAllDungeonModels(ctx context.Context, uid int64) ([]*Dungeon, error) {
	ds, err := orm.GetAll[*Dungeon](ctx, uid)
	return ds, err
}
