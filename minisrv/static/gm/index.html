<!DOCTYPE html>
<html>
<head>
    <title>GM管理后台</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 200px;
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
        }

        .sidebar .header {
            padding: 0 20px 20px 20px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar .menu {
            margin-top: 20px;
        }

        .sidebar .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .sidebar .menu-item:hover {
            background-color: #34495e;
        }

        .sidebar .menu-item.active {
            background-color: #3498db;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .top-bar {
            background-color: white;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .panel {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .logout-btn {
            padding: 8px 15px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .logout-btn:hover {
            background-color: #c0392b;
        }

        .search-box {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }

        .edit-btn {
            background-color: #3498db;
            color: white;
        }

        .delete-btn {
            background-color: #e74c3c;
            color: white;
        }

        /* 添加导航栏样式 */
        .nav-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }

        .nav-tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            margin-bottom: -1px;
        }

        .nav-tab.active {
            border: 1px solid #ddd;
            border-bottom-color: white;
            background: white;
        }

        /* 添加表格操作按钮样式 */
        .table-actions {
            display: flex;
            gap: 5px;
        }

        /* 添加用户搜索栏样式 */
        .search-container {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }

        .search-input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .search-btn {
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        /* 子菜单样式 */
        .submenu {
            margin-top: 5px;
            padding-left: 20px;
            display: none;
        }

        .submenu-item {
            padding: 8px 15px;
            margin: 2px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .submenu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .submenu-item.active {
            background: #007bff;
            color: white;
        }

        .menu-item:hover .submenu {
            display: block;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="header">
            <h2>GM管理后台</h2>
        </div>
        <div class="menu">
            <div class="menu-item active" onclick="showPanel('dashboard')">数据概览</div>
            <div class="menu-item" onclick="window.location.href='/gm/country'">国家限制</div>
            <div class="menu-item" onclick="window.location.href='/gm/users'">用户管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/items'">道具管理</div>
            <div class="menu-item" onclick="showPanel('mail')">邮件系统</div>
            <div class="menu-item" onclick="showPanel('announce')">公告管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/activity'">活动管理</div>
            <div class="menu-item">
                版本配置
                <div class="submenu">
                    <div class="submenu-item" onclick="window.location.href='/gm/version'">客户端版本管理</div>
                    <div class="submenu-item" onclick="window.location.href='/gm/huatuo_release'">华佗发版</div>
                </div>
            </div>
            <div class="menu-item" onclick="showPanel('logs')">系统日志</div>
        </div>
    </div>

    <div class="main-content">
        <div class="top-bar">
            <input type="text" class="search-box" placeholder="搜索...">
            <button class="logout-btn" onclick="logout()">退出登录</button>
        </div>

        <div class="content">
            <!-- 数据概览面板 -->
            <div id="dashboard-panel" class="panel">
                <h2>数据概览</h2>

                <!-- 日期选择器 -->
                <div style="margin: 20px 0;">
                    <label for="dateSelect">选择日期：</label>
                    <input type="date" id="dateSelect" style="padding: 5px; margin-right: 10px;">
                    <button onclick="loadRetentionData()" style="padding: 5px 15px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">查询</button>
                    <button onclick="resetToToday()" style="padding: 5px 15px; margin-left: 10px; background: #95a5a6; color: white; border: none; border-radius: 4px; cursor: pointer;">返回今日</button>
                </div>

                <!-- 数据展示面板 (与之前相同) -->
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 20px 0;">
                    <div style="background: #2ecc71; color: white; padding: 20px; border-radius: 4px;">
                        <h3>注册用户数</h3>
                        <p style="font-size: 24px; margin-top: 10px;" id="yesterday-new-users">-</p>
                    </div>
                    <div style="background: #3498db; color: white; padding: 20px; border-radius: 4px;">
                        <h3>登录用户数</h3>
                        <p style="font-size: 24px; margin-top: 10px;" id="yesterday-login-users">-</p>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 20px 0;">
                    <div class="retention-card" style="background: #e74c3c; color: white; padding: 20px; border-radius: 4px;">
                        <h3>新增次留</h3>
                        <p style="font-size: 20px; margin-top: 10px;">
                            <span id="new-d1-retention-rate">-</span>
                            <span style="font-size: 16px;">（<span id="new-d1-retention-count">-</span>人）</span>
                        </p>
                    </div>
                    <div class="retention-card" style="background: #9b59b6; color: white; padding: 20px; border-radius: 4px;">
                        <h3>活跃次留</h3>
                        <p style="font-size: 20px; margin-top: 10px;">
                            <span id="active-d1-retention-rate">-</span>
                            <span style="font-size: 16px;">（<span id="active-d1-retention-count">-</span>人）</span>
                        </p>
                    </div>

                    <div class="retention-card" style="background: #f1c40f; color: white; padding: 20px; border-radius: 4px;">
                        <h3>新增3留</h3>
                        <p style="font-size: 20px; margin-top: 10px;">
                            <span id="new-d3-retention-rate">-</span>
                            <span style="font-size: 16px;">（<span id="new-d3-retention-count">-</span>人）</span>
                        </p>
                    </div>
                    <div class="retention-card" style="background: #e67e22; color: white; padding: 20px; border-radius: 4px;">
                        <h3>活跃3留</h3>
                        <p style="font-size: 20px; margin-top: 10px;">
                            <span id="active-d3-retention-rate">-</span>
                            <span style="font-size: 16px;">（<span id="active-d3-retention-count">-</span>人）</span>
                        </p>
                    </div>

                    <div class="retention-card" style="background: #16a085; color: white; padding: 20px; border-radius: 4px;">
                        <h3>新增7留</h3>
                        <p style="font-size: 20px; margin-top: 10px;">
                            <span id="new-d7-retention-rate">-</span>
                            <span style="font-size: 16px;">（<span id="new-d7-retention-count">-</span>人）</span>
                        </p>
                    </div>
                    <div class="retention-card" style="background: #2980b9; color: white; padding: 20px; border-radius: 4px;">
                        <h3>活跃7留</h3>
                        <p style="font-size: 20px; margin-top: 10px;">
                            <span id="active-d7-retention-rate">-</span>
                            <span style="font-size: 16px;">（<span id="active-d7-retention-count">-</span>人）</span>
                        </p>
                    </div>
                </div>
            </div>





            <!-- 邮件系统面板 -->
            <div id="mail-panel" class="panel" style="display: none;">
                <h2>邮件系统</h2>
                <div class="mail-form" id="mail-form">
                    <h3>发送新邮件</h3>
                    <div style="margin-top: 20px;">
                        <div style="margin-bottom: 15px;">
                            <label>发送类型：</label>
                            <select id="mailRecipientType">
                                <option value="single">单个用户</option>
                                <option value="multiple">多个用户</option>
                                <option value="all">全服邮件</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label>收件人：</label>
                            <input type="text" id="mailRecipient" placeholder="用户ID（多个用逗号分隔）或全服">
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label>邮件类型：</label>
                            <select id="mailType">
                                <option value="1">系统邮件</option>
                                <option value="2">活动邮件</option>
                                <option value="3">补偿邮件</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label>标题：</label>
                            <input type="text" id="mailTitle" placeholder="邮件标题">
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label>内容：</label>
                            <textarea id="mailContent" placeholder="邮件内容" rows="4"></textarea>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label>附件：</label>
                            <button type="button" onclick="addItemRow()" class="action-btn edit-btn">添加物品</button>
                            <div id="mailItemsContainer">
                                <!-- 物品行将在这里动态添加 -->
                            </div>
                        </div>
                        <button onclick="sendMail()" class="action-btn edit-btn">发送邮件</button>
                    </div>
                </div>
            </div>

            <!-- 公告管理面板 -->
            <div id="announce-panel" class="panel" style="display: none;">
                <h2>公告管理</h2>
                <div class="search-container">
                    <button class="action-btn edit-btn" onclick="showAddAnnouncementDialog()">发布公告</button>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>标题</th>
                            <th>内容</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="announcements-table-body">
                        <!-- 公告数据将在这里动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 活动管理面板 -->
            <div id="activity-panel" class="panel" style="display: none;">
                <h2>活动管理</h2>
                <div class="search-container">
                    <button class="action-btn edit-btn" onclick="showAddActivityDialog()">添加活动</button>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>活动名称</th>
                            <th>活动类型</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="activities-table-body">
                        <!-- 活动数据将在这里动态加载 -->
                    </tbody>
                </table>

                <!-- 添加活动对话框 -->
                <div id="add-activity-dialog" class="dialog" style="display: none;">
                    <div class="dialog-content">
                        <span class="close-btn" onclick="closeAddActivityDialog()">&times;</span>
                        <h3>添加活动</h3>
                        <div class="form-group">
                            <label for="activityType">活动类型:</label>
                            <select id="activityType">
                                <!-- 活动类型将从配置中动态加载 -->
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="activityStartTime">开始时间:</label>
                            <input type="datetime-local" id="activityStartTime">
                        </div>
                        <div class="form-group">
                            <label for="activityEndTime">结束时间:</label>
                            <input type="datetime-local" id="activityEndTime">
                        </div>
                        <div class="form-group">
                            <button onclick="submitAddActivity()" class="action-btn edit-btn">提交</button>
                        </div>
                    </div>
                </div>

                <!-- 编辑活动对话框 -->
                <div id="edit-activity-dialog" class="dialog" style="display: none;">
                    <div class="dialog-content">
                        <span class="close-btn" onclick="document.getElementById('edit-activity-dialog').style.display='none'">&times;</span>
                        <h3>编辑活动</h3>
                        <input type="hidden" id="editActivityId">
                        <div class="form-group">
                            <label for="editActivityStartTime">开始时间:</label>
                            <input type="datetime-local" id="editActivityStartTime">
                        </div>
                        <div class="form-group">
                            <label for="editActivityEndTime">结束时间:</label>
                            <input type="datetime-local" id="editActivityEndTime">
                        </div>
                        <div class="form-group">
                            <button onclick="submitEditActivity()" class="action-btn edit-btn">保存</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统日志面板 -->
            <div id="logs-panel" class="panel" style="display: none;">
                <h2>系统日志</h2>
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索日志内容">
                    <select class="log-level-select">
                        <option value="">所有级别</option>
                        <option value="INFO">信息</option>
                        <option value="WARNING">警告</option>
                        <option value="ERROR">错误</option>
                    </select>
                    <button class="search-btn">搜索</button>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>级别</th>
                            <th>类别</th>
                            <th>内容</th>
                            <th>操作人</th>
                        </tr>
                    </thead>
                    <tbody id="logs-table-body">
                        <!-- 日志数据将在这里动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        function formatDate(date) {
            return date.toISOString().split('T')[0];
        }

        function resetToToday() {
            document.getElementById('dateSelect').value = formatDate(new Date());
            loadRetentionData();
        }

        function loadRetentionData() {
            const selectedDate = new Date(document.getElementById('dateSelect').value);
            const nextDay = new Date(selectedDate);
            nextDay.setDate(nextDay.getDate() + 1);
            const day3 = new Date(selectedDate);
            day3.setDate(day3.getDate() - 2); // 选择日期的3天前
            const day7 = new Date(selectedDate);
            day7.setDate(day7.getDate() - 6); // 选择日期的7天前

            // 获取基础数据
            fetch(`/cmd/getLoginData?date=${formatDate(selectedDate)}`, {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('yesterday-login-users').textContent = data.loginUsers;
                document.getElementById('yesterday-new-users').textContent = data.newUsers;
            });

            // 获取次留数据
            fetch(`/cmd/getLoginRemain?day1=${formatDate(selectedDate)}&day2=${formatDate(nextDay)}`, {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                // 新增次留
                const newRetentionRate = (data.newUsers > 0) ?
                    ((data.newRetained / data.newUsers) * 100).toFixed(2) + '%' : '-';
                document.getElementById('new-d1-retention-rate').textContent = newRetentionRate;
                document.getElementById('new-d1-retention-count').textContent = data.newRetained;

                // 活跃次留
                const activeRetentionRate = (data.activeUsers > 0) ?
                    ((data.activeRetained / data.activeUsers) * 100).toFixed(2) + '%' : '-';
                document.getElementById('active-d1-retention-rate').textContent = activeRetentionRate;
                document.getElementById('active-d1-retention-count').textContent = data.activeRetained;
            });

            // 获取3留数据
            fetch(`/cmd/getLoginRemain?day1=${formatDate(day3)}&day2=${formatDate(selectedDate)}`, {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                // 新增3留
                const newRetentionRate = (data.newUsers > 0) ?
                    ((data.newRetained / data.newUsers) * 100).toFixed(2) + '%' : '-';
                document.getElementById('new-d3-retention-rate').textContent = newRetentionRate;
                document.getElementById('new-d3-retention-count').textContent = data.newRetained;

                // 活跃3留
                const activeRetentionRate = (data.activeUsers > 0) ?
                    ((data.activeRetained / data.activeUsers) * 100).toFixed(2) + '%' : '-';
                document.getElementById('active-d3-retention-rate').textContent = activeRetentionRate;
                document.getElementById('active-d3-retention-count').textContent = data.activeRetained;
            });

            // 获取7留数据
            fetch(`/cmd/getLoginRemain?day1=${formatDate(day7)}&day2=${formatDate(selectedDate)}`, {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                // 新增7留
                const newRetentionRate = (data.newUsers > 0) ?
                    ((data.newRetained / data.newUsers) * 100).toFixed(2) + '%' : '-';
                document.getElementById('new-d7-retention-rate').textContent = newRetentionRate;
                document.getElementById('new-d7-retention-count').textContent = data.newRetained;

                // 活跃7留
                const activeRetentionRate = (data.activeUsers > 0) ?
                    ((data.activeRetained / data.activeUsers) * 100).toFixed(2) + '%' : '-';
                document.getElementById('active-d7-retention-rate').textContent = activeRetentionRate;
                document.getElementById('active-d7-retention-count').textContent = data.activeRetained;
            });
        }

        // 页面加载时初始化
        window.onload = function() {
            const token = localStorage.getItem('gmToken');
            if (!token) {
                window.location.href = '/gm/login';
                return;
            }

            // 设置日期选择器默认值为今天
            document.getElementById('dateSelect').value = formatDate(new Date());

            // 验证token并加载数据
            fetch('/gm/api/verify-token', {
                headers: {
                    'X-GM-Token': token
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Token invalid');
                }
                loadRetentionData();
            })
            .catch(error => {
                console.error('Token validation failed:', error);
                localStorage.removeItem('gmToken');
                window.location.href = '/gm/login';
            });
        }



        // 公告数据加载
        function loadAnnouncements() {
            fetch('/gm/api/announcements', {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                // 实现公告列表更新逻辑
                const tbody = document.getElementById('announcements-table-body');
                if (tbody) {
                    tbody.innerHTML = '';
                    data.announcements.forEach(announcement => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `
                            <td>${announcement.id}</td>
                            <td>${announcement.title}</td>
                            <td>${announcement.content}</td>
                            <td>${new Date(announcement.startTime).toLocaleString()}</td>
                            <td>${new Date(announcement.endTime).toLocaleString()}</td>
                            <td>${announcement.status}</td>
                            <td>
                                <button class="action-btn edit-btn" onclick="editAnnouncement(${announcement.id})">Edit</button>
                                <button class="action-btn delete-btn" onclick="deleteAnnouncement(${announcement.id})">Delete</button>
                            </td>
                        `;
                        tbody.appendChild(tr);
                    });
                }
            })
            .catch(error => console.error('Failed to load announcements:', error));
        }

        // 日志数据加载
        function loadLogs() {
            fetch('/gm/api/logs', {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                // 实现日志列表更新逻辑
                const tbody = document.getElementById('logs-table-body');
                if (tbody) {
                    tbody.innerHTML = '';
                    data.logs.forEach(log => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `
                            <td>${log.timestamp}</td>
                            <td>${log.level}</td>
                            <td>${log.category}</td>
                            <td>${log.message}</td>
                            <td>${log.operator}</td>
                        `;
                        tbody.appendChild(tr);
                    });
                }
            })
            .catch(error => console.error('Failed to load logs:', error));
        }

        // 面板切换功能
        function showPanel(panelName) {
            // Hide all panels
            document.querySelectorAll('.panel').forEach(panel => {
                panel.style.display = 'none';
            });

            // Show selected panel
            document.getElementById(panelName + '-panel').style.display = 'block';

            // Update active menu item
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // 根据需要重新加载数据
            switch(panelName) {
                case 'dashboard':
                    loadDashboardData();
                    break;
                case 'announce':
                    loadAnnouncements();
                    break;
                case 'logs':
                    loadLogs();
                    break;
            }
        }

        function logout() {
            localStorage.removeItem('gmToken');
            window.location.href = '/gm/login';
        }

        function showPanel(panelName) {
            // Hide all panels
            document.querySelectorAll('.panel').forEach(panel => {
                panel.style.display = 'none';
            });

            // Show selected panel
            document.getElementById(panelName + '-panel').style.display = 'block';

            // Update active menu item
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
        }



        // 邮件系统相关函数
        function addItemRow() {
            const container = document.getElementById('mailItemsContainer');
            const row = document.createElement('div');
            row.className = 'item-row';
            row.style = 'display: flex; gap: 10px; margin-bottom: 10px;';
            row.innerHTML = `
                <input type="text" placeholder="物品ID" style="width: 40%; padding: 8px;">
                <input type="number" placeholder="数量" style="width: 40%; padding: 8px;">
                <button type="button" onclick="removeItemRow(this)" class="delete-btn">删除</button>
            `;
            container.appendChild(row);
        }

        function removeItemRow(button) {
            button.parentElement.remove();
        }

        function sendMail() {
            const recipientType = document.getElementById('mailRecipientType').value;
            const recipient = document.getElementById('mailRecipient').value;
            const mailType = document.getElementById('mailType').value;
            const title = document.getElementById('mailTitle').value;
            const content = document.getElementById('mailContent').value;

            // 验证必填字段
            if (!title || !content) {
                alert('请填写邮件标题和内容');
                return;
            }

            if (recipientType !== 'all' && !recipient) {
                alert('请填写收件人');
                return;
            }

            // 收集物品数据
            const items = [];
            document.querySelectorAll('.item-row').forEach(row => {
                const inputs = row.getElementsByTagName('input');
                if (inputs[0].value && inputs[1].value) {
                    items.push({
                        id: inputs[0].value,
                        amount: parseInt(inputs[1].value)
                    });
                }
            });

            // 发送请求
            fetch('/gm/api/mail/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify({
                    recipientType,
                    recipient,
                    mailType,
                    title,
                    content,
                    items
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('邮件发送成功');
                    // 重置表单
                    document.getElementById('mail-form').reset();
                    document.getElementById('mailItemsContainer').innerHTML = '';
                } else {
                    alert('发送失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('发送邮件失败:', error);
                alert('发送失败，请检查网络连接');
            });
        }

        // 添加计算留存率的函数
        function calculateRetention() {
            const date1 = document.getElementById('date1').value;
            const date2 = document.getElementById('date2').value;

            if (!date1 || !date2) {
                alert('请选择两个日期');
                return;
            }

            fetch(`/cmd/getLoginRemain?day1=${date1}&day2=${date2}`, {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.text())
            .then(data => {
                const lines = data.split('\n');
                let day1Users = 0;
                let day2Users = 0;
                let retainedUsers = 0;

                lines.forEach(line => {
                    if (line.includes('day1 用户数:')) {
                        day1Users = parseInt(line.split(':')[1].trim());
                    } else if (line.includes('day2 用户数:')) {
                        day2Users = parseInt(line.split(':')[1].trim());
                    } else if (line.includes('day2日留存day1用户数:')) {
                        retainedUsers = parseInt(line.split(':')[1].trim());
                    }
                });

                // 更新显示
                document.querySelector('.day1-users').textContent = day1Users;
                document.querySelector('.day2-users').textContent = day2Users;
                document.querySelector('.retained-users').textContent = retainedUsers;

                // 计算并显示留存率
                const retentionRate = (day1Users > 0) ? ((retainedUsers / day1Users) * 100).toFixed(2) : 0;
                document.getElementById('retention-results').innerHTML =
                    `<strong>留存率: ${retentionRate}%</strong>`;
            })
            .catch(error => {
                console.error('Error:', error);
                alert('获取数据失败');
            });
        }

        // 设置默认日期
        window.onload = function() {
            const token = localStorage.getItem('gmToken');
            if (!token) {
                window.location.href = '/gm/login';
                return;
            }

            // 设置默认日期为今天和昨天
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            document.getElementById('dateSelect').value = formatDate(new Date());
            document.getElementById('date2').value = today.toISOString().split('T')[0];
            document.getElementById('date1').value = yesterday.toISOString().split('T')[0];
        }

        // 活动管理相关函数
        function loadActivities() {

            fetch('/gm/api/activities', {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                const tbody = document.getElementById('activities-table-body');
                if (tbody) {
                    tbody.innerHTML = '';
                    console.log("loadActivities--------------------------");
                    console.log(data.data);
                    data.data.activities.forEach(activity => {
                        // 获取活动状态文本
                        const statusText = activity.status ? '开启' : '关闭';
                        const statusBtnText = activity.status ? '关闭' : '开启';
                        const statusBtnClass = activity.status ? 'delete-btn' : 'edit-btn';

                        const tr = document.createElement('tr');
                        tr.innerHTML = `
                            <td>${activity.id}</td>
                            <td>${activity.name}</td>
                            <td>${activity.typeName || getActivityTypeName(activity.activityType)}</td>
                            <td>${new Date(activity.startTime * 1000).toLocaleString()}</td>
                            <td>${new Date(activity.endTime * 1000).toLocaleString()}</td>
                            <td>${statusText}</td>
                            <td>
                                <button class="action-btn ${statusBtnClass}"
                                        onclick="toggleActivity(${activity.id}, ${!activity.status})">
                                    ${statusBtnText}
                                </button>
                                <button class="action-btn edit-btn" onclick="editActivity(${activity.id})">
                                    编辑
                                </button>
                            </td>
                        `;
                        tbody.appendChild(tr);
                    });
                }
            })
            .catch(error => console.error('Failed to load activities:', error));
        }

        // 全局活动类型映射（从后端动态获取）
        let activityTypeMap = {};

        // 获取活动类型名称
        function getActivityTypeName(typeId) {
            return activityTypeMap[typeId] || `未知类型(${typeId})`;
        }

        // 初始化活动类型映射
        function initActivityTypeMap() {
            fetch('/gm/api/activity-types', {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.data && data.data.types) {
                    // 构建类型映射
                    data.data.types.forEach(type => {
                        activityTypeMap[type.type] = type.name;
                    });
                }
            })
            .catch(error => console.error('Failed to load activity type map:', error));
        }

        function loadActivityTypes() {
            fetch('/gm/api/activity-types', {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                const select = document.getElementById('activityType');
                if (select) {
                    select.innerHTML = '';
                    data.data.types.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type.type; // 使用type字段作为值
                        option.textContent = type.name;
                        select.appendChild(option);
                    });
                }
            })
            .catch(error => console.error('Failed to load activity types:', error));
        }

        function showAddActivityDialog() {
            loadActivityTypes();

            // 设置默认开始和结束时间
            const now = new Date();
            const startTime = new Date(now);
            startTime.setHours(0, 0, 0, 0); // 今天0点

            const endTime = new Date(now);
            endTime.setDate(endTime.getDate() + 7); // 7天后
            endTime.setHours(23, 59, 59, 0);

            document.getElementById('activityStartTime').value = startTime.toISOString().slice(0, 16);
            document.getElementById('activityEndTime').value = endTime.toISOString().slice(0, 16);

            document.getElementById('add-activity-dialog').style.display = 'block';
        }

        function closeAddActivityDialog() {
            document.getElementById('add-activity-dialog').style.display = 'none';
        }

        function submitAddActivity() {
            const activityType = document.getElementById('activityType').value;
            const startTime = new Date(document.getElementById('activityStartTime').value).getTime() / 1000;
            const endTime = new Date(document.getElementById('activityEndTime').value).getTime() / 1000;

            fetch('/gm/api/activities/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify({
                    activityType: parseInt(activityType),
                    startTime: startTime,
                    endTime: endTime
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert('活动添加成功');
                    closeAddActivityDialog();
                    loadActivities();
                } else {
                    alert('添加失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('添加活动失败:', error);
                alert('添加失败，请检查网络连接');
            });
        }

        function editActivity(activityId) {
            // 获取活动详情
            fetch(`/gm/api/activities/${activityId}`, {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    // 显示编辑对话框
                    document.getElementById('editActivityId').value = activityId;
                    document.getElementById('editActivityStartTime').value = new Date(data.activity.startTime * 1000).toISOString().slice(0, 16);
                    document.getElementById('editActivityEndTime').value = new Date(data.activity.endTime * 1000).toISOString().slice(0, 16);
                    document.getElementById('edit-activity-dialog').style.display = 'block';
                } else {
                    alert('获取活动详情失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('获取活动详情失败:', error);
                alert('获取活动详情失败，请检查网络连接');
            });
        }

        function submitEditActivity() {
            const activityId = document.getElementById('editActivityId').value;
            const startTime = new Date(document.getElementById('editActivityStartTime').value).getTime() / 1000;
            const endTime = new Date(document.getElementById('editActivityEndTime').value).getTime() / 1000;

            fetch('/gm/api/activities/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify({
                    activityId: parseInt(activityId),
                    startTime: startTime,
                    endTime: endTime
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert('活动更新成功');
                    document.getElementById('edit-activity-dialog').style.display = 'none';
                    loadActivities();
                } else {
                    alert('更新失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('更新活动失败:', error);
                alert('更新失败，请检查网络连接');
            });
        }

        function toggleActivity(activityId, status) {
            fetch('/gm/api/activities/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify({
                    activityId: activityId,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    alert(status ? '活动已开启' : '活动已关闭');
                    loadActivities();
                } else {
                    alert('操作失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('操作活动失败:', error);
                alert('操作失败，请检查网络连接');
            });
        }

        // 在页面加载时初始化活动列表
        window.addEventListener('load', function() {
            // 检查当前是否在活动管理面板
            if (document.getElementById('activity-panel')) {
                // 初始化活动类型映射，然后加载活动列表
                initActivityTypeMap();
                loadActivities();
            }
        });
    </script>
</body>
</html>
