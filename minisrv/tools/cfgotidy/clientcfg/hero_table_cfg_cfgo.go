// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"text/template"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t HeroTable) saveJson(dir string) error {
	return t.SaveJsonWithSuffix(dir, "", t.Filter<PERSON>lice(func(v *HeroTableCfg) bool {
		return true
	}))
}

func (t HeroTable) SaveJsonWithSuffix(dir string, suffix string, cfgs []*HeroTableCfg) error {
	jsonPath := filepath.Join(dir, "HeroTable"+suffix+".json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[HeroTable]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	// sort
	s := cfgoHeroTableSlice{}
	for _, cfgoCfg := range cfgs {
		if _, ok := t.localIds[cfgoCfg.Id]; !ok {
			continue
		}
		s = append(s, cfgoCfg)
	}
	sort.Sort(s)

	w := bufio.NewWriter(f)
	w.WriteString("{\n")
	w.WriteString("\t\"Cells\": ")
	bytes, err := json.MarshalIndent(s, "\t", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	w.WriteString("\n}\n")

	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

type cfgoHeroTableSlice []*HeroTableCfg

func (x cfgoHeroTableSlice) Len() int           { return len(x) }
func (x cfgoHeroTableSlice) Less(i, j int) bool { return x[i].Id < x[j].Id }
func (x cfgoHeroTableSlice) Swap(i, j int)      { x[i], x[j] = x[j], x[i] }

func (t *HeroTable) LoadJsonByPath(jsonPath string, configs *Configs) error {
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[HeroTable]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*HeroTableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[HeroTable]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		if err = t.Put(k, v); err != nil {
			return fmt.Errorf("[HeroTable]put record failed, path=%s, err=[%w]", jsonPath, err)
		}
	}
	if err = t.setupIndexes(); err != nil {
		return fmt.Errorf("[HeroTable]setup indexes failed, path=%s, err=[%w]", jsonPath, err)
	}
	return nil
}

func (t HeroTable) saveMeta(dir string) error {
	return t.SaveMetaWithSuffix(dir, "")
}

func (t HeroTable) SaveMetaWithSuffix(dir string, suffix string) error {
	fbPlus := false
	td := &TableData{}
	if err := json.Unmarshal(string2Bytes(HeroTableJsonContent), td); err != nil {
		return fmt.Errorf("[client]unmarshal json failed, err=[%w]", err)
	}
	if len(suffix) > 0 {
		td.FileName = td.FileName + suffix
		td.TableName = td.TableName + suffix
	}
	path := filepath.Join(dir, td.FileName+".fbs")
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	defer f.Close()

	tplStr := FlatBufferTableTpl
	if fbPlus {
		tplStr = FlatBufferPlusTableTpl
	}
	tpl, err := template.New("table_schema").Parse(tplStr)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}

	if err = tpl.Execute(f, td); err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	return nil
}

var HeroTableJsonContent string = `{
		"FileName": "HeroTable",
		"ConstTable": false,
		"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
		"TableName": "HeroTable",
		"Key": {
			"FieldName": "Id",
			"FieldType": "int32"
		},
		"Fields": [
			{
				"FieldName": "ItemId",
				"FieldType": "int32"
			},
			{
				"FieldName": "ItemGeneId",
				"FieldType": "int32"
			},
			{
				"FieldName": "HeroName",
				"FieldType": "string"
			},
			{
				"FieldName": "StarUpCostItem",
				"FieldType": "int32"
			},
			{
				"FieldName": "GradeUpCostItem",
				"FieldType": "int32"
			},
			{
				"FieldName": "StarPlanID",
				"FieldType": "HeroStarUpPlan = HeroStarPlanLegendary"
			},
			{
				"FieldName": "LevelPlanID",
				"FieldType": "HeroLevelUpPlan = HeroLevelPlanLegendaryDefense"
			},
			{
				"FieldName": "ShowPriority",
				"FieldType": "int32"
			},
			{
				"FieldName": "HeroCareer",
				"FieldType": "HeroCareer = HeroDefense"
			},
			{
				"FieldName": "HeroElement",
				"FieldType": "int32"
			},
			{
				"FieldName": "HeroQuality",
				"FieldType": "HeroQuality = HeroLegendary"
			},
			{
				"FieldName": "HeroType",
				"FieldType": "HeroType = Magic"
			},
			{
				"FieldName": "HasAtkFirstCareer",
				"FieldType": "bool"
			},
			{
				"FieldName": "AtkFirstCareer",
				"FieldType": "HeroCareer = HeroDefense"
			},
			{
				"FieldName": "SkillRectangle",
				"FieldType": "HeroSkillRangePolygon = Rectangle"
			},
			{
				"FieldName": "CollisionRadius",
				"FieldType": "float"
			},
			{
				"FieldName": "HitSkill",
				"FieldType": "int32"
			},
			{
				"FieldName": "NegativeSkill",
				"FieldType": "int32"
			},
			{
				"FieldName": "GiftSkill",
				"FieldType": "int32"
			},
			{
				"FieldName": "GiftSkillBenefits",
				"FieldType": "[BenefitsKVS]"
			},
			{
				"FieldName": "HeroImage",
				"FieldType": "string"
			},
			{
				"FieldName": "HeroTabImage",
				"FieldType": "string"
			},
			{
				"FieldName": "HeroTabBgImage",
				"FieldType": "string"
			},
			{
				"FieldName": "SettlementHeroTabImage",
				"FieldType": "string"
			},
			{
				"FieldName": "SettlementHeroTabBgImage",
				"FieldType": "string"
			},
			{
				"FieldName": "HeroSpine",
				"FieldType": "string"
			},
			{
				"FieldName": "HeroFullSpine",
				"FieldType": "string"
			},
			{
				"FieldName": "HeroFullPrefab",
				"FieldType": "string"
			},
			{
				"FieldName": "HeroFullPrefabExtra",
				"FieldType": "string"
			}
		]
	}`
