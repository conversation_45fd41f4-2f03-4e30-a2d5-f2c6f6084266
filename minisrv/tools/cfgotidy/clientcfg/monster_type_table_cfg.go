// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type MonsterTypeTableCfg struct {
	Id                     int32                   `json:"Id"`                     // Id
	Name                   string                  `json:"Name"`                   // 怪物类型名
	MonsterGrade           int32                   `json:"MonsterGrade"`           // 怪物阶级
	MonsterGradeRef        *MonsterGradeTableCfg   `json:"-"`                      // 怪物阶级
	Pos                    int32                   `json:"Pos"`                    // 怪物位置
	PosRef                 *MonsterPosTypeTableCfg `json:"-"`                      // 怪物位置
	Career                 int32                   `json:"Career"`                 // 怪物职业
	CareerRef              *MonsterCareerTableCfg  `json:"-"`                      // 怪物职业
	Immunity               []int32                 `json:"Immunity"`               // 元素伤害免疫组
	ImmunityRef            []*SkillDmgTypeTableCfg `json:"-"`                      // 元素伤害免疫组
	Weak                   []int32                 `json:"Weak"`                   // 弱点元素
	WeakRef                []*SkillDmgTypeTableCfg `json:"-"`                      // 弱点元素
	WeakParam              []string                `json:"WeakParam"`              // 弱点参数
	Resist                 []int32                 `json:"Resist"`                 // 抗性元素
	ResistRef              []*SkillDmgTypeTableCfg `json:"-"`                      // 抗性元素
	ResistParam            []string                `json:"ResistParam"`            // 抗性参数
	Feat                   []string                `json:"Feat"`                   // 特性
	FeatParam              []string                `json:"FeatParam"`              // 特性参数
	Skill                  string                  `json:"Skill"`                  // 技能
	SkillParam             []string                `json:"SkillParam"`             // 技能参数
	MonsterSkill           []int32                 `json:"MonsterSkill"`           // 怪物技能组
	MonsterSkillRef        []*MonsterSkillTableCfg `json:"-"`                      // 怪物技能组
	BallisticInterception  bool                    `json:"BallisticInterception"`  // 是否阻挡弹道
	BallisticDeviation     bool                    `json:"BallisticDeviation"`     // 是否偏折弹道
	BallisticEvasionChance float32                 `json:"BallisticEvasionChance"` // 闪避弹道概率
	RepelRatio             float32                 `json:"RepelRatio"`             // 击退比例
	ClaimingDistance       float32                 `json:"ClaimingDistance"`       // 索敌距离
	HeroRec                []int32                 `json:"HeroRec"`                // 推荐英雄
	HeroRecRef             []*HeroTableCfg         `json:"-"`                      // 推荐英雄
	Habit                  []float32               `json:"Habit"`                  // 怪物体型随机区间
	MoveSpeedTD            float32                 `json:"MoveSpeedTD"`            // 塔防模式下移动速度，m/s
	MoveSpeedPK            float32                 `json:"MoveSpeedPK"`            // 跑酷模式下移动速度，m/s
	CollisionRadius        float32                 `json:"CollisionRadius"`        // 碰撞半径
	InManual               bool                    `json:"InManual"`               // 是否在图鉴内显示
	InManualOrder          int32                   `json:"InManualOrder"`          // 图鉴排序，越小越靠前
	UnlockReward           *RewardKVS              `json:"UnlockReward"`           // 图鉴奖励
	PrefabPass             string                  `json:"PrefabPass"`             // 特殊怪物模型
	MonsterSpine           string                  `json:"MonsterSpine"`           // 怪物Spine动画
	MonsterImage           string                  `json:"MonsterImage"`           // 怪物预制（居中）
	MonsterImageBottom     string                  `json:"MonsterImageBottom"`     // 怪物预制（居底）
	ModelHeight            float32                 `json:"ModelHeight"`            // 模型高度，m
	ManualScaleRatio       float32                 `json:"ManualScaleRatio"`       // 怪物图鉴页面缩放比例
	PreviewScaleRatio      float32                 `json:"PreviewScaleRatio"`      // 预览页面的缩放比例
	MonsterDeathAudio      string                  `json:"MonsterDeathAudio"`      // 怪物死亡音效
	MoveSlowlyAction       string                  `json:"MoveSlowlyAction"`       // 慢速移动动画
	MoveSlowlySpeedRange   []float32               `json:"MoveSlowlySpeedRange"`   // 慢速移动速度范围
	MoveSlowlySpeed        float32                 `json:"MoveSlowlySpeed"`        // 慢速移动速度标准
	MoveFastAction         string                  `json:"MoveFastAction"`         // 快速移动动画
	MoveFastSpeedRange     []float32               `json:"MoveFastSpeedRange"`     // 快速移动速度范围
	MoveFastSpeed          float32                 `json:"MoveFastSpeed"`          // 快速移动速度标准
}

func NewMonsterTypeTableCfg() *MonsterTypeTableCfg {
	return &MonsterTypeTableCfg{
		Id:                     0,
		Name:                   "",
		MonsterGrade:           0,
		MonsterGradeRef:        nil,
		Pos:                    0,
		PosRef:                 nil,
		Career:                 0,
		CareerRef:              nil,
		Immunity:               []int32{},
		ImmunityRef:            []*SkillDmgTypeTableCfg{},
		Weak:                   []int32{},
		WeakRef:                []*SkillDmgTypeTableCfg{},
		WeakParam:              []string{},
		Resist:                 []int32{},
		ResistRef:              []*SkillDmgTypeTableCfg{},
		ResistParam:            []string{},
		Feat:                   []string{},
		FeatParam:              []string{},
		Skill:                  "",
		SkillParam:             []string{},
		MonsterSkill:           []int32{},
		MonsterSkillRef:        []*MonsterSkillTableCfg{},
		BallisticInterception:  false,
		BallisticDeviation:     false,
		BallisticEvasionChance: 0.0,
		RepelRatio:             0.0,
		ClaimingDistance:       0.0,
		HeroRec:                []int32{},
		HeroRecRef:             []*HeroTableCfg{},
		Habit:                  []float32{},
		MoveSpeedTD:            0.0,
		MoveSpeedPK:            0.0,
		CollisionRadius:        0.0,
		InManual:               false,
		InManualOrder:          0,
		UnlockReward:           NewRewardKVS(),
		PrefabPass:             "",
		MonsterSpine:           "",
		MonsterImage:           "",
		MonsterImageBottom:     "",
		ModelHeight:            0.0,
		ManualScaleRatio:       0.0,
		PreviewScaleRatio:      0.0,
		MonsterDeathAudio:      "",
		MoveSlowlyAction:       "",
		MoveSlowlySpeedRange:   []float32{},
		MoveSlowlySpeed:        0.0,
		MoveFastAction:         "",
		MoveFastSpeedRange:     []float32{},
		MoveFastSpeed:          0.0,
	}
}

func NewMockMonsterTypeTableCfg() *MonsterTypeTableCfg {
	return &MonsterTypeTableCfg{
		Id:                     0,
		Name:                   "",
		MonsterGrade:           0,
		MonsterGradeRef:        nil,
		Pos:                    0,
		PosRef:                 nil,
		Career:                 0,
		CareerRef:              nil,
		Immunity:               []int32{0},
		ImmunityRef:            []*SkillDmgTypeTableCfg{},
		Weak:                   []int32{0},
		WeakRef:                []*SkillDmgTypeTableCfg{},
		WeakParam:              []string{""},
		Resist:                 []int32{0},
		ResistRef:              []*SkillDmgTypeTableCfg{},
		ResistParam:            []string{""},
		Feat:                   []string{""},
		FeatParam:              []string{""},
		Skill:                  "",
		SkillParam:             []string{""},
		MonsterSkill:           []int32{0},
		MonsterSkillRef:        []*MonsterSkillTableCfg{},
		BallisticInterception:  false,
		BallisticDeviation:     false,
		BallisticEvasionChance: 0.0,
		RepelRatio:             0.0,
		ClaimingDistance:       0.0,
		HeroRec:                []int32{0},
		HeroRecRef:             []*HeroTableCfg{},
		Habit:                  []float32{0.0},
		MoveSpeedTD:            0.0,
		MoveSpeedPK:            0.0,
		CollisionRadius:        0.0,
		InManual:               false,
		InManualOrder:          0,
		UnlockReward:           NewMockRewardKVS(),
		PrefabPass:             "",
		MonsterSpine:           "",
		MonsterImage:           "",
		MonsterImageBottom:     "",
		ModelHeight:            0.0,
		ManualScaleRatio:       0.0,
		PreviewScaleRatio:      0.0,
		MonsterDeathAudio:      "",
		MoveSlowlyAction:       "",
		MoveSlowlySpeedRange:   []float32{0.0},
		MoveSlowlySpeed:        0.0,
		MoveFastAction:         "",
		MoveFastSpeedRange:     []float32{0.0},
		MoveFastSpeed:          0.0,
	}
}

type MonsterTypeTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*MonsterTypeTableCfg
	localIds         map[int32]struct{}
}

func NewMonsterTypeTable(configs *Configs) *MonsterTypeTable {
	return &MonsterTypeTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*MonsterTypeTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *MonsterTypeTable) Get(key int32) *MonsterTypeTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *MonsterTypeTable) GetAll() map[int32]*MonsterTypeTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *MonsterTypeTable) put(key int32, value *MonsterTypeTableCfg, local bool) *MonsterTypeTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *MonsterTypeTable) putFromInheritedTable(key int32, value *MonsterTypeTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[MonsterTypeTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *MonsterTypeTable) Put(key int32, value *MonsterTypeTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[MonsterTypeTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *MonsterTypeTable) PutAll(m map[int32]*MonsterTypeTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *MonsterTypeTable) Range(f func(v *MonsterTypeTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *MonsterTypeTable) Filter(filterFuncs ...func(v *MonsterTypeTableCfg) bool) map[int32]*MonsterTypeTableCfg {
	filtered := map[int32]*MonsterTypeTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *MonsterTypeTable) FilterSlice(filterFuncs ...func(v *MonsterTypeTableCfg) bool) []*MonsterTypeTableCfg {
	filtered := []*MonsterTypeTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *MonsterTypeTable) FilterKeys(filterFuncs ...func(v *MonsterTypeTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *MonsterTypeTable) satisfied(v *MonsterTypeTableCfg, filterFuncs ...func(v *MonsterTypeTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *MonsterTypeTable) setupIndexes() error {
	return nil
}

func (t *MonsterTypeTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *MonsterTypeTableCfg) bindRefs(c *Configs) {
	r.MonsterGradeRef = c.MonsterGradeTable.Get(r.MonsterGrade)
	r.PosRef = c.MonsterPosTypeTable.Get(r.Pos)
	r.CareerRef = c.MonsterCareerTable.Get(r.Career)
	for _, e := range r.Immunity {
		cfgoRefRecord := c.SkillDmgTypeTable.Get(e)
		r.ImmunityRef = append(r.ImmunityRef, cfgoRefRecord)
	}
	for _, e := range r.Weak {
		cfgoRefRecord := c.SkillDmgTypeTable.Get(e)
		r.WeakRef = append(r.WeakRef, cfgoRefRecord)
	}
	for _, e := range r.Resist {
		cfgoRefRecord := c.SkillDmgTypeTable.Get(e)
		r.ResistRef = append(r.ResistRef, cfgoRefRecord)
	}
	for _, e := range r.MonsterSkill {
		cfgoRefRecord := c.MonsterSkillTable.Get(e)
		r.MonsterSkillRef = append(r.MonsterSkillRef, cfgoRefRecord)
	}
	for _, e := range r.HeroRec {
		cfgoRefRecord := c.HeroTable.Get(e)
		r.HeroRecRef = append(r.HeroRecRef, cfgoRefRecord)
	}
	r.UnlockReward.bindRefs(c)
}

func (t *MonsterTypeTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[MonsterTypeTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewMonsterTypeTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// Name
		{
			recordCfg.Name = strings.TrimSpace(record[t.getIndexInCsv("Name")])
		}
		// MonsterGrade
		if record[t.getIndexInCsv("MonsterGrade")] == "" {
			recordCfg.MonsterGrade = 0
		} else {
			var err error
			recordCfg.MonsterGrade, err = configs.MonsterGradeTable.getIdByRef(record[t.getIndexInCsv("MonsterGrade")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=MonsterGrade, type=ref@MonsterGradeTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("MonsterGrade")], err)
				} else {
					return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=MonsterGrade, type=ref@MonsterGradeTable, value=%s, err:[%s]", record[t.getIndexInCsv("MonsterGrade")], err)
				}
			}
		}
		// Pos
		if record[t.getIndexInCsv("Pos")] == "" {
			recordCfg.Pos = 0
		} else {
			var err error
			recordCfg.Pos, err = configs.MonsterPosTypeTable.getIdByRef(record[t.getIndexInCsv("Pos")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=Pos, type=ref@MonsterPosTypeTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Pos")], err)
				} else {
					return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=Pos, type=ref@MonsterPosTypeTable, value=%s, err:[%s]", record[t.getIndexInCsv("Pos")], err)
				}
			}
		}
		// Career
		if record[t.getIndexInCsv("Career")] == "" {
			recordCfg.Career = 0
		} else {
			var err error
			recordCfg.Career, err = configs.MonsterCareerTable.getIdByRef(record[t.getIndexInCsv("Career")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=Career, type=ref@MonsterCareerTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Career")], err)
				} else {
					return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=Career, type=ref@MonsterCareerTable, value=%s, err:[%s]", record[t.getIndexInCsv("Career")], err)
				}
			}
		}
		// Immunity
		{
			if record[t.getIndexInCsv("Immunity")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Immunity")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfImmunity int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfImmunity = 0
					} else {
						var err error
						cfgoElemOfImmunity, err = configs.SkillDmgTypeTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MonsterTypeTable]unmarshal record failed, cannot parse ref@SkillDmgTypeTable in vector, varName=Immunity, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MonsterTypeTable]unmarshal record failed, cannot parse ref@SkillDmgTypeTable in vector, varName=Immunity, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.Immunity = append(recordCfg.Immunity, cfgoElemOfImmunity)
				}
			}
		}
		// Weak
		{
			if record[t.getIndexInCsv("Weak")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Weak")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfWeak int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfWeak = 0
					} else {
						var err error
						cfgoElemOfWeak, err = configs.SkillDmgTypeTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MonsterTypeTable]unmarshal record failed, cannot parse ref@SkillDmgTypeTable in vector, varName=Weak, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MonsterTypeTable]unmarshal record failed, cannot parse ref@SkillDmgTypeTable in vector, varName=Weak, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.Weak = append(recordCfg.Weak, cfgoElemOfWeak)
				}
			}
		}
		// WeakParam
		{
			if record[t.getIndexInCsv("WeakParam")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("WeakParam")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfWeakParam string = ""
					cfgoElemOfWeakParam = strings.TrimSpace(cfgoSplitStr)

					recordCfg.WeakParam = append(recordCfg.WeakParam, cfgoElemOfWeakParam)
				}
			}
		}
		// Resist
		{
			if record[t.getIndexInCsv("Resist")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Resist")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfResist int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfResist = 0
					} else {
						var err error
						cfgoElemOfResist, err = configs.SkillDmgTypeTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MonsterTypeTable]unmarshal record failed, cannot parse ref@SkillDmgTypeTable in vector, varName=Resist, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MonsterTypeTable]unmarshal record failed, cannot parse ref@SkillDmgTypeTable in vector, varName=Resist, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.Resist = append(recordCfg.Resist, cfgoElemOfResist)
				}
			}
		}
		// ResistParam
		{
			if record[t.getIndexInCsv("ResistParam")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("ResistParam")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfResistParam string = ""
					cfgoElemOfResistParam = strings.TrimSpace(cfgoSplitStr)

					recordCfg.ResistParam = append(recordCfg.ResistParam, cfgoElemOfResistParam)
				}
			}
		}
		// Feat
		{
			if record[t.getIndexInCsv("Feat")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Feat")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfFeat string = ""
					cfgoElemOfFeat = strings.TrimSpace(cfgoSplitStr)

					recordCfg.Feat = append(recordCfg.Feat, cfgoElemOfFeat)
				}
			}
		}
		// FeatParam
		{
			if record[t.getIndexInCsv("FeatParam")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("FeatParam")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfFeatParam string = ""
					cfgoElemOfFeatParam = strings.TrimSpace(cfgoSplitStr)

					recordCfg.FeatParam = append(recordCfg.FeatParam, cfgoElemOfFeatParam)
				}
			}
		}
		// Skill
		{
			recordCfg.Skill = strings.TrimSpace(record[t.getIndexInCsv("Skill")])
		}
		// SkillParam
		{
			if record[t.getIndexInCsv("SkillParam")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("SkillParam")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfSkillParam string = ""
					cfgoElemOfSkillParam = strings.TrimSpace(cfgoSplitStr)

					recordCfg.SkillParam = append(recordCfg.SkillParam, cfgoElemOfSkillParam)
				}
			}
		}
		// MonsterSkill
		{
			if record[t.getIndexInCsv("MonsterSkill")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("MonsterSkill")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfMonsterSkill int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfMonsterSkill = 0
					} else {
						var err error
						cfgoElemOfMonsterSkill, err = configs.MonsterSkillTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MonsterTypeTable]unmarshal record failed, cannot parse ref@MonsterSkillTable in vector, varName=MonsterSkill, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MonsterTypeTable]unmarshal record failed, cannot parse ref@MonsterSkillTable in vector, varName=MonsterSkill, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.MonsterSkill = append(recordCfg.MonsterSkill, cfgoElemOfMonsterSkill)
				}
			}
		}
		// BallisticInterception
		{
			if record[t.getIndexInCsv("BallisticInterception")] == "" {
				recordCfg.BallisticInterception = false
			} else {
				var err error
				recordCfg.BallisticInterception, err = strconv.ParseBool(record[t.getIndexInCsv("BallisticInterception")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=BallisticInterception, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("BallisticInterception")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=BallisticInterception, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("BallisticInterception")], err)
					}
				}
			}
		}
		// BallisticDeviation
		{
			if record[t.getIndexInCsv("BallisticDeviation")] == "" {
				recordCfg.BallisticDeviation = false
			} else {
				var err error
				recordCfg.BallisticDeviation, err = strconv.ParseBool(record[t.getIndexInCsv("BallisticDeviation")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=BallisticDeviation, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("BallisticDeviation")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=BallisticDeviation, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("BallisticDeviation")], err)
					}
				}
			}
		}
		// BallisticEvasionChance
		{
			if record[t.getIndexInCsv("BallisticEvasionChance")] == "" {
				recordCfg.BallisticEvasionChance = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("BallisticEvasionChance")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=BallisticEvasionChance, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("BallisticEvasionChance")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=BallisticEvasionChance, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("BallisticEvasionChance")], err)
					}
				}
				recordCfg.BallisticEvasionChance = float32(cfgoFloat)
			}
		}
		// RepelRatio
		{
			if record[t.getIndexInCsv("RepelRatio")] == "" {
				recordCfg.RepelRatio = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("RepelRatio")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=RepelRatio, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("RepelRatio")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=RepelRatio, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("RepelRatio")], err)
					}
				}
				recordCfg.RepelRatio = float32(cfgoFloat)
			}
		}
		// ClaimingDistance
		{
			if record[t.getIndexInCsv("ClaimingDistance")] == "" {
				recordCfg.ClaimingDistance = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("ClaimingDistance")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=ClaimingDistance, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("ClaimingDistance")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=ClaimingDistance, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("ClaimingDistance")], err)
					}
				}
				recordCfg.ClaimingDistance = float32(cfgoFloat)
			}
		}
		// HeroRec
		{
			if record[t.getIndexInCsv("HeroRec")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("HeroRec")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfHeroRec int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfHeroRec = 0
					} else {
						var err error
						cfgoElemOfHeroRec, err = configs.HeroTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MonsterTypeTable]unmarshal record failed, cannot parse ref@HeroTable in vector, varName=HeroRec, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MonsterTypeTable]unmarshal record failed, cannot parse ref@HeroTable in vector, varName=HeroRec, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.HeroRec = append(recordCfg.HeroRec, cfgoElemOfHeroRec)
				}
			}
		}
		// Habit
		{
			if record[t.getIndexInCsv("Habit")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Habit")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfHabit float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfHabit = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MonsterTypeTable]unmarshal record failed, cannot parse float in vector, varName=Habit, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MonsterTypeTable]unmarshal record failed, cannot parse float in vector, varName=Habit, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfHabit = float32(cfgoFloat)
					}

					recordCfg.Habit = append(recordCfg.Habit, cfgoElemOfHabit)
				}
			}
		}
		// MoveSpeedTD
		{
			if record[t.getIndexInCsv("MoveSpeedTD")] == "" {
				recordCfg.MoveSpeedTD = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("MoveSpeedTD")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=MoveSpeedTD, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("MoveSpeedTD")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=MoveSpeedTD, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("MoveSpeedTD")], err)
					}
				}
				recordCfg.MoveSpeedTD = float32(cfgoFloat)
			}
		}
		// MoveSpeedPK
		{
			if record[t.getIndexInCsv("MoveSpeedPK")] == "" {
				recordCfg.MoveSpeedPK = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("MoveSpeedPK")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=MoveSpeedPK, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("MoveSpeedPK")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=MoveSpeedPK, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("MoveSpeedPK")], err)
					}
				}
				recordCfg.MoveSpeedPK = float32(cfgoFloat)
			}
		}
		// CollisionRadius
		{
			if record[t.getIndexInCsv("CollisionRadius")] == "" {
				recordCfg.CollisionRadius = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("CollisionRadius")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=CollisionRadius, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("CollisionRadius")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=CollisionRadius, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("CollisionRadius")], err)
					}
				}
				recordCfg.CollisionRadius = float32(cfgoFloat)
			}
		}
		// InManual
		{
			if record[t.getIndexInCsv("InManual")] == "" {
				recordCfg.InManual = false
			} else {
				var err error
				recordCfg.InManual, err = strconv.ParseBool(record[t.getIndexInCsv("InManual")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=InManual, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("InManual")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=InManual, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("InManual")], err)
					}
				}
			}
		}
		// InManualOrder
		{
			if record[t.getIndexInCsv("InManualOrder")] == "" {
				recordCfg.InManualOrder = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("InManualOrder")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=InManualOrder, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("InManualOrder")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=InManualOrder, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("InManualOrder")], err)
					}
				}
				recordCfg.InManualOrder = int32(cfgoInt)
			}
		}
		// UnlockReward
		{
			// RewardType
			if record[t.getIndexInCsv("UnlockRewardRewardType")] == "" {
				recordCfg.UnlockReward.RewardType = 0
			} else {
				var err error
				recordCfg.UnlockReward.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("UnlockRewardRewardType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=RewardType, type=ref@ItemTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("UnlockRewardRewardType")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=RewardType, type=ref@ItemTable, value=%s, err:[%s]", record[t.getIndexInCsv("UnlockRewardRewardType")], err)
					}
				}
			}
			// RewardValue
			{
				if record[t.getIndexInCsv("UnlockRewardRewardValue")] == "" {
					recordCfg.UnlockReward.RewardValue = 0
				} else {
					cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("UnlockRewardRewardValue")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=RewardValue, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("UnlockRewardRewardValue")], err)
						} else {
							return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=RewardValue, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("UnlockRewardRewardValue")], err)
						}
					}
					recordCfg.UnlockReward.RewardValue = int32(cfgoInt)
				}
			}
		}
		// PrefabPass
		{
			recordCfg.PrefabPass = strings.TrimSpace(record[t.getIndexInCsv("PrefabPass")])
		}
		// MonsterSpine
		{
			recordCfg.MonsterSpine = strings.TrimSpace(record[t.getIndexInCsv("MonsterSpine")])
		}
		// MonsterImage
		{
			recordCfg.MonsterImage = strings.TrimSpace(record[t.getIndexInCsv("MonsterImage")])
		}
		// MonsterImageBottom
		{
			recordCfg.MonsterImageBottom = strings.TrimSpace(record[t.getIndexInCsv("MonsterImageBottom")])
		}
		// ModelHeight
		{
			if record[t.getIndexInCsv("ModelHeight")] == "" {
				recordCfg.ModelHeight = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("ModelHeight")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=ModelHeight, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("ModelHeight")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=ModelHeight, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("ModelHeight")], err)
					}
				}
				recordCfg.ModelHeight = float32(cfgoFloat)
			}
		}
		// ManualScaleRatio
		{
			if record[t.getIndexInCsv("ManualScaleRatio")] == "" {
				recordCfg.ManualScaleRatio = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("ManualScaleRatio")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=ManualScaleRatio, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("ManualScaleRatio")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=ManualScaleRatio, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("ManualScaleRatio")], err)
					}
				}
				recordCfg.ManualScaleRatio = float32(cfgoFloat)
			}
		}
		// PreviewScaleRatio
		{
			if record[t.getIndexInCsv("PreviewScaleRatio")] == "" {
				recordCfg.PreviewScaleRatio = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("PreviewScaleRatio")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=PreviewScaleRatio, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("PreviewScaleRatio")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=PreviewScaleRatio, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("PreviewScaleRatio")], err)
					}
				}
				recordCfg.PreviewScaleRatio = float32(cfgoFloat)
			}
		}
		// MonsterDeathAudio
		{
			recordCfg.MonsterDeathAudio = strings.TrimSpace(record[t.getIndexInCsv("MonsterDeathAudio")])
		}
		// MoveSlowlyAction
		{
			recordCfg.MoveSlowlyAction = strings.TrimSpace(record[t.getIndexInCsv("MoveSlowlyAction")])
		}
		// MoveSlowlySpeedRange
		{
			if record[t.getIndexInCsv("MoveSlowlySpeedRange")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("MoveSlowlySpeedRange")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfMoveSlowlySpeedRange float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfMoveSlowlySpeedRange = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MonsterTypeTable]unmarshal record failed, cannot parse float in vector, varName=MoveSlowlySpeedRange, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MonsterTypeTable]unmarshal record failed, cannot parse float in vector, varName=MoveSlowlySpeedRange, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfMoveSlowlySpeedRange = float32(cfgoFloat)
					}

					recordCfg.MoveSlowlySpeedRange = append(recordCfg.MoveSlowlySpeedRange, cfgoElemOfMoveSlowlySpeedRange)
				}
			}
		}
		// MoveSlowlySpeed
		{
			if record[t.getIndexInCsv("MoveSlowlySpeed")] == "" {
				recordCfg.MoveSlowlySpeed = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("MoveSlowlySpeed")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=MoveSlowlySpeed, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("MoveSlowlySpeed")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=MoveSlowlySpeed, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("MoveSlowlySpeed")], err)
					}
				}
				recordCfg.MoveSlowlySpeed = float32(cfgoFloat)
			}
		}
		// MoveFastAction
		{
			recordCfg.MoveFastAction = strings.TrimSpace(record[t.getIndexInCsv("MoveFastAction")])
		}
		// MoveFastSpeedRange
		{
			if record[t.getIndexInCsv("MoveFastSpeedRange")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("MoveFastSpeedRange")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfMoveFastSpeedRange float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfMoveFastSpeedRange = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MonsterTypeTable]unmarshal record failed, cannot parse float in vector, varName=MoveFastSpeedRange, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MonsterTypeTable]unmarshal record failed, cannot parse float in vector, varName=MoveFastSpeedRange, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfMoveFastSpeedRange = float32(cfgoFloat)
					}

					recordCfg.MoveFastSpeedRange = append(recordCfg.MoveFastSpeedRange, cfgoElemOfMoveFastSpeedRange)
				}
			}
		}
		// MoveFastSpeed
		{
			if record[t.getIndexInCsv("MoveFastSpeed")] == "" {
				recordCfg.MoveFastSpeed = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("MoveFastSpeed")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, varName=MoveFastSpeed, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("MoveFastSpeed")], err)
					} else {
						return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, varName=MoveFastSpeed, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("MoveFastSpeed")], err)
					}
				}
				recordCfg.MoveFastSpeed = float32(cfgoFloat)
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [MonsterTypeTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[MonsterTypeTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *MonsterTypeTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "MonsterTypeTable.csv") && (!strings.HasPrefix(fileName, "MonsterTypeTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for MonsterTypeTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[MonsterTypeTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[MonsterTypeTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[MonsterTypeTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[MonsterTypeTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[MonsterTypeTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[MonsterTypeTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[MonsterTypeTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[MonsterTypeTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [MonsterTypeTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *MonsterTypeTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[MonsterTypeTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [MonsterTypeTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *MonsterTypeTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[MonsterTypeTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *MonsterTypeTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[MonsterTypeTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[MonsterTypeTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
