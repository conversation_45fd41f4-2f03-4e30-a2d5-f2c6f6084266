{"EtcdAddrs": ["127.0.0.1:2379"], "EtcdClusterDir": "/gog-knights/cluster", "KdmSrvTTL": 10, "GameServerUrl": "http://localhost/api/", "BackupGameServerUrl": "http://localhost/api/", "GameServerMaxRetry": 6, "PathFindingMap": "kdm.map", "NumberOfReportGroups": 16, "SessionTokenExpireInSecs": 20, "MaxOngoingRequests": 10000, "CallBackJob": false, "NoticeUrl": "http://127.0.0.1:8080/panic_log.php", "ServerSecret": "4690f7bc-049a-4cfc-98b7-8270cc51780b", "PaymentServerSecret": "4690f7bc-049a-4cfc-98b7-8270cc51780b", "ORM": {"Uri": "mongodb://127.0.0.1:27017/?retryWrites=false", "_Uri": "*********************************************************************************", "MaxPoolSize": 20, "Suffix": {"default": "", "kingdom": "", "misc": "", "__isMap": ""}, "TicketsDB": "misc", "TicketsCollection": "tickets", "RedoLog": {"Dir": "redo", "Enabled": true, "SyncInterval": "1s"}, "Tables": {"default": {"Concurrence": 2, "TimeOut": "5s", "FlushInterval": "30s"}, "kingdom.march": {"Concurrence": 8}, "kingdom.kingdom_map": {"Concurrence": 8}, "__isMap": {}}}, "StargateAgent": {"Category": 0, "ProxyName": ""}, "S3Config": {"Online": false, "Prefix": "master", "Bucket": "zday-global-asset", "Region": "us-west-2", "Aws_access_key_id": "********************", "Aws_secret_access_key": "FHUHb41XdeEmktlRzgW2efSxY+/MgI1nDedBlp1l"}, "AsyncPushConfig": {"AsyncPushEnabled": false, "AsyncPushInterval": "100ms", "AsyncPushConcurrence": 8}, "BiConfig": {"BI_PATH": "gogx.global.dev"}, "RankRedisAddr": "localhost:6379", "RootDir": "/data/knight/gog-knights/minisrv", "IPCountryPath": "/usr/share/GeoIP/GeoLite2-Country.mmdb", "ClientConfig": {"cdn": "http://gogx-global-cdn.akamaized.net/"}}