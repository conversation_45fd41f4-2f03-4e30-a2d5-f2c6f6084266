package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
	"strconv"
)

func init() {
	http_post.AddPostHandler("unlockStage", unlockStage)
}
func unlockStage(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid")        // 获取查询参数q的值
		stgeIdStr := values.Get("stageId") // 获取查询参数q的值
		if uidStr == "" {
			fmt.Fprintf(w, "need uid")
			return
		}
		if stgeIdStr == "" {
			fmt.Fprintf(w, "need stageId")
			return
		}
		uid, _ := strconv.ParseInt(uidStr, 10, 64)
		stageId, _ := strconv.ParseInt(stgeIdStr, 10, 32)
		stageLine := cfg_mgr.Cfg.MainLevelTable.Get(int32(stageId))
		if stageLine == nil {
			fmt.Fprintf(w, "stage not exist")
		}
		stageModel, _ := model.GetMainLineStage(ctx, uid)
		if stageModel == nil {
			fmt.Fprintf(w, "stage model not exist")
		}
		stageModel.SetUnlockStageId(ctx, stageLine.Id)
		fmt.Fprintf(w, "success unlock stage")
	})

}
