{"10001": {"Id": 10001, "Group": 1, "Sub": 1, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10002": {"Id": 10002, "Group": 1, "Sub": 2, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10003": {"Id": 10003, "Group": 1, "Sub": 3, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10004": {"Id": 10004, "Group": 1, "Sub": 4, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 20, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10005": {"Id": 10005, "Group": 1, "Sub": 5, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 25, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10006": {"Id": 10006, "Group": 1, "Sub": 6, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 30, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10007": {"Id": 10007, "Group": 1, "Sub": 7, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 40, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10008": {"Id": 10008, "Group": 1, "Sub": 8, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 50, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10009": {"Id": 10009, "Group": 1, "Sub": 9, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 60, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10010": {"Id": 10010, "Group": 1, "Sub": 10, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 80, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10011": {"Id": 10011, "Group": 1, "Sub": 11, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 100, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10012": {"Id": 10012, "Group": 1, "Sub": 12, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 120, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10013": {"Id": 10013, "Group": 1, "Sub": 13, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 140, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10014": {"Id": 10014, "Group": 1, "Sub": 14, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 160, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10015": {"Id": 10015, "Group": 1, "Sub": 15, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 180, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10016": {"Id": 10016, "Group": 1, "Sub": 16, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10017": {"Id": 10017, "Group": 1, "Sub": 17, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 220, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10018": {"Id": 10018, "Group": 1, "Sub": 18, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 240, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10019": {"Id": 10019, "Group": 1, "Sub": 19, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 260, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10020": {"Id": 10020, "Group": 1, "Sub": 20, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 280, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10021": {"Id": 10021, "Group": 1, "Sub": 21, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 300, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10022": {"Id": 10022, "Group": 1, "Sub": 22, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 320, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10023": {"Id": 10023, "Group": 1, "Sub": 23, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 340, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10024": {"Id": 10024, "Group": 1, "Sub": 24, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 360, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10025": {"Id": 10025, "Group": 1, "Sub": 25, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 380, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10026": {"Id": 10026, "Group": 1, "Sub": 26, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10027": {"Id": 10027, "Group": 1, "Sub": 27, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 420, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10028": {"Id": 10028, "Group": 1, "Sub": 28, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 440, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10029": {"Id": 10029, "Group": 1, "Sub": 29, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 460, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10030": {"Id": 10030, "Group": 1, "Sub": 30, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 480, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "10031": {"Id": 10031, "Group": 1, "Sub": 31, "TaskType": 2, "TaskCounterType": 2, "Formula": "", "Value": 500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11001": {"Id": 11001, "Group": 2, "Sub": 1, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=8", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11002": {"Id": 11002, "Group": 2, "Sub": 2, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=13", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11003": {"Id": 11003, "Group": 2, "Sub": 3, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=18", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11004": {"Id": 11004, "Group": 2, "Sub": 4, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=23", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11005": {"Id": 11005, "Group": 2, "Sub": 5, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=28", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11006": {"Id": 11006, "Group": 2, "Sub": 6, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=33", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11007": {"Id": 11007, "Group": 2, "Sub": 7, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=38", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11008": {"Id": 11008, "Group": 2, "Sub": 8, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=43", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11009": {"Id": 11009, "Group": 2, "Sub": 9, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=48", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11010": {"Id": 11010, "Group": 2, "Sub": 10, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=53", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11011": {"Id": 11011, "Group": 2, "Sub": 11, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=58", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11012": {"Id": 11012, "Group": 2, "Sub": 12, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=63", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11013": {"Id": 11013, "Group": 2, "Sub": 13, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=68", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11014": {"Id": 11014, "Group": 2, "Sub": 14, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=73", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11015": {"Id": 11015, "Group": 2, "Sub": 15, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=78", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11016": {"Id": 11016, "Group": 2, "Sub": 16, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=83", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11017": {"Id": 11017, "Group": 2, "Sub": 17, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=88", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11018": {"Id": 11018, "Group": 2, "Sub": 18, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=93", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11019": {"Id": 11019, "Group": 2, "Sub": 19, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=98", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11020": {"Id": 11020, "Group": 2, "Sub": 20, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=103", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11021": {"Id": 11021, "Group": 2, "Sub": 21, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=108", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11022": {"Id": 11022, "Group": 2, "Sub": 22, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=113", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11023": {"Id": 11023, "Group": 2, "Sub": 23, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=118", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11024": {"Id": 11024, "Group": 2, "Sub": 24, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=123", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11025": {"Id": 11025, "Group": 2, "Sub": 25, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=128", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11026": {"Id": 11026, "Group": 2, "Sub": 26, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=133", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11027": {"Id": 11027, "Group": 2, "Sub": 27, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=138", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11028": {"Id": 11028, "Group": 2, "Sub": 28, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=143", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11029": {"Id": 11029, "Group": 2, "Sub": 29, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=148", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11030": {"Id": 11030, "Group": 2, "Sub": 30, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=153", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11031": {"Id": 11031, "Group": 2, "Sub": 31, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=158", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11032": {"Id": 11032, "Group": 2, "Sub": 32, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=163", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11033": {"Id": 11033, "Group": 2, "Sub": 33, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=168", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11034": {"Id": 11034, "Group": 2, "Sub": 34, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=173", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11035": {"Id": 11035, "Group": 2, "Sub": 35, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=178", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11036": {"Id": 11036, "Group": 2, "Sub": 36, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=183", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11037": {"Id": 11037, "Group": 2, "Sub": 37, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=188", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "11038": {"Id": 11038, "Group": 2, "Sub": 38, "TaskType": 5, "TaskCounterType": 2, "Formula": "level>=193", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "12001": {"Id": 12001, "Group": 3, "Sub": 1, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=diamond", "Value": 1000, "Reward": [{"RewardType": 1, "RewardValue": 100}]}, "12002": {"Id": 12002, "Group": 3, "Sub": 2, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=diamond", "Value": 3000, "Reward": [{"RewardType": 1, "RewardValue": 100}]}, "12003": {"Id": 12003, "Group": 3, "Sub": 3, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=diamond", "Value": 6000, "Reward": [{"RewardType": 1, "RewardValue": 100}]}, "12004": {"Id": 12004, "Group": 3, "Sub": 4, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=diamond", "Value": 10000, "Reward": [{"RewardType": 1, "RewardValue": 100}]}, "12005": {"Id": 12005, "Group": 3, "Sub": 5, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=diamond", "Value": 20000, "Reward": [{"RewardType": 1, "RewardValue": 100}]}, "12006": {"Id": 12006, "Group": 3, "Sub": 6, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=diamond", "Value": 30000, "Reward": [{"RewardType": 1, "RewardValue": 100}]}, "12007": {"Id": 12007, "Group": 3, "Sub": 7, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=diamond", "Value": 40000, "Reward": [{"RewardType": 1, "RewardValue": 100}]}, "12008": {"Id": 12008, "Group": 3, "Sub": 8, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=diamond", "Value": 50000, "Reward": [{"RewardType": 1, "RewardValue": 100}]}, "12009": {"Id": 12009, "Group": 3, "Sub": 9, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=diamond", "Value": 60000, "Reward": [{"RewardType": 1, "RewardValue": 100}]}, "12010": {"Id": 12010, "Group": 3, "Sub": 10, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=diamond", "Value": 70000, "Reward": [{"RewardType": 1, "RewardValue": 100}]}, "12011": {"Id": 12011, "Group": 3, "Sub": 11, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=diamond", "Value": 80000, "Reward": [{"RewardType": 1, "RewardValue": 100}]}, "13001": {"Id": 13001, "Group": 4, "Sub": 1, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13002": {"Id": 13002, "Group": 4, "Sub": 2, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 1000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13003": {"Id": 13003, "Group": 4, "Sub": 3, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 1500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13004": {"Id": 13004, "Group": 4, "Sub": 4, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 2000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13005": {"Id": 13005, "Group": 4, "Sub": 5, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 2500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13006": {"Id": 13006, "Group": 4, "Sub": 6, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 3000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13007": {"Id": 13007, "Group": 4, "Sub": 7, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 6000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13008": {"Id": 13008, "Group": 4, "Sub": 8, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 10000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13009": {"Id": 13009, "Group": 4, "Sub": 9, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 15000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13010": {"Id": 13010, "Group": 4, "Sub": 10, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 20000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13011": {"Id": 13011, "Group": 4, "Sub": 11, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 25000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13012": {"Id": 13012, "Group": 4, "Sub": 12, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 30000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13013": {"Id": 13013, "Group": 4, "Sub": 13, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 35000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13014": {"Id": 13014, "Group": 4, "Sub": 14, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 40000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13015": {"Id": 13015, "Group": 4, "Sub": 15, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 45000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13016": {"Id": 13016, "Group": 4, "Sub": 16, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 50000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13017": {"Id": 13017, "Group": 4, "Sub": 17, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 55000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13018": {"Id": 13018, "Group": 4, "Sub": 18, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 60000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13019": {"Id": 13019, "Group": 4, "Sub": 19, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 65000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13020": {"Id": 13020, "Group": 4, "Sub": 20, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 70000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13021": {"Id": 13021, "Group": 4, "Sub": 21, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 75000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "13022": {"Id": 13022, "Group": 4, "Sub": 22, "TaskType": 7, "TaskCounterType": 2, "Formula": "itemid=item_energy_base", "Value": 80000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14001": {"Id": 14001, "Group": 5, "Sub": 1, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=20", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14002": {"Id": 14002, "Group": 5, "Sub": 2, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=30", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14003": {"Id": 14003, "Group": 5, "Sub": 3, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=40", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14004": {"Id": 14004, "Group": 5, "Sub": 4, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=50", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14005": {"Id": 14005, "Group": 5, "Sub": 5, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=60", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14006": {"Id": 14006, "Group": 5, "Sub": 6, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=70", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14007": {"Id": 14007, "Group": 5, "Sub": 7, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=80", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14008": {"Id": 14008, "Group": 5, "Sub": 8, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=90", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14009": {"Id": 14009, "Group": 5, "Sub": 9, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=100", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14010": {"Id": 14010, "Group": 5, "Sub": 10, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=110", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14011": {"Id": 14011, "Group": 5, "Sub": 11, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=120", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14012": {"Id": 14012, "Group": 5, "Sub": 12, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=130", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14013": {"Id": 14013, "Group": 5, "Sub": 13, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=140", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "14014": {"Id": 14014, "Group": 5, "Sub": 14, "TaskType": 9, "TaskCounterType": 2, "Formula": "level>=150", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15001": {"Id": 15001, "Group": 6, "Sub": 1, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15002": {"Id": 15002, "Group": 6, "Sub": 2, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 20, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15003": {"Id": 15003, "Group": 6, "Sub": 3, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 30, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15004": {"Id": 15004, "Group": 6, "Sub": 4, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 40, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15005": {"Id": 15005, "Group": 6, "Sub": 5, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 50, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15006": {"Id": 15006, "Group": 6, "Sub": 6, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 60, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15007": {"Id": 15007, "Group": 6, "Sub": 7, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 80, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15008": {"Id": 15008, "Group": 6, "Sub": 8, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 100, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15009": {"Id": 15009, "Group": 6, "Sub": 9, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 120, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15010": {"Id": 15010, "Group": 6, "Sub": 10, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 150, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15011": {"Id": 15011, "Group": 6, "Sub": 11, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15012": {"Id": 15012, "Group": 6, "Sub": 12, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 250, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15013": {"Id": 15013, "Group": 6, "Sub": 13, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 300, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15014": {"Id": 15014, "Group": 6, "Sub": 14, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 350, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15015": {"Id": 15015, "Group": 6, "Sub": 15, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "15016": {"Id": 15016, "Group": 6, "Sub": 16, "TaskType": 47, "TaskCounterType": 2, "Formula": "", "Value": 450, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16001": {"Id": 16001, "Group": 7, "Sub": 1, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=2", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16002": {"Id": 16002, "Group": 7, "Sub": 2, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=4", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16003": {"Id": 16003, "Group": 7, "Sub": 3, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=6", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16004": {"Id": 16004, "Group": 7, "Sub": 4, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=8", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16005": {"Id": 16005, "Group": 7, "Sub": 5, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=10", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16006": {"Id": 16006, "Group": 7, "Sub": 6, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=15", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16007": {"Id": 16007, "Group": 7, "Sub": 7, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=20", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16008": {"Id": 16008, "Group": 7, "Sub": 8, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=25", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16009": {"Id": 16009, "Group": 7, "Sub": 9, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=30", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16010": {"Id": 16010, "Group": 7, "Sub": 10, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=5", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16011": {"Id": 16011, "Group": 7, "Sub": 11, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=10", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16012": {"Id": 16012, "Group": 7, "Sub": 12, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=15", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16013": {"Id": 16013, "Group": 7, "Sub": 13, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=20", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16014": {"Id": 16014, "Group": 7, "Sub": 14, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=25", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16015": {"Id": 16015, "Group": 7, "Sub": 15, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=30", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16016": {"Id": 16016, "Group": 7, "Sub": 16, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=5", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16017": {"Id": 16017, "Group": 7, "Sub": 17, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=10", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16018": {"Id": 16018, "Group": 7, "Sub": 18, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=15", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16019": {"Id": 16019, "Group": 7, "Sub": 19, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=20", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16020": {"Id": 16020, "Group": 7, "Sub": 20, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=25", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "16021": {"Id": 16021, "Group": 7, "Sub": 21, "TaskType": 15, "TaskCounterType": 2, "Formula": "level>=30", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17001": {"Id": 17001, "Group": 8, "Sub": 1, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 25, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17002": {"Id": 17002, "Group": 8, "Sub": 2, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 50, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17003": {"Id": 17003, "Group": 8, "Sub": 3, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 75, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17004": {"Id": 17004, "Group": 8, "Sub": 4, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 100, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17005": {"Id": 17005, "Group": 8, "Sub": 5, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 125, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17006": {"Id": 17006, "Group": 8, "Sub": 6, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 150, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17007": {"Id": 17007, "Group": 8, "Sub": 7, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17008": {"Id": 17008, "Group": 8, "Sub": 8, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 250, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17009": {"Id": 17009, "Group": 8, "Sub": 9, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 300, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17010": {"Id": 17010, "Group": 8, "Sub": 10, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17011": {"Id": 17011, "Group": 8, "Sub": 11, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17012": {"Id": 17012, "Group": 8, "Sub": 12, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 600, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17013": {"Id": 17013, "Group": 8, "Sub": 13, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 700, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17014": {"Id": 17014, "Group": 8, "Sub": 14, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 1200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17015": {"Id": 17015, "Group": 8, "Sub": 15, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 900, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17016": {"Id": 17016, "Group": 8, "Sub": 16, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 2000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17017": {"Id": 17017, "Group": 8, "Sub": 17, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 2500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17018": {"Id": 17018, "Group": 8, "Sub": 18, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 3000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17019": {"Id": 17019, "Group": 8, "Sub": 19, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 3500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17020": {"Id": 17020, "Group": 8, "Sub": 20, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 4000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17021": {"Id": 17021, "Group": 8, "Sub": 21, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 4500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17022": {"Id": 17022, "Group": 8, "Sub": 22, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 5000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17023": {"Id": 17023, "Group": 8, "Sub": 23, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 6000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17024": {"Id": 17024, "Group": 8, "Sub": 24, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 7000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17025": {"Id": 17025, "Group": 8, "Sub": 25, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 8000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17026": {"Id": 17026, "Group": 8, "Sub": 26, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 9000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17027": {"Id": 17027, "Group": 8, "Sub": 27, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 10000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17028": {"Id": 17028, "Group": 8, "Sub": 28, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 11000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17029": {"Id": 17029, "Group": 8, "Sub": 29, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 12000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17030": {"Id": 17030, "Group": 8, "Sub": 30, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 13000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17031": {"Id": 17031, "Group": 8, "Sub": 31, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 14000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "17032": {"Id": 17032, "Group": 8, "Sub": 32, "TaskType": 25, "TaskCounterType": 2, "Formula": "", "Value": 15000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18001": {"Id": 18001, "Group": 9, "Sub": 1, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=10", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18002": {"Id": 18002, "Group": 9, "Sub": 2, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=20", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18003": {"Id": 18003, "Group": 9, "Sub": 3, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=30", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18004": {"Id": 18004, "Group": 9, "Sub": 4, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=40", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18005": {"Id": 18005, "Group": 9, "Sub": 5, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=50", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18006": {"Id": 18006, "Group": 9, "Sub": 6, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=60", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18007": {"Id": 18007, "Group": 9, "Sub": 7, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=70", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18008": {"Id": 18008, "Group": 9, "Sub": 8, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=80", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18009": {"Id": 18009, "Group": 9, "Sub": 9, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=90", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18010": {"Id": 18010, "Group": 9, "Sub": 10, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=100", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18011": {"Id": 18011, "Group": 9, "Sub": 11, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=110", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18012": {"Id": 18012, "Group": 9, "Sub": 12, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=120", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18013": {"Id": 18013, "Group": 9, "Sub": 13, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=130", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18014": {"Id": 18014, "Group": 9, "Sub": 14, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=140", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18015": {"Id": 18015, "Group": 9, "Sub": 15, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=150", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18016": {"Id": 18016, "Group": 9, "Sub": 16, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=160", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18017": {"Id": 18017, "Group": 9, "Sub": 17, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=170", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18018": {"Id": 18018, "Group": 9, "Sub": 18, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=180", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18019": {"Id": 18019, "Group": 9, "Sub": 19, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=190", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "18020": {"Id": 18020, "Group": 9, "Sub": 20, "TaskType": 29, "TaskCounterType": 2, "Formula": "level>=200", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "19001": {"Id": 19001, "Group": 10, "Sub": 1, "TaskType": 44, "TaskCounterType": 2, "Formula": "quality>=2", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "19002": {"Id": 19002, "Group": 10, "Sub": 2, "TaskType": 44, "TaskCounterType": 2, "Formula": "quality>=3", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "19003": {"Id": 19003, "Group": 10, "Sub": 3, "TaskType": 44, "TaskCounterType": 2, "Formula": "quality>=4", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "19004": {"Id": 19004, "Group": 10, "Sub": 4, "TaskType": 44, "TaskCounterType": 2, "Formula": "quality>=5", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "19005": {"Id": 19005, "Group": 10, "Sub": 5, "TaskType": 44, "TaskCounterType": 2, "Formula": "quality>=6", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "19006": {"Id": 19006, "Group": 10, "Sub": 6, "TaskType": 44, "TaskCounterType": 2, "Formula": "quality>=7", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20001": {"Id": 20001, "Group": 11, "Sub": 1, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20002": {"Id": 20002, "Group": 11, "Sub": 2, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 20, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20003": {"Id": 20003, "Group": 11, "Sub": 3, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 40, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20004": {"Id": 20004, "Group": 11, "Sub": 4, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 60, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20005": {"Id": 20005, "Group": 11, "Sub": 5, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 80, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20006": {"Id": 20006, "Group": 11, "Sub": 6, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 100, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20007": {"Id": 20007, "Group": 11, "Sub": 7, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20008": {"Id": 20008, "Group": 11, "Sub": 8, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 300, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20009": {"Id": 20009, "Group": 11, "Sub": 9, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20010": {"Id": 20010, "Group": 11, "Sub": 10, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 600, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20011": {"Id": 20011, "Group": 11, "Sub": 11, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 800, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20012": {"Id": 20012, "Group": 11, "Sub": 12, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 1000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20013": {"Id": 20013, "Group": 11, "Sub": 13, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 1500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20014": {"Id": 20014, "Group": 11, "Sub": 14, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 2000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20015": {"Id": 20015, "Group": 11, "Sub": 15, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 2500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20016": {"Id": 20016, "Group": 11, "Sub": 16, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 3000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20017": {"Id": 20017, "Group": 11, "Sub": 17, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 3500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20018": {"Id": 20018, "Group": 11, "Sub": 18, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 4000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20019": {"Id": 20019, "Group": 11, "Sub": 19, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 4500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20020": {"Id": 20020, "Group": 11, "Sub": 20, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 5000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20021": {"Id": 20021, "Group": 11, "Sub": 21, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 5500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20022": {"Id": 20022, "Group": 11, "Sub": 22, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 6000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20023": {"Id": 20023, "Group": 11, "Sub": 23, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 6500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20024": {"Id": 20024, "Group": 11, "Sub": 24, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 7000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20025": {"Id": 20025, "Group": 11, "Sub": 25, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 7500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20026": {"Id": 20026, "Group": 11, "Sub": 26, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 8000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20027": {"Id": 20027, "Group": 11, "Sub": 27, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 8500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20028": {"Id": 20028, "Group": 11, "Sub": 28, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 9000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20029": {"Id": 20029, "Group": 11, "Sub": 29, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 9500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "20030": {"Id": 20030, "Group": 11, "Sub": 30, "TaskType": 51, "TaskCounterType": 2, "Formula": "", "Value": 10000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21001": {"Id": 21001, "Group": 12, "Sub": 1, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=3", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21002": {"Id": 21002, "Group": 12, "Sub": 2, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=3", "Value": 20, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21003": {"Id": 21003, "Group": 12, "Sub": 3, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=3", "Value": 30, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21004": {"Id": 21004, "Group": 12, "Sub": 4, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=4", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21005": {"Id": 21005, "Group": 12, "Sub": 5, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=4", "Value": 20, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21006": {"Id": 21006, "Group": 12, "Sub": 6, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=4", "Value": 30, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21007": {"Id": 21007, "Group": 12, "Sub": 7, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=5", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21008": {"Id": 21008, "Group": 12, "Sub": 8, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=5", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21009": {"Id": 21009, "Group": 12, "Sub": 9, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=5", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21010": {"Id": 21010, "Group": 12, "Sub": 10, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=5", "Value": 20, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21011": {"Id": 21011, "Group": 12, "Sub": 11, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=5", "Value": 25, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21012": {"Id": 21012, "Group": 12, "Sub": 12, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=5", "Value": 30, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21013": {"Id": 21013, "Group": 12, "Sub": 13, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=6", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21014": {"Id": 21014, "Group": 12, "Sub": 14, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=6", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21015": {"Id": 21015, "Group": 12, "Sub": 15, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=6", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21016": {"Id": 21016, "Group": 12, "Sub": 16, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=6", "Value": 20, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21017": {"Id": 21017, "Group": 12, "Sub": 17, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=6", "Value": 25, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21018": {"Id": 21018, "Group": 12, "Sub": 18, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=6", "Value": 30, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21019": {"Id": 21019, "Group": 12, "Sub": 19, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=7", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21020": {"Id": 21020, "Group": 12, "Sub": 20, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=7", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21021": {"Id": 21021, "Group": 12, "Sub": 21, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=7", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21022": {"Id": 21022, "Group": 12, "Sub": 22, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=7", "Value": 20, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21023": {"Id": 21023, "Group": 12, "Sub": 23, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=7", "Value": 25, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "21024": {"Id": 21024, "Group": 12, "Sub": 24, "TaskType": 42, "TaskCounterType": 2, "Formula": "quality>=7", "Value": 30, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22001": {"Id": 22001, "Group": 13, "Sub": 1, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22002": {"Id": 22002, "Group": 13, "Sub": 2, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 50, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22003": {"Id": 22003, "Group": 13, "Sub": 3, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 100, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22004": {"Id": 22004, "Group": 13, "Sub": 4, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22005": {"Id": 22005, "Group": 13, "Sub": 5, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22006": {"Id": 22006, "Group": 13, "Sub": 6, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 600, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22007": {"Id": 22007, "Group": 13, "Sub": 7, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 800, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22008": {"Id": 22008, "Group": 13, "Sub": 8, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 1000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22009": {"Id": 22009, "Group": 13, "Sub": 9, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 1500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22010": {"Id": 22010, "Group": 13, "Sub": 10, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 2000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22011": {"Id": 22011, "Group": 13, "Sub": 11, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 2500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22012": {"Id": 22012, "Group": 13, "Sub": 12, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 3000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22013": {"Id": 22013, "Group": 13, "Sub": 13, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 3500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22014": {"Id": 22014, "Group": 13, "Sub": 14, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 4000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22015": {"Id": 22015, "Group": 13, "Sub": 15, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 4500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22016": {"Id": 22016, "Group": 13, "Sub": 16, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 5000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22017": {"Id": 22017, "Group": 13, "Sub": 17, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 5500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22018": {"Id": 22018, "Group": 13, "Sub": 18, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 6000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22019": {"Id": 22019, "Group": 13, "Sub": 19, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 6500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22020": {"Id": 22020, "Group": 13, "Sub": 20, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 7000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22021": {"Id": 22021, "Group": 13, "Sub": 21, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 7500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22022": {"Id": 22022, "Group": 13, "Sub": 22, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 8000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22023": {"Id": 22023, "Group": 13, "Sub": 23, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 8500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22024": {"Id": 22024, "Group": 13, "Sub": 24, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 9000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22025": {"Id": 22025, "Group": 13, "Sub": 25, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 9500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "22026": {"Id": 22026, "Group": 13, "Sub": 26, "TaskType": 50, "TaskCounterType": 2, "Formula": "", "Value": 10000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23001": {"Id": 23001, "Group": 14, "Sub": 1, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 1, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23002": {"Id": 23002, "Group": 14, "Sub": 2, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 2, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23003": {"Id": 23003, "Group": 14, "Sub": 3, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 3, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23004": {"Id": 23004, "Group": 14, "Sub": 4, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23005": {"Id": 23005, "Group": 14, "Sub": 5, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23006": {"Id": 23006, "Group": 14, "Sub": 6, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 6, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23007": {"Id": 23007, "Group": 14, "Sub": 7, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 7, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23008": {"Id": 23008, "Group": 14, "Sub": 8, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 8, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23009": {"Id": 23009, "Group": 14, "Sub": 9, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 9, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23010": {"Id": 23010, "Group": 14, "Sub": 10, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23011": {"Id": 23011, "Group": 14, "Sub": 11, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 11, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23012": {"Id": 23012, "Group": 14, "Sub": 12, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 12, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23013": {"Id": 23013, "Group": 14, "Sub": 13, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 13, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23014": {"Id": 23014, "Group": 14, "Sub": 14, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 14, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "23015": {"Id": 23015, "Group": 14, "Sub": 15, "TaskType": 36, "TaskCounterType": 2, "Formula": "", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24001": {"Id": 24001, "Group": 15, "Sub": 1, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=10", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24002": {"Id": 24002, "Group": 15, "Sub": 2, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=15", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24003": {"Id": 24003, "Group": 15, "Sub": 3, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=20", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24004": {"Id": 24004, "Group": 15, "Sub": 4, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=25", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24005": {"Id": 24005, "Group": 15, "Sub": 5, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=30", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24006": {"Id": 24006, "Group": 15, "Sub": 6, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=35", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24007": {"Id": 24007, "Group": 15, "Sub": 7, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=40", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24008": {"Id": 24008, "Group": 15, "Sub": 8, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=45", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24009": {"Id": 24009, "Group": 15, "Sub": 9, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=50", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24010": {"Id": 24010, "Group": 15, "Sub": 10, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=55", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24011": {"Id": 24011, "Group": 15, "Sub": 11, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=60", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24012": {"Id": 24012, "Group": 15, "Sub": 12, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=65", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24013": {"Id": 24013, "Group": 15, "Sub": 13, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=70", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24014": {"Id": 24014, "Group": 15, "Sub": 14, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=75", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24015": {"Id": 24015, "Group": 15, "Sub": 15, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=80", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24016": {"Id": 24016, "Group": 15, "Sub": 16, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=85", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24017": {"Id": 24017, "Group": 15, "Sub": 17, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=90", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24018": {"Id": 24018, "Group": 15, "Sub": 18, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=95", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "24019": {"Id": 24019, "Group": 15, "Sub": 19, "TaskType": 41, "TaskCounterType": 2, "Formula": "level>=100", "Value": 4, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25001": {"Id": 25001, "Group": 16, "Sub": 1, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25002": {"Id": 25002, "Group": 16, "Sub": 2, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 10, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25003": {"Id": 25003, "Group": 16, "Sub": 3, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25004": {"Id": 25004, "Group": 16, "Sub": 4, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 25, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25005": {"Id": 25005, "Group": 16, "Sub": 5, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 50, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25006": {"Id": 25006, "Group": 16, "Sub": 6, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 75, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25007": {"Id": 25007, "Group": 16, "Sub": 7, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 100, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25008": {"Id": 25008, "Group": 16, "Sub": 8, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 125, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25009": {"Id": 25009, "Group": 16, "Sub": 9, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 150, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25010": {"Id": 25010, "Group": 16, "Sub": 10, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25011": {"Id": 25011, "Group": 16, "Sub": 11, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 250, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25012": {"Id": 25012, "Group": 16, "Sub": 12, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 300, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25013": {"Id": 25013, "Group": 16, "Sub": 13, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25014": {"Id": 25014, "Group": 16, "Sub": 14, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25015": {"Id": 25015, "Group": 16, "Sub": 15, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 600, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25016": {"Id": 25016, "Group": 16, "Sub": 16, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 700, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25017": {"Id": 25017, "Group": 16, "Sub": 17, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 800, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25018": {"Id": 25018, "Group": 16, "Sub": 18, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 900, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25019": {"Id": 25019, "Group": 16, "Sub": 19, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 1000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25020": {"Id": 25020, "Group": 16, "Sub": 20, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 1100, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25021": {"Id": 25021, "Group": 16, "Sub": 21, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 1200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25022": {"Id": 25022, "Group": 16, "Sub": 22, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 1300, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25023": {"Id": 25023, "Group": 16, "Sub": 23, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 1400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25024": {"Id": 25024, "Group": 16, "Sub": 24, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 1500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25025": {"Id": 25025, "Group": 16, "Sub": 25, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 1600, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25026": {"Id": 25026, "Group": 16, "Sub": 26, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 1700, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25027": {"Id": 25027, "Group": 16, "Sub": 27, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 1800, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25028": {"Id": 25028, "Group": 16, "Sub": 28, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 1900, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25029": {"Id": 25029, "Group": 16, "Sub": 29, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 2000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25030": {"Id": 25030, "Group": 16, "Sub": 30, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 2100, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25031": {"Id": 25031, "Group": 16, "Sub": 31, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 2200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25032": {"Id": 25032, "Group": 16, "Sub": 32, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 2300, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25033": {"Id": 25033, "Group": 16, "Sub": 33, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 2400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "25034": {"Id": 25034, "Group": 16, "Sub": 34, "TaskType": 39, "TaskCounterType": 2, "Formula": "", "Value": 2500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26001": {"Id": 26001, "Group": 17, "Sub": 1, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 2, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26002": {"Id": 26002, "Group": 17, "Sub": 2, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 5, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26003": {"Id": 26003, "Group": 17, "Sub": 3, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 8, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26004": {"Id": 26004, "Group": 17, "Sub": 4, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 15, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26005": {"Id": 26005, "Group": 17, "Sub": 5, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 20, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26006": {"Id": 26006, "Group": 17, "Sub": 6, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 25, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26007": {"Id": 26007, "Group": 17, "Sub": 7, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 30, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26008": {"Id": 26008, "Group": 17, "Sub": 8, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 35, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26009": {"Id": 26009, "Group": 17, "Sub": 9, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 40, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26010": {"Id": 26010, "Group": 17, "Sub": 10, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 45, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26011": {"Id": 26011, "Group": 17, "Sub": 11, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 50, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26012": {"Id": 26012, "Group": 17, "Sub": 12, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 60, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26013": {"Id": 26013, "Group": 17, "Sub": 13, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 70, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26014": {"Id": 26014, "Group": 17, "Sub": 14, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 80, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26015": {"Id": 26015, "Group": 17, "Sub": 15, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 90, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26016": {"Id": 26016, "Group": 17, "Sub": 16, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 100, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26017": {"Id": 26017, "Group": 17, "Sub": 17, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 120, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26018": {"Id": 26018, "Group": 17, "Sub": 18, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 140, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26019": {"Id": 26019, "Group": 17, "Sub": 19, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 160, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26020": {"Id": 26020, "Group": 17, "Sub": 20, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 180, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26021": {"Id": 26021, "Group": 17, "Sub": 21, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26022": {"Id": 26022, "Group": 17, "Sub": 22, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 220, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26023": {"Id": 26023, "Group": 17, "Sub": 23, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 240, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26024": {"Id": 26024, "Group": 17, "Sub": 24, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 260, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26025": {"Id": 26025, "Group": 17, "Sub": 25, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 280, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26026": {"Id": 26026, "Group": 17, "Sub": 26, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 300, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "26027": {"Id": 26027, "Group": 17, "Sub": 27, "TaskType": 40, "TaskCounterType": 2, "Formula": "", "Value": 320, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27001": {"Id": 27001, "Group": 18, "Sub": 1, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 60, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27002": {"Id": 27002, "Group": 18, "Sub": 2, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 120, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27003": {"Id": 27003, "Group": 18, "Sub": 3, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27004": {"Id": 27004, "Group": 18, "Sub": 4, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 300, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27005": {"Id": 27005, "Group": 18, "Sub": 5, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 600, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27006": {"Id": 27006, "Group": 18, "Sub": 6, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 900, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27007": {"Id": 27007, "Group": 18, "Sub": 7, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 1200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27008": {"Id": 27008, "Group": 18, "Sub": 8, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 1500, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27009": {"Id": 27009, "Group": 18, "Sub": 9, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 1800, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27010": {"Id": 27010, "Group": 18, "Sub": 10, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 2400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27011": {"Id": 27011, "Group": 18, "Sub": 11, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 3000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27012": {"Id": 27012, "Group": 18, "Sub": 12, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 3600, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27013": {"Id": 27013, "Group": 18, "Sub": 13, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 4800, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27014": {"Id": 27014, "Group": 18, "Sub": 14, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 6000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27015": {"Id": 27015, "Group": 18, "Sub": 15, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 7200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27016": {"Id": 27016, "Group": 18, "Sub": 16, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 8400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27017": {"Id": 27017, "Group": 18, "Sub": 17, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 9600, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27018": {"Id": 27018, "Group": 18, "Sub": 18, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 10800, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27019": {"Id": 27019, "Group": 18, "Sub": 19, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 12000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27020": {"Id": 27020, "Group": 18, "Sub": 20, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 13200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27021": {"Id": 27021, "Group": 18, "Sub": 21, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 14400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27022": {"Id": 27022, "Group": 18, "Sub": 22, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 15600, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27023": {"Id": 27023, "Group": 18, "Sub": 23, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 16800, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27024": {"Id": 27024, "Group": 18, "Sub": 24, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 18000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27025": {"Id": 27025, "Group": 18, "Sub": 25, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 19200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27026": {"Id": 27026, "Group": 18, "Sub": 26, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 20400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27027": {"Id": 27027, "Group": 18, "Sub": 27, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 21600, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27028": {"Id": 27028, "Group": 18, "Sub": 28, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 22800, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27029": {"Id": 27029, "Group": 18, "Sub": 29, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 24000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27030": {"Id": 27030, "Group": 18, "Sub": 30, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 25200, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27031": {"Id": 27031, "Group": 18, "Sub": 31, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 26400, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27032": {"Id": 27032, "Group": 18, "Sub": 32, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 27600, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27033": {"Id": 27033, "Group": 18, "Sub": 33, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 28800, "Reward": [{"RewardType": 1, "RewardValue": 20}]}, "27034": {"Id": 27034, "Group": 18, "Sub": 34, "TaskType": 45, "TaskCounterType": 2, "Formula": "", "Value": 30000, "Reward": [{"RewardType": 1, "RewardValue": 20}]}}