// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type HeroFragmentTableCfg struct {
	Id                              int32         `json:"Id"`                              // Id
	HeroFragment                    int32         `json:"HeroFragment"`                    // 碎片类型
	HeroFragmentRef                 *ItemTableCfg `json:"-"`                               // 碎片类型
	Hero                            int32         `json:"Hero"`                            // 对应英雄
	HeroRef                         *HeroTableCfg `json:"-"`                               // 对应英雄
	ComposeCnt                      int32         `json:"ComposeCnt"`                      // 合成英雄所需碎片数量
	DivisionCnt                     int32         `json:"DivisionCnt"`                     // 重复英雄自动分解获得数量
	CanUseUniversalFragmentExchange bool          `json:"CanUseUniversalFragmentExchange"` // 是否可以使用通用碎片兑换（已获得英雄方可兑换）
	UniversalFragmentType           int32         `json:"UniversalFragmentType"`           // 通用碎片类型
	UniversalFragmentTypeRef        *ItemTableCfg `json:"-"`                               // 通用碎片类型
}

func NewHeroFragmentTableCfg() *HeroFragmentTableCfg {
	return &HeroFragmentTableCfg{
		Id:                              0,
		HeroFragment:                    0,
		HeroFragmentRef:                 nil,
		Hero:                            0,
		HeroRef:                         nil,
		ComposeCnt:                      0,
		DivisionCnt:                     0,
		CanUseUniversalFragmentExchange: false,
		UniversalFragmentType:           0,
		UniversalFragmentTypeRef:        nil,
	}
}

func NewMockHeroFragmentTableCfg() *HeroFragmentTableCfg {
	return &HeroFragmentTableCfg{
		Id:                              0,
		HeroFragment:                    0,
		HeroFragmentRef:                 nil,
		Hero:                            0,
		HeroRef:                         nil,
		ComposeCnt:                      0,
		DivisionCnt:                     0,
		CanUseUniversalFragmentExchange: false,
		UniversalFragmentType:           0,
		UniversalFragmentTypeRef:        nil,
	}
}

type HeroFragmentTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*HeroFragmentTableCfg
	localIds         map[int32]struct{}
}

func NewHeroFragmentTable(configs *Configs) *HeroFragmentTable {
	return &HeroFragmentTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*HeroFragmentTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *HeroFragmentTable) Get(key int32) *HeroFragmentTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *HeroFragmentTable) GetAll() map[int32]*HeroFragmentTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *HeroFragmentTable) put(key int32, value *HeroFragmentTableCfg, local bool) *HeroFragmentTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *HeroFragmentTable) putFromInheritedTable(key int32, value *HeroFragmentTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[HeroFragmentTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *HeroFragmentTable) Put(key int32, value *HeroFragmentTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[HeroFragmentTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *HeroFragmentTable) PutAll(m map[int32]*HeroFragmentTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *HeroFragmentTable) Range(f func(v *HeroFragmentTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *HeroFragmentTable) Filter(filterFuncs ...func(v *HeroFragmentTableCfg) bool) map[int32]*HeroFragmentTableCfg {
	filtered := map[int32]*HeroFragmentTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *HeroFragmentTable) FilterSlice(filterFuncs ...func(v *HeroFragmentTableCfg) bool) []*HeroFragmentTableCfg {
	filtered := []*HeroFragmentTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *HeroFragmentTable) FilterKeys(filterFuncs ...func(v *HeroFragmentTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *HeroFragmentTable) satisfied(v *HeroFragmentTableCfg, filterFuncs ...func(v *HeroFragmentTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *HeroFragmentTable) setupIndexes() error {
	return nil
}

func (t *HeroFragmentTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *HeroFragmentTableCfg) bindRefs(c *Configs) {
	r.HeroFragmentRef = c.ItemTable.Get(r.HeroFragment)
	r.HeroRef = c.HeroTable.Get(r.Hero)
	r.UniversalFragmentTypeRef = c.ItemTable.Get(r.UniversalFragmentType)
}

func (t *HeroFragmentTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[HeroFragmentTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewHeroFragmentTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroFragmentTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[HeroFragmentTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// HeroFragment
		if record[t.getIndexInCsv("HeroFragment")] == "" {
			recordCfg.HeroFragment = 0
		} else {
			var err error
			recordCfg.HeroFragment, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("HeroFragment")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroFragmentTable]unmarshal csv record failed, varName=HeroFragment, type=ref@ItemTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("HeroFragment")], err)
				} else {
					return fmt.Errorf("[HeroFragmentTable]unmarshal csv record failed, varName=HeroFragment, type=ref@ItemTable, value=%s, err:[%s]", record[t.getIndexInCsv("HeroFragment")], err)
				}
			}
		}
		// Hero
		if record[t.getIndexInCsv("Hero")] == "" {
			recordCfg.Hero = 0
		} else {
			var err error
			recordCfg.Hero, err = configs.HeroTable.getIdByRef(record[t.getIndexInCsv("Hero")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroFragmentTable]unmarshal csv record failed, varName=Hero, type=ref@HeroTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Hero")], err)
				} else {
					return fmt.Errorf("[HeroFragmentTable]unmarshal csv record failed, varName=Hero, type=ref@HeroTable, value=%s, err:[%s]", record[t.getIndexInCsv("Hero")], err)
				}
			}
		}
		// ComposeCnt
		{
			if record[t.getIndexInCsv("ComposeCnt")] == "" {
				recordCfg.ComposeCnt = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("ComposeCnt")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroFragmentTable]unmarshal csv record failed, varName=ComposeCnt, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("ComposeCnt")], err)
					} else {
						return fmt.Errorf("[HeroFragmentTable]unmarshal csv record failed, varName=ComposeCnt, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("ComposeCnt")], err)
					}
				}
				recordCfg.ComposeCnt = int32(cfgoInt)
			}
		}
		// DivisionCnt
		{
			if record[t.getIndexInCsv("DivisionCnt")] == "" {
				recordCfg.DivisionCnt = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("DivisionCnt")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroFragmentTable]unmarshal csv record failed, varName=DivisionCnt, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("DivisionCnt")], err)
					} else {
						return fmt.Errorf("[HeroFragmentTable]unmarshal csv record failed, varName=DivisionCnt, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("DivisionCnt")], err)
					}
				}
				recordCfg.DivisionCnt = int32(cfgoInt)
			}
		}
		// CanUseUniversalFragmentExchange
		{
			if record[t.getIndexInCsv("CanUseUniversalFragmentExchange")] == "" {
				recordCfg.CanUseUniversalFragmentExchange = false
			} else {
				var err error
				recordCfg.CanUseUniversalFragmentExchange, err = strconv.ParseBool(record[t.getIndexInCsv("CanUseUniversalFragmentExchange")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroFragmentTable]unmarshal csv record failed, varName=CanUseUniversalFragmentExchange, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("CanUseUniversalFragmentExchange")], err)
					} else {
						return fmt.Errorf("[HeroFragmentTable]unmarshal csv record failed, varName=CanUseUniversalFragmentExchange, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("CanUseUniversalFragmentExchange")], err)
					}
				}
			}
		}
		// UniversalFragmentType
		if record[t.getIndexInCsv("UniversalFragmentType")] == "" {
			recordCfg.UniversalFragmentType = 0
		} else {
			var err error
			recordCfg.UniversalFragmentType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("UniversalFragmentType")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroFragmentTable]unmarshal csv record failed, varName=UniversalFragmentType, type=ref@ItemTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("UniversalFragmentType")], err)
				} else {
					return fmt.Errorf("[HeroFragmentTable]unmarshal csv record failed, varName=UniversalFragmentType, type=ref@ItemTable, value=%s, err:[%s]", record[t.getIndexInCsv("UniversalFragmentType")], err)
				}
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [HeroFragmentTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[HeroFragmentTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *HeroFragmentTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "HeroFragmentTable.csv") && (!strings.HasPrefix(fileName, "HeroFragmentTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for HeroFragmentTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[HeroFragmentTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[HeroFragmentTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[HeroFragmentTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[HeroFragmentTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[HeroFragmentTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[HeroFragmentTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[HeroFragmentTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[HeroFragmentTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [HeroFragmentTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *HeroFragmentTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[HeroFragmentTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [HeroFragmentTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *HeroFragmentTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[HeroFragmentTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *HeroFragmentTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[HeroFragmentTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[HeroFragmentTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
