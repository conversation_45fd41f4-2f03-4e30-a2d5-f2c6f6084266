package city

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"context"
	"time"
)

func CollectPhotovoltaicReward(ctx context.Context, uid int64, isAll bool) (map[int32]int64, error) {

	amount, _ := GetPhotovoltaicReward(ctx, uid)
	if amount == 0 {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("no reward")
	}
	line := cfg_mgr.Cfg.PhotovoltaicTable.Get(1)
	rewards := make(map[int32]int64)
	userData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypePhotoVoltaicUpdateTime))
	if isAll {
		rewards[line.EnergyType] = int64(amount * line.EnergyCnt)
		userData.SetValue(ctx, time.Now().Unix())
	} else {
		rewards[line.EnergyType] = int64(line.EnergyCnt)
		userData.SetValue(ctx, userData.Value()+6*int64(time.Hour.Seconds()))
	}
	sequence := util.GenerateSequenceString()
	for k, v := range rewards {
		model.AddItem(ctx, uid, k, v, bi.ItemFlowReason_Photovoltaic_reward, "", sequence)
		quest.Notify(ctx, uid, servercfg.TaskType_EnergyFactory, map[string]string{}, v)
	}

	return rewards, nil
}

func GetPhotovoltaicReward(ctx context.Context, uid int64) (int32, error) {
	line := cfg_mgr.Cfg.PhotovoltaicTable.Get(1)
	userData, err := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypePhotoVoltaicUpdateTime))
	if err != nil {
		return 0, err
	}
	lastRefreshTimeUnix := userData.Value()
	//lastRefreshTimeUnix = 1736398762
	now := time.Now()
	dayNum := len(line.EnergyTime) //每天领取数量
	//间隔天数
	intervalDay := (now.Unix() - lastRefreshTimeUnix) / 86400
	if intervalDay*int64(dayNum) >= int64(line.EnergyLimit) {
		return line.EnergyLimit, nil
	}
	// 初始化体力值
	stamina := int32(0)
	lastRefreshTime := time.Unix(lastRefreshTimeUnix, 0)
	// 遍历每个可能的刷新点（0, 6, 12, 18小时）
	for i := int32(0); i < line.EnergyLimit; i++ {
		for _, hour := range line.EnergyTime {
			// 构建当前刷新点的时间
			refreshTime := time.Date(now.Year(), now.Month(), now.Day(), int(hour), 0, 0, 0, time.UTC)
			refreshTime = refreshTime.Add(-24 * time.Duration(i) * time.Hour)
			// 如果当前时间已经过了刷新点，并且上次刷新时间在当前刷新点之前
			if now.After(refreshTime) && lastRefreshTime.Before(refreshTime) {
				stamina++ // 增加体力
			}

		}
		if stamina > line.EnergyLimit {
			stamina = line.EnergyLimit
			break
		}
	}

	return stamina, nil
}
