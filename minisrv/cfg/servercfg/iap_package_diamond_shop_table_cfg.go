// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type IapPackageDiamondShopTableCfg struct {
	Id                int32               `json:"Id"`                // Id
	IapPackageId      int32               `json:"IapPackageId"`      // 内购商品id
	IapPackageIdRef   *IapPackageTableCfg `json:"-"`                 // 内购商品id
	FirstDoubleReward bool                `json:"FirstDoubleReward"` // 首次购买双倍?
}

func NewIapPackageDiamondShopTableCfg() *IapPackageDiamondShopTableCfg {
	return &IapPackageDiamondShopTableCfg{
		Id:                0,
		IapPackageId:      0,
		IapPackageIdRef:   nil,
		FirstDoubleReward: false,
	}
}

type IapPackageDiamondShopTable struct {
	records  map[int32]*IapPackageDiamondShopTableCfg
	localIds map[int32]struct{}
}

func NewIapPackageDiamondShopTable() *IapPackageDiamondShopTable {
	return &IapPackageDiamondShopTable{
		records:  map[int32]*IapPackageDiamondShopTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *IapPackageDiamondShopTable) Get(key int32) *IapPackageDiamondShopTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapPackageDiamondShopTable) GetAll() map[int32]*IapPackageDiamondShopTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapPackageDiamondShopTable) put(key int32, value *IapPackageDiamondShopTableCfg, local bool) *IapPackageDiamondShopTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapPackageDiamondShopTable) Range(f func(v *IapPackageDiamondShopTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapPackageDiamondShopTable) Filter(filterFuncs ...func(v *IapPackageDiamondShopTableCfg) bool) map[int32]*IapPackageDiamondShopTableCfg {
	filtered := map[int32]*IapPackageDiamondShopTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapPackageDiamondShopTable) FilterSlice(filterFuncs ...func(v *IapPackageDiamondShopTableCfg) bool) []*IapPackageDiamondShopTableCfg {
	filtered := []*IapPackageDiamondShopTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapPackageDiamondShopTable) FilterKeys(filterFuncs ...func(v *IapPackageDiamondShopTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapPackageDiamondShopTable) satisfied(v *IapPackageDiamondShopTableCfg, filterFuncs ...func(v *IapPackageDiamondShopTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapPackageDiamondShopTable) setupIndexes() error {
	return nil
}

func (t *IapPackageDiamondShopTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapPackageDiamondShopTableCfg) bindRefs(c *Configs) {
	r.IapPackageIdRef = c.IapPackageTable.Get(r.IapPackageId)
}
