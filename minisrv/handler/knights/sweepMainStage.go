package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/main_line_stage"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 挂机扫荡
func (_ KnightsHandlers) SweepMainStageHandler(ctx *wctx.Context, req *wrpc.SweepMainStageRequest) (*wrpc.SweepMainStageReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		rewards := []*wrpc.Rewards{}
		if req.Amount == 0 {
			req.Amount = 1
		}
		for i := int32(0); i < req.Amount; i++ {
			reward, e := main_line_stage.SweepMainStage(ctx, req.UID)
			if e != nil {
				err = e
				continue
			}
			rewards = consumable.CombineRewards(rewards, reward)
		}
		rawResp = &wrpc.SweepMainStageReply{
			Result: rewards,
		}
	})
	return rawResp.(*wrpc.SweepMainStageReply), err
}
