// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type HeroLotteryMustTableCfg struct {
	Id                  int32                        `json:"Id"`               // Id
	HeroLotteryGroup    int32                        `json:"HeroLotteryGroup"` // 所属召唤池
	HeroLotteryGroupRef *HeroLotteryGroupTableCfg    `json:"-"`                // 所属召唤池
	MustCnt             int32                        `json:"MustCnt"`          // 必中次数
	IncreaseCnt         int32                        `json:"IncreaseCnt"`      // 橙卡在第几次开始概率递增
	Increment           []int32                      `json:"Increment"`        // 橙卡权重递增幅度
	IsRefresh           bool                         `json:"IsRefresh"`        // 中了之后是否刷新
	Must11              []int32                      `json:"Must11"`           // 前11抽安排
	Must11Ref           []*HeroLotteryRandomTableCfg `json:"-"`                // 前11抽安排
	MustSSR             *HeroLotterySSR              `json:"MustSSR"`          // 橙卡保底安排
	FirstHero           []int32                      `json:"FirstHero"`        // 首抽保底英雄池
	FirstHeroRef        []*HeroTableCfg              `json:"-"`                // 首抽保底英雄池
	FirstHeroWeight     []int32                      `json:"FirstHeroWeight"`  // 首抽保底英雄权重
	TenthHero           []int32                      `json:"TenthHero"`        // 第十抽保底英雄池
	TenthHeroRef        []*HeroTableCfg              `json:"-"`                // 第十抽保底英雄池
	TenthHeroWeight     []int32                      `json:"TenthHeroWeight"`  // 第十抽保底英雄权重
}

func NewHeroLotteryMustTableCfg() *HeroLotteryMustTableCfg {
	return &HeroLotteryMustTableCfg{
		Id:                  0,
		HeroLotteryGroup:    0,
		HeroLotteryGroupRef: nil,
		MustCnt:             0,
		IncreaseCnt:         0,
		Increment:           []int32{},
		IsRefresh:           false,
		Must11:              []int32{},
		Must11Ref:           []*HeroLotteryRandomTableCfg{},
		MustSSR:             NewHeroLotterySSR(),
		FirstHero:           []int32{},
		FirstHeroRef:        []*HeroTableCfg{},
		FirstHeroWeight:     []int32{},
		TenthHero:           []int32{},
		TenthHeroRef:        []*HeroTableCfg{},
		TenthHeroWeight:     []int32{},
	}
}

type HeroLotteryMustTable struct {
	records  map[int32]*HeroLotteryMustTableCfg
	localIds map[int32]struct{}
}

func NewHeroLotteryMustTable() *HeroLotteryMustTable {
	return &HeroLotteryMustTable{
		records:  map[int32]*HeroLotteryMustTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *HeroLotteryMustTable) Get(key int32) *HeroLotteryMustTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *HeroLotteryMustTable) GetAll() map[int32]*HeroLotteryMustTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *HeroLotteryMustTable) put(key int32, value *HeroLotteryMustTableCfg, local bool) *HeroLotteryMustTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *HeroLotteryMustTable) Range(f func(v *HeroLotteryMustTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *HeroLotteryMustTable) Filter(filterFuncs ...func(v *HeroLotteryMustTableCfg) bool) map[int32]*HeroLotteryMustTableCfg {
	filtered := map[int32]*HeroLotteryMustTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *HeroLotteryMustTable) FilterSlice(filterFuncs ...func(v *HeroLotteryMustTableCfg) bool) []*HeroLotteryMustTableCfg {
	filtered := []*HeroLotteryMustTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *HeroLotteryMustTable) FilterKeys(filterFuncs ...func(v *HeroLotteryMustTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *HeroLotteryMustTable) satisfied(v *HeroLotteryMustTableCfg, filterFuncs ...func(v *HeroLotteryMustTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *HeroLotteryMustTable) setupIndexes() error {
	return nil
}

func (t *HeroLotteryMustTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *HeroLotteryMustTableCfg) bindRefs(c *Configs) {
	r.HeroLotteryGroupRef = c.HeroLotteryGroupTable.Get(r.HeroLotteryGroup)
	for _, e := range r.Must11 {
		cfgoRefRecord := c.HeroLotteryRandomTable.Get(e)
		r.Must11Ref = append(r.Must11Ref, cfgoRefRecord)
	}
	r.MustSSR.bindRefs(c)
	for _, e := range r.FirstHero {
		cfgoRefRecord := c.HeroTable.Get(e)
		r.FirstHeroRef = append(r.FirstHeroRef, cfgoRefRecord)
	}
	for _, e := range r.TenthHero {
		cfgoRefRecord := c.HeroTable.Get(e)
		r.TenthHeroRef = append(r.TenthHeroRef, cfgoRefRecord)
	}
}
