package rank

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"fmt"
	"github.com/go-redis/redis"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"sort"
	"strconv"
	"time"
)

const RANK_STAGE_LEVEL_FINISH_TIME_MAX = 9999

type PlayerLevel struct {
	Uid     int64 `json:"uid"`
	LevelId int32 `json:"levelId"`
}

func SetStageLevelScore(uid int64, level int32, finishTime int32) error {
	TimeScore := fmt.Sprintf("%09d", RANK_TIME_END-time.Now().Second())
	finishTimeScore := fmt.Sprintf("%04d", util.MaxInt32(RANK_STAGE_LEVEL_FINISH_TIME_MAX-finishTime, 0))
	scoreStr := strconv.Itoa(int(level)) + "." + finishTimeScore + TimeScore
	score, err := strconv.ParseFloat(scoreStr, 64)
	if err != nil {
		return err
	}

	rdb := GetRankClient()
	currentScore, err := rdb.ZScore(RANK_STAGE_LEVEL, strconv.Itoa(int(uid))).Result()
	if err != nil && err != redis.Nil {
		panic(err) // 处理错误
	}
	if err == redis.Nil || score > currentScore {
		rdb.ZAdd(RANK_STAGE_LEVEL, redis.Z{
			Score:  score,
			Member: strconv.Itoa(int(uid)),
		})
	}
	return nil
}

func GetStageLevelRank(uid int64) ([]PlayerLevel, error) {
	rdb := GetRankClient()
	val, err := rdb.ZRevRangeWithScores(RANK_STAGE_LEVEL, 0, 99).Result() // 获取所有用户的排行，分数从高到低
	if err != nil {
		return nil, err
	}
	retInfos := []PlayerLevel{}
	for _, z := range val {
		uidStr := z.Member.(string)
		level := int32(z.Score)
		var playerLevelStruct PlayerLevel
		playerLevelStruct.Uid, err = strconv.ParseInt(uidStr, 10, 64)
		playerLevelStruct.LevelId = level

		if err == nil {
			retInfos = append(retInfos, playerLevelStruct)
		}

	}
	return retInfos, nil
}

func GetStageLevelRankInfoList(ctx context.Context, uid int64, num int32) []*wrpc.LevelRankInfo {
	rankInfos, err := GetStageLevelRank(uid)
	if err != nil {
		return nil
	}
	retRankList := []*wrpc.LevelRankInfo{}
	for i, rankInfo := range rankInfos {
		rankUser, _ := model.GetUserModel(ctx, rankInfo.Uid)
		unlock := model.LockUser(ctx, rankInfo.Uid)
		curLevelInfo := &wrpc.LevelRankInfo{
			Uid:   rankInfo.Uid,
			Rank:  int32(i + 1),
			Level: rankInfo.LevelId,
			Icon:  rankUser.AvatarConfigId(),
			Name:  rankUser.Name(),
		}
		unlock()
		stage, _ := model.GetMainLineStage(ctx, rankInfo.Uid)
		if stage != nil {
			finishStatuses := stage.StageFinishStatus()
			finishStatus := finishStatuses[rankInfo.LevelId]
			if finishStatus != nil {
				curLevelInfo.FinishTime = finishStatus.MinTime
			}
		}

		retRankList = append(retRankList, curLevelInfo)
	}
	return retRankList
}

func GetStageRankInfoByStageId(ctx context.Context, uid int64, stageId int32) []*wrpc.LevelRankInfo {
	userModel, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userModel)
	infoModel := model.GetGlobalStageLevelModel(ctx, userModel.WorldId())
	orm.Unlock(userModel)
	if infoModel == nil {
		return nil
	}
	levelInfos := infoModel.LevelInfo()
	if levelInfos == nil {
		return nil
	}
	levelInfo := levelInfos[stageId]
	if levelInfo == nil {
		return nil
	}
	playerScores := levelInfo.PlayerScore
	if playerScores == nil {
		return nil
	}
	retRankInfo := []*wrpc.LevelRankInfo{}
	for i, rankInfo := range playerScores {
		rankUser, _ := model.GetUserModel(ctx, rankInfo.Uid)
		unlock := model.LockUser(ctx, rankInfo.Uid)
		curLevelInfo := &wrpc.LevelRankInfo{
			Uid:           rankInfo.Uid,
			Rank:          int32(i + 1),
			Level:         stageId,
			FinishTime:    rankInfo.Score,
			Icon:          rankUser.AvatarConfigId(),
			Name:          rankUser.Name(),
			ChallengeTime: rankInfo.ChallengeTime,
			ChallengeHero: rankInfo.ChallengeHero,
		}
		unlock()
		retRankInfo = append(retRankInfo, curLevelInfo)

	}
	return retRankInfo
}

func CollectStageLevelRewards(ctx context.Context, uid int64) ([]*wrpc.Rewards, error) {
	rewardLines := cfg_mgr.Cfg.RankRewardTable.GetAll()
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	if stageModel == nil {
		return nil, kdmerr.SysInvalidArguments.CastError("stage is null")
	}
	collectRankRedward := stageModel.CollectionRankReward()
	if collectRankRedward == nil {
		collectRankRedward = make(map[int32]bool)
	}
	userInfo, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userInfo)
	globalStage := model.GetGlobalStageLevelModel(ctx, userInfo.WorldId())
	orm.Unlock(userInfo)
	if globalStage == nil {
		return nil, kdmerr.SysInvalidArguments.CastError("globalStage is null")
	}
	levelInfo := globalStage.LevelInfo()
	if levelInfo == nil {
		return nil, kdmerr.SysInvalidArguments.CastError("levelInfo is null")
	}

	rewardMap := map[int32]int64{}
	retRewards := []*wrpc.Rewards{}
	sequence := util.GenerateSequenceString()
	for _, rewardLine := range rewardLines {
		stageId := rewardLine.Level
		rewardId := rewardLine.Id
		if levelInfo[stageId] == nil {
			continue
		}
		playerScores := levelInfo[stageId].PlayerScore
		if playerScores == nil {
			continue
		}
		if collectRankRedward[stageId] {
			continue
		}
		rewardMap[rewardLine.Reward.RewardType] += int64(rewardLine.Reward.RewardValue)
		model.AddItem(ctx, uid, rewardLine.Reward.RewardType, int64(rewardLine.Reward.RewardValue), bi.ItemFlowReason_RankReward, strconv.Itoa(int(rewardId)), sequence)
		collectRankRedward[stageId] = true
	}
	for k, v := range rewardMap {
		retRewards = append(retRewards, &wrpc.Rewards{
			ItemId:    k,
			ItemValue: v,
		})
	}

	stageModel.SetCollectionRankReward(ctx, collectRankRedward)
	return retRewards, nil
}

func GetAllStageRankInfo(ctx context.Context, uid int64) []*wrpc.LevelRankInfo {
	userModel, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userModel)
	infoModel := model.GetGlobalStageLevelModel(ctx, userModel.WorldId())
	orm.Unlock(userModel)
	if infoModel == nil {
		return nil
	}
	levelInfos := infoModel.LevelInfo()
	if levelInfos == nil {
		return nil
	}
	rankRewardLines := cfg_mgr.Cfg.RankRewardTable.FilterSlice(func(v *servercfg.RankRewardTableCfg) bool {
		return true
	})
	sort.Slice(rankRewardLines, func(i, j int) bool {
		return rankRewardLines[i].Id < rankRewardLines[j].Id
	})

	retRankInfo := []*wrpc.LevelRankInfo{}
	for index, rewardLine := range rankRewardLines {

		levelInfo := levelInfos[rewardLine.Level]

		if levelInfo == nil {
			//curLevelInfo := &wrpc.LevelRankInfo{
			//	Uid:        0,
			//	Rank:       int32(index + 1),
			//	Level:      rewardLine.Level,
			//	FinishTime: 0,
			//	Icon:       0,
			//	Name:       "",
			//}
			//retRankInfo = append(retRankInfo, curLevelInfo)
			//continue
			break
		}
		playerScores := levelInfo.PlayerScore
		for _, rankInfo := range playerScores {
			rankUser, _ := model.GetUserModel(ctx, rankInfo.Uid)
			unlock := model.LockUser(ctx, rankInfo.Uid)
			curLevelInfo := &wrpc.LevelRankInfo{
				Uid:           rankInfo.Uid,
				Rank:          int32(index + 1),
				Level:         rewardLine.Level,
				FinishTime:    rankInfo.Score,
				Icon:          rankUser.AvatarConfigId(),
				Name:          rankUser.Name(),
				ChallengeTime: rankInfo.ChallengeTime,
				ChallengeHero: rankInfo.ChallengeHero,
			}
			unlock()
			retRankInfo = append(retRankInfo, curLevelInfo)
			break
		}
	}
	return retRankInfo
}
