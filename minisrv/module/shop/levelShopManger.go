package shop

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/timestamp"
	"strconv"
	"time"
)

func BuyAllianceShop(ctx context.Context, uid int64, commodityId int32, amount int32) error {
	line := cfg_mgr.Cfg.GuildShopTable.Get(commodityId)
	if line == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("GuildShopTable not find")
	}
	userModel, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userModel)
	defer orm.Unlock(userModel)
	allianceShopModel := model.GetAllianceShop(ctx, userModel.AllianceId(), uid, commodityId)
	if allianceShopModel.BuyTimes()+amount > line.TimesLimit {
		return kdmerr.SysInvalidArguments.CastErrorf("more than limit")
	}
	allianceShopModel.SetBuyTimes(ctx, allianceShopModel.BuyTimes()+amount)

	//扣除道具
	seq := util.GenerateSequenceString()
	consumable.DeductItem(ctx, uid, line.Currency, int64(line.Price*amount), bi.ItemFlowReason_Alliance_ShopBuy, strconv.Itoa(int(commodityId)), seq)
	consumable.AddItem(ctx, uid, line.RewardType, int64(line.RewardValue*amount), bi.ItemFlowReason_Alliance_ShopBuy, strconv.Itoa(int(commodityId)), seq)

	// 构建get_item_type字段
	getItemType := map[string]interface{}{
		"price":     line.Price,
		"limit_num": line.TimesLimit,
		"cur_num":   line.TimesLimit - (allianceShopModel.BuyTimes()),
	}

	// 记录商店购买日志
	logger.LogCustom(ctx, uid, "activity_shop", logger.NewDetail().
		Put("buy_item_id", line.RewardType).
		Put("buy_item_num", line.RewardValue*amount).
		Put("cost_item_id", line.Currency).
		Put("cost_item_num", line.Price*amount).
		Put("group_id", line.Shop).
		Put("get_item_type", getItemType).
		Put("shop_type", 1)) // 1 表示公会商店

	return nil
}

func refreshAllianceShop(ctx context.Context, allianceId int64) {
	allianceModel, _ := model.GetAllianceModel(ctx, allianceId)
	if !util.IsSameWeek(allianceModel.ShopLastResetTime().Time(), time.Now()) {
		allianceModel.SetShopLastResetTime(ctx, timestamp.NewUTCSeconds(time.Now()))
		shops, _ := orm.GetAll[*model.AllianceShopBuy](ctx, allianceId)
		for _, v := range shops {
			v.SetBuyTimes(ctx, 0)
		}
		maxPeriod := int32(1)
		lines := cfg_mgr.Cfg.GuildShopTable.GetAll()
		for _, line := range lines {
			if line.Period > maxPeriod {
				maxPeriod = line.Period
			}
		}
		if allianceModel.CurShopPeriod() < maxPeriod {
			allianceModel.SetCurShopPeriod(ctx, allianceModel.CurShopPeriod()+1)
		} else {
			allianceModel.SetCurShopPeriod(ctx, 1)
		}
	}
}
