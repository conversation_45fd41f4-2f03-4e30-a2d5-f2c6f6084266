package quest

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"strconv"
)

func GenMainQuest(ctx context.Context, curId int32, uid int64) error {
	mainTaskLine := cfg_mgr.Cfg.ChapterTaskMainTable.Get(curId)
	if mainTaskLine == nil {
		return nil
	}
	NeedInitQuestLines := cfg_mgr.Cfg.ChapterTaskTable.FilterSlice(func(v *servercfg.ChapterTaskTableCfg) bool {
		return v.Chapter == curId
	})
	if NeedInitQuestLines == nil {
		return nil
	}

	for _, v := range NeedInitQuestLines {
		vQuests, _ := orm.Get[*model.MainTask](ctx, uid, v.Id)
		if vQuests != nil {
			continue
		}
		vQuests, _ = orm.Create[*model.MainTask](ctx, &minirpc.MainTask{
			Uid:      uid,
			QuestId:  v.Id,
			Type:     int32(v.TaskType),
			Progress: 0,
			Status:   int32(minirpc.QuestActivity),
		})
		if v.TaskCounterType == servercfg.TaskCounterType_Total {
			update(ctx, vQuests, map[string]string{}, 0)
		}
	}

	return nil
}

func CollectMainReward(ctx context.Context, uid int64, questId int32) (map[int32]int64, error) {
	quest, err := model.GetMainTaskModel(ctx, uid, questId)
	if err != nil {
		return nil, kdmerr.SysDBError.WrapError(err)
	}
	if quest == nil || quest.Status() != int32(minirpc.QuestComplete) {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("quest is error")
	}
	line := cfg_mgr.Cfg.MainLineTasksTable.Get(questId)
	if line == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("quest is error")
	}
	quest.SetStatus(ctx, int32(minirpc.QuestRewarded))
	model.AddItem(ctx, uid, line.RewardType, int64(line.RewardValue), bi.ItemFlowReason_Main_Task_Reward, strconv.Itoa(int(questId)), util.GenerateSequenceString())

	Notify(ctx, uid, servercfg.TaskType_CompleteTask, map[string]string{}, 1)

	return map[int32]int64{line.RewardType: int64(line.RewardValue)}, nil
}

func CollectGroupReward(ctx context.Context, uid int64, mainId int32) (map[int32]int64, error) {
	mainLine := cfg_mgr.Cfg.ChapterTaskMainTable.Get(mainId)
	if mainLine == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("main id is error")
	}
	questLines := cfg_mgr.Cfg.ChapterTaskTable.FilterSlice(func(v *servercfg.ChapterTaskTableCfg) bool {
		return v.Chapter == mainId
	})
	if questLines == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("main id is error")
	}
	for _, v := range questLines {
		m, _ := model.GetMainTaskModel(ctx, uid, v.Id)
		if m == nil {
			continue
		}
		if m.Status() == int32(minirpc.QuestComplete) {
			CollectMainReward(ctx, uid, v.Id)
		}
		if m.Status() == int32(minirpc.QuestRewarded) {
			return nil, kdmerr.SysInvalidArguments.CastErrorf("not all quest finished")
		}
	}
	rewardMap := make(map[int32]int64)
	for _, v := range mainLine.Reward {
		rewardMap[v.RewardType] += int64(v.RewardValue)
	}
	sequence := util.GenerateSequenceString()
	for k, v := range rewardMap {
		model.AddItem(ctx, uid, k, v, bi.ItemFlowReason_Main_Task_Group_Reward, strconv.Itoa(int(mainId)), sequence)
	}

	for _, v := range questLines {
		m, _ := model.GetMainTaskModel(ctx, uid, v.Id)
		if m == nil {
			continue
		}
		m.Delete(ctx)
	}
	GenMainQuest(ctx, mainId+1, uid)
	Notify(ctx, uid, servercfg.TaskType_ChapterTaskComplete, map[string]string{}, 1)
	return rewardMap, nil

}
