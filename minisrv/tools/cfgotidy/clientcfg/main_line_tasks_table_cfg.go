// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type MainLineTasksTableCfg struct {
	Id                  int32                  `json:"Id"`               // Id
	ReqFunction         int32                  `json:"ReqFunction"`      // 前置功能
	ReqFunctionRef      *FunctionTableCfg      `json:"-"`                // 前置功能
	ReqMainLineTasks    int32                  `json:"ReqMainLineTasks"` // 所属母任务
	ReqMainLineTasksRef *MainLineTasksTableCfg `json:"-"`                // 所属母任务
	TaskType            TaskType               `json:"TaskType"`         // 任务类型
	TaskCounterType     TaskCounterType        `json:"TaskCounterType"`  // 任务计数类型
	Formula             string                 `json:"Formula"`          // 达成条件
	Value               int32                  `json:"Value"`            // 值
	GoTo                int32                  `json:"GoTo"`             // 跳转
	GoToRef             *GoToTableCfg          `json:"-"`                // 跳转
	Desc                string                 `json:"Desc"`             // 任务描述
	Param               string                 `json:"Param"`            // 描述参数
	RewardType          int32                  `json:"RewardType"`       // 奖励类型
	RewardTypeRef       *ItemTableCfg          `json:"-"`                // 奖励类型
	RewardValue         int32                  `json:"RewardValue"`      // 奖励数量
	GuideTrigger        bool                   `json:"GuideTrigger"`     // 是否触发指引
}

func NewMainLineTasksTableCfg() *MainLineTasksTableCfg {
	return &MainLineTasksTableCfg{
		Id:                  0,
		ReqFunction:         0,
		ReqFunctionRef:      nil,
		ReqMainLineTasks:    0,
		ReqMainLineTasksRef: nil,
		TaskType:            TaskType(enumDefaultValue),
		TaskCounterType:     TaskCounterType(enumDefaultValue),
		Formula:             "",
		Value:               0,
		GoTo:                0,
		GoToRef:             nil,
		Desc:                "",
		Param:               "",
		RewardType:          0,
		RewardTypeRef:       nil,
		RewardValue:         0,
		GuideTrigger:        false,
	}
}

func NewMockMainLineTasksTableCfg() *MainLineTasksTableCfg {
	return &MainLineTasksTableCfg{
		Id:                  0,
		ReqFunction:         0,
		ReqFunctionRef:      nil,
		ReqMainLineTasks:    0,
		ReqMainLineTasksRef: nil,
		TaskType:            TaskType(enumDefaultValue),
		TaskCounterType:     TaskCounterType(enumDefaultValue),
		Formula:             "",
		Value:               0,
		GoTo:                0,
		GoToRef:             nil,
		Desc:                "",
		Param:               "",
		RewardType:          0,
		RewardTypeRef:       nil,
		RewardValue:         0,
		GuideTrigger:        false,
	}
}

type MainLineTasksTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*MainLineTasksTableCfg
	localIds         map[int32]struct{}
}

func NewMainLineTasksTable(configs *Configs) *MainLineTasksTable {
	return &MainLineTasksTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*MainLineTasksTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *MainLineTasksTable) Get(key int32) *MainLineTasksTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *MainLineTasksTable) GetAll() map[int32]*MainLineTasksTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *MainLineTasksTable) put(key int32, value *MainLineTasksTableCfg, local bool) *MainLineTasksTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *MainLineTasksTable) putFromInheritedTable(key int32, value *MainLineTasksTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[MainLineTasksTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *MainLineTasksTable) Put(key int32, value *MainLineTasksTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[MainLineTasksTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *MainLineTasksTable) PutAll(m map[int32]*MainLineTasksTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *MainLineTasksTable) Range(f func(v *MainLineTasksTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *MainLineTasksTable) Filter(filterFuncs ...func(v *MainLineTasksTableCfg) bool) map[int32]*MainLineTasksTableCfg {
	filtered := map[int32]*MainLineTasksTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *MainLineTasksTable) FilterSlice(filterFuncs ...func(v *MainLineTasksTableCfg) bool) []*MainLineTasksTableCfg {
	filtered := []*MainLineTasksTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *MainLineTasksTable) FilterKeys(filterFuncs ...func(v *MainLineTasksTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *MainLineTasksTable) satisfied(v *MainLineTasksTableCfg, filterFuncs ...func(v *MainLineTasksTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *MainLineTasksTable) setupIndexes() error {
	return nil
}

func (t *MainLineTasksTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *MainLineTasksTableCfg) bindRefs(c *Configs) {
	r.ReqFunctionRef = c.FunctionTable.Get(r.ReqFunction)
	r.ReqMainLineTasksRef = c.MainLineTasksTable.Get(r.ReqMainLineTasks)
	r.GoToRef = c.GoToTable.Get(r.GoTo)
	r.RewardTypeRef = c.ItemTable.Get(r.RewardType)
}

func (t *MainLineTasksTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[MainLineTasksTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewMainLineTasksTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLineTasksTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[MainLineTasksTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// ReqFunction
		if record[t.getIndexInCsv("ReqFunction")] == "" {
			recordCfg.ReqFunction = 0
		} else {
			var err error
			recordCfg.ReqFunction, err = configs.FunctionTable.getIdByRef(record[t.getIndexInCsv("ReqFunction")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MainLineTasksTable]unmarshal csv record failed, varName=ReqFunction, type=ref@FunctionTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("ReqFunction")], err)
				} else {
					return fmt.Errorf("[MainLineTasksTable]unmarshal csv record failed, varName=ReqFunction, type=ref@FunctionTable, value=%s, err:[%s]", record[t.getIndexInCsv("ReqFunction")], err)
				}
			}
		}
		// ReqMainLineTasks
		if record[t.getIndexInCsv("ReqMainLineTasks")] == "" {
			recordCfg.ReqMainLineTasks = 0
		} else {
			var err error
			recordCfg.ReqMainLineTasks, err = configs.MainLineTasksTable.getIdByRef(record[t.getIndexInCsv("ReqMainLineTasks")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MainLineTasksTable]unmarshal csv record failed, varName=ReqMainLineTasks, type=ref@MainLineTasksTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("ReqMainLineTasks")], err)
				} else {
					return fmt.Errorf("[MainLineTasksTable]unmarshal csv record failed, varName=ReqMainLineTasks, type=ref@MainLineTasksTable, value=%s, err:[%s]", record[t.getIndexInCsv("ReqMainLineTasks")], err)
				}
			}
		}
		// TaskType
		{
			if record[t.getIndexInCsv("TaskType")] == "" {
				recordCfg.TaskType = TaskType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseTaskType(record[t.getIndexInCsv("TaskType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLineTasksTable]unmarshal csv record failed, varName=TaskType, type=enum@TaskType, value=%s, err:[%s]\n", record[t.getIndexInCsv("TaskType")], err)
					} else {
						return fmt.Errorf("[MainLineTasksTable]unmarshal csv record failed, varName=TaskType, type=enum@TaskType, value=%s, err:[%s]", record[t.getIndexInCsv("TaskType")], err)
					}
				}
				recordCfg.TaskType = cfgoEnum
			}
		}
		// TaskCounterType
		{
			if record[t.getIndexInCsv("TaskCounterType")] == "" {
				recordCfg.TaskCounterType = TaskCounterType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseTaskCounterType(record[t.getIndexInCsv("TaskCounterType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLineTasksTable]unmarshal csv record failed, varName=TaskCounterType, type=enum@TaskCounterType, value=%s, err:[%s]\n", record[t.getIndexInCsv("TaskCounterType")], err)
					} else {
						return fmt.Errorf("[MainLineTasksTable]unmarshal csv record failed, varName=TaskCounterType, type=enum@TaskCounterType, value=%s, err:[%s]", record[t.getIndexInCsv("TaskCounterType")], err)
					}
				}
				recordCfg.TaskCounterType = cfgoEnum
			}
		}
		// Formula
		{
			recordCfg.Formula = strings.TrimSpace(record[t.getIndexInCsv("Formula")])
		}
		// Value
		{
			if record[t.getIndexInCsv("Value")] == "" {
				recordCfg.Value = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Value")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLineTasksTable]unmarshal csv record failed, varName=Value, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Value")], err)
					} else {
						return fmt.Errorf("[MainLineTasksTable]unmarshal csv record failed, varName=Value, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Value")], err)
					}
				}
				recordCfg.Value = int32(cfgoInt)
			}
		}
		// GoTo
		if record[t.getIndexInCsv("GoTo")] == "" {
			recordCfg.GoTo = 0
		} else {
			var err error
			recordCfg.GoTo, err = configs.GoToTable.getIdByRef(record[t.getIndexInCsv("GoTo")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MainLineTasksTable]unmarshal csv record failed, varName=GoTo, type=ref@GoToTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoTo")], err)
				} else {
					return fmt.Errorf("[MainLineTasksTable]unmarshal csv record failed, varName=GoTo, type=ref@GoToTable, value=%s, err:[%s]", record[t.getIndexInCsv("GoTo")], err)
				}
			}
		}
		// Desc
		{
			recordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc")])
		}
		// Param
		{
			recordCfg.Param = strings.TrimSpace(record[t.getIndexInCsv("Param")])
		}
		// RewardType
		if record[t.getIndexInCsv("RewardType")] == "" {
			recordCfg.RewardType = 0
		} else {
			var err error
			recordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("RewardType")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [MainLineTasksTable]unmarshal csv record failed, varName=RewardType, type=ref@ItemTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardType")], err)
				} else {
					return fmt.Errorf("[MainLineTasksTable]unmarshal csv record failed, varName=RewardType, type=ref@ItemTable, value=%s, err:[%s]", record[t.getIndexInCsv("RewardType")], err)
				}
			}
		}
		// RewardValue
		{
			if record[t.getIndexInCsv("RewardValue")] == "" {
				recordCfg.RewardValue = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("RewardValue")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLineTasksTable]unmarshal csv record failed, varName=RewardValue, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardValue")], err)
					} else {
						return fmt.Errorf("[MainLineTasksTable]unmarshal csv record failed, varName=RewardValue, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("RewardValue")], err)
					}
				}
				recordCfg.RewardValue = int32(cfgoInt)
			}
		}
		// GuideTrigger
		{
			if record[t.getIndexInCsv("GuideTrigger")] == "" {
				recordCfg.GuideTrigger = false
			} else {
				var err error
				recordCfg.GuideTrigger, err = strconv.ParseBool(record[t.getIndexInCsv("GuideTrigger")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MainLineTasksTable]unmarshal csv record failed, varName=GuideTrigger, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("GuideTrigger")], err)
					} else {
						return fmt.Errorf("[MainLineTasksTable]unmarshal csv record failed, varName=GuideTrigger, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("GuideTrigger")], err)
					}
				}
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [MainLineTasksTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[MainLineTasksTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *MainLineTasksTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "MainLineTasksTable.csv") && (!strings.HasPrefix(fileName, "MainLineTasksTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for MainLineTasksTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[MainLineTasksTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[MainLineTasksTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[MainLineTasksTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[MainLineTasksTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[MainLineTasksTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[MainLineTasksTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[MainLineTasksTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[MainLineTasksTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [MainLineTasksTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *MainLineTasksTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[MainLineTasksTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [MainLineTasksTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *MainLineTasksTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[MainLineTasksTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *MainLineTasksTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[MainLineTasksTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[MainLineTasksTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
