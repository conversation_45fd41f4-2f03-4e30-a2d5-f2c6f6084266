package srvconf

type kdmsrvConf struct {
	BackupGameServerURL  string   `json:"BackupGameServerUrl"`
	CallBackJob          bool     `json:"CallBackJob"`
	EtcdAddrs            []string `json:"EtcdAddrs"`
	EtcdClusterDir       string   `json:"EtcdClusterDir"`
	GameServerMaxRetry   int32    `json:"GameServerMaxRetry"`
	GameServerURL        string   `json:"GameServerUrl"`
	KdmSrvTTL            int32    `json:"KdmSrvTTL"`
	MaxOngoingRequests   int32    `json:"MaxOngoingRequests"`
	NoticeURL            string   `json:"NoticeUrl"`
	NumberOfReportGroups int32    `json:"NumberOfReportGroups"`
	ServerSecret         string   `json:"ServerSecret"`
	PaymentServerSecret  string   `json:"PaymentServerSecret"`
	Orm                  *struct {
		MaxPoolSize int32 `json:"MaxPoolSize"`
		RedoLog     *struct {
			Dir          string `json:"Dir"`
			Enabled      bool   `json:"Enabled"`
			SyncInterval string `json:"SyncInterval"`
		} `json:"RedoLog"`
		Suffix map[string]string `json:"Suffix"`
		Tables map[string]*struct {
			Concurrence   int32  `json:"Concurrence"`
			FlushInterval string `json:"FlushInterval"`
			TimeOut       string `json:"TimeOut"`
		} `json:"Tables"`
		TicketsCollection string `json:"TicketsCollection"`
		TicketsDB         string `json:"TicketsDB"`
		URI               string `json:"Uri"`
	} `json:"ORM"`
	PathFindingMap string `json:"PathFindingMap"`
	S3Config       *struct {
		AwsAccessKeyID     string `json:"Aws_access_key_id"`
		AwsSecretAccessKey string `json:"Aws_secret_access_key"`
		Bucket             string `json:"Bucket"`
		Online             bool   `json:"Online"`
		Prefix             string `json:"Prefix"`
		Region             string `json:"Region"`
	} `json:"S3Config"`
	SessionTokenExpireInSecs int32 `json:"SessionTokenExpireInSecs"`
	StargateAgent            *struct {
		Category  int32  `json:"Category"`
		ProxyName string `json:"ProxyName"`
	} `json:"StargateAgent"`
	AsyncPushConfig *struct {
		AsyncPushEnabled     bool   `json:"AsyncPushEnabled"`
		AsyncPushInterval    string `json:"AsyncPushInterval"`
		AsyncPushConcurrence uint32 `json:"AsyncPushConcurrence"`
	} `json:"AsyncPushConfig"`
	BiConfig *struct {
		BI_PATH string `json:"BI_PATH"`
	} `json:"BiConfig"`
	PaymentConf *struct {
		PaymentServer string `json:"PaymentServer"`
		ChannelInfo   map[string]*struct {
			AppId           string `json:"AppId"`
			PaymentPlatform string `json:"PaymentPlatform"`
		}
	}
	RankRedisAddr string `json:"RankRedisAddr"`
	RootDir       string `json:"RootDir"`
	IPCountryPath string `json:"IPCountryPath"`
	ClientConfig  *struct {
		Cdn string `json:"cdn"`
	} `json:"ClientConfig"`
}
