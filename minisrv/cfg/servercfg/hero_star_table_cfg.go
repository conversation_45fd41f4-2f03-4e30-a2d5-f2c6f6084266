// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type HeroStarTableCfg struct {
	Id                  int32                `json:"Id"`                  // Id
	PlanID              HeroStarUpPlan       `json:"PlanID"`              // 升星方案
	HeroStarLevel       int32                `json:"HeroStarLevel"`       // 英雄星级
	HeroSkillLevelLimit int32                `json:"HeroSkillLevelLimit"` // 对应的主动技能等级上限值
	IsMax               bool                 `json:"IsMax"`               // 是否最大星级
	StarUpCostValue     int32                `json:"StarUpCostValue"`     // 升星消耗数量
	StarUpDifference    int32                `json:"StarUpDifference"`    // 升星差值
	StarUpCommonItem    string               `json:"StarUpCommonItem"`    // 升星通用材料
	StarUpCommonItemNum int32                `json:"StarUpCommonItemNum"` // 升星通用材料数量
	IsGradeUp           bool                 `json:"IsGradeUp"`           // 需要升品
	GradeUpCostValue    int32                `json:"GradeUpCostValue"`    // 升品消耗数量
	HeroQuality         int32                `json:"HeroQuality"`         // 英雄品质
	HeroQualityRef      *HeroQualityTableCfg `json:"-"`                   // 英雄品质
	Power               int32                `json:"Power"`               // 戰力
	Attr                *AttrStr             `json:"Attr"`                // 属性
}

func NewHeroStarTableCfg() *HeroStarTableCfg {
	return &HeroStarTableCfg{
		Id:                  0,
		PlanID:              HeroStarUpPlan(enumDefaultValue),
		HeroStarLevel:       0,
		HeroSkillLevelLimit: 0,
		IsMax:               false,
		StarUpCostValue:     0,
		StarUpDifference:    0,
		StarUpCommonItem:    "",
		StarUpCommonItemNum: 0,
		IsGradeUp:           false,
		GradeUpCostValue:    0,
		HeroQuality:         0,
		HeroQualityRef:      nil,
		Power:               0,
		Attr:                NewAttrStr(),
	}
}

type HeroStarTable struct {
	records  map[int32]*HeroStarTableCfg
	localIds map[int32]struct{}
}

func NewHeroStarTable() *HeroStarTable {
	return &HeroStarTable{
		records:  map[int32]*HeroStarTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *HeroStarTable) Get(key int32) *HeroStarTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *HeroStarTable) GetAll() map[int32]*HeroStarTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *HeroStarTable) put(key int32, value *HeroStarTableCfg, local bool) *HeroStarTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *HeroStarTable) Range(f func(v *HeroStarTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *HeroStarTable) Filter(filterFuncs ...func(v *HeroStarTableCfg) bool) map[int32]*HeroStarTableCfg {
	filtered := map[int32]*HeroStarTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *HeroStarTable) FilterSlice(filterFuncs ...func(v *HeroStarTableCfg) bool) []*HeroStarTableCfg {
	filtered := []*HeroStarTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *HeroStarTable) FilterKeys(filterFuncs ...func(v *HeroStarTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *HeroStarTable) satisfied(v *HeroStarTableCfg, filterFuncs ...func(v *HeroStarTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *HeroStarTable) setupIndexes() error {
	return nil
}

func (t *HeroStarTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *HeroStarTableCfg) bindRefs(c *Configs) {
	r.HeroQualityRef = c.HeroQualityTable.Get(r.HeroQuality)
	r.Attr.bindRefs(c)
}
