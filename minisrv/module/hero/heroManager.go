package hero

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	cfg_mgr "bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/misc"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/logging"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"sort"
	"strconv"
)

func AddHero(ctx context.Context, uid int64, heroId int32, reason bi.ItemFlowReason, sbuReason int32) (*model.Hero, error) {
	// 如果英雄已经存在，直接返回
	hero, err := orm.Get[*model.Hero](ctx, uid, heroId)
	if err != nil {
		logging.Errorf(ctx, "hero %d - %d nil, err %+v, caller %s", uid, heroId, err, misc.GetCallerName(2))
		return nil, err
	}
	if hero != nil {
		return hero, nil
	}

	// 不存在，添加
	hero, err = model.NewHero(ctx, uid, heroId)
	if err != nil {
		logging.Errorf(ctx, "hero %d - %d nil, err %+v, caller %s", uid, heroId, err, misc.GetCallerName(2))
		return nil, err
	}
	CheckHeroSkillUnlock(ctx, hero)
	updateHeroBenefits(ctx, hero)
	heroLine := cfg_mgr.Cfg.HeroTable.Get(heroId)
	quest.Notify(ctx, uid, servercfg.TaskType_TotalActivateHero, map[string]string{}, 1)
	logger.LogCustom(ctx, uid, "legend_hero", logger.NewDetail().Put("action", "hero_gain").
		Put("hero_id", heroId).Put("quality", heroLine.HeroQuality.String()).Put("main_reason", reason).Put("extra_reason", sbuReason))
	return hero, nil
}

func UpgradeLevel(ctx context.Context, uid int64, heroId int32, amount int32) error {
	hero, err := model.GetHero(ctx, uid, heroId)
	if err != nil {
		return err
	}
	curLevelLine := cfg_mgr.Cfg.HeroLevelTable.Get(hero.LevelId())
	curLevel := curLevelLine.HeroLevel
	destLevel := curLevel + amount
	//寻找中间所有等级的配置
	levelLines := cfg_mgr.Cfg.HeroLevelTable.FilterSlice(func(v *servercfg.HeroLevelTableCfg) bool {
		return v.HeroLevel > curLevel && v.HeroLevel <= destLevel && v.PlanID == curLevelLine.PlanID
	})
	starLine := cfg_mgr.Cfg.HeroStarTable.Get(hero.StarId())

	//配置不全
	if int32(len(levelLines)) < destLevel-curLevel {
		panic("config level is error")
	}
	sequence := util.GenerateSequenceString()
	sort.Slice(levelLines, func(i, j int) bool {
		return levelLines[i].HeroLevel < levelLines[j].HeroLevel
	})
	for _, levelLine := range levelLines {
		amount--
		if amount < 0 {
			break
		}
		for _, v := range curLevelLine.LevelUp {
			err = consumable.DeductItem(ctx, uid, v.CostType, int64(v.CostValue), bi.ItemFlowReason_HERO_UPGRADE, strconv.Itoa(int(heroId)), sequence)
			if err != nil {
				return err
			}
		}

		//目标等级
		if levelLine.HeroLevel == destLevel {
			hero.SetLevelId(ctx, levelLine.Id)
		}
		curLevelLine = levelLine

	}
	CheckHeroSkillUnlock(ctx, hero)
	updateHeroBenefits(ctx, hero)
	updateSymbioticLevel(ctx, uid)
	heroLine := cfg_mgr.Cfg.HeroTable.Get(heroId)
	logger.LogCustom(ctx, uid, "legend_hero", logger.NewDetail().Put("action", "level_up").
		Put("hero_id", heroId).Put("quality", heroLine.HeroQuality.String()).Put("level_before", curLevel).Put("level_after", destLevel).
		Put("star", starLine.HeroStarLevel))
	quest.Notify(ctx, uid, servercfg.TaskType_HeroLevelUp, map[string]string{"level": strconv.Itoa(int(destLevel))}, 1)
	quest.Notify(ctx, uid, servercfg.TaskType_HeroLevelUpTo, map[string]string{"level": strconv.Itoa(int(destLevel))}, 1)
	return nil
}

func UpgradeQuality(ctx context.Context, uid int64, heroId int32, starId int32) error {
	hero, err := model.GetHero(ctx, uid, heroId)
	if err != nil {
		return err
	}
	preStarLine := cfg_mgr.Cfg.HeroStarTable.Get(hero.StarId())
	nextStarLine := cfg_mgr.Cfg.HeroStarTable.Get(starId)
	if preStarLine.HeroStarLevel != nextStarLine.HeroStarLevel || preStarLine.HeroQuality == nextStarLine.HeroQuality || !nextStarLine.IsGradeUp {
		return kdmerr.SysInvalidArguments.CastError("star id is error")
	}
	sequence := util.GenerateSequenceString()
	heroLine := cfg_mgr.Cfg.HeroTable.Get(heroId)
	err = consumable.DeductItem(ctx, uid, heroLine.GradeUpCostItem, int64(nextStarLine.GradeUpCostValue), bi.ItemFlowReason_HERO_QUALITY, strconv.Itoa(int(heroId)), sequence)
	if err != nil {
		return err
	}

	hero.SetStarId(ctx, starId)
	return nil
}

func UpgradeStar(ctx context.Context, uid int64, heroId int32, amount int32) error {
	hero, err := model.GetHero(ctx, uid, heroId)
	if err != nil {
		return err
	}
	curStarLine := cfg_mgr.Cfg.HeroStarTable.Get(hero.StarId())
	curStar := curStarLine.HeroStarLevel
	destStar := curStar + amount
	//寻找中间所有星级的配置
	starLines := cfg_mgr.Cfg.HeroStarTable.FilterSlice(func(v *servercfg.HeroStarTableCfg) bool {
		return v.HeroStarLevel >= curStar && v.HeroStarLevel <= destStar && v.PlanID == curStarLine.PlanID
	})
	sort.Slice(starLines, func(i, j int) bool {
		return starLines[i].Id < starLines[j].Id
	})
	//配置不全
	if int32(len(starLines)) < destStar-curStar {
		panic("config level is error")
	}
	//heroLine := cfg_mgr.Cfg.HeroTable.Get(heroId)
	//itemLine := cfg_mgr.Cfg.ItemTable.Get(heroLine.ItemId)
	heroLine := cfg_mgr.Cfg.HeroTable.Get(heroId)
	sequence := util.GenerateSequenceString()
	for _, starLine := range starLines {
		//目标星级
		if starLine.HeroStarLevel != curStar {
			if starLine.IsGradeUp {
				continue
			}
			err = consumable.DeductItem(ctx, uid, heroLine.StarUpCostItem, int64(starLine.StarUpCostValue), bi.ItemFlowReason_HERO_STAR, strconv.Itoa(int(heroId)), sequence)
			if err != nil {
				return err
			}
			hero.SetStarId(ctx, starLine.Id)
		}
	}
	CheckHeroSkillUnlock(ctx, hero)
	levelLine := cfg_mgr.Cfg.HeroLevelTable.Get(hero.LevelId())

	logger.LogCustom(ctx, uid, "legend_hero", logger.NewDetail().Put("action", "star_up").
		Put("hero_id", heroId).Put("quality", heroLine.HeroQuality.String()).Put("level", levelLine.HeroLevel).Put("star_before", curStar).
		Put("star_after", destStar))
	trace := map[string]string{
		"level": strconv.Itoa(int(destStar)),
	}
	quest.Notify(ctx, uid, servercfg.TaskType_HeroStarUp, trace, 1)
	quest.Notify(ctx, uid, servercfg.TaskType_HeroStarUpTo, trace, 1)
	quest.Notify(ctx, uid, servercfg.TaskType_TotalHeroStarUp, map[string]string{}, 1)
	return nil
}

func updateHeroBenefits(ctx context.Context, hero *model.Hero) {
	heroLine := cfg_mgr.Cfg.HeroTable.Get(hero.ConfigId())
	if heroLine.GiftSkillBenefits == nil {
		return
	}
	benefits := hero.Benefits()
	if benefits == nil {
		benefits = make(map[int32]float32)
	}
	for i := 0; i < len(heroLine.GiftSkillBenefits); i++ {
		id := heroLine.GiftSkillBenefits[i].BenefitsID
		benefits[id] = benefits[id] + heroLine.GiftSkillBenefits[i].BenefitsValue

	}
	skillModels := model.GetHeroSkills(ctx, hero.Uid(), hero.ConfigId())
	if skillModels != nil {
		for _, skillModel := range skillModels {
			skillLine := cfg_mgr.Cfg.HeroSkillAwakeTable.Get(skillModel.ConfigId())
			if skillLine.PassiveSkillEffect != nil {
				for _, effect := range skillLine.PassiveSkillEffect {
					skillEffectLine := cfg_mgr.Cfg.HeroSkillEffectTable.Get(effect)
					if skillEffectLine == nil {
						continue
					}
					if skillEffectLine.BuffParam.BuffTime != -1 {
						continue
					}
					buffLine := cfg_mgr.Cfg.HeroSkillBuffTable.Get(skillEffectLine.BuffParam.BuffType)
					if buffLine == nil {
						continue
					}
					//benefits[buffLine.BenefitsParam.BenefitsID] = benefits[buffLine.BenefitsParam.BenefitsID] + buffLine.BenefitsParam.BenefitsValue
				}
			}
		}
	}

	hero.SetBenefits(ctx, benefits)

}

func SetHeroBattle(ctx context.Context, uid int64, posId int32, heroId int32) error {
	user, err := model.GetUserModel(ctx, uid)
	unlock := model.LockUser(ctx, uid)
	defer unlock()
	heroBattle := user.BattlePosInfo()
	if heroBattle == nil {
		heroBattle = make(map[int32]*minirpc.BattlePosInfo)
	}
	if err != nil {
		return err
	}
	if heroBattle[posId] != nil {
		heroModel, _ := model.GetHero(ctx, uid, heroBattle[posId].HeroId)
		if heroModel != nil {
			heroModel.SetIsBattle(ctx, false)
		}

	}
	if heroId == 0 {
		delete(heroBattle, posId)
	} else {
		hero, err := model.GetHero(ctx, uid, heroId)
		if err != nil {
			return err
		}
		if hero == nil {
			return kdmerr.SysDBError.CastErrorf("can not find hero")
		}

		heroBattle[posId] = &minirpc.BattlePosInfo{
			HeroId: heroId,
		}
		hero.SetIsBattle(ctx, true)
	}

	user.SetBattlePosInfo(ctx, heroBattle)
	calcHerBattleBenefits(ctx, uid)
	quest.Notify(ctx, uid, servercfg.TaskType_HeroConfig, map[string]string{}, 1)
	return nil
}

func GetHeroAttr(ctx context.Context, uid int64, heroId int32) (float32, float32, float32, int32) {
	heroModel, _ := model.GetHero(ctx, uid, heroId)
	heroLine := cfg_mgr.Cfg.HeroTable.Get(heroId)

	//计算英雄天赋
	heroBenefits := map[int32]float32{}
	for _, b := range heroLine.GiftSkillBenefits {
		heroBenefits[b.BenefitsID] += b.BenefitsValue
	}

	levelLine := cfg_mgr.Cfg.HeroLevelTable.Get(heroModel.LevelId())
	starLine := cfg_mgr.Cfg.HeroStarTable.Get(heroModel.StarId())
	geneLine := cfg_mgr.Cfg.HeroGeneTable.Get(heroModel.GeneConfigId())
	hp := float32(levelLine.Attr.Hp+starLine.Attr.Hp) * (1.0 + float32(geneLine.Attr.Hp) + heroBenefits[constDef.BENEFIT_HP_UP_PERCENT])
	atk := float32(levelLine.Attr.Atk+starLine.Attr.Atk) * (1.0 + float32(geneLine.Attr.Atk) + heroBenefits[constDef.BENEFIT_ATK_UP_PERCENT])
	def := float32(levelLine.Attr.Def+starLine.Attr.Def) * (1.0 + float32(geneLine.Attr.Def) + heroBenefits[constDef.BENEFIT_DEF_UP_PERCENT])
	heroSkills := model.GetHeroSkills(ctx, uid, heroId)
	skillId := int32(0)
	for _, v := range heroSkills {
		heroSkillLine := cfg_mgr.Cfg.HeroSkillAwakeTable.Get(v.ConfigId())
		if heroSkillLine.SkillType != servercfg.SkillType_ActiveSkill {
			continue
		}
		skillId = v.ConfigId()
		break
	}
	return hp, atk, def, skillId
	//return 0, 0, 0, 0
}

func calcHerBattleBenefits(ctx context.Context, uid int64) {
	//benefit.ClearBenefitByTrace(ctx, uid, enum.HeroTrace)
	//user, _ := model.GetUserModel(ctx, uid)
	//heroBattle := user.BattlePosInfo()
	//if heroBattle == nil {
	//	return
	//}
	//for _, v := range heroBattle {
	//	heroLine := cfg_mgr.Cfg.HeroTable.Get(v.HeroId)
	//	for _, b := range heroLine.GiftSkillBenefits {
	//		benefit.ChangeBenefitByHero(ctx, uid, b.BenefitsID, float64(b.BenefitsValue))
	//	}
	//}

}

func SetHeroTroop(ctx context.Context, uid int64, heroId int32, num int64) error {
	hero, err := model.GetHero(ctx, uid, heroId)
	if err != nil {
		return err
	}
	if hero == nil {
		return kdmerr.SysDBError.CastErrorf("can not find hero")
	}
	hero.SetTroopNum(ctx, num)
	return nil
}

func SetDefaultBattlePos(ctx context.Context, uid int64, posId int32) error {
	user, err := model.GetUserModel(ctx, uid)
	unlock := model.LockUser(ctx, uid)
	defer unlock()
	if err != nil {
		return err
	}
	user.SetDefaultBattlePos(ctx, posId)
	return nil
}

func CheckHeroSkillUnlock(ctx context.Context, hero *model.Hero) {
	heroId := hero.ConfigId()
	heroSkillGroupLines := cfg_mgr.Cfg.HeroSkillGroupTable.FilterSlice(func(v *servercfg.HeroSkillGroupTableCfg) bool {
		return v.HeroId == heroId
	})

	if len(heroSkillGroupLines) == 0 {
		return
	}
	heroLevel := cfg_mgr.Cfg.HeroLevelTable.Get(hero.LevelId()).HeroLevel
	for i, _ := range heroSkillGroupLines {
		if heroLevel < heroSkillGroupLines[i].UnlockHeroLevel {
			continue
		}
		heroStar := cfg_mgr.Cfg.HeroStarTable.Get(hero.StarId()).HeroStarLevel
		if heroStar < heroSkillGroupLines[i].UnlockHeroStar {
			continue
		}
		skillModel, _ := model.GetHeroSkill(ctx, hero.Uid(), heroSkillGroupLines[i].Id)
		if skillModel != nil {
			continue
		}
		model.NewHeroSkill(ctx, hero.Uid(), hero.ConfigId(), heroSkillGroupLines[i].Id)
	}
	CalcHeroPower(ctx, hero.Uid(), heroId)
}
func UpgradeSkillLevel(ctx context.Context, uid int64, groupId int32) error {
	skillModel, _ := model.GetHeroSkill(ctx, uid, groupId)
	if skillModel == nil {
		return kdmerr.SysDBError.CastErrorf("can not find skill")
	}

	skillLevelLine := cfg_mgr.Cfg.HeroSkillAwakeTable.Get(skillModel.ConfigId())
	if skillLevelLine.IsMax {
		return kdmerr.SysDBError.CastErrorf("skill is max")
	}
	heroModel, _ := model.GetHero(ctx, uid, skillModel.HeroId())
	starLine := cfg_mgr.Cfg.HeroStarTable.Get(heroModel.StarId())
	nextLevel := skillLevelLine.Level + 1
	nextSkillLevelLines := cfg_mgr.Cfg.HeroSkillAwakeTable.FilterSlice(func(v *servercfg.HeroSkillAwakeTableCfg) bool {
		return v.SkillGroupId == groupId && v.Level == nextLevel
	})
	if len(nextSkillLevelLines) == 0 {
		return kdmerr.SysDBError.CastErrorf("can not find next level")
	}
	if nextSkillLevelLines[0].UnlockHeroStar > starLine.HeroStarLevel {
		return kdmerr.SysDBError.CastErrorf("hero star is limit")
	}
	sequence := util.GenerateSequenceString()
	for _, v := range skillLevelLine.LevelUp {
		err := consumable.DeductItem(ctx, uid, v.CostType, int64(v.CostValue), bi.ItemFlowReason_HERO_SKILL, strconv.Itoa(int(groupId)), sequence)
		if err != nil {
			return err
		}
	}

	skillModel.SetConfigId(ctx, nextSkillLevelLines[0].Id)
	CalcHeroPower(ctx, uid, skillModel.HeroId())

	levelLine := cfg_mgr.Cfg.HeroLevelTable.Get(heroModel.LevelId())

	heroLine := cfg_mgr.Cfg.HeroTable.Get(skillModel.HeroId())
	trace := map[string]string{
		"skilltype": nextSkillLevelLines[0].SkillType.String(),
	}
	quest.Notify(ctx, uid, servercfg.TaskType_HeroSkillUp, trace, 1)
	quest.Notify(ctx, uid, servercfg.TaskType_HeroSkillUpTo, trace, 1)
	quest.Notify(ctx, uid, servercfg.TaskType_TotalHeroSkillUp, trace, 1)
	logger.LogCustom(ctx, uid, "legend_hero", logger.NewDetail().Put("action", "skill_level_up").
		Put("hero_id", skillModel.HeroId()).Put("quality", heroLine.HeroQuality.String()).Put("level", levelLine.HeroLevel).Put("star", starLine.HeroStarLevel).
		Put("skill_id", groupId).Put("skill_type", skillLevelLine.SkillType).
		Put("skill_level_before", skillLevelLine.Level).Put("skill_level_after", nextLevel))
	return nil
}

func CalcHeroPower(ctx context.Context, uid int64, heroId int32) int64 {
	hero, err := model.GetHero(ctx, uid, heroId)
	if err != nil {
		return 0
	}
	if hero == nil {
		return 0
	}
	//等级
	power := int64(0)
	heroLevelLine := cfg_mgr.Cfg.HeroLevelTable.Get(hero.LevelId())
	if heroLevelLine == nil {
		hero.SetPower(ctx, power)
		return power
	}
	power += int64(heroLevelLine.Power)

	//星级
	heroStarLine := cfg_mgr.Cfg.HeroStarTable.Get(hero.StarId())
	if heroStarLine == nil {
		hero.SetPower(ctx, power)
		return power
	}
	power += int64(heroStarLine.Power)

	//技能

	skillModels := model.GetHeroSkills(ctx, uid, heroId)
	for _, skillModel := range skillModels {
		skillLine := cfg_mgr.Cfg.HeroSkillAwakeTable.Get(skillModel.ConfigId())
		if skillLine == nil {
			continue
		}
		power += int64(skillLine.Power)
	}

	//基因
	heroGeneLine := cfg_mgr.Cfg.HeroGeneTable.Get(hero.GeneConfigId())
	if heroGeneLine != nil {
		power += int64(heroGeneLine.Power)
	}

	hero.SetPower(ctx, power)
	CalcAllHeroPower(ctx, uid)
	return power
}

func CalcAllHeroPower(ctx context.Context, uid int64) int64 {
	heros, err := model.ExportHeros(ctx, uid)
	if err != nil {
		return 0
	}
	power := int64(0)
	for _, hero := range heros {
		power += hero.Power
	}
	userModel, _ := model.GetUserModel(ctx, uid)
	if power != userModel.Power() {
		userModel.SetPower(ctx, power)
		rank.SetPowerScore(uid, power)
	}

	return power
}
