// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type HeroGeneTableCfg struct {
	Id                       int32                      `json:"Id"`                       // Id
	HeroID                   int32                      `json:"HeroID"`                   // 英雄ID
	HeroIDRef                *HeroTableCfg              `json:"-"`                        // 英雄ID
	HeroGeneLevel            int32                      `json:"HeroGeneLevel"`            // 英雄基因等级
	IsMax                    bool                       `json:"IsMax"`                    // 是否最大基因等级
	LevelUp                  []*CostKVS                 `json:"LevelUp"`                  // 基因升级
	Power                    int32                      `json:"Power"`                    // 戰力
	Attr                     *AttrStr                   `json:"Attr"`                     // 属性
	AllHeroCritDmgUp         float32                    `json:"AllHeroCritDmgUp"`         // 全体英雄暴击伤害提升
	IsUnlockExtraSkillEffect bool                       `json:"IsUnlockExtraSkillEffect"` // 是否解锁额外技能效果
	RefHeroStarLevel         int32                      `json:"RefHeroStarLevel"`         // 解锁额外技能所需英雄星级
	PVEPassiveSkillEffect    []int32                    `json:"PVEPassiveSkillEffect"`    // 仅pve生效被动技能段组
	PVEPassiveSkillEffectRef []*HeroSkillEffectTableCfg `json:"-"`                        // 仅pve生效被动技能段组
}

func NewHeroGeneTableCfg() *HeroGeneTableCfg {
	return &HeroGeneTableCfg{
		Id:                       0,
		HeroID:                   0,
		HeroIDRef:                nil,
		HeroGeneLevel:            0,
		IsMax:                    false,
		LevelUp:                  []*CostKVS{},
		Power:                    0,
		Attr:                     NewAttrStr(),
		AllHeroCritDmgUp:         0.0,
		IsUnlockExtraSkillEffect: false,
		RefHeroStarLevel:         0,
		PVEPassiveSkillEffect:    []int32{},
		PVEPassiveSkillEffectRef: []*HeroSkillEffectTableCfg{},
	}
}

type HeroGeneTable struct {
	records  map[int32]*HeroGeneTableCfg
	localIds map[int32]struct{}
}

func NewHeroGeneTable() *HeroGeneTable {
	return &HeroGeneTable{
		records:  map[int32]*HeroGeneTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *HeroGeneTable) Get(key int32) *HeroGeneTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *HeroGeneTable) GetAll() map[int32]*HeroGeneTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *HeroGeneTable) put(key int32, value *HeroGeneTableCfg, local bool) *HeroGeneTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *HeroGeneTable) Range(f func(v *HeroGeneTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *HeroGeneTable) Filter(filterFuncs ...func(v *HeroGeneTableCfg) bool) map[int32]*HeroGeneTableCfg {
	filtered := map[int32]*HeroGeneTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *HeroGeneTable) FilterSlice(filterFuncs ...func(v *HeroGeneTableCfg) bool) []*HeroGeneTableCfg {
	filtered := []*HeroGeneTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *HeroGeneTable) FilterKeys(filterFuncs ...func(v *HeroGeneTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *HeroGeneTable) satisfied(v *HeroGeneTableCfg, filterFuncs ...func(v *HeroGeneTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *HeroGeneTable) setupIndexes() error {
	return nil
}

func (t *HeroGeneTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *HeroGeneTableCfg) bindRefs(c *Configs) {
	r.HeroIDRef = c.HeroTable.Get(r.HeroID)
	for _, e := range r.LevelUp {
		e.bindRefs(c)
	}
	r.Attr.bindRefs(c)
	for _, e := range r.PVEPassiveSkillEffect {
		cfgoRefRecord := c.HeroSkillEffectTable.Get(e)
		r.PVEPassiveSkillEffectRef = append(r.PVEPassiveSkillEffectRef, cfgoRefRecord)
	}
}
