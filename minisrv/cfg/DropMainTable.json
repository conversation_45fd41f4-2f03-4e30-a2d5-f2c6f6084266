{"1": {"Id": 1, "DropGroupId": 1, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 10, "MaxValue": 10}, "2": {"Id": 2, "DropGroupId": 2, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 20, "MaxValue": 20}, "3": {"Id": 3, "DropGroupId": 3, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 60, "MaxValue": 60}, "4": {"Id": 4, "DropGroupId": 4, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 100, "MaxValue": 100}, "5": {"Id": 5, "DropGroupId": 5, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 150, "MaxValue": 150}, "6": {"Id": 6, "DropGroupId": 6, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 190, "MaxValue": 190}, "7": {"Id": 7, "DropGroupId": 7, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 240, "MaxValue": 240}, "8": {"Id": 8, "DropGroupId": 8, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 300, "MaxValue": 300}, "9": {"Id": 9, "DropGroupId": 9, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 360, "MaxValue": 360}, "10": {"Id": 10, "DropGroupId": 10, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 420, "MaxValue": 420}, "11": {"Id": 11, "DropGroupId": 11, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 5, "MaxValue": 5}, "12": {"Id": 12, "DropGroupId": 12, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 10, "MaxValue": 10}, "13": {"Id": 13, "DropGroupId": 13, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 30, "MaxValue": 30}, "14": {"Id": 14, "DropGroupId": 14, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 50, "MaxValue": 50}, "15": {"Id": 15, "DropGroupId": 15, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 75, "MaxValue": 75}, "16": {"Id": 16, "DropGroupId": 16, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 95, "MaxValue": 95}, "17": {"Id": 17, "DropGroupId": 17, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 120, "MaxValue": 120}, "18": {"Id": 18, "DropGroupId": 18, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 150, "MaxValue": 150}, "19": {"Id": 19, "DropGroupId": 19, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 180, "MaxValue": 180}, "20": {"Id": 20, "DropGroupId": 20, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 210, "MaxValue": 210}, "21": {"Id": 21, "DropGroupId": 21, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 20, "MaxValue": 20}, "22": {"Id": 22, "DropGroupId": 22, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 40, "MaxValue": 40}, "23": {"Id": 23, "DropGroupId": 23, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 120, "MaxValue": 120}, "24": {"Id": 24, "DropGroupId": 24, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 200, "MaxValue": 200}, "25": {"Id": 25, "DropGroupId": 25, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 300, "MaxValue": 300}, "26": {"Id": 26, "DropGroupId": 26, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 380, "MaxValue": 380}, "27": {"Id": 27, "DropGroupId": 27, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 480, "MaxValue": 480}, "28": {"Id": 28, "DropGroupId": 28, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 600, "MaxValue": 600}, "29": {"Id": 29, "DropGroupId": 29, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 720, "MaxValue": 720}, "30": {"Id": 30, "DropGroupId": 30, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 840, "MaxValue": 840}, "31": {"Id": 31, "DropGroupId": 31, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 20, "MaxValue": 20}, "32": {"Id": 32, "DropGroupId": 32, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 40, "MaxValue": 40}, "33": {"Id": 33, "DropGroupId": 33, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 120, "MaxValue": 120}, "34": {"Id": 34, "DropGroupId": 34, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 200, "MaxValue": 200}, "35": {"Id": 35, "DropGroupId": 35, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 300, "MaxValue": 300}, "36": {"Id": 36, "DropGroupId": 36, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 380, "MaxValue": 380}, "37": {"Id": 37, "DropGroupId": 37, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 480, "MaxValue": 480}, "38": {"Id": 38, "DropGroupId": 38, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 600, "MaxValue": 600}, "39": {"Id": 39, "DropGroupId": 39, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 720, "MaxValue": 720}, "40": {"Id": 40, "DropGroupId": 40, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 840, "MaxValue": 840}, "41": {"Id": 41, "DropGroupId": 1001, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 500, "MaxValue": 500}, "42": {"Id": 42, "DropGroupId": 1002, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 1000, "MaxValue": 1000}, "43": {"Id": 43, "DropGroupId": 1003, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 3000, "MaxValue": 3000}, "44": {"Id": 44, "DropGroupId": 1004, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 5000, "MaxValue": 5000}, "45": {"Id": 45, "DropGroupId": 1005, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 7500, "MaxValue": 7500}, "46": {"Id": 46, "DropGroupId": 1006, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 9500, "MaxValue": 9500}, "47": {"Id": 47, "DropGroupId": 1007, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 12000, "MaxValue": 12000}, "48": {"Id": 48, "DropGroupId": 1008, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 15000, "MaxValue": 15000}, "49": {"Id": 49, "DropGroupId": 1009, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 18000, "MaxValue": 18000}, "50": {"Id": 50, "DropGroupId": 1010, "Chance": 0, "Weight": 10000, "ItemId": 2, "MinValue": 21000, "MaxValue": 21000}, "51": {"Id": 51, "DropGroupId": 1011, "Chance": 10000, "Weight": 0, "ItemId": 77, "MinValue": 1, "MaxValue": 1}, "52": {"Id": 52, "DropGroupId": 1011, "Chance": 1000, "Weight": 0, "ItemId": 88, "MinValue": 1, "MaxValue": 1}, "53": {"Id": 53, "DropGroupId": 1012, "Chance": 10000, "Weight": 0, "ItemId": 78, "MinValue": 1, "MaxValue": 1}, "54": {"Id": 54, "DropGroupId": 1012, "Chance": 3000, "Weight": 0, "ItemId": 88, "MinValue": 1, "MaxValue": 1}, "55": {"Id": 55, "DropGroupId": 1013, "Chance": 10000, "Weight": 0, "ItemId": 79, "MinValue": 1, "MaxValue": 1}, "56": {"Id": 56, "DropGroupId": 1013, "Chance": 5000, "Weight": 0, "ItemId": 88, "MinValue": 1, "MaxValue": 1}, "57": {"Id": 57, "DropGroupId": 1014, "Chance": 10000, "Weight": 0, "ItemId": 77, "MinValue": 1, "MaxValue": 1}, "58": {"Id": 58, "DropGroupId": 1014, "Chance": 1000, "Weight": 0, "ItemId": 88, "MinValue": 1, "MaxValue": 1}, "60": {"Id": 60, "DropGroupId": 1015, "Chance": 10000, "Weight": 0, "ItemId": 77, "MinValue": 1, "MaxValue": 1}, "61": {"Id": 61, "DropGroupId": 1015, "Chance": 1000, "Weight": 0, "ItemId": 88, "MinValue": 1, "MaxValue": 1}, "63": {"Id": 63, "DropGroupId": 1016, "Chance": 10000, "Weight": 0, "ItemId": 77, "MinValue": 1, "MaxValue": 1}, "64": {"Id": 64, "DropGroupId": 1016, "Chance": 1500, "Weight": 0, "ItemId": 88, "MinValue": 1, "MaxValue": 1}, "200101": {"Id": 200101, "DropGroupId": 2001, "Chance": 0, "Weight": 5000, "ItemId": 122, "MinValue": 1, "MaxValue": 1}, "200102": {"Id": 200102, "DropGroupId": 2001, "Chance": 0, "Weight": 2500, "ItemId": 122, "MinValue": 2, "MaxValue": 2}, "200103": {"Id": 200103, "DropGroupId": 2001, "Chance": 0, "Weight": 1200, "ItemId": 122, "MinValue": 3, "MaxValue": 3}, "200104": {"Id": 200104, "DropGroupId": 2001, "Chance": 0, "Weight": 800, "ItemId": 122, "MinValue": 4, "MaxValue": 4}, "200105": {"Id": 200105, "DropGroupId": 2001, "Chance": 0, "Weight": 200, "ItemId": 122, "MinValue": 5, "MaxValue": 5}, "200106": {"Id": 200106, "DropGroupId": 2001, "Chance": 0, "Weight": 140, "ItemId": 122, "MinValue": 6, "MaxValue": 6}, "200107": {"Id": 200107, "DropGroupId": 2001, "Chance": 0, "Weight": 70, "ItemId": 122, "MinValue": 7, "MaxValue": 7}, "200108": {"Id": 200108, "DropGroupId": 2001, "Chance": 0, "Weight": 50, "ItemId": 122, "MinValue": 8, "MaxValue": 8}, "200109": {"Id": 200109, "DropGroupId": 2001, "Chance": 0, "Weight": 30, "ItemId": 122, "MinValue": 9, "MaxValue": 9}, "200110": {"Id": 200110, "DropGroupId": 2001, "Chance": 0, "Weight": 10, "ItemId": 122, "MinValue": 10, "MaxValue": 10}, "200201": {"Id": 200201, "DropGroupId": 2002, "Chance": 0, "Weight": 5000, "ItemId": 123, "MinValue": 1, "MaxValue": 1}, "200202": {"Id": 200202, "DropGroupId": 2002, "Chance": 0, "Weight": 2500, "ItemId": 123, "MinValue": 2, "MaxValue": 2}, "200203": {"Id": 200203, "DropGroupId": 2002, "Chance": 0, "Weight": 1200, "ItemId": 123, "MinValue": 3, "MaxValue": 3}, "200204": {"Id": 200204, "DropGroupId": 2002, "Chance": 0, "Weight": 800, "ItemId": 123, "MinValue": 4, "MaxValue": 4}, "200205": {"Id": 200205, "DropGroupId": 2002, "Chance": 0, "Weight": 200, "ItemId": 123, "MinValue": 5, "MaxValue": 5}, "200206": {"Id": 200206, "DropGroupId": 2002, "Chance": 0, "Weight": 140, "ItemId": 123, "MinValue": 6, "MaxValue": 6}, "200207": {"Id": 200207, "DropGroupId": 2002, "Chance": 0, "Weight": 70, "ItemId": 123, "MinValue": 7, "MaxValue": 7}, "200208": {"Id": 200208, "DropGroupId": 2002, "Chance": 0, "Weight": 50, "ItemId": 123, "MinValue": 8, "MaxValue": 8}, "200209": {"Id": 200209, "DropGroupId": 2002, "Chance": 0, "Weight": 30, "ItemId": 123, "MinValue": 9, "MaxValue": 9}, "200210": {"Id": 200210, "DropGroupId": 2002, "Chance": 0, "Weight": 10, "ItemId": 123, "MinValue": 10, "MaxValue": 10}, "200301": {"Id": 200301, "DropGroupId": 2003, "Chance": 0, "Weight": 5000, "ItemId": 124, "MinValue": 1, "MaxValue": 1}, "200302": {"Id": 200302, "DropGroupId": 2003, "Chance": 0, "Weight": 2500, "ItemId": 124, "MinValue": 2, "MaxValue": 2}, "200303": {"Id": 200303, "DropGroupId": 2003, "Chance": 0, "Weight": 1200, "ItemId": 124, "MinValue": 3, "MaxValue": 3}, "200304": {"Id": 200304, "DropGroupId": 2003, "Chance": 0, "Weight": 800, "ItemId": 124, "MinValue": 4, "MaxValue": 4}, "200305": {"Id": 200305, "DropGroupId": 2003, "Chance": 0, "Weight": 200, "ItemId": 124, "MinValue": 5, "MaxValue": 5}, "200306": {"Id": 200306, "DropGroupId": 2003, "Chance": 0, "Weight": 140, "ItemId": 124, "MinValue": 6, "MaxValue": 6}, "200307": {"Id": 200307, "DropGroupId": 2003, "Chance": 0, "Weight": 70, "ItemId": 124, "MinValue": 7, "MaxValue": 7}, "200308": {"Id": 200308, "DropGroupId": 2003, "Chance": 0, "Weight": 50, "ItemId": 124, "MinValue": 8, "MaxValue": 8}, "200309": {"Id": 200309, "DropGroupId": 2003, "Chance": 0, "Weight": 30, "ItemId": 124, "MinValue": 9, "MaxValue": 9}, "200310": {"Id": 200310, "DropGroupId": 2003, "Chance": 0, "Weight": 10, "ItemId": 124, "MinValue": 10, "MaxValue": 10}, "200401": {"Id": 200401, "DropGroupId": 2004, "Chance": 0, "Weight": 5000, "ItemId": 125, "MinValue": 1, "MaxValue": 1}, "200402": {"Id": 200402, "DropGroupId": 2004, "Chance": 0, "Weight": 2500, "ItemId": 125, "MinValue": 2, "MaxValue": 2}, "200403": {"Id": 200403, "DropGroupId": 2004, "Chance": 0, "Weight": 1200, "ItemId": 125, "MinValue": 3, "MaxValue": 3}, "200404": {"Id": 200404, "DropGroupId": 2004, "Chance": 0, "Weight": 800, "ItemId": 125, "MinValue": 4, "MaxValue": 4}, "200405": {"Id": 200405, "DropGroupId": 2004, "Chance": 0, "Weight": 200, "ItemId": 125, "MinValue": 5, "MaxValue": 5}, "200406": {"Id": 200406, "DropGroupId": 2004, "Chance": 0, "Weight": 140, "ItemId": 125, "MinValue": 6, "MaxValue": 6}, "200407": {"Id": 200407, "DropGroupId": 2004, "Chance": 0, "Weight": 70, "ItemId": 125, "MinValue": 7, "MaxValue": 7}, "200408": {"Id": 200408, "DropGroupId": 2004, "Chance": 0, "Weight": 50, "ItemId": 125, "MinValue": 8, "MaxValue": 8}, "200409": {"Id": 200409, "DropGroupId": 2004, "Chance": 0, "Weight": 30, "ItemId": 125, "MinValue": 9, "MaxValue": 9}, "200410": {"Id": 200410, "DropGroupId": 2004, "Chance": 0, "Weight": 10, "ItemId": 125, "MinValue": 10, "MaxValue": 10}, "200501": {"Id": 200501, "DropGroupId": 2005, "Chance": 0, "Weight": 5000, "ItemId": 126, "MinValue": 1, "MaxValue": 1}, "200502": {"Id": 200502, "DropGroupId": 2005, "Chance": 0, "Weight": 2500, "ItemId": 126, "MinValue": 2, "MaxValue": 2}, "200503": {"Id": 200503, "DropGroupId": 2005, "Chance": 0, "Weight": 1200, "ItemId": 126, "MinValue": 3, "MaxValue": 3}, "200504": {"Id": 200504, "DropGroupId": 2005, "Chance": 0, "Weight": 800, "ItemId": 126, "MinValue": 4, "MaxValue": 4}, "200505": {"Id": 200505, "DropGroupId": 2005, "Chance": 0, "Weight": 200, "ItemId": 126, "MinValue": 5, "MaxValue": 5}, "200506": {"Id": 200506, "DropGroupId": 2005, "Chance": 0, "Weight": 140, "ItemId": 126, "MinValue": 6, "MaxValue": 6}, "200507": {"Id": 200507, "DropGroupId": 2005, "Chance": 0, "Weight": 70, "ItemId": 126, "MinValue": 7, "MaxValue": 7}, "200508": {"Id": 200508, "DropGroupId": 2005, "Chance": 0, "Weight": 50, "ItemId": 126, "MinValue": 8, "MaxValue": 8}, "200509": {"Id": 200509, "DropGroupId": 2005, "Chance": 0, "Weight": 30, "ItemId": 126, "MinValue": 9, "MaxValue": 9}, "200510": {"Id": 200510, "DropGroupId": 2005, "Chance": 0, "Weight": 10, "ItemId": 126, "MinValue": 10, "MaxValue": 10}, "200601": {"Id": 200601, "DropGroupId": 2006, "Chance": 0, "Weight": 5000, "ItemId": 127, "MinValue": 1, "MaxValue": 1}, "200602": {"Id": 200602, "DropGroupId": 2006, "Chance": 0, "Weight": 2500, "ItemId": 127, "MinValue": 2, "MaxValue": 2}, "200603": {"Id": 200603, "DropGroupId": 2006, "Chance": 0, "Weight": 1200, "ItemId": 127, "MinValue": 3, "MaxValue": 3}, "200604": {"Id": 200604, "DropGroupId": 2006, "Chance": 0, "Weight": 800, "ItemId": 127, "MinValue": 4, "MaxValue": 4}, "200605": {"Id": 200605, "DropGroupId": 2006, "Chance": 0, "Weight": 200, "ItemId": 127, "MinValue": 5, "MaxValue": 5}, "200606": {"Id": 200606, "DropGroupId": 2006, "Chance": 0, "Weight": 140, "ItemId": 127, "MinValue": 6, "MaxValue": 6}, "200607": {"Id": 200607, "DropGroupId": 2006, "Chance": 0, "Weight": 70, "ItemId": 127, "MinValue": 7, "MaxValue": 7}, "200608": {"Id": 200608, "DropGroupId": 2006, "Chance": 0, "Weight": 50, "ItemId": 127, "MinValue": 8, "MaxValue": 8}, "200609": {"Id": 200609, "DropGroupId": 2006, "Chance": 0, "Weight": 30, "ItemId": 127, "MinValue": 9, "MaxValue": 9}, "200610": {"Id": 200610, "DropGroupId": 2006, "Chance": 0, "Weight": 10, "ItemId": 127, "MinValue": 10, "MaxValue": 10}}