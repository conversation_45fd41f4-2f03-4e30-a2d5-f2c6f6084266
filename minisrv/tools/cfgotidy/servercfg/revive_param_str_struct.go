// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type ReviveParamStr struct {
	LostHpPer float32 `json:"LostHpPer"` // 损失生命百分比
	Cnt       int32   `json:"Cnt"`       // 复活次数
}

func NewReviveParamStr() *ReviveParamStr {
	return &ReviveParamStr{
		LostHpPer: 0.0,
		Cnt:       0,
	}
}

func NewMockReviveParamStr() *ReviveParamStr {
	return &ReviveParamStr{
		LostHpPer: 0.0,
		Cnt:       0,
	}
}

func (s *ReviveParamStr) bindRefs(c *Configs) {
}
