// Code generated by <PERSON>j<PERSON> for marshaling/unmarshaling. DO NOT EDIT.

package wrpc

import (
	minirpc "bitbucket.org/kingsgroup/gog-knights/minirpc"
	json "encoding/json"
	easyjson "github.com/mailru/easyjson"
	jlexer "github.com/mailru/easyjson/jlexer"
	jwriter "github.com/mailru/easyjson/jwriter"
)

// suppress unused package warning
var (
	_ *json.RawMessage
	_ *jlexer.Lexer
	_ *jwriter.Writer
	_ easyjson.Marshaler
)

func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc(in *jlexer.Lexer, out *UserData) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "Ui1":
			if in.IsNull() {
				in.Skip()
				out.Ui1 = nil
			} else {
				if out.Ui1 == nil {
					out.Ui1 = new(minirpc.UserInfo)
				}
				(*out.Ui1).UnmarshalEasyJSON(in)
			}
		case "building":
			if in.IsNull() {
				in.Skip()
				out.Building = nil
			} else {
				in.Delim('[')
				if out.Building == nil {
					if !in.IsDelim(']') {
						out.Building = make([]*minirpc.BuildingInfo, 0, 8)
					} else {
						out.Building = []*minirpc.BuildingInfo{}
					}
				} else {
					out.Building = (out.Building)[:0]
				}
				for !in.IsDelim(']') {
					var v1 *minirpc.BuildingInfo
					if in.IsNull() {
						in.Skip()
						v1 = nil
					} else {
						if v1 == nil {
							v1 = new(minirpc.BuildingInfo)
						}
						(*v1).UnmarshalEasyJSON(in)
					}
					out.Building = append(out.Building, v1)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "hero":
			if in.IsNull() {
				in.Skip()
				out.Hero = nil
			} else {
				in.Delim('[')
				if out.Hero == nil {
					if !in.IsDelim(']') {
						out.Hero = make([]*minirpc.HeroInfo, 0, 8)
					} else {
						out.Hero = []*minirpc.HeroInfo{}
					}
				} else {
					out.Hero = (out.Hero)[:0]
				}
				for !in.IsDelim(']') {
					var v2 *minirpc.HeroInfo
					if in.IsNull() {
						in.Skip()
						v2 = nil
					} else {
						if v2 == nil {
							v2 = new(minirpc.HeroInfo)
						}
						(*v2).UnmarshalEasyJSON(in)
					}
					out.Hero = append(out.Hero, v2)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "hero_lottery":
			if in.IsNull() {
				in.Skip()
				out.HeroLottery = nil
			} else {
				if out.HeroLottery == nil {
					out.HeroLottery = new(minirpc.HeroLottery)
				}
				(*out.HeroLottery).UnmarshalEasyJSON(in)
			}
		case "consumables":
			if in.IsNull() {
				in.Skip()
				out.Consumables = nil
			} else {
				in.Delim('[')
				if out.Consumables == nil {
					if !in.IsDelim(']') {
						out.Consumables = make([]*minirpc.Consumable, 0, 8)
					} else {
						out.Consumables = []*minirpc.Consumable{}
					}
				} else {
					out.Consumables = (out.Consumables)[:0]
				}
				for !in.IsDelim(']') {
					var v3 *minirpc.Consumable
					if in.IsNull() {
						in.Skip()
						v3 = nil
					} else {
						if v3 == nil {
							v3 = new(minirpc.Consumable)
						}
						(*v3).UnmarshalEasyJSON(in)
					}
					out.Consumables = append(out.Consumables, v3)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "user_benefits":
			if in.IsNull() {
				in.Skip()
				out.UserBenefits = nil
			} else {
				in.Delim('[')
				if out.UserBenefits == nil {
					if !in.IsDelim(']') {
						out.UserBenefits = make([]*minirpc.UserBenefit, 0, 8)
					} else {
						out.UserBenefits = []*minirpc.UserBenefit{}
					}
				} else {
					out.UserBenefits = (out.UserBenefits)[:0]
				}
				for !in.IsDelim(']') {
					var v4 *minirpc.UserBenefit
					if in.IsNull() {
						in.Skip()
						v4 = nil
					} else {
						if v4 == nil {
							v4 = new(minirpc.UserBenefit)
						}
						(*v4).UnmarshalEasyJSON(in)
					}
					out.UserBenefits = append(out.UserBenefits, v4)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "user_job":
			if in.IsNull() {
				in.Skip()
				out.UserJob = nil
			} else {
				in.Delim('[')
				if out.UserJob == nil {
					if !in.IsDelim(']') {
						out.UserJob = make([]*minirpc.UserJob, 0, 8)
					} else {
						out.UserJob = []*minirpc.UserJob{}
					}
				} else {
					out.UserJob = (out.UserJob)[:0]
				}
				for !in.IsDelim(']') {
					var v5 *minirpc.UserJob
					if in.IsNull() {
						in.Skip()
						v5 = nil
					} else {
						if v5 == nil {
							v5 = new(minirpc.UserJob)
						}
						(*v5).UnmarshalEasyJSON(in)
					}
					out.UserJob = append(out.UserJob, v5)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "user_troop":
			if in.IsNull() {
				in.Skip()
				out.UserTroop = nil
			} else {
				in.Delim('[')
				if out.UserTroop == nil {
					if !in.IsDelim(']') {
						out.UserTroop = make([]*minirpc.UserTroop, 0, 8)
					} else {
						out.UserTroop = []*minirpc.UserTroop{}
					}
				} else {
					out.UserTroop = (out.UserTroop)[:0]
				}
				for !in.IsDelim(']') {
					var v6 *minirpc.UserTroop
					if in.IsNull() {
						in.Skip()
						v6 = nil
					} else {
						if v6 == nil {
							v6 = new(minirpc.UserTroop)
						}
						(*v6).UnmarshalEasyJSON(in)
					}
					out.UserTroop = append(out.UserTroop, v6)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "research":
			if in.IsNull() {
				in.Skip()
				out.Research = nil
			} else {
				in.Delim('[')
				if out.Research == nil {
					if !in.IsDelim(']') {
						out.Research = make([]*minirpc.Research, 0, 8)
					} else {
						out.Research = []*minirpc.Research{}
					}
				} else {
					out.Research = (out.Research)[:0]
				}
				for !in.IsDelim(']') {
					var v7 *minirpc.Research
					if in.IsNull() {
						in.Skip()
						v7 = nil
					} else {
						if v7 == nil {
							v7 = new(minirpc.Research)
						}
						(*v7).UnmarshalEasyJSON(in)
					}
					out.Research = append(out.Research, v7)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "main_task":
			if in.IsNull() {
				in.Skip()
				out.MainTask = nil
			} else {
				in.Delim('[')
				if out.MainTask == nil {
					if !in.IsDelim(']') {
						out.MainTask = make([]*minirpc.MainTask, 0, 8)
					} else {
						out.MainTask = []*minirpc.MainTask{}
					}
				} else {
					out.MainTask = (out.MainTask)[:0]
				}
				for !in.IsDelim(']') {
					var v8 *minirpc.MainTask
					if in.IsNull() {
						in.Skip()
						v8 = nil
					} else {
						if v8 == nil {
							v8 = new(minirpc.MainTask)
						}
						(*v8).UnmarshalEasyJSON(in)
					}
					out.MainTask = append(out.MainTask, v8)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "map_event":
			if in.IsNull() {
				in.Skip()
				out.MapEvent = nil
			} else {
				if out.MapEvent == nil {
					out.MapEvent = new(minirpc.MapEvent)
				}
				(*out.MapEvent).UnmarshalEasyJSON(in)
			}
		case "hero_skill":
			if in.IsNull() {
				in.Skip()
				out.HeroSkill = nil
			} else {
				in.Delim('[')
				if out.HeroSkill == nil {
					if !in.IsDelim(']') {
						out.HeroSkill = make([]*minirpc.HeroSkill, 0, 8)
					} else {
						out.HeroSkill = []*minirpc.HeroSkill{}
					}
				} else {
					out.HeroSkill = (out.HeroSkill)[:0]
				}
				for !in.IsDelim(']') {
					var v9 *minirpc.HeroSkill
					if in.IsNull() {
						in.Skip()
						v9 = nil
					} else {
						if v9 == nil {
							v9 = new(minirpc.HeroSkill)
						}
						(*v9).UnmarshalEasyJSON(in)
					}
					out.HeroSkill = append(out.HeroSkill, v9)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "dave":
			if in.IsNull() {
				in.Skip()
				out.Dave = nil
			} else {
				if out.Dave == nil {
					out.Dave = new(minirpc.Dave)
				}
				(*out.Dave).UnmarshalEasyJSON(in)
			}
		case "user_data":
			if in.IsNull() {
				in.Skip()
				out.UserData = nil
			} else {
				in.Delim('[')
				if out.UserData == nil {
					if !in.IsDelim(']') {
						out.UserData = make([]*minirpc.UserData, 0, 8)
					} else {
						out.UserData = []*minirpc.UserData{}
					}
				} else {
					out.UserData = (out.UserData)[:0]
				}
				for !in.IsDelim(']') {
					var v10 *minirpc.UserData
					if in.IsNull() {
						in.Skip()
						v10 = nil
					} else {
						if v10 == nil {
							v10 = new(minirpc.UserData)
						}
						(*v10).UnmarshalEasyJSON(in)
					}
					out.UserData = append(out.UserData, v10)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "main_line_stage":
			if in.IsNull() {
				in.Skip()
				out.MainLineStage = nil
			} else {
				if out.MainLineStage == nil {
					out.MainLineStage = new(minirpc.MainLineStage)
				}
				(*out.MainLineStage).UnmarshalEasyJSON(in)
			}
		case "cur_server_time":
			out.CurServerTime = int64(in.Int64())
		case "is_new_player":
			out.IsNewPlayer = bool(in.Bool())
		case "rtm_token":
			out.RtmToken = string(in.String())
		case "user_iap_buy_log":
			if in.IsNull() {
				in.Skip()
				out.UserIapBuyLog = nil
			} else {
				if out.UserIapBuyLog == nil {
					out.UserIapBuyLog = new(minirpc.UserIapBuyTimes)
				}
				(*out.UserIapBuyLog).UnmarshalEasyJSON(in)
			}
		case "lord_equip":
			if in.IsNull() {
				in.Skip()
				out.LordEquip = nil
			} else {
				in.Delim('[')
				if out.LordEquip == nil {
					if !in.IsDelim(']') {
						out.LordEquip = make([]*minirpc.LordEquip, 0, 8)
					} else {
						out.LordEquip = []*minirpc.LordEquip{}
					}
				} else {
					out.LordEquip = (out.LordEquip)[:0]
				}
				for !in.IsDelim(']') {
					var v11 *minirpc.LordEquip
					if in.IsNull() {
						in.Skip()
						v11 = nil
					} else {
						if v11 == nil {
							v11 = new(minirpc.LordEquip)
						}
						(*v11).UnmarshalEasyJSON(in)
					}
					out.LordEquip = append(out.LordEquip, v11)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "lord_gem":
			if in.IsNull() {
				in.Skip()
				out.LordGem = nil
			} else {
				in.Delim('[')
				if out.LordGem == nil {
					if !in.IsDelim(']') {
						out.LordGem = make([]*minirpc.LordGem, 0, 8)
					} else {
						out.LordGem = []*minirpc.LordGem{}
					}
				} else {
					out.LordGem = (out.LordGem)[:0]
				}
				for !in.IsDelim(']') {
					var v12 *minirpc.LordGem
					if in.IsNull() {
						in.Skip()
						v12 = nil
					} else {
						if v12 == nil {
							v12 = new(minirpc.LordGem)
						}
						(*v12).UnmarshalEasyJSON(in)
					}
					out.LordGem = append(out.LordGem, v12)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "lord_gem_random":
			if in.IsNull() {
				in.Skip()
				out.LordGemRandom = nil
			} else {
				in.Delim('[')
				if out.LordGemRandom == nil {
					if !in.IsDelim(']') {
						out.LordGemRandom = make([]*minirpc.LordGemRandom, 0, 8)
					} else {
						out.LordGemRandom = []*minirpc.LordGemRandom{}
					}
				} else {
					out.LordGemRandom = (out.LordGemRandom)[:0]
				}
				for !in.IsDelim(']') {
					var v13 *minirpc.LordGemRandom
					if in.IsNull() {
						in.Skip()
						v13 = nil
					} else {
						if v13 == nil {
							v13 = new(minirpc.LordGemRandom)
						}
						(*v13).UnmarshalEasyJSON(in)
					}
					out.LordGemRandom = append(out.LordGemRandom, v13)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "dungeon":
			if in.IsNull() {
				in.Skip()
				out.Dungeon = nil
			} else {
				in.Delim('[')
				if out.Dungeon == nil {
					if !in.IsDelim(']') {
						out.Dungeon = make([]*minirpc.Dungeon, 0, 8)
					} else {
						out.Dungeon = []*minirpc.Dungeon{}
					}
				} else {
					out.Dungeon = (out.Dungeon)[:0]
				}
				for !in.IsDelim(']') {
					var v14 *minirpc.Dungeon
					if in.IsNull() {
						in.Skip()
						v14 = nil
					} else {
						if v14 == nil {
							v14 = new(minirpc.Dungeon)
						}
						(*v14).UnmarshalEasyJSON(in)
					}
					out.Dungeon = append(out.Dungeon, v14)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "function_open":
			if in.IsNull() {
				in.Skip()
				out.FunctionOpen = nil
			} else {
				if out.FunctionOpen == nil {
					out.FunctionOpen = new(minirpc.FunctionOpen)
				}
				(*out.FunctionOpen).UnmarshalEasyJSON(in)
			}
		case "newbie_guide":
			if in.IsNull() {
				in.Skip()
				out.NewbieGuide = nil
			} else {
				if out.NewbieGuide == nil {
					out.NewbieGuide = new(minirpc.NewbieGuide)
				}
				(*out.NewbieGuide).UnmarshalEasyJSON(in)
			}
		case "activity_task":
			if in.IsNull() {
				in.Skip()
				out.ActivityTask = nil
			} else {
				in.Delim('[')
				if out.ActivityTask == nil {
					if !in.IsDelim(']') {
						out.ActivityTask = make([]*minirpc.ActivityTask, 0, 8)
					} else {
						out.ActivityTask = []*minirpc.ActivityTask{}
					}
				} else {
					out.ActivityTask = (out.ActivityTask)[:0]
				}
				for !in.IsDelim(']') {
					var v15 *minirpc.ActivityTask
					if in.IsNull() {
						in.Skip()
						v15 = nil
					} else {
						if v15 == nil {
							v15 = new(minirpc.ActivityTask)
						}
						(*v15).UnmarshalEasyJSON(in)
					}
					out.ActivityTask = append(out.ActivityTask, v15)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "first_charge":
			if in.IsNull() {
				in.Skip()
				out.FirstCharge = nil
			} else {
				in.Delim('[')
				if out.FirstCharge == nil {
					if !in.IsDelim(']') {
						out.FirstCharge = make([]*minirpc.FirstCharge, 0, 8)
					} else {
						out.FirstCharge = []*minirpc.FirstCharge{}
					}
				} else {
					out.FirstCharge = (out.FirstCharge)[:0]
				}
				for !in.IsDelim(']') {
					var v16 *minirpc.FirstCharge
					if in.IsNull() {
						in.Skip()
						v16 = nil
					} else {
						if v16 == nil {
							v16 = new(minirpc.FirstCharge)
						}
						(*v16).UnmarshalEasyJSON(in)
					}
					out.FirstCharge = append(out.FirstCharge, v16)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "growth_fund":
			if in.IsNull() {
				in.Skip()
				out.GrowthFund = nil
			} else {
				in.Delim('[')
				if out.GrowthFund == nil {
					if !in.IsDelim(']') {
						out.GrowthFund = make([]*minirpc.GrowthFund, 0, 8)
					} else {
						out.GrowthFund = []*minirpc.GrowthFund{}
					}
				} else {
					out.GrowthFund = (out.GrowthFund)[:0]
				}
				for !in.IsDelim(']') {
					var v17 *minirpc.GrowthFund
					if in.IsNull() {
						in.Skip()
						v17 = nil
					} else {
						if v17 == nil {
							v17 = new(minirpc.GrowthFund)
						}
						(*v17).UnmarshalEasyJSON(in)
					}
					out.GrowthFund = append(out.GrowthFund, v17)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "month_card":
			if in.IsNull() {
				in.Skip()
				out.MonthCard = nil
			} else {
				in.Delim('[')
				if out.MonthCard == nil {
					if !in.IsDelim(']') {
						out.MonthCard = make([]*minirpc.MonthCard, 0, 8)
					} else {
						out.MonthCard = []*minirpc.MonthCard{}
					}
				} else {
					out.MonthCard = (out.MonthCard)[:0]
				}
				for !in.IsDelim(']') {
					var v18 *minirpc.MonthCard
					if in.IsNull() {
						in.Skip()
						v18 = nil
					} else {
						if v18 == nil {
							v18 = new(minirpc.MonthCard)
						}
						(*v18).UnmarshalEasyJSON(in)
					}
					out.MonthCard = append(out.MonthCard, v18)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "regular_pack":
			if in.IsNull() {
				in.Skip()
				out.RegularPack = nil
			} else {
				in.Delim('[')
				if out.RegularPack == nil {
					if !in.IsDelim(']') {
						out.RegularPack = make([]*minirpc.RegularPack, 0, 8)
					} else {
						out.RegularPack = []*minirpc.RegularPack{}
					}
				} else {
					out.RegularPack = (out.RegularPack)[:0]
				}
				for !in.IsDelim(']') {
					var v19 *minirpc.RegularPack
					if in.IsNull() {
						in.Skip()
						v19 = nil
					} else {
						if v19 == nil {
							v19 = new(minirpc.RegularPack)
						}
						(*v19).UnmarshalEasyJSON(in)
					}
					out.RegularPack = append(out.RegularPack, v19)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "achievement_task":
			if in.IsNull() {
				in.Skip()
				out.AchievementTask = nil
			} else {
				in.Delim('[')
				if out.AchievementTask == nil {
					if !in.IsDelim(']') {
						out.AchievementTask = make([]*minirpc.AchievementTask, 0, 8)
					} else {
						out.AchievementTask = []*minirpc.AchievementTask{}
					}
				} else {
					out.AchievementTask = (out.AchievementTask)[:0]
				}
				for !in.IsDelim(']') {
					var v20 *minirpc.AchievementTask
					if in.IsNull() {
						in.Skip()
						v20 = nil
					} else {
						if v20 == nil {
							v20 = new(minirpc.AchievementTask)
						}
						(*v20).UnmarshalEasyJSON(in)
					}
					out.AchievementTask = append(out.AchievementTask, v20)
					in.WantComma()
				}
				in.Delim(']')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc(out *jwriter.Writer, in UserData) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"Ui1\":"
		out.RawString(prefix[1:])
		if in.Ui1 == nil {
			out.RawString("null")
		} else {
			(*in.Ui1).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"building\":"
		out.RawString(prefix)
		if in.Building == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v21, v22 := range in.Building {
				if v21 > 0 {
					out.RawByte(',')
				}
				if v22 == nil {
					out.RawString("null")
				} else {
					(*v22).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"hero\":"
		out.RawString(prefix)
		if in.Hero == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v23, v24 := range in.Hero {
				if v23 > 0 {
					out.RawByte(',')
				}
				if v24 == nil {
					out.RawString("null")
				} else {
					(*v24).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"hero_lottery\":"
		out.RawString(prefix)
		if in.HeroLottery == nil {
			out.RawString("null")
		} else {
			(*in.HeroLottery).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"consumables\":"
		out.RawString(prefix)
		if in.Consumables == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v25, v26 := range in.Consumables {
				if v25 > 0 {
					out.RawByte(',')
				}
				if v26 == nil {
					out.RawString("null")
				} else {
					(*v26).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"user_benefits\":"
		out.RawString(prefix)
		if in.UserBenefits == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v27, v28 := range in.UserBenefits {
				if v27 > 0 {
					out.RawByte(',')
				}
				if v28 == nil {
					out.RawString("null")
				} else {
					(*v28).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"user_job\":"
		out.RawString(prefix)
		if in.UserJob == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v29, v30 := range in.UserJob {
				if v29 > 0 {
					out.RawByte(',')
				}
				if v30 == nil {
					out.RawString("null")
				} else {
					(*v30).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"user_troop\":"
		out.RawString(prefix)
		if in.UserTroop == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v31, v32 := range in.UserTroop {
				if v31 > 0 {
					out.RawByte(',')
				}
				if v32 == nil {
					out.RawString("null")
				} else {
					(*v32).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"research\":"
		out.RawString(prefix)
		if in.Research == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v33, v34 := range in.Research {
				if v33 > 0 {
					out.RawByte(',')
				}
				if v34 == nil {
					out.RawString("null")
				} else {
					(*v34).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"main_task\":"
		out.RawString(prefix)
		if in.MainTask == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v35, v36 := range in.MainTask {
				if v35 > 0 {
					out.RawByte(',')
				}
				if v36 == nil {
					out.RawString("null")
				} else {
					(*v36).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"map_event\":"
		out.RawString(prefix)
		if in.MapEvent == nil {
			out.RawString("null")
		} else {
			(*in.MapEvent).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"hero_skill\":"
		out.RawString(prefix)
		if in.HeroSkill == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v37, v38 := range in.HeroSkill {
				if v37 > 0 {
					out.RawByte(',')
				}
				if v38 == nil {
					out.RawString("null")
				} else {
					(*v38).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"dave\":"
		out.RawString(prefix)
		if in.Dave == nil {
			out.RawString("null")
		} else {
			(*in.Dave).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"user_data\":"
		out.RawString(prefix)
		if in.UserData == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v39, v40 := range in.UserData {
				if v39 > 0 {
					out.RawByte(',')
				}
				if v40 == nil {
					out.RawString("null")
				} else {
					(*v40).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"main_line_stage\":"
		out.RawString(prefix)
		if in.MainLineStage == nil {
			out.RawString("null")
		} else {
			(*in.MainLineStage).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"cur_server_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.CurServerTime))
	}
	{
		const prefix string = ",\"is_new_player\":"
		out.RawString(prefix)
		out.Bool(bool(in.IsNewPlayer))
	}
	{
		const prefix string = ",\"rtm_token\":"
		out.RawString(prefix)
		out.String(string(in.RtmToken))
	}
	{
		const prefix string = ",\"user_iap_buy_log\":"
		out.RawString(prefix)
		if in.UserIapBuyLog == nil {
			out.RawString("null")
		} else {
			(*in.UserIapBuyLog).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"lord_equip\":"
		out.RawString(prefix)
		if in.LordEquip == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v41, v42 := range in.LordEquip {
				if v41 > 0 {
					out.RawByte(',')
				}
				if v42 == nil {
					out.RawString("null")
				} else {
					(*v42).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"lord_gem\":"
		out.RawString(prefix)
		if in.LordGem == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v43, v44 := range in.LordGem {
				if v43 > 0 {
					out.RawByte(',')
				}
				if v44 == nil {
					out.RawString("null")
				} else {
					(*v44).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"lord_gem_random\":"
		out.RawString(prefix)
		if in.LordGemRandom == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v45, v46 := range in.LordGemRandom {
				if v45 > 0 {
					out.RawByte(',')
				}
				if v46 == nil {
					out.RawString("null")
				} else {
					(*v46).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"dungeon\":"
		out.RawString(prefix)
		if in.Dungeon == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v47, v48 := range in.Dungeon {
				if v47 > 0 {
					out.RawByte(',')
				}
				if v48 == nil {
					out.RawString("null")
				} else {
					(*v48).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"function_open\":"
		out.RawString(prefix)
		if in.FunctionOpen == nil {
			out.RawString("null")
		} else {
			(*in.FunctionOpen).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"newbie_guide\":"
		out.RawString(prefix)
		if in.NewbieGuide == nil {
			out.RawString("null")
		} else {
			(*in.NewbieGuide).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"activity_task\":"
		out.RawString(prefix)
		if in.ActivityTask == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v49, v50 := range in.ActivityTask {
				if v49 > 0 {
					out.RawByte(',')
				}
				if v50 == nil {
					out.RawString("null")
				} else {
					(*v50).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"first_charge\":"
		out.RawString(prefix)
		if in.FirstCharge == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v51, v52 := range in.FirstCharge {
				if v51 > 0 {
					out.RawByte(',')
				}
				if v52 == nil {
					out.RawString("null")
				} else {
					(*v52).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"growth_fund\":"
		out.RawString(prefix)
		if in.GrowthFund == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v53, v54 := range in.GrowthFund {
				if v53 > 0 {
					out.RawByte(',')
				}
				if v54 == nil {
					out.RawString("null")
				} else {
					(*v54).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"month_card\":"
		out.RawString(prefix)
		if in.MonthCard == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v55, v56 := range in.MonthCard {
				if v55 > 0 {
					out.RawByte(',')
				}
				if v56 == nil {
					out.RawString("null")
				} else {
					(*v56).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"regular_pack\":"
		out.RawString(prefix)
		if in.RegularPack == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v57, v58 := range in.RegularPack {
				if v57 > 0 {
					out.RawByte(',')
				}
				if v58 == nil {
					out.RawString("null")
				} else {
					(*v58).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"achievement_task\":"
		out.RawString(prefix)
		if in.AchievementTask == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v59, v60 := range in.AchievementTask {
				if v59 > 0 {
					out.RawByte(',')
				}
				if v60 == nil {
					out.RawString("null")
				} else {
					(*v60).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v UserData) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v UserData) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *UserData) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *UserData) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc1(in *jlexer.Lexer, out *SendTopicMsgParams) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "Topic":
			out.Topic = string(in.String())
		case "MsgType":
			out.MsgType = uint32(in.Uint32())
		case "Msg":
			out.Msg = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc1(out *jwriter.Writer, in SendTopicMsgParams) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"Topic\":"
		out.RawString(prefix[1:])
		out.String(string(in.Topic))
	}
	{
		const prefix string = ",\"MsgType\":"
		out.RawString(prefix)
		out.Uint32(uint32(in.MsgType))
	}
	{
		const prefix string = ",\"Msg\":"
		out.RawString(prefix)
		out.String(string(in.Msg))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v SendTopicMsgParams) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc1(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v SendTopicMsgParams) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc1(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *SendTopicMsgParams) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc1(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *SendTopicMsgParams) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc1(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc2(in *jlexer.Lexer, out *SendPBTopicMsgParams) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "Topic":
			out.Topic = string(in.String())
		case "MsgType":
			out.MsgType = uint32(in.Uint32())
		case "Msg":
			out.Msg = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc2(out *jwriter.Writer, in SendPBTopicMsgParams) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"Topic\":"
		out.RawString(prefix[1:])
		out.String(string(in.Topic))
	}
	{
		const prefix string = ",\"MsgType\":"
		out.RawString(prefix)
		out.Uint32(uint32(in.MsgType))
	}
	{
		const prefix string = ",\"Msg\":"
		out.RawString(prefix)
		out.String(string(in.Msg))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v SendPBTopicMsgParams) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc2(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v SendPBTopicMsgParams) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc2(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *SendPBTopicMsgParams) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc2(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *SendPBTopicMsgParams) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc2(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc3(in *jlexer.Lexer, out *SendPBMsgParams) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "FromUid":
			out.FromUid = int64(in.Int64())
		case "Uids":
			if in.IsNull() {
				in.Skip()
				out.Uids = nil
			} else {
				in.Delim('[')
				if out.Uids == nil {
					if !in.IsDelim(']') {
						out.Uids = make([]int64, 0, 8)
					} else {
						out.Uids = []int64{}
					}
				} else {
					out.Uids = (out.Uids)[:0]
				}
				for !in.IsDelim(']') {
					var v61 int64
					v61 = int64(in.Int64())
					out.Uids = append(out.Uids, v61)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "MsgType":
			out.MsgType = uint32(in.Uint32())
		case "Msg":
			if in.IsNull() {
				in.Skip()
				out.Msg = nil
			} else {
				out.Msg = in.Bytes()
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc3(out *jwriter.Writer, in SendPBMsgParams) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"FromUid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.FromUid))
	}
	{
		const prefix string = ",\"Uids\":"
		out.RawString(prefix)
		if in.Uids == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v63, v64 := range in.Uids {
				if v63 > 0 {
					out.RawByte(',')
				}
				out.Int64(int64(v64))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"MsgType\":"
		out.RawString(prefix)
		out.Uint32(uint32(in.MsgType))
	}
	{
		const prefix string = ",\"Msg\":"
		out.RawString(prefix)
		out.Base64Bytes(in.Msg)
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v SendPBMsgParams) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc3(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v SendPBMsgParams) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc3(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *SendPBMsgParams) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc3(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *SendPBMsgParams) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc3(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc4(in *jlexer.Lexer, out *SendMsgParams) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "FromUid":
			out.FromUid = int64(in.Int64())
		case "Uids":
			if in.IsNull() {
				in.Skip()
				out.Uids = nil
			} else {
				in.Delim('[')
				if out.Uids == nil {
					if !in.IsDelim(']') {
						out.Uids = make([]int64, 0, 8)
					} else {
						out.Uids = []int64{}
					}
				} else {
					out.Uids = (out.Uids)[:0]
				}
				for !in.IsDelim(']') {
					var v67 int64
					v67 = int64(in.Int64())
					out.Uids = append(out.Uids, v67)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "MsgType":
			out.MsgType = uint32(in.Uint32())
		case "Msg":
			out.Msg = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc4(out *jwriter.Writer, in SendMsgParams) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"FromUid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.FromUid))
	}
	{
		const prefix string = ",\"Uids\":"
		out.RawString(prefix)
		if in.Uids == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v68, v69 := range in.Uids {
				if v68 > 0 {
					out.RawByte(',')
				}
				out.Int64(int64(v69))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"MsgType\":"
		out.RawString(prefix)
		out.Uint32(uint32(in.MsgType))
	}
	{
		const prefix string = ",\"Msg\":"
		out.RawString(prefix)
		out.String(string(in.Msg))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v SendMsgParams) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc4(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v SendMsgParams) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc4(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *SendMsgParams) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc4(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *SendMsgParams) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc4(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc5(in *jlexer.Lexer, out *Rewards) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "itemId":
			out.ItemId = int32(in.Int32())
		case "itemValue":
			out.ItemValue = int64(in.Int64())
		case "type":
			out.Type = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc5(out *jwriter.Writer, in Rewards) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"itemId\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.ItemId))
	}
	{
		const prefix string = ",\"itemValue\":"
		out.RawString(prefix)
		out.Int64(int64(in.ItemValue))
	}
	{
		const prefix string = ",\"type\":"
		out.RawString(prefix)
		out.Int32(int32(in.Type))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v Rewards) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc5(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v Rewards) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc5(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *Rewards) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc5(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *Rewards) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc5(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc6(in *jlexer.Lexer, out *RequestParams) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "Type":
			out.Type = string(in.String())
		case "Data":
			if in.IsNull() {
				in.Skip()
				out.Data = nil
			} else {
				out.Data = in.Bytes()
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc6(out *jwriter.Writer, in RequestParams) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"Type\":"
		out.RawString(prefix[1:])
		out.String(string(in.Type))
	}
	{
		const prefix string = ",\"Data\":"
		out.RawString(prefix)
		out.Base64Bytes(in.Data)
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v RequestParams) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc6(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v RequestParams) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc6(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *RequestParams) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc6(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *RequestParams) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc6(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc7(in *jlexer.Lexer, out *RankHomeInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "power_rank_info":
			if in.IsNull() {
				in.Skip()
				out.PowerRankInfo = nil
			} else {
				if out.PowerRankInfo == nil {
					out.PowerRankInfo = new(PowerRankInfo)
				}
				(*out.PowerRankInfo).UnmarshalEasyJSON(in)
			}
		case "level_rank_info":
			if in.IsNull() {
				in.Skip()
				out.LevelRankInfo = nil
			} else {
				if out.LevelRankInfo == nil {
					out.LevelRankInfo = new(LevelRankInfo)
				}
				(*out.LevelRankInfo).UnmarshalEasyJSON(in)
			}
		case "guild_power_rank_info":
			if in.IsNull() {
				in.Skip()
				out.GuildPowerRankInfo = nil
			} else {
				if out.GuildPowerRankInfo == nil {
					out.GuildPowerRankInfo = new(GuildPowerRankInfo)
				}
				(*out.GuildPowerRankInfo).UnmarshalEasyJSON(in)
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc7(out *jwriter.Writer, in RankHomeInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"power_rank_info\":"
		out.RawString(prefix[1:])
		if in.PowerRankInfo == nil {
			out.RawString("null")
		} else {
			(*in.PowerRankInfo).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"level_rank_info\":"
		out.RawString(prefix)
		if in.LevelRankInfo == nil {
			out.RawString("null")
		} else {
			(*in.LevelRankInfo).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"guild_power_rank_info\":"
		out.RawString(prefix)
		if in.GuildPowerRankInfo == nil {
			out.RawString("null")
		} else {
			(*in.GuildPowerRankInfo).MarshalEasyJSON(out)
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v RankHomeInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc7(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v RankHomeInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc7(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *RankHomeInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc7(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *RankHomeInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc7(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc8(in *jlexer.Lexer, out *PowerRankInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "rank":
			out.Rank = int32(in.Int32())
		case "uid":
			out.Uid = int64(in.Int64())
		case "name":
			out.Name = string(in.String())
		case "power":
			out.Power = int64(in.Int64())
		case "score":
			out.Score = int32(in.Int32())
		case "icon":
			out.Icon = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc8(out *jwriter.Writer, in PowerRankInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"rank\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.Rank))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"name\":"
		out.RawString(prefix)
		out.String(string(in.Name))
	}
	{
		const prefix string = ",\"power\":"
		out.RawString(prefix)
		out.Int64(int64(in.Power))
	}
	{
		const prefix string = ",\"score\":"
		out.RawString(prefix)
		out.Int32(int32(in.Score))
	}
	{
		const prefix string = ",\"icon\":"
		out.RawString(prefix)
		out.Int32(int32(in.Icon))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v PowerRankInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc8(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v PowerRankInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc8(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *PowerRankInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc8(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *PowerRankInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc8(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc9(in *jlexer.Lexer, out *OneKeyHeroLevelUpgradeRet) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "heroId":
			out.HeroId = int32(in.Int32())
		case "amount":
			out.Amount = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc9(out *jwriter.Writer, in OneKeyHeroLevelUpgradeRet) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"heroId\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.HeroId))
	}
	{
		const prefix string = ",\"amount\":"
		out.RawString(prefix)
		out.Int32(int32(in.Amount))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v OneKeyHeroLevelUpgradeRet) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc9(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v OneKeyHeroLevelUpgradeRet) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc9(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *OneKeyHeroLevelUpgradeRet) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc9(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *OneKeyHeroLevelUpgradeRet) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc9(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc10(in *jlexer.Lexer, out *OneKeyEquipGemReq) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "GemId":
			out.GemId = int32(in.Int32())
		case "Pos":
			out.Pos = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc10(out *jwriter.Writer, in OneKeyEquipGemReq) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"GemId\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.GemId))
	}
	{
		const prefix string = ",\"Pos\":"
		out.RawString(prefix)
		out.Int32(int32(in.Pos))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v OneKeyEquipGemReq) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc10(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v OneKeyEquipGemReq) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc10(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *OneKeyEquipGemReq) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc10(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *OneKeyEquipGemReq) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc10(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc11(in *jlexer.Lexer, out *MultiSendPBMsgParams) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "Params":
			if in.IsNull() {
				in.Skip()
				out.Params = nil
			} else {
				in.Delim('[')
				if out.Params == nil {
					if !in.IsDelim(']') {
						out.Params = make([]*SendPBMsgParams, 0, 8)
					} else {
						out.Params = []*SendPBMsgParams{}
					}
				} else {
					out.Params = (out.Params)[:0]
				}
				for !in.IsDelim(']') {
					var v73 *SendPBMsgParams
					if in.IsNull() {
						in.Skip()
						v73 = nil
					} else {
						if v73 == nil {
							v73 = new(SendPBMsgParams)
						}
						(*v73).UnmarshalEasyJSON(in)
					}
					out.Params = append(out.Params, v73)
					in.WantComma()
				}
				in.Delim(']')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc11(out *jwriter.Writer, in MultiSendPBMsgParams) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"Params\":"
		out.RawString(prefix[1:])
		if in.Params == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v74, v75 := range in.Params {
				if v74 > 0 {
					out.RawByte(',')
				}
				if v75 == nil {
					out.RawString("null")
				} else {
					(*v75).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v MultiSendPBMsgParams) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc11(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v MultiSendPBMsgParams) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc11(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *MultiSendPBMsgParams) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc11(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *MultiSendPBMsgParams) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc11(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc12(in *jlexer.Lexer, out *MultiSendMsgParams) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "Params":
			if in.IsNull() {
				in.Skip()
				out.Params = nil
			} else {
				in.Delim('[')
				if out.Params == nil {
					if !in.IsDelim(']') {
						out.Params = make([]*SendMsgParams, 0, 8)
					} else {
						out.Params = []*SendMsgParams{}
					}
				} else {
					out.Params = (out.Params)[:0]
				}
				for !in.IsDelim(']') {
					var v76 *SendMsgParams
					if in.IsNull() {
						in.Skip()
						v76 = nil
					} else {
						if v76 == nil {
							v76 = new(SendMsgParams)
						}
						(*v76).UnmarshalEasyJSON(in)
					}
					out.Params = append(out.Params, v76)
					in.WantComma()
				}
				in.Delim(']')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc12(out *jwriter.Writer, in MultiSendMsgParams) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"Params\":"
		out.RawString(prefix[1:])
		if in.Params == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v77, v78 := range in.Params {
				if v77 > 0 {
					out.RawByte(',')
				}
				if v78 == nil {
					out.RawString("null")
				} else {
					(*v78).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v MultiSendMsgParams) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc12(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v MultiSendMsgParams) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc12(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *MultiSendMsgParams) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc12(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *MultiSendMsgParams) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc12(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc13(in *jlexer.Lexer, out *ManiFest) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "langUrl":
			out.LangUrl = string(in.String())
		case "appId":
			out.AppId = string(in.String())
		case "bundleVersionR":
			out.BundleVersionR = string(in.String())
		case "bundleVersionG":
			out.BundleVersionG = string(in.String())
		case "cdn":
			out.Cdn = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc13(out *jwriter.Writer, in ManiFest) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"langUrl\":"
		out.RawString(prefix[1:])
		out.String(string(in.LangUrl))
	}
	{
		const prefix string = ",\"appId\":"
		out.RawString(prefix)
		out.String(string(in.AppId))
	}
	{
		const prefix string = ",\"bundleVersionR\":"
		out.RawString(prefix)
		out.String(string(in.BundleVersionR))
	}
	{
		const prefix string = ",\"bundleVersionG\":"
		out.RawString(prefix)
		out.String(string(in.BundleVersionG))
	}
	{
		const prefix string = ",\"cdn\":"
		out.RawString(prefix)
		out.String(string(in.Cdn))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v ManiFest) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc13(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v ManiFest) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc13(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *ManiFest) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc13(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *ManiFest) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc13(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc14(in *jlexer.Lexer, out *LordGemCraft) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "gem_craft_id":
			out.GemCraftId = int32(in.Int32())
		case "gem_ids":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.GemIds = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v79 int32
					v79 = int32(in.Int32())
					(out.GemIds)[key] = v79
					in.WantComma()
				}
				in.Delim('}')
			}
		case "amount":
			out.Amount = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc14(out *jwriter.Writer, in LordGemCraft) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"gem_craft_id\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.GemCraftId))
	}
	{
		const prefix string = ",\"gem_ids\":"
		out.RawString(prefix)
		if in.GemIds == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v80First := true
			for v80Name, v80Value := range in.GemIds {
				if v80First {
					v80First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v80Name))
				out.RawByte(':')
				out.Int32(int32(v80Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"amount\":"
		out.RawString(prefix)
		out.Int32(int32(in.Amount))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v LordGemCraft) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc14(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v LordGemCraft) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc14(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *LordGemCraft) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc14(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *LordGemCraft) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc14(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc15(in *jlexer.Lexer, out *LevelStruct) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "card_ids":
			if in.IsNull() {
				in.Skip()
				out.CardIds = nil
			} else {
				in.Delim('[')
				if out.CardIds == nil {
					if !in.IsDelim(']') {
						out.CardIds = make([]int32, 0, 16)
					} else {
						out.CardIds = []int32{}
					}
				} else {
					out.CardIds = (out.CardIds)[:0]
				}
				for !in.IsDelim(']') {
					var v81 int32
					v81 = int32(in.Int32())
					out.CardIds = append(out.CardIds, v81)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "total_exp":
			out.TotalExp = int32(in.Int32())
		case "hero_hp":
			if in.IsNull() {
				in.Skip()
				out.HeroHp = nil
			} else {
				in.Delim('[')
				if out.HeroHp == nil {
					if !in.IsDelim(']') {
						out.HeroHp = make([]int32, 0, 16)
					} else {
						out.HeroHp = []int32{}
					}
				} else {
					out.HeroHp = (out.HeroHp)[:0]
				}
				for !in.IsDelim(']') {
					var v82 int32
					v82 = int32(in.Int32())
					out.HeroHp = append(out.HeroHp, v82)
					in.WantComma()
				}
				in.Delim(']')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc15(out *jwriter.Writer, in LevelStruct) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"card_ids\":"
		out.RawString(prefix[1:])
		if in.CardIds == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v83, v84 := range in.CardIds {
				if v83 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v84))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"total_exp\":"
		out.RawString(prefix)
		out.Int32(int32(in.TotalExp))
	}
	{
		const prefix string = ",\"hero_hp\":"
		out.RawString(prefix)
		if in.HeroHp == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v85, v86 := range in.HeroHp {
				if v85 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v86))
			}
			out.RawByte(']')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v LevelStruct) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc15(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v LevelStruct) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc15(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *LevelStruct) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc15(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *LevelStruct) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc15(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc16(in *jlexer.Lexer, out *LevelRankInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "rank":
			out.Rank = int32(in.Int32())
		case "uid":
			out.Uid = int64(in.Int64())
		case "name":
			out.Name = string(in.String())
		case "level":
			out.Level = int32(in.Int32())
		case "score":
			out.Score = int32(in.Int32())
		case "icon":
			out.Icon = int32(in.Int32())
		case "finish_time":
			out.FinishTime = int32(in.Int32())
		case "challenge_time":
			out.ChallengeTime = int64(in.Int64())
		case "challenge_hero":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.ChallengeHero = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v87 int32
					v87 = int32(in.Int32())
					(out.ChallengeHero)[key] = v87
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc16(out *jwriter.Writer, in LevelRankInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"rank\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.Rank))
	}
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix)
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"name\":"
		out.RawString(prefix)
		out.String(string(in.Name))
	}
	{
		const prefix string = ",\"level\":"
		out.RawString(prefix)
		out.Int32(int32(in.Level))
	}
	{
		const prefix string = ",\"score\":"
		out.RawString(prefix)
		out.Int32(int32(in.Score))
	}
	{
		const prefix string = ",\"icon\":"
		out.RawString(prefix)
		out.Int32(int32(in.Icon))
	}
	{
		const prefix string = ",\"finish_time\":"
		out.RawString(prefix)
		out.Int32(int32(in.FinishTime))
	}
	{
		const prefix string = ",\"challenge_time\":"
		out.RawString(prefix)
		out.Int64(int64(in.ChallengeTime))
	}
	{
		const prefix string = ",\"challenge_hero\":"
		out.RawString(prefix)
		if in.ChallengeHero == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v88First := true
			for v88Name, v88Value := range in.ChallengeHero {
				if v88First {
					v88First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v88Name))
				out.RawByte(':')
				out.Int32(int32(v88Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v LevelRankInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc16(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v LevelRankInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc16(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *LevelRankInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc16(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *LevelRankInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc16(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc17(in *jlexer.Lexer, out *KillMonsterRet) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "rewards":
			if in.IsNull() {
				in.Skip()
				out.Rewards = nil
			} else {
				in.Delim('[')
				if out.Rewards == nil {
					if !in.IsDelim(']') {
						out.Rewards = make([]*Rewards, 0, 8)
					} else {
						out.Rewards = []*Rewards{}
					}
				} else {
					out.Rewards = (out.Rewards)[:0]
				}
				for !in.IsDelim(']') {
					var v89 *Rewards
					if in.IsNull() {
						in.Skip()
						v89 = nil
					} else {
						if v89 == nil {
							v89 = new(Rewards)
						}
						(*v89).UnmarshalEasyJSON(in)
					}
					out.Rewards = append(out.Rewards, v89)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "selectRougeIds":
			if in.IsNull() {
				in.Skip()
				out.SelectRougeIds = nil
			} else {
				in.Delim('[')
				if out.SelectRougeIds == nil {
					if !in.IsDelim(']') {
						out.SelectRougeIds = make([]int32, 0, 16)
					} else {
						out.SelectRougeIds = []int32{}
					}
				} else {
					out.SelectRougeIds = (out.SelectRougeIds)[:0]
				}
				for !in.IsDelim(']') {
					var v90 int32
					v90 = int32(in.Int32())
					out.SelectRougeIds = append(out.SelectRougeIds, v90)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "unSelectRougeIds":
			if in.IsNull() {
				in.Skip()
				out.UnSelectRougeIds = nil
			} else {
				in.Delim('[')
				if out.UnSelectRougeIds == nil {
					if !in.IsDelim(']') {
						out.UnSelectRougeIds = make([]int32, 0, 16)
					} else {
						out.UnSelectRougeIds = []int32{}
					}
				} else {
					out.UnSelectRougeIds = (out.UnSelectRougeIds)[:0]
				}
				for !in.IsDelim(']') {
					var v91 int32
					v91 = int32(in.Int32())
					out.UnSelectRougeIds = append(out.UnSelectRougeIds, v91)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "kill_amount":
			out.KillAmount = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc17(out *jwriter.Writer, in KillMonsterRet) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"rewards\":"
		out.RawString(prefix[1:])
		if in.Rewards == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v92, v93 := range in.Rewards {
				if v92 > 0 {
					out.RawByte(',')
				}
				if v93 == nil {
					out.RawString("null")
				} else {
					(*v93).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"selectRougeIds\":"
		out.RawString(prefix)
		if in.SelectRougeIds == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v94, v95 := range in.SelectRougeIds {
				if v94 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v95))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"unSelectRougeIds\":"
		out.RawString(prefix)
		if in.UnSelectRougeIds == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v96, v97 := range in.UnSelectRougeIds {
				if v96 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v97))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"kill_amount\":"
		out.RawString(prefix)
		out.Int32(int32(in.KillAmount))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v KillMonsterRet) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc17(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v KillMonsterRet) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc17(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *KillMonsterRet) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc17(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *KillMonsterRet) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc17(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc18(in *jlexer.Lexer, out *InitSymbioticRet) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "resetHeroId":
			if in.IsNull() {
				in.Skip()
				out.ResetHeroId = nil
			} else {
				in.Delim('[')
				if out.ResetHeroId == nil {
					if !in.IsDelim(']') {
						out.ResetHeroId = make([]int32, 0, 16)
					} else {
						out.ResetHeroId = []int32{}
					}
				} else {
					out.ResetHeroId = (out.ResetHeroId)[:0]
				}
				for !in.IsDelim(']') {
					var v98 int32
					v98 = int32(in.Int32())
					out.ResetHeroId = append(out.ResetHeroId, v98)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "resetItemMap":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.ResetItemMap = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v99 int32
					v99 = int32(in.Int32())
					(out.ResetItemMap)[key] = v99
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc18(out *jwriter.Writer, in InitSymbioticRet) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"resetHeroId\":"
		out.RawString(prefix[1:])
		if in.ResetHeroId == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v100, v101 := range in.ResetHeroId {
				if v100 > 0 {
					out.RawByte(',')
				}
				out.Int32(int32(v101))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"resetItemMap\":"
		out.RawString(prefix)
		if in.ResetItemMap == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v102First := true
			for v102Name, v102Value := range in.ResetItemMap {
				if v102First {
					v102First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v102Name))
				out.RawByte(':')
				out.Int32(int32(v102Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v InitSymbioticRet) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc18(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v InitSymbioticRet) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc18(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *InitSymbioticRet) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc18(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *InitSymbioticRet) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc18(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc19(in *jlexer.Lexer, out *GuildPowerRankInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "rank":
			out.Rank = int32(in.Int32())
		case "alliance_id":
			out.AllianceId = int64(in.Int64())
		case "name":
			out.Name = string(in.String())
		case "power":
			out.Power = int64(in.Int64())
		case "flag_base":
			out.FlagBase = int32(in.Int32())
		case "flag_emblem":
			out.FlagEmblem = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc19(out *jwriter.Writer, in GuildPowerRankInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"rank\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.Rank))
	}
	{
		const prefix string = ",\"alliance_id\":"
		out.RawString(prefix)
		out.Int64(int64(in.AllianceId))
	}
	{
		const prefix string = ",\"name\":"
		out.RawString(prefix)
		out.String(string(in.Name))
	}
	{
		const prefix string = ",\"power\":"
		out.RawString(prefix)
		out.Int64(int64(in.Power))
	}
	{
		const prefix string = ",\"flag_base\":"
		out.RawString(prefix)
		out.Int32(int32(in.FlagBase))
	}
	{
		const prefix string = ",\"flag_emblem\":"
		out.RawString(prefix)
		out.Int32(int32(in.FlagEmblem))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v GuildPowerRankInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc19(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v GuildPowerRankInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc19(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *GuildPowerRankInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc19(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *GuildPowerRankInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc19(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc20(in *jlexer.Lexer, out *GiftStruct) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "id":
			out.Id = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc20(out *jwriter.Writer, in GiftStruct) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"id\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.Id))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v GiftStruct) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc20(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v GiftStruct) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc20(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *GiftStruct) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc20(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *GiftStruct) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc20(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc21(in *jlexer.Lexer, out *GetUserInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "name":
			out.Name = string(in.String())
		case "alliance_name":
			out.AllianceName = string(in.String())
		case "alliance_acronym":
			out.AllianceAcronym = string(in.String())
		case "max_stage":
			out.MaxStage = int32(in.Int32())
		case "power":
			out.Power = int64(in.Int64())
		case "friend_stage_limit":
			out.FriendStageLimit = int32(in.Int32())
		case "allow_strangers":
			out.AllowStrangers = bool(in.Bool())
		case "avatar_config_id":
			out.AvatarConfigId = int32(in.Int32())
		case "hero":
			if in.IsNull() {
				in.Skip()
				out.Hero = nil
			} else {
				in.Delim('[')
				if out.Hero == nil {
					if !in.IsDelim(']') {
						out.Hero = make([]*minirpc.HeroInfo, 0, 8)
					} else {
						out.Hero = []*minirpc.HeroInfo{}
					}
				} else {
					out.Hero = (out.Hero)[:0]
				}
				for !in.IsDelim(']') {
					var v103 *minirpc.HeroInfo
					if in.IsNull() {
						in.Skip()
						v103 = nil
					} else {
						if v103 == nil {
							v103 = new(minirpc.HeroInfo)
						}
						(*v103).UnmarshalEasyJSON(in)
					}
					out.Hero = append(out.Hero, v103)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "lord_equip":
			if in.IsNull() {
				in.Skip()
				out.LordEquip = nil
			} else {
				in.Delim('[')
				if out.LordEquip == nil {
					if !in.IsDelim(']') {
						out.LordEquip = make([]*minirpc.LordEquip, 0, 8)
					} else {
						out.LordEquip = []*minirpc.LordEquip{}
					}
				} else {
					out.LordEquip = (out.LordEquip)[:0]
				}
				for !in.IsDelim(']') {
					var v104 *minirpc.LordEquip
					if in.IsNull() {
						in.Skip()
						v104 = nil
					} else {
						if v104 == nil {
							v104 = new(minirpc.LordEquip)
						}
						(*v104).UnmarshalEasyJSON(in)
					}
					out.LordEquip = append(out.LordEquip, v104)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "lordGemPower":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.LordGemPower = make(map[int32]int32)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v105 int32
					v105 = int32(in.Int32())
					(out.LordGemPower)[key] = v105
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc21(out *jwriter.Writer, in GetUserInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"name\":"
		out.RawString(prefix)
		out.String(string(in.Name))
	}
	{
		const prefix string = ",\"alliance_name\":"
		out.RawString(prefix)
		out.String(string(in.AllianceName))
	}
	{
		const prefix string = ",\"alliance_acronym\":"
		out.RawString(prefix)
		out.String(string(in.AllianceAcronym))
	}
	{
		const prefix string = ",\"max_stage\":"
		out.RawString(prefix)
		out.Int32(int32(in.MaxStage))
	}
	{
		const prefix string = ",\"power\":"
		out.RawString(prefix)
		out.Int64(int64(in.Power))
	}
	{
		const prefix string = ",\"friend_stage_limit\":"
		out.RawString(prefix)
		out.Int32(int32(in.FriendStageLimit))
	}
	{
		const prefix string = ",\"allow_strangers\":"
		out.RawString(prefix)
		out.Bool(bool(in.AllowStrangers))
	}
	{
		const prefix string = ",\"avatar_config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.AvatarConfigId))
	}
	{
		const prefix string = ",\"hero\":"
		out.RawString(prefix)
		if in.Hero == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v106, v107 := range in.Hero {
				if v106 > 0 {
					out.RawByte(',')
				}
				if v107 == nil {
					out.RawString("null")
				} else {
					(*v107).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"lord_equip\":"
		out.RawString(prefix)
		if in.LordEquip == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v108, v109 := range in.LordEquip {
				if v108 > 0 {
					out.RawByte(',')
				}
				if v109 == nil {
					out.RawString("null")
				} else {
					(*v109).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"lordGemPower\":"
		out.RawString(prefix)
		if in.LordGemPower == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v110First := true
			for v110Name, v110Value := range in.LordGemPower {
				if v110First {
					v110First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v110Name))
				out.RawByte(':')
				out.Int32(int32(v110Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v GetUserInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc21(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v GetUserInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc21(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *GetUserInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc21(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *GetUserInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc21(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc22(in *jlexer.Lexer, out *GetRewardReturn) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "rewards":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Rewards = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v111 int64
					v111 = int64(in.Int64())
					(out.Rewards)[key] = v111
					in.WantComma()
				}
				in.Delim('}')
			}
		case "isFull":
			out.IsFull = bool(in.Bool())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc22(out *jwriter.Writer, in GetRewardReturn) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"rewards\":"
		out.RawString(prefix[1:])
		if in.Rewards == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v112First := true
			for v112Name, v112Value := range in.Rewards {
				if v112First {
					v112First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v112Name))
				out.RawByte(':')
				out.Int64(int64(v112Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"isFull\":"
		out.RawString(prefix)
		out.Bool(bool(in.IsFull))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v GetRewardReturn) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc22(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v GetRewardReturn) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc22(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *GetRewardReturn) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc22(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *GetRewardReturn) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc22(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc23(in *jlexer.Lexer, out *GetMailListResult) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "mail_list":
			if in.IsNull() {
				in.Skip()
				out.MailList = nil
			} else {
				in.Delim('[')
				if out.MailList == nil {
					if !in.IsDelim(']') {
						out.MailList = make([]*minirpc.UserMail, 0, 8)
					} else {
						out.MailList = []*minirpc.UserMail{}
					}
				} else {
					out.MailList = (out.MailList)[:0]
				}
				for !in.IsDelim(']') {
					var v113 *minirpc.UserMail
					if in.IsNull() {
						in.Skip()
						v113 = nil
					} else {
						if v113 == nil {
							v113 = new(minirpc.UserMail)
						}
						(*v113).UnmarshalEasyJSON(in)
					}
					out.MailList = append(out.MailList, v113)
					in.WantComma()
				}
				in.Delim(']')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc23(out *jwriter.Writer, in GetMailListResult) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"mail_list\":"
		out.RawString(prefix[1:])
		if in.MailList == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v114, v115 := range in.MailList {
				if v114 > 0 {
					out.RawByte(',')
				}
				if v115 == nil {
					out.RawString("null")
				} else {
					(*v115).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v GetMailListResult) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc23(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v GetMailListResult) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc23(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *GetMailListResult) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc23(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *GetMailListResult) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc23(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc24(in *jlexer.Lexer, out *GetAllianceMembersInfosRet) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "infos":
			if in.IsNull() {
				in.Skip()
				out.Infos = nil
			} else {
				in.Delim('[')
				if out.Infos == nil {
					if !in.IsDelim(']') {
						out.Infos = make([]*AllianceMemberInfo, 0, 8)
					} else {
						out.Infos = []*AllianceMemberInfo{}
					}
				} else {
					out.Infos = (out.Infos)[:0]
				}
				for !in.IsDelim(']') {
					var v116 *AllianceMemberInfo
					if in.IsNull() {
						in.Skip()
						v116 = nil
					} else {
						if v116 == nil {
							v116 = new(AllianceMemberInfo)
						}
						(*v116).UnmarshalEasyJSON(in)
					}
					out.Infos = append(out.Infos, v116)
					in.WantComma()
				}
				in.Delim(']')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc24(out *jwriter.Writer, in GetAllianceMembersInfosRet) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"infos\":"
		out.RawString(prefix[1:])
		if in.Infos == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v117, v118 := range in.Infos {
				if v117 > 0 {
					out.RawByte(',')
				}
				if v118 == nil {
					out.RawString("null")
				} else {
					(*v118).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v GetAllianceMembersInfosRet) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc24(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v GetAllianceMembersInfosRet) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc24(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *GetAllianceMembersInfosRet) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc24(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *GetAllianceMembersInfosRet) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc24(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc25(in *jlexer.Lexer, out *GetAllianceListRet) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "alliance":
			if in.IsNull() {
				in.Skip()
				out.Alliance = nil
			} else {
				in.Delim('[')
				if out.Alliance == nil {
					if !in.IsDelim(']') {
						out.Alliance = make([]*minirpc.Alliance, 0, 8)
					} else {
						out.Alliance = []*minirpc.Alliance{}
					}
				} else {
					out.Alliance = (out.Alliance)[:0]
				}
				for !in.IsDelim(']') {
					var v119 *minirpc.Alliance
					if in.IsNull() {
						in.Skip()
						v119 = nil
					} else {
						if v119 == nil {
							v119 = new(minirpc.Alliance)
						}
						(*v119).UnmarshalEasyJSON(in)
					}
					out.Alliance = append(out.Alliance, v119)
					in.WantComma()
				}
				in.Delim(']')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc25(out *jwriter.Writer, in GetAllianceListRet) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"alliance\":"
		out.RawString(prefix[1:])
		if in.Alliance == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v120, v121 := range in.Alliance {
				if v120 > 0 {
					out.RawByte(',')
				}
				if v121 == nil {
					out.RawString("null")
				} else {
					(*v121).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v GetAllianceListRet) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc25(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v GetAllianceListRet) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc25(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *GetAllianceListRet) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc25(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *GetAllianceListRet) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc25(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc26(in *jlexer.Lexer, out *FinishEventReturn) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "success":
			out.Success = bool(in.Bool())
		case "rewards":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Rewards = make(map[int32]int64)
				for !in.IsDelim('}') {
					key := int32(in.Int32Str())
					in.WantColon()
					var v122 int64
					v122 = int64(in.Int64())
					(out.Rewards)[key] = v122
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc26(out *jwriter.Writer, in FinishEventReturn) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"success\":"
		out.RawString(prefix[1:])
		out.Bool(bool(in.Success))
	}
	{
		const prefix string = ",\"rewards\":"
		out.RawString(prefix)
		if in.Rewards == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v123First := true
			for v123Name, v123Value := range in.Rewards {
				if v123First {
					v123First = false
				} else {
					out.RawByte(',')
				}
				out.Int32Str(int32(v123Name))
				out.RawByte(':')
				out.Int64(int64(v123Value))
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v FinishEventReturn) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc26(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v FinishEventReturn) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc26(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *FinishEventReturn) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc26(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *FinishEventReturn) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc26(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc27(in *jlexer.Lexer, out *DailyResult) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "daily_task":
			if in.IsNull() {
				in.Skip()
				out.DailyTask = nil
			} else {
				in.Delim('[')
				if out.DailyTask == nil {
					if !in.IsDelim(']') {
						out.DailyTask = make([]*minirpc.DailyTask, 0, 8)
					} else {
						out.DailyTask = []*minirpc.DailyTask{}
					}
				} else {
					out.DailyTask = (out.DailyTask)[:0]
				}
				for !in.IsDelim(']') {
					var v124 *minirpc.DailyTask
					if in.IsNull() {
						in.Skip()
						v124 = nil
					} else {
						if v124 == nil {
							v124 = new(minirpc.DailyTask)
						}
						(*v124).UnmarshalEasyJSON(in)
					}
					out.DailyTask = append(out.DailyTask, v124)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "daily_chest":
			if in.IsNull() {
				in.Skip()
				out.DailyChest = nil
			} else {
				if out.DailyChest == nil {
					out.DailyChest = new(minirpc.UserData)
				}
				(*out.DailyChest).UnmarshalEasyJSON(in)
			}
		case "weekly_chest":
			if in.IsNull() {
				in.Skip()
				out.WeeklyChest = nil
			} else {
				if out.WeeklyChest == nil {
					out.WeeklyChest = new(minirpc.UserData)
				}
				(*out.WeeklyChest).UnmarshalEasyJSON(in)
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc27(out *jwriter.Writer, in DailyResult) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"daily_task\":"
		out.RawString(prefix[1:])
		if in.DailyTask == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v125, v126 := range in.DailyTask {
				if v125 > 0 {
					out.RawByte(',')
				}
				if v126 == nil {
					out.RawString("null")
				} else {
					(*v126).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"daily_chest\":"
		out.RawString(prefix)
		if in.DailyChest == nil {
			out.RawString("null")
		} else {
			(*in.DailyChest).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"weekly_chest\":"
		out.RawString(prefix)
		if in.WeeklyChest == nil {
			out.RawString("null")
		} else {
			(*in.WeeklyChest).MarshalEasyJSON(out)
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v DailyResult) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc27(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v DailyResult) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc27(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *DailyResult) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc27(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *DailyResult) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc27(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc28(in *jlexer.Lexer, out *AllianceMemberInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "uid":
			out.Uid = int64(in.Int64())
		case "stage_id":
			out.StageId = int32(in.Int32())
		case "power":
			out.Power = int64(in.Int64())
		case "name":
			out.Name = string(in.String())
		case "avatar_config_id":
			out.AvatarConfigId = int32(in.Int32())
		case "step_id":
			out.StepId = int32(in.Int32())
		case "finish_time":
			out.FinishTime = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc28(out *jwriter.Writer, in AllianceMemberInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"uid\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.Uid))
	}
	{
		const prefix string = ",\"stage_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.StageId))
	}
	{
		const prefix string = ",\"power\":"
		out.RawString(prefix)
		out.Int64(int64(in.Power))
	}
	{
		const prefix string = ",\"name\":"
		out.RawString(prefix)
		out.String(string(in.Name))
	}
	{
		const prefix string = ",\"avatar_config_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.AvatarConfigId))
	}
	{
		const prefix string = ",\"step_id\":"
		out.RawString(prefix)
		out.Int32(int32(in.StepId))
	}
	{
		const prefix string = ",\"finish_time\":"
		out.RawString(prefix)
		out.Int32(int32(in.FinishTime))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v AllianceMemberInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc28(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v AllianceMemberInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc28(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *AllianceMemberInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc28(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *AllianceMemberInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc28(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc29(in *jlexer.Lexer, out *AllianceInfoRet) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "alliance":
			if in.IsNull() {
				in.Skip()
				out.Alliance = nil
			} else {
				if out.Alliance == nil {
					out.Alliance = new(minirpc.Alliance)
				}
				(*out.Alliance).UnmarshalEasyJSON(in)
			}
		case "alliance_members":
			if in.IsNull() {
				in.Skip()
				out.AllianceMembers = nil
			} else {
				in.Delim('[')
				if out.AllianceMembers == nil {
					if !in.IsDelim(']') {
						out.AllianceMembers = make([]*minirpc.AllianceMember, 0, 8)
					} else {
						out.AllianceMembers = []*minirpc.AllianceMember{}
					}
				} else {
					out.AllianceMembers = (out.AllianceMembers)[:0]
				}
				for !in.IsDelim(']') {
					var v127 *minirpc.AllianceMember
					if in.IsNull() {
						in.Skip()
						v127 = nil
					} else {
						if v127 == nil {
							v127 = new(minirpc.AllianceMember)
						}
						(*v127).UnmarshalEasyJSON(in)
					}
					out.AllianceMembers = append(out.AllianceMembers, v127)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "alliance_shop":
			if in.IsNull() {
				in.Skip()
				out.AllianceShop = nil
			} else {
				in.Delim('[')
				if out.AllianceShop == nil {
					if !in.IsDelim(']') {
						out.AllianceShop = make([]*minirpc.AllianceShopBuy, 0, 8)
					} else {
						out.AllianceShop = []*minirpc.AllianceShopBuy{}
					}
				} else {
					out.AllianceShop = (out.AllianceShop)[:0]
				}
				for !in.IsDelim(']') {
					var v128 *minirpc.AllianceShopBuy
					if in.IsNull() {
						in.Skip()
						v128 = nil
					} else {
						if v128 == nil {
							v128 = new(minirpc.AllianceShopBuy)
						}
						(*v128).UnmarshalEasyJSON(in)
					}
					out.AllianceShop = append(out.AllianceShop, v128)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "alliance_member_info":
			if in.IsNull() {
				in.Skip()
				out.AllianceMemberInfo = nil
			} else {
				in.Delim('[')
				if out.AllianceMemberInfo == nil {
					if !in.IsDelim(']') {
						out.AllianceMemberInfo = make([]*AllianceMemberInfo, 0, 8)
					} else {
						out.AllianceMemberInfo = []*AllianceMemberInfo{}
					}
				} else {
					out.AllianceMemberInfo = (out.AllianceMemberInfo)[:0]
				}
				for !in.IsDelim(']') {
					var v129 *AllianceMemberInfo
					if in.IsNull() {
						in.Skip()
						v129 = nil
					} else {
						if v129 == nil {
							v129 = new(AllianceMemberInfo)
						}
						(*v129).UnmarshalEasyJSON(in)
					}
					out.AllianceMemberInfo = append(out.AllianceMemberInfo, v129)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "alliance_task":
			if in.IsNull() {
				in.Skip()
				out.AllianceTask = nil
			} else {
				in.Delim('[')
				if out.AllianceTask == nil {
					if !in.IsDelim(']') {
						out.AllianceTask = make([]*minirpc.AllianceTask, 0, 8)
					} else {
						out.AllianceTask = []*minirpc.AllianceTask{}
					}
				} else {
					out.AllianceTask = (out.AllianceTask)[:0]
				}
				for !in.IsDelim(']') {
					var v130 *minirpc.AllianceTask
					if in.IsNull() {
						in.Skip()
						v130 = nil
					} else {
						if v130 == nil {
							v130 = new(minirpc.AllianceTask)
						}
						(*v130).UnmarshalEasyJSON(in)
					}
					out.AllianceTask = append(out.AllianceTask, v130)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "alliance_chest":
			if in.IsNull() {
				in.Skip()
				out.AllianceChest = nil
			} else {
				if out.AllianceChest == nil {
					out.AllianceChest = new(minirpc.UserData)
				}
				(*out.AllianceChest).UnmarshalEasyJSON(in)
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc29(out *jwriter.Writer, in AllianceInfoRet) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"alliance\":"
		out.RawString(prefix[1:])
		if in.Alliance == nil {
			out.RawString("null")
		} else {
			(*in.Alliance).MarshalEasyJSON(out)
		}
	}
	{
		const prefix string = ",\"alliance_members\":"
		out.RawString(prefix)
		if in.AllianceMembers == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v131, v132 := range in.AllianceMembers {
				if v131 > 0 {
					out.RawByte(',')
				}
				if v132 == nil {
					out.RawString("null")
				} else {
					(*v132).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"alliance_shop\":"
		out.RawString(prefix)
		if in.AllianceShop == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v133, v134 := range in.AllianceShop {
				if v133 > 0 {
					out.RawByte(',')
				}
				if v134 == nil {
					out.RawString("null")
				} else {
					(*v134).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"alliance_member_info\":"
		out.RawString(prefix)
		if in.AllianceMemberInfo == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v135, v136 := range in.AllianceMemberInfo {
				if v135 > 0 {
					out.RawByte(',')
				}
				if v136 == nil {
					out.RawString("null")
				} else {
					(*v136).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"alliance_task\":"
		out.RawString(prefix)
		if in.AllianceTask == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v137, v138 := range in.AllianceTask {
				if v137 > 0 {
					out.RawByte(',')
				}
				if v138 == nil {
					out.RawString("null")
				} else {
					(*v138).MarshalEasyJSON(out)
				}
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"alliance_chest\":"
		out.RawString(prefix)
		if in.AllianceChest == nil {
			out.RawString("null")
		} else {
			(*in.AllianceChest).MarshalEasyJSON(out)
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v AllianceInfoRet) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc29(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v AllianceInfoRet) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc29(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *AllianceInfoRet) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc29(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *AllianceInfoRet) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc29(l, v)
}
func easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc30(in *jlexer.Lexer, out *ActivityInfo) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "startTime":
			out.StartTime = int64(in.Int64())
		case "endTime":
			out.EndTime = int64(in.Int64())
		case "status":
			out.Status = int32(in.Int32())
		case "activityName":
			out.ActivityName = string(in.String())
		case "activityId":
			out.ActivityId = int32(in.Int32())
		case "publicTime":
			out.PublicTime = int64(in.Int64())
		case "activityType":
			out.ActivityType = int32(in.Int32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc30(out *jwriter.Writer, in ActivityInfo) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"startTime\":"
		out.RawString(prefix[1:])
		out.Int64(int64(in.StartTime))
	}
	{
		const prefix string = ",\"endTime\":"
		out.RawString(prefix)
		out.Int64(int64(in.EndTime))
	}
	{
		const prefix string = ",\"status\":"
		out.RawString(prefix)
		out.Int32(int32(in.Status))
	}
	{
		const prefix string = ",\"activityName\":"
		out.RawString(prefix)
		out.String(string(in.ActivityName))
	}
	{
		const prefix string = ",\"activityId\":"
		out.RawString(prefix)
		out.Int32(int32(in.ActivityId))
	}
	{
		const prefix string = ",\"publicTime\":"
		out.RawString(prefix)
		out.Int64(int64(in.PublicTime))
	}
	{
		const prefix string = ",\"activityType\":"
		out.RawString(prefix)
		out.Int32(int32(in.ActivityType))
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v ActivityInfo) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc30(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v ActivityInfo) MarshalEasyJSON(w *jwriter.Writer) {
	easyjson7e2e86a9EncodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc30(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *ActivityInfo) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc30(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *ActivityInfo) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjson7e2e86a9DecodeBitbucketOrgKingsgroupGogKnightsMinisrvWireWrpc30(l, v)
}
