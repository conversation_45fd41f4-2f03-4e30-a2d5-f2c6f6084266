package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
	"strconv"
)

func init() {
	http_post.AddPostHandler("clearGem", clearGem)
}
func clearGem(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid") // 获取查询参数q的值
		if uidStr != "" {
			uid, _ := strconv.ParseInt(uidStr, 10, 64)
			gemModels, _ := orm.GetAll[*model.LordGem](ctx, uid)
			if gemModels == nil {
				fmt.Fprintf(w, "宝石不存在")
			} else {
				for _, v := range gemModels {
					v.Delete(ctx)
				}
				fmt.Fprintf(w, "宝石已删除")
			}
			return
		}
	})

}
