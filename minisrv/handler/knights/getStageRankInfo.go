package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 获取关卡排行榜信息
func (_ KnightsHandlers) GetStageRankInfoHandler(ctx *wctx.Context, req *wrpc.GetStageRankInfoRequest) (*wrpc.GetStageRankInfoReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		ret := rank.GetStageLevelRankInfoList(ctx, req.UID, 99)
		replay := wrpc.GetStageRankInfoReply{
			Result: ret,
		}
		rawResp = &replay
		err = nil
	})
	return rawResp.(*wrpc.GetStageRankInfoReply), err
}
