package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 领取联盟任务奖励
func (_ KnightsHandlers) CollectAllianceTaskRewardHandler(ctx *wctx.Context, req *wrpc.CollectAllianceTaskRewardRequest) (*wrpc.CollectAllianceTaskRewardReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		ret, e := quest.CollectAllianceTaskReward(ctx, req.UID, req.QuestId)
		rawResp = &wrpc.CollectAllianceTaskRewardReply{Result: ret}
		err = e
	})
	return rawResp.(*wrpc.CollectAllianceTaskRewardReply), err
}
