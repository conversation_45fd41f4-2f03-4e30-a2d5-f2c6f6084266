package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/main_line_stage"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
	"strconv"
)

func init() {
	http_post.AddPostHandler("pvpTest", pvpTest)
}
func pvpTest(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid") // 获取查询参数q的值
		if uidStr == "" {
			fmt.Fprintf(w, "need uid")
			return
		}
		destUidStr := values.Get("destUid") // 获取查询参数q的值
		if destUidStr == "" {
			fmt.Fprintf(w, "need destUidStr")
			return
		}
		uid, _ := strconv.ParseInt(uidStr, 10, 64)
		destUid, _ := strconv.ParseInt(destUidStr, 10, 64)
		unlock := model.LockUser(ctx, uid)
		defer unlock()
		//heroId, _ := strconv.ParseInt(heroIdStr, 10, 32)
		main_line_stage.GetBattleResult(ctx, uid, destUid, w)
		fmt.Fprintf(w, "本次完成------------------------------------------------\n")

	})

}
