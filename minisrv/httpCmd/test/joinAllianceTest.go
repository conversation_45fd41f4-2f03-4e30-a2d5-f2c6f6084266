package test

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/misc/push"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/ds"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
	"strconv"
)

func init() {
	http_post.AddPostHandler("joinAllianceTest", joinAllianceTest)
}
func joinAllianceTest(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	values := r.URL.Query()
	uidStr := values.Get("uid") // 获取查询参数q的值
	if uidStr == "" {
		fmt.Fprintf(w, "need uid")
		return
	}
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		uid, _ := strconv.ParseInt(uidStr, 10, 64)
		destUserInfo, _ := model.GetUserModel(ctx, uid)
		pushData := push.NewNotificationWithPayload(minirpc.PushCmdType_AGREE_JOIN_ALLIANCE, &minirpc.AgreeJoinAlliance{AllianceId: 1, Name: "test1", Acronym: "test2", IsAgree: true})
		ds.SyncCustom(ctx, pushData, []ds.Observer{destUserInfo})
		fmt.Fprintf(w, "推送成功")
	})
}
