// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type IapMonthCardTableCfg struct {
	Id              int32               `json:"Id"`            // Id
	StringId        string              `json:"StringId"`      // StringId
	Benefits        []int32             `json:"Benefits"`      // 特权
	BenefitsRef     []*BenefitsTableCfg `json:"-"`             // 特权
	BenefitsValue   []float32           `json:"BenefitsValue"` // 特权值
	IapPackageId    int32               `json:"IapPackageId"`  // 内购商品id
	IapPackageIdRef *IapPackageTableCfg `json:"-"`             // 内购商品id
	Limit           PurchaseLimitType   `json:"Limit"`         // 限购类型
	Times           int32               `json:"Times"`         // 限购次数
	RewardDaily     []*RewardKVS        `json:"RewardDaily"`   // 每日领取奖励
	Title           string              `json:"Title"`         // 大标题
	Desc            []*DescStr          `json:"Desc"`          // 描述
	Image           string              `json:"Image"`         // 背景图
	Discount        string              `json:"Discount"`      // 折扣
}

func NewIapMonthCardTableCfg() *IapMonthCardTableCfg {
	return &IapMonthCardTableCfg{
		Id:              0,
		StringId:        "",
		Benefits:        []int32{},
		BenefitsRef:     []*BenefitsTableCfg{},
		BenefitsValue:   []float32{},
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
		RewardDaily:     []*RewardKVS{},
		Title:           "",
		Desc:            []*DescStr{},
		Image:           "",
		Discount:        "",
	}
}

func NewMockIapMonthCardTableCfg() *IapMonthCardTableCfg {
	return &IapMonthCardTableCfg{
		Id:              0,
		StringId:        "",
		Benefits:        []int32{0},
		BenefitsRef:     []*BenefitsTableCfg{},
		BenefitsValue:   []float32{0.0},
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
		RewardDaily:     []*RewardKVS{NewMockRewardKVS()},
		Title:           "",
		Desc:            []*DescStr{NewMockDescStr()},
		Image:           "",
		Discount:        "",
	}
}

type IapMonthCardTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*IapMonthCardTableCfg
	localIds         map[int32]struct{}
}

func NewIapMonthCardTable(configs *Configs) *IapMonthCardTable {
	return &IapMonthCardTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*IapMonthCardTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *IapMonthCardTable) Get(key int32) *IapMonthCardTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapMonthCardTable) GetAll() map[int32]*IapMonthCardTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapMonthCardTable) put(key int32, value *IapMonthCardTableCfg, local bool) *IapMonthCardTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *IapMonthCardTable) putFromInheritedTable(key int32, value *IapMonthCardTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[IapMonthCardTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *IapMonthCardTable) Put(key int32, value *IapMonthCardTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[IapMonthCardTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *IapMonthCardTable) PutAll(m map[int32]*IapMonthCardTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapMonthCardTable) Range(f func(v *IapMonthCardTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapMonthCardTable) Filter(filterFuncs ...func(v *IapMonthCardTableCfg) bool) map[int32]*IapMonthCardTableCfg {
	filtered := map[int32]*IapMonthCardTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapMonthCardTable) FilterSlice(filterFuncs ...func(v *IapMonthCardTableCfg) bool) []*IapMonthCardTableCfg {
	filtered := []*IapMonthCardTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapMonthCardTable) FilterKeys(filterFuncs ...func(v *IapMonthCardTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapMonthCardTable) satisfied(v *IapMonthCardTableCfg, filterFuncs ...func(v *IapMonthCardTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapMonthCardTable) setupIndexes() error {
	return nil
}

func (t *IapMonthCardTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapMonthCardTableCfg) bindRefs(c *Configs) {
	for _, e := range r.Benefits {
		cfgoRefRecord := c.BenefitsTable.Get(e)
		r.BenefitsRef = append(r.BenefitsRef, cfgoRefRecord)
	}
	r.IapPackageIdRef = c.IapPackageTable.Get(r.IapPackageId)
	for _, e := range r.RewardDaily {
		e.bindRefs(c)
	}
	for _, e := range r.Desc {
		e.bindRefs(c)
	}
}

func (t *IapMonthCardTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[IapMonthCardTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewIapMonthCardTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapMonthCardTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[IapMonthCardTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// Benefits
		{
			if record[t.getIndexInCsv("Benefits")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Benefits")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfBenefits int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfBenefits = 0
					} else {
						var err error
						cfgoElemOfBenefits, err = configs.BenefitsTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [IapMonthCardTable]unmarshal record failed, cannot parse ref@BenefitsTable in vector, varName=Benefits, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[IapMonthCardTable]unmarshal record failed, cannot parse ref@BenefitsTable in vector, varName=Benefits, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.Benefits = append(recordCfg.Benefits, cfgoElemOfBenefits)
				}
			}
		}
		// BenefitsValue
		{
			if record[t.getIndexInCsv("BenefitsValue")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("BenefitsValue")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfBenefitsValue float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfBenefitsValue = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [IapMonthCardTable]unmarshal record failed, cannot parse float in vector, varName=BenefitsValue, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[IapMonthCardTable]unmarshal record failed, cannot parse float in vector, varName=BenefitsValue, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfBenefitsValue = float32(cfgoFloat)
					}

					recordCfg.BenefitsValue = append(recordCfg.BenefitsValue, cfgoElemOfBenefitsValue)
				}
			}
		}
		// IapPackageId
		if record[t.getIndexInCsv("IapPackageId")] == "" {
			recordCfg.IapPackageId = 0
		} else {
			var err error
			recordCfg.IapPackageId, err = configs.IapPackageTable.getIdByRef(record[t.getIndexInCsv("IapPackageId")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [IapMonthCardTable]unmarshal csv record failed, varName=IapPackageId, type=ref@IapPackageTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("IapPackageId")], err)
				} else {
					return fmt.Errorf("[IapMonthCardTable]unmarshal csv record failed, varName=IapPackageId, type=ref@IapPackageTable, value=%s, err:[%s]", record[t.getIndexInCsv("IapPackageId")], err)
				}
			}
		}
		// Limit
		{
			if record[t.getIndexInCsv("Limit")] == "" {
				recordCfg.Limit = PurchaseLimitType(enumDefaultValue)
			} else {
				cfgoEnum, err := parsePurchaseLimitType(record[t.getIndexInCsv("Limit")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapMonthCardTable]unmarshal csv record failed, varName=Limit, type=enum@PurchaseLimitType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Limit")], err)
					} else {
						return fmt.Errorf("[IapMonthCardTable]unmarshal csv record failed, varName=Limit, type=enum@PurchaseLimitType, value=%s, err:[%s]", record[t.getIndexInCsv("Limit")], err)
					}
				}
				recordCfg.Limit = cfgoEnum
			}
		}
		// Times
		{
			if record[t.getIndexInCsv("Times")] == "" {
				recordCfg.Times = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Times")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapMonthCardTable]unmarshal csv record failed, varName=Times, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Times")], err)
					} else {
						return fmt.Errorf("[IapMonthCardTable]unmarshal csv record failed, varName=Times, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Times")], err)
					}
				}
				recordCfg.Times = int32(cfgoInt)
			}
		}
		// RewardDaily
		{
			cfgoMeetNilForRewardDailyOfRecordCfg := false
			// element 0 of RewardDaily
			if !cfgoMeetNilForRewardDailyOfRecordCfg {
				cfgoMeetNilForRewardDailyOfRecordCfg = true
				var cfgoElemOfRewardDailyOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("RewardDaily1RewardType")] != "" {
						cfgoMeetNilForRewardDailyOfRecordCfg = false
						var err error
						cfgoElemOfRewardDailyOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("RewardDaily1RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [IapMonthCardTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardDailyOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardDaily1RewardType")], err)
							} else {
								return fmt.Errorf("[IapMonthCardTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardDailyOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("RewardDaily1RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RewardDaily1RewardValue")] != "" {
						cfgoMeetNilForRewardDailyOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("RewardDaily1RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [IapMonthCardTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardDailyOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardDaily1RewardValue")], err)
							} else {
								return fmt.Errorf("[IapMonthCardTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardDailyOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("RewardDaily1RewardValue")], err)
							}
						}
						cfgoElemOfRewardDailyOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardDailyOfRecordCfg {
					recordCfg.RewardDaily = append(recordCfg.RewardDaily, cfgoElemOfRewardDailyOfRecordCfg)
				}
			}
			// element 1 of RewardDaily
			if !cfgoMeetNilForRewardDailyOfRecordCfg {
				cfgoMeetNilForRewardDailyOfRecordCfg = true
				var cfgoElemOfRewardDailyOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("RewardDaily2RewardType")] != "" {
						cfgoMeetNilForRewardDailyOfRecordCfg = false
						var err error
						cfgoElemOfRewardDailyOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("RewardDaily2RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [IapMonthCardTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardDailyOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardDaily2RewardType")], err)
							} else {
								return fmt.Errorf("[IapMonthCardTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardDailyOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("RewardDaily2RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RewardDaily2RewardValue")] != "" {
						cfgoMeetNilForRewardDailyOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("RewardDaily2RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [IapMonthCardTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardDailyOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardDaily2RewardValue")], err)
							} else {
								return fmt.Errorf("[IapMonthCardTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardDailyOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("RewardDaily2RewardValue")], err)
							}
						}
						cfgoElemOfRewardDailyOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardDailyOfRecordCfg {
					recordCfg.RewardDaily = append(recordCfg.RewardDaily, cfgoElemOfRewardDailyOfRecordCfg)
				}
			}
			// element 2 of RewardDaily
			if !cfgoMeetNilForRewardDailyOfRecordCfg {
				cfgoMeetNilForRewardDailyOfRecordCfg = true
				var cfgoElemOfRewardDailyOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("RewardDaily3RewardType")] != "" {
						cfgoMeetNilForRewardDailyOfRecordCfg = false
						var err error
						cfgoElemOfRewardDailyOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("RewardDaily3RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [IapMonthCardTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardDailyOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardDaily3RewardType")], err)
							} else {
								return fmt.Errorf("[IapMonthCardTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardDailyOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("RewardDaily3RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RewardDaily3RewardValue")] != "" {
						cfgoMeetNilForRewardDailyOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("RewardDaily3RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [IapMonthCardTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardDailyOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardDaily3RewardValue")], err)
							} else {
								return fmt.Errorf("[IapMonthCardTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardDailyOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("RewardDaily3RewardValue")], err)
							}
						}
						cfgoElemOfRewardDailyOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardDailyOfRecordCfg {
					recordCfg.RewardDaily = append(recordCfg.RewardDaily, cfgoElemOfRewardDailyOfRecordCfg)
				}
			}

		}
		// Title
		{
			recordCfg.Title = strings.TrimSpace(record[t.getIndexInCsv("Title")])
		}
		// Desc
		{
			cfgoMeetNilForDescOfRecordCfg := false
			// element 0 of Desc
			if !cfgoMeetNilForDescOfRecordCfg {
				cfgoMeetNilForDescOfRecordCfg = true
				var cfgoElemOfDescOfRecordCfg *DescStr = NewDescStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc1Desc")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc1Desc")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc1Param")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Param = strings.TrimSpace(record[t.getIndexInCsv("Desc1Param")])
					}
				}

				if !cfgoMeetNilForDescOfRecordCfg {
					recordCfg.Desc = append(recordCfg.Desc, cfgoElemOfDescOfRecordCfg)
				}
			}
			// element 1 of Desc
			if !cfgoMeetNilForDescOfRecordCfg {
				cfgoMeetNilForDescOfRecordCfg = true
				var cfgoElemOfDescOfRecordCfg *DescStr = NewDescStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc2Desc")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc2Desc")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc2Param")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Param = strings.TrimSpace(record[t.getIndexInCsv("Desc2Param")])
					}
				}

				if !cfgoMeetNilForDescOfRecordCfg {
					recordCfg.Desc = append(recordCfg.Desc, cfgoElemOfDescOfRecordCfg)
				}
			}
			// element 2 of Desc
			if !cfgoMeetNilForDescOfRecordCfg {
				cfgoMeetNilForDescOfRecordCfg = true
				var cfgoElemOfDescOfRecordCfg *DescStr = NewDescStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc3Desc")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc3Desc")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc3Param")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Param = strings.TrimSpace(record[t.getIndexInCsv("Desc3Param")])
					}
				}

				if !cfgoMeetNilForDescOfRecordCfg {
					recordCfg.Desc = append(recordCfg.Desc, cfgoElemOfDescOfRecordCfg)
				}
			}
			// element 3 of Desc
			if !cfgoMeetNilForDescOfRecordCfg {
				cfgoMeetNilForDescOfRecordCfg = true
				var cfgoElemOfDescOfRecordCfg *DescStr = NewDescStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc4Desc")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc4Desc")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc4Param")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Param = strings.TrimSpace(record[t.getIndexInCsv("Desc4Param")])
					}
				}

				if !cfgoMeetNilForDescOfRecordCfg {
					recordCfg.Desc = append(recordCfg.Desc, cfgoElemOfDescOfRecordCfg)
				}
			}
			// element 4 of Desc
			if !cfgoMeetNilForDescOfRecordCfg {
				cfgoMeetNilForDescOfRecordCfg = true
				var cfgoElemOfDescOfRecordCfg *DescStr = NewDescStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc5Desc")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc5Desc")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc5Param")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Param = strings.TrimSpace(record[t.getIndexInCsv("Desc5Param")])
					}
				}

				if !cfgoMeetNilForDescOfRecordCfg {
					recordCfg.Desc = append(recordCfg.Desc, cfgoElemOfDescOfRecordCfg)
				}
			}
			// element 5 of Desc
			if !cfgoMeetNilForDescOfRecordCfg {
				cfgoMeetNilForDescOfRecordCfg = true
				var cfgoElemOfDescOfRecordCfg *DescStr = NewDescStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc6Desc")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc6Desc")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc6Param")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Param = strings.TrimSpace(record[t.getIndexInCsv("Desc6Param")])
					}
				}

				if !cfgoMeetNilForDescOfRecordCfg {
					recordCfg.Desc = append(recordCfg.Desc, cfgoElemOfDescOfRecordCfg)
				}
			}
			// element 6 of Desc
			if !cfgoMeetNilForDescOfRecordCfg {
				cfgoMeetNilForDescOfRecordCfg = true
				var cfgoElemOfDescOfRecordCfg *DescStr = NewDescStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc7Desc")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc7Desc")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Desc7Param")]) != "" {
						cfgoMeetNilForDescOfRecordCfg = false
						cfgoElemOfDescOfRecordCfg.Param = strings.TrimSpace(record[t.getIndexInCsv("Desc7Param")])
					}
				}

				if !cfgoMeetNilForDescOfRecordCfg {
					recordCfg.Desc = append(recordCfg.Desc, cfgoElemOfDescOfRecordCfg)
				}
			}

		}
		// Image
		{
			recordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Image")])
		}
		// Discount
		{
			recordCfg.Discount = strings.TrimSpace(record[t.getIndexInCsv("Discount")])
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [IapMonthCardTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[IapMonthCardTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *IapMonthCardTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "IapMonthCardTable.csv") && (!strings.HasPrefix(fileName, "IapMonthCardTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for IapMonthCardTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[IapMonthCardTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[IapMonthCardTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[IapMonthCardTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[IapMonthCardTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[IapMonthCardTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[IapMonthCardTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[IapMonthCardTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[IapMonthCardTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [IapMonthCardTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *IapMonthCardTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[IapMonthCardTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [IapMonthCardTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *IapMonthCardTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[IapMonthCardTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *IapMonthCardTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[IapMonthCardTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[IapMonthCardTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
