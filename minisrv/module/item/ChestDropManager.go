package item

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"github.com/bytedance/gopkg/util/logger"
	"math/rand"
)

func init() {
	model.GetDropItem = GetDropItem
}

func GetDropItem(dropId int32) map[int32]int64 {
	dropGroupLine := cfg_mgr.Cfg.DropGroupTable.Get(dropId)
	if dropGroupLine == nil {
		logger.Errorf("GetDropItem dropGroupLine is nil dropId:%d", dropId)
		return nil
	}

	dropMainLines := cfg_mgr.Cfg.DropMainTable.GetAll()
	switch dropGroupLine.DropType {
	case 1: //单项掉落
		randomWeight := make(map[int32]int32)
		for _, v := range dropMainLines {
			if v.DropGroupId != dropId {
				continue
			}
			randomWeight[v.Id] = v.Weight
		}
		id := util.RandomByWeight(randomWeight)
		dropMainLine := cfg_mgr.Cfg.DropMainTable.Get(id)
		retRewards := make(map[int32]int64)
		if dropMainLine.MaxValue == dropMainLine.MinValue {
			retRewards[dropMainLine.ItemId] = int64(dropMainLine.MinValue)

		} else {
			retRewards[dropMainLine.ItemId] = int64(rand.Intn(int(dropMainLine.MaxValue-dropMainLine.MinValue))) + int64(dropMainLine.MinValue)
		}
		return retRewards
	case 2: //多项掉落
		retRewards := make(map[int32]int64)
		maxNum := 10000
		for _, v := range dropMainLines {
			if v.DropGroupId != dropId {
				continue
			}
			curValue := rand.Intn(maxNum)
			if v.Chance > int32(curValue) {
				if v.MaxValue > v.MinValue {
					retRewards[v.ItemId] = int64(rand.Intn(int(v.MaxValue-v.MinValue))) + int64(v.MinValue)
				} else {
					retRewards[v.ItemId] = int64(v.MinValue)
				}
			}
		}
		return retRewards

	}

	return nil
}
