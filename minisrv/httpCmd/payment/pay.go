package payment

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/iap"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/misc/push"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/ds"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"io"
	"net/http"
	"strconv"
)

const ServerSecret = "23a93404-6089-42fa-90c3-511be903306f"
const ServerSecretSandbox = "4690f7bc-049a-4cfc-98b7-8270cc51780b"

type RetJson struct {
	ReturnCode string `json:"return_code"`
	ReturnMsg  string `json:"return_msg"`
}

type BodyJson struct {
	AppID                       string `json:"app_id"`
	Fpid                        int    `json:"fpid"`
	AccountID                   string `json:"account_id"`
	ServerID                    string `json:"server_id"`
	RoleID                      string `json:"role_id"`
	OrderID                     string `json:"order_id"`
	ProductID                   string `json:"product_id"`
	ChannelID                   string `json:"channel_id"`
	ChannelTransactionID        string `json:"channel_transaction_id"`
	ChannelTransactionIsSandbox int    `json:"channel_transaction_is_sandbox"`
	SubChannelID                string `json:"sub_channel_id"`
	PackageID                   string `json:"package_id"`
	PayTime                     int64  `json:"pay_time"`
	ResultCode                  string `json:"result_code"`
	ExtraData                   string `json:"extra_data"`
	NonceStr                    string `json:"nonce_str"`
	Currency                    string `json:"currency"`
	Amount                      string `json:"amount"`
	AmountFen                   int    `json:"amount_fen"`
	PaidCurrency                string `json:"paid_currency"`
	PaidAmount                  string `json:"paid_amount"`
	PaidAmountFen               int    `json:"paid_amount_fen"`
	ProductType                 string `json:"product_type"`
}

type ExtraData struct {
	PackageId int32 `json:"package_id"`
	Seq       int32 `json:"seq"`
}

func init() {
	http_post.AddPostHandler("pay", pay)
	http_post.AddPostHandler("refund", refund)
	http_post.AddPostHandler("testPay", testPay)
}
func testPay(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//通过uid删除
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid") // 获取查询参数q的值
		orderId := values.Get("order_id")
		uid, _ := strconv.ParseInt(uidStr, 10, 32)
		packageId := int32(1)
		iapLine := cfg_mgr.Cfg.IapPackageTable.Get(int32(packageId))
		if iapLine == nil {
			fmt.Fprintf(w, genReturnJson("FAIL", " product id is error:"+strconv.Itoa(int(packageId))))
			return
		}
		model.GetPaymentOrderModel(ctx, uid, orderId)
		rewardLine := iapLine.IapPackageRewardIdRef
		seq := util.GenerateSequenceString()
		for k, _ := range rewardLine.RewardType {
			model.AddItem(ctx, uid, rewardLine.RewardType[k], int64(rewardLine.RewardValue[k]), bi.ItemFlowReason_BUY_GIFT_PACK, strconv.Itoa(int(packageId)), seq)
		}
		fmt.Fprintf(w, genReturnJson("SUCCESS", "SUCCESS"))
		pushData := push.NewNotificationWithPayload(minirpc.PushCmdType_Order_Finish, &minirpc.OrderResult{Seq: 1, OrderId: "1231231", PackageId: packageId})
		user, _ := model.GetUserModel(ctx, uid)
		ds.SyncCustom(ctx, pushData, []ds.Observer{user})
	})
}
func pay(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	bodyData, _ := io.ReadAll(r.Body)
	defer r.Body.Close()
	var data BodyJson
	err := json.Unmarshal([]byte(bodyData), &data)
	if err != nil {
		// 处理可能出现的错误
		fmt.Fprintf(w, genReturnJson("FAIL", " parse body error:"+string(bodyData)))
		return
	}
	secret := ServerSecret
	if data.ChannelTransactionIsSandbox == 1 {
		secret = ServerSecretSandbox
	}
	sign := Base64WithSha256(string(bodyData), secret)
	if r.Header.Get("auth") != sign {
		fmt.Fprintf(w, genReturnJson("FAIL", "cur sign is: "+sign))
		return
	}
	var extData ExtraData
	err = json.Unmarshal([]byte(data.ExtraData), &extData)
	if err != nil {
		// 处理可能出现的错误
		fmt.Fprintf(w, genReturnJson("FAIL", " parse ExtraData error:"+string(data.ExtraData)))
		return
	}
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//通过uid删除
		uid, _ := strconv.ParseInt(data.RoleID, 10, 32)
		packageId := extData.PackageId
		iap.SubmitOrder(ctx, uid, packageId, data.OrderID, data.ProductID, extData.Seq, w)
	})

}
func Base64WithSha256(str string, secret string) string {
	hash := hmac.New(sha256.New, []byte(secret))
	_, _ = hash.Write([]byte(str))
	return base64.StdEncoding.EncodeToString(hash.Sum(nil))
}
func genReturnJson(code string, msg string) string {
	response := RetJson{
		ReturnCode: code,
		ReturnMsg:  msg,
	}
	jsonData, _ := json.Marshal(response)
	return string(jsonData)
}

func refund(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	bodyData, _ := io.ReadAll(r.Body)
	defer r.Body.Close()
	var data BodyJson
	err := json.Unmarshal([]byte(bodyData), &data)
	if err != nil {
		// 处理可能出现的错误
		fmt.Fprintf(w, genReturnJson("FAIL", " parse body error:"+string(bodyData)))
		return
	}
	secret := ServerSecret
	if data.ChannelTransactionIsSandbox == 1 {
		secret = ServerSecretSandbox
	}
	sign := Base64WithSha256(string(bodyData), secret)
	if r.Header.Get("auth") != sign {
		fmt.Fprintf(w, genReturnJson("FAIL", "cur sign is: "+sign))
	}
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {

		//通过uid删除
		uid, _ := strconv.ParseInt(data.RoleID, 10, 32)
		productId, _ := strconv.ParseInt(data.ProductID, 10, 32)
		orderModel, _ := model.GetPaymentOrderModel(ctx, uid, data.OrderID)
		if orderModel.Status() == model.ORDER_STATE_PREPARE {
			//发奖励
			if orderModel.ProductId() != int32(productId) {
				fmt.Fprintf(w, genReturnJson("FAIL", " product id is error:"+strconv.Itoa(int(orderModel.ProductId()))))
				return
			}
			orderModel.SetStatus(ctx, model.ORDER_STATE_REFUSE)

		}
		fmt.Fprintf(w, genReturnJson("SUCCESS", "SUCCESS"))
	})
}
