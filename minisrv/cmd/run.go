package cmd

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/clienthttp"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/g"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/handler"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd"
	_ "bitbucket.org/kingsgroup/gog-knights/minisrv/model/auto_use"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model/job_worker"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model/observer"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/account"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/payment"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/misc/stargate"
	"context"
	"fmt"
	"github.com/highras/rtm-server-sdk-go/src/rtm"
	"github.com/oschwald/geoip2-golang"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"gitlab-ee.funplus.io/watcher/watcher/misc/chrono"
	"gitlab-ee.funplus.io/watcher/watcher/misc/wlog"
	"gitlab-ee.funplus.io/watcher/watcher/stargate/gkit"
	"gitlab-ee.funplus.io/watcher/watcher/wrpc/client"
	"gitlab-ee.funplus.io/watcher/watcher/wrpc/codec"
	"gitlab-ee.funplus.io/watcher/watcher/wrpc/proc"
	"gitlab-ee.funplus.io/watcher/watcher/wrpc/wire/enum"
	"gitlab-ee.funplus.io/watcher/watcher/wrpc/wire/pb"
	"gitlab-ee.funplus.io/watcher/watcher/wrpc/wnet"
	"log"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"path/filepath"
	"reflect"
	"runtime/debug"
	"strings"
	"sync"
	"syscall"
	"time"

	"bitbucket.org/kingsgroup/gog-knights/conf"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cluster"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/globals"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/logging"
	monitor_deadlock "gitlab-ee.funplus.io/backend-platform/zplus-go/monitor/deadlock"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/monitor/guard"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/timeutils"
	"go.etcd.io/etcd/client/v3"

	"bitbucket.org/kingsgroup/gog-knights/misc/push"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/crash"

	"bitbucket.org/kingsgroup/gog-knights/minisrv/system"
	"bitbucket.org/kingsgroup/gog-knights/misc/callback"
	"bitbucket.org/kingsgroup/gog-knights/misc/leaderboard"
	"github.com/sasha-s/go-deadlock"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/ds"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/ioc"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm/session"

	"bitbucket.org/kingsgroup/gog-knights/minisrv/srvconf"

	conf2 "bitbucket.org/kingsgroup/gog-knights/misc/conf"

	"bitbucket.org/kingsgroup/gog-knights/minisrv/service"

	"bitbucket.org/kingsgroup/gog-knights/misc"
)

var (
	gKingdomServer *service.Server
)

func Run() {
	sw := timeutils.NewStopwatch()

	deadlock.Opts.DeadlockTimeout = 30 * time.Second
	deadlock.Opts.Disable = true
	kingdomId := int32(DefaultCmdArgs.Kingdom)

	ctx := context.Background()
	syncLog := setupLogging(ctx)

	defer func() {
		if r := recover(); r != nil {
			_, _ = fmt.Fprintf(os.Stderr, "<gog-knights-server> panic: %+v\n%s", r, debug.Stack())
			logging.Errorf(ctx, "<gog-knights-server> panic: %+v\n%s", r, debug.Stack())
		}

		syncLog()
	}()

	monitor_deadlock.Start()

	if DefaultCmdArgs.GuardEnabled {
		guard.Setup(guard.LogPeriodic(time.Minute), guard.AddFilter(func(t reflect.Type) bool {
			return true
		}))
	}

	if len(DefaultCmdArgs.AddrPProf) == 0 {
		misc.SendNoticeMsg(ctx, misc.MsgTypeNotice, "addr pprof null", fmt.Errorf("kingdom %d not set addr pprof", system.KingdomId))
		panic("pprof addr nil")
	}

	// 先监听服务，检查是否被占用
	gKingdomServer = service.New(DefaultCmdArgs.AddrGrpc)

	conf2.SetUpConfigMgr()
	if err := srvconf.SetUp(); err != nil {
		panic(err)
	}
	kdmSrvConfig := srvconf.GetKdmSrvConfig()
	misc.NoticeUrl = kdmSrvConfig.KdmsrvConf.NoticeURL
	misc.TestMode = system.ProfileTest

	//bi路径
	bi.BiPath = kdmSrvConfig.KdmsrvConf.BiConfig.BI_PATH
	//初始化sdk
	account.ServerSecret = kdmSrvConfig.KdmsrvConf.ServerSecret
	payment.PaymentServerSecret = kdmSrvConfig.KdmsrvConf.PaymentServerSecret
	misc.SetUpNotice(ctx, kdmSrvConfig.KdmsrvConf.EtcdAddrs, fmt.Sprintf("Kingdom-%d", kingdomId))
	ctx = conf2.WithConfig(ctx)
	setUpCluster(ctx, kingdomId, DefaultCmdArgs.AddrGrpc, kdmSrvConfig.KdmsrvConf.EtcdAddrs)
	k := cluster.GetKingdom()
	if k.Ready() {
		if k.Topic() == "" {
			panic("topic nil")
		}
		system.GameConfDir = filepath.Join(system.ConfigRoot, k.Topic())
		logging.Infow(ctx, "GameConfDir", "dir", system.GameConfDir)
		if err := conf.SetUp(system.GameConfDir); err != nil {
			panic(err)
		}
	}
	//FIXME
	//k.RedisClient().Set(enum.ADDRPROF, DefaultCmdArgs.AddrPProf, 0)
	rank.SetUpRank(kdmSrvConfig.KdmsrvConf.RankRedisAddr)
	proxyName := kdmSrvConfig.KdmsrvConf.StargateAgent.ProxyName
	if len(proxyName) == 0 {
		proxyName = globals.AppName()
	}
	proxyName = fmt.Sprintf("%s_%s_%s", proxyName, misc.GetIpWithUnderLine(), gKingdomServer.Port())
	//初始化chrono
	chrono.Initialize(chrono.WithLogger(wlog.Default()), chrono.WithMultipleThreadBeatInterval(5*time.Millisecond))
	// 建立stargate与server路由关系
	var wg sync.WaitGroup
	// Create a proc dispatcher
	procDispatcher := proc.NewDispatcher(proc.WithLogger(wlog.Default()))
	procDispatcher.Launch()

	// Init wrpc
	stargateMapConnOpts := client.DefaultConnOptsB

	// wrpc handler
	var wrpcHandlers = wrpc.BlendHandlerMap(handler.WrpcHandlers{})

	dispatcherOption := wrpc.NewDispatcherForAppBackend(procDispatcher, func() map[int32]wnet.MsgHandler {
		return wrpcHandlers
	})
	g.EtcdClient, _ = gkit.NewEtcdClient(kdmSrvConfig.KdmsrvConf.EtcdAddrs)
	g.StargateMap = gkit.NewStargateMap(wlog.Default(), g.EtcdClient, gkit.StargateBasic, "AddrB", g.Name,
		stargateMapConnOpts, dispatcherOption)
	g.StargateMap.Launch(&wg)

	stargate.Setup(ctx, &stargate.Options{
		EtcdAddrs: kdmSrvConfig.KdmsrvConf.EtcdAddrs,
		ProxyName: proxyName,
		Category:  int32(kdmSrvConfig.KdmsrvConf.StargateAgent.Category),
		ConnHandler: func(conn wnet.Conn, msg wnet.Message) error {
			gs := callback.GetGameServer()
			if gs == nil {
				return nil
			}
			offlineNtf := &pb.UserOfflineNtf{}
			err := codec.Autofit{}.Unmarshal(enum.Codec_JSON, msg.Payload(), offlineNtf)
			if err == nil {
				Data := struct {
					Fpid        int64 `json:"fpid"`
					OfflineTime int64 `json:"offlineTime"`
				}{
					offlineNtf.UID,
					time.Now().Unix(),
				}

				_, err1 := callback.GetGameServer().Call(context.Background(), "UpdateOfflineTimeCallBack", 0, Data)
				if err1 != nil {
					logging.Error(ctx, "UpdateOfflineTime post error: ", err1)
				}
			}
			return nil
		},
	})

	// FIXME
	//err := aws.Setup(&aws.S3Config{
	//	Online:             kdmSrvConfig.KdmsrvConf.S3Config.Online,
	//	Prefix:             kdmSrvConfig.KdmsrvConf.S3Config.Prefix,
	//	Bucket:             kdmSrvConfig.KdmsrvConf.S3Config.Bucket,
	//	Region:             kdmSrvConfig.KdmsrvConf.S3Config.Region,
	//	AwsAccessKeyId:     kdmSrvConfig.KdmsrvConf.S3Config.AwsAccessKeyID,
	//	AwsSecretAccessKey: kdmSrvConfig.KdmsrvConf.S3Config.AwsSecretAccessKey,
	//})
	//if err != nil {
	//	panic(err)
	//}

	// TODO hotfix模块
	// 加载ioc业务逻辑前先执行hotfix，如果有新的patch代码应先生效
	//hotfix.Setup(ctx)
	//system.PatchVersion = hotfix.GetLastVersion()

	// TODO 虚拟时间
	// vtime 启动
	//if err := vtime.Start(ctx); err != nil {
	//	panic(err)
	//}

	err := setUpTicket(kdmSrvConfig)
	if err != nil {
		panic(err)
	}

	// 初始化orm
	err = setUpOrm(kdmSrvConfig)
	tasklet.WithMiddleware(orm.NewBufferMiddleware(func() bool { return true }), orm.CacheMiddleware, ds.TaskletMiddleware)
	if err != nil {
		panic(err)
	}

	var asyncPushInterval time.Duration
	if kdmSrvConfig.KdmsrvConf.AsyncPushConfig.AsyncPushInterval == "" {
		asyncPushInterval = 100 * time.Millisecond
	} else {
		asyncPushInterval, err = time.ParseDuration(kdmSrvConfig.KdmsrvConf.AsyncPushConfig.AsyncPushInterval)
		if err != nil {
			panic(err)
		}
	}

	asyncPushEnabled := kdmSrvConfig.KdmsrvConf.AsyncPushConfig.AsyncPushEnabled
	//asyncPushInterval := kdmSrvConfig.KdmsrvConf.AsyncPushConfig.AsyncPushInterval
	asyncPushConcurrence := kdmSrvConfig.KdmsrvConf.AsyncPushConfig.AsyncPushConcurrence
	// setup data sync component
	ds.Setup(ctx, ds.SetNotifier(push.NewNotifier(observer.NewLinker)),
		ds.SetAsyncPushEnabled(asyncPushEnabled), ds.SetAsyncPushInterval(asyncPushInterval), ds.SetAsyncPushConcurrence(asyncPushConcurrence))

	// 组装应用
	ioc.Setup()

	//gKingdomServer.Register(ctx, DefaultCmdArgs.AddrGrpc, kdmSrvConfig.KdmsrvConf.EtcdAddrs, kingdomId)

	//var wg sync.WaitGroup
	chErr := make(chan error, 100)
	//startIoc(ctx, kingdomId, func(ctx2 context.Context) {
	//	gKingdomServer.Serve(ctx, int64(kdmSrvConfig.KdmsrvConf.KdmSrvTTL), &wg, chErr)
	//})

	crash.AddHandler(func(ctx context.Context, msg interface{}, stack []byte, fatal bool) {
		misc.SendNoticeMsg(ctx, misc.MsgTypePanic, msg, string(stack))
		if fatal {
			misc.BroadcastShutdownSignal(msg)
		}
	})

	if err1 := leaderboard.Reload(ctx, kingdomId); err1 != nil {
		panic(err)
	}

	go func() {
		//http.Handle("/metrics", metrics.Setup(append([]prometheus.Collector{orm.Collector(), g.Collector(), guard.Collector(), aoi.Collector()}, ioc.StatsCollector()...))) // TODO 性能指标
		http.Handle("/", misc.StringHandler("hello, kingdom server"))
		//http.HandleFunc("/hotfix", hotfix.Handler) TODO hotfix
		if err := http.ListenAndServe(DefaultCmdArgs.AddrPProf, nil); err != nil {
			panic(err)
		}
	}()
	//初始化ticket
	go func() {
		ticker := time.NewTicker(1 * time.Second)
		defer ticker.Stop()
		for _ = range ticker.C {
			job_worker.WakeUpAll(ctx)
		}
	}()

	//初始化GM http server
	go func() {
		err := httpcmd.ServeHttp(DefaultCmdArgs.AddrHttpcmd)
		if err != nil {
			panic(err)
		}
	}()

	//初始化客户端 http server
	go func() {
		err := clienthttp.ServeClientHttp(DefaultCmdArgs.AddrClientHttp)
		if err != nil {
			panic(err)
		}
	}()

	misc.SendNoticeMsg(ctx, misc.MsgTypeNotice, fmt.Errorf("kingdom %d started ok, ip %s, \n%s", DefaultCmdArgs.Kingdom, k.Host(), time.Now()),
		fmt.Sprintf("Boot elapsed %.1f seconds, \nGitVersion:%s\nGitBranch:%s\nPatchVersion:%s\nBuildTime:%s",
			sw.UnixEscaped(), system.GitVersion, system.GitBranch, system.PatchVersion, system.BuildTime))

	// Boot time
	logging.Infof(ctx, "boot time: %.1f seconds, GitVersion:%s\nGitBranch:%s\nPatchVersion:%s\nBuildTime:%s",
		sw.UnixEscaped(), system.GitVersion, system.GitBranch, system.PatchVersion, system.BuildTime)

	// Wait for signals
	chSignal := make(chan os.Signal, 1)
	signal.Notify(chSignal, syscall.SIGINT, syscall.SIGTERM)
	g.RootDir = kdmSrvConfig.KdmsrvConf.RootDir
	//g.ClientRtm = rtm.NewRTMServerClient(80000782, "c055d9f1-65e5-477d-88d5-9e5c68a98302", "fp-rtm-intl-backgate.ilivedata.com:13315")
	g.ClientRtm = rtm.NewRTMServerClient(80000734, "09e0c78f-2868-4bbe-9474-8d322136a2db", "fp-rtm-intl-backgate.ilivedata.com:13315")
	geoDB, geoDBErr := geoip2.Open(kdmSrvConfig.KdmsrvConf.IPCountryPath)
	if geoDBErr != nil {
		logging.Error(ctx, geoDBErr)
	}
	g.GeoDB = geoDB
	Test(ctx)
	select {
	case s := <-chSignal:
		logging.Infof(ctx, "signal received: %v", s)
		signal.Reset(syscall.SIGINT, syscall.SIGTERM)
	case err := <-chErr:
		logging.Error(ctx, err)
	case msg := <-misc.ShutdownChan: // 接到shutdown，退出
		logging.Warn(ctx, "shutdown received: ", msg)
	}

	// shutdown
	misc.DiscardFurtherErrors(chErr)
	shutdown(ctx, &wg)
	log.Print("THE END")
}

func startIoc(ctx context.Context, kingdomId int32, onStart func(ctx2 context.Context)) {
	config := srvconf.GetKdmSrvConfigFromContext(ctx)
	callback.SetupCallback(config.KdmsrvConf.GameServerURL, config.KdmsrvConf.BackupGameServerURL, kingdomId)
	session.SetTTL(time.Duration(config.KdmsrvConf.SessionTokenExpireInSecs) * time.Second)

	ioc.AddStartHandler(ctx, func(ctx context.Context) {
		onStart(ctx)
		callback.Enable()
	})
	ioc.Start(ctx)
}

func shutdown(ctx context.Context, wg *sync.WaitGroup) {
	gKingdomServer.ShutDown(ctx)
	//leaderboard.Stop(ctx) //TODO
	ioc.Stop(ctx)
	orm.Stop(ctx)
	//stargate.Stop(ctx)
	//vtime.Stop() TODO
	wg.Wait()
}

func setUpCluster(ctx context.Context, kingdomId int32, addr string, etcdAddrs []string) {
	etcdClient, err := clientv3.New(clientv3.Config{Endpoints: etcdAddrs})
	if err != nil {
		panic(err)
	}
	config := srvconf.GetKdmSrvConfigFromContext(ctx)
	strs := strings.Split(addr, ":")
	if len(strs) != 2 {
		panic(fmt.Errorf("grpc addr invalid, addr: %s", addr))
	}
	srvPort := strs[1]
	err = cluster.Setup(ctx, etcdClient, config.KdmsrvConf.EtcdClusterDir, kingdomId, misc.MyInstanceInfo(srvPort).Endpoint)
	if err != nil {
		panic(err)
	}
}
