// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type HeroLotterySSR struct {
	Hero    []int32                      `json:"Hero"`   // 英雄
	HeroRef []*HeroLotteryRandomTableCfg `json:"-"`      // 英雄
	Weight  []int32                      `json:"Weight"` // 权重
}

func NewHeroLotterySSR() *HeroLotterySSR {
	return &HeroLotterySSR{
		Hero:    []int32{},
		HeroRef: []*HeroLotteryRandomTableCfg{},
		Weight:  []int32{},
	}
}

func NewMockHeroLotterySSR() *HeroLotterySSR {
	return &HeroLotterySSR{
		Hero:    []int32{0},
		HeroRef: []*HeroLotteryRandomTableCfg{},
		Weight:  []int32{0},
	}
}

func (s *HeroLotterySSR) bindRefs(c *Configs) {
	for _, e := range s.Hero {
		cfgoRefRecord := c.HeroLotteryRandomTable.Get(e)
		s.HeroRef = append(s.<PERSON>Ref, cfgoRefRecord)
	}
}
