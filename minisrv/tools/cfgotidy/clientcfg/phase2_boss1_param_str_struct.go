// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

type Phase2Boss1ParamStr struct {
	Target      MissileTarget        `json:"Target"`      // 目标
	DmgRatio    float32              `json:"DmgRatio"`    // 伤害系数
	IsParabola  bool                 `json:"IsParabola"`  // 是否抛物线
	FlySpeed    float32              `json:"FlySpeed"`    // 飞行速度米/s
	Model       int32                `json:"Model"`       // 导弹模型
	ModelRef    *BattleModelTableCfg `json:"-"`           // 导弹模型
	SEAffected  string               `json:"SEAffected"`  // 受击特效
	SESelfLeft  string               `json:"SESelfLeft"`  // 枪口特效左
	SESelfRight string               `json:"SESelfRight"` // 枪口特效右
}

func NewPhase2Boss1ParamStr() *Phase2Boss1ParamStr {
	return &Phase2Boss1ParamStr{
		Target:      MissileTarget(enumDefaultValue),
		DmgRatio:    0.0,
		IsParabola:  false,
		FlySpeed:    0.0,
		Model:       0,
		ModelRef:    nil,
		SEAffected:  "",
		SESelfLeft:  "",
		SESelfRight: "",
	}
}

func NewMockPhase2Boss1ParamStr() *Phase2Boss1ParamStr {
	return &Phase2Boss1ParamStr{
		Target:      MissileTarget(enumDefaultValue),
		DmgRatio:    0.0,
		IsParabola:  false,
		FlySpeed:    0.0,
		Model:       0,
		ModelRef:    nil,
		SEAffected:  "",
		SESelfLeft:  "",
		SESelfRight: "",
	}
}

func (s *Phase2Boss1ParamStr) bindRefs(c *Configs) {
	s.ModelRef = c.BattleModelTable.Get(s.Model)
}
