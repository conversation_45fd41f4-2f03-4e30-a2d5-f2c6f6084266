package lord

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/timestamp"
	"sort"
	"strconv"
	"time"
)

// 获取对应的 random group 表
func GetHeroGemRandomTableByGroup(ctx context.Context, uid int64, groupId int32) *servercfg.LordGemRandomRewardGroupTableCfg {
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	if stageModel == nil {
		return nil
	}
	unlockStageId := stageModel.UnlockStageId()
	lines := cfg_mgr.Cfg.LordGemRandomRewardGroupTable.FilterSlice(func(v *servercfg.LordGemRandomRewardGroupTableCfg) bool {
		return v.LordGemRandomGroup == groupId
	})
	finishStatus := stageModel.StageFinishStatus()
	sort.Slice(lines, func(i, j int) bool {
		return lines[i].Id < lines[j].Id
	})
	curStatus := finishStatus[unlockStageId-1]
	if curStatus == nil {
		return lines[0]
	}

	for _, v := range lines {
		if v.MinLevel != 0 && v.MinLevel > curStatus.StageId {
			continue
		}
		if v.MaxLevel != 0 && v.MaxLevel < curStatus.StageId {
			continue
		}
		return v
	}
	return nil
}

// LordGemRandom  宝石随机
func LordGemRandom(ctx context.Context, uid int64, groupId int32, isFree bool) (*map[int32]int32, error) {
	gemRandomModel, err := model.GetLordGemRandomModel(ctx, uid, groupId)
	retReward := map[int32]int32{}
	if err != nil {
		return &retReward, err
	}
	groupLine := cfg_mgr.Cfg.LordGemRandomGroupTable.Get(groupId)
	if isFree {
		if gemRandomModel.TodayFreeRandomTimes()+1 > groupLine.DailyFreeTimesLimit {
			return &retReward, kdmerr.SysInvalidArguments.CastErrorf("free times more than")
		}
		if time.Now().Unix()-gemRandomModel.LastFreeRandomTime().Time().Unix() < int64(groupLine.FreeCD) {
			return &retReward, kdmerr.SysInvalidArguments.CastErrorf("not in free time ")
		}
		gemRandomModel.SetTodayFreeRandomTimes(ctx, gemRandomModel.TodayFreeRandomTimes()+1)
		gemRandomModel.SetLastFreeRandomTime(ctx, timestamp.NewUTCSeconds(time.Now()))
	} else {
		sequence := util.GenerateSequenceString()
		e := consumable.DeductItem(ctx, uid, groupLine.SingleDrawCostType, int64(groupLine.SingleDrawCostValue), bi.ItemFlowReason_Random_Lord_Gem, strconv.Itoa(int(groupId)), sequence)
		if e != nil {
			//消耗钻石
			e = consumable.DeductItem(ctx, uid, constDef.ITEM_DIAMOND, int64(groupLine.SingleDrawCostDiamdondCnt), bi.ItemFlowReason_Random_Lord_Gem, strconv.Itoa(int(groupId)), sequence)
			if e != nil {
				return &retReward, e
			}
		}
	}
	gemRandomModel.SetMustTimes(ctx, gemRandomModel.MustTimes()+1)

	//检测必中
	randomMustTables := cfg_mgr.Cfg.LordGemRandomGroupMustTable.FilterSlice(func(v *servercfg.LordGemRandomGroupMustTableCfg) bool {
		return v.LordGemRandomGroup == groupLine.Id
	})
	if randomMustTables == nil {
		return &retReward, kdmerr.SysInvalidArguments.CastErrorf("conf is error")
	}

	isMust := false
	if randomMustTables[0].MustCnt <= gemRandomModel.MustTimes() {
		isMust = true
		gemRandomModel.SetMustTimes(ctx, 0)
	}
	qualityId := randomMustTables[0].GemQualityType
	if !isMust {
		//获取 random group line
		randomGroupLine := GetHeroGemRandomTableByGroup(ctx, uid, groupId)
		if randomGroupLine == nil {
			return &retReward, kdmerr.SysInvalidArguments.CastErrorf("conf is error")
		}
		gemRandomLines := cfg_mgr.Cfg.LordGemRandomGroupChanceTable.Filter(func(v *servercfg.LordGemRandomGroupChanceTableCfg) bool {
			return v.LordGemRandomRewardGroup == randomGroupLine.Id
		})

		radomIdWeight := map[int32]int32{}
		for _, v := range gemRandomLines {
			radomIdWeight[v.Id] += v.Weight
		}
		if radomIdWeight == nil {
			return &retReward, kdmerr.SysInvalidArguments.CastErrorf("conf is error")
		}
		randomId := util.RandomByWeight(radomIdWeight)
		curGemRandomLine := cfg_mgr.Cfg.LordGemRandomGroupChanceTable.Get(randomId)
		if qualityId == curGemRandomLine.GemQualityType {
			if randomMustTables[0].IsRefresh {
				gemRandomModel.SetMustTimes(ctx, 0)
			}
		}
		qualityId = curGemRandomLine.GemQualityType

	}
	//随机部位
	equipTypeIdWeight := map[int32]int32{}
	equipTypeLines := cfg_mgr.Cfg.LordEquipTypeTable.GetAll()
	for _, v := range equipTypeLines {
		equipTypeIdWeight[v.Id] += v.Weight
	}
	equipTypeId := util.RandomByWeight(equipTypeIdWeight)
	gemLines := cfg_mgr.Cfg.LordGemTable.FilterSlice(func(v *servercfg.LordGemTableCfg) bool {
		return v.LordEquipType == equipTypeId && v.GemQualityType == qualityId
	})
	if gemLines == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("conf is error")
	}
	gemIdWeights := map[int32]int32{}
	for _, v := range gemLines {
		gemIdWeights[v.Id] += v.Weight
	}
	gemId := util.RandomByWeight(gemIdWeights)
	model.AddLordGem(ctx, uid, gemId, 1, bi.ItemFlowReason_Random_Lord_Gem, strconv.Itoa(int(groupId)))
	retReward[gemId] += 1
	trace := map[string]string{
		"type": strconv.Itoa(int(groupId)),
	}
	quest.Notify(ctx, uid, servercfg.TaskType_GemSummon, trace, 1)
	if groupId == 1 {
		model.SetTaskCounter(ctx, uid, int32(servercfg.TaskType_TotalGemSummon_1), "", 1)
	} else if groupId == 2 {
		model.SetTaskCounter(ctx, uid, int32(servercfg.TaskType_TotalGemSummon_2), "", 1)
	}
	model.SetTaskCounter(ctx, uid, int32(servercfg.TaskType_TotalGemSummon), "", 1)
	quest.Notify(ctx, uid, servercfg.TaskType_TotalGemSummon, trace, 1)
	quest.Notify(ctx, uid, servercfg.TaskType_TotalGemSummon_1, trace, 1)
	quest.Notify(ctx, uid, servercfg.TaskType_TotalGemSummon_2, trace, 1)
	return &retReward, nil
}
