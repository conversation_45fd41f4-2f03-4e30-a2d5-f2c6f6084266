// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"text/template"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t DailyTasksTable) saveJson(dir string) error {
	return t.SaveJsonWithSuffix(dir, "", t.Filter<PERSON>(func(v *DailyTasksTableCfg) bool {
		return true
	}))
}

func (t DailyTasksTable) SaveJsonWithSuffix(dir string, suffix string, cfgs []*DailyTasksTableCfg) error {
	jsonPath := filepath.Join(dir, "DailyTasksTable"+suffix+".json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[DailyTasksTable]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	// sort
	s := cfgoDailyTasksTableSlice{}
	for _, cfgoCfg := range cfgs {
		if _, ok := t.localIds[cfgoCfg.Id]; !ok {
			continue
		}
		s = append(s, cfgoCfg)
	}
	sort.Sort(s)

	w := bufio.NewWriter(f)
	w.WriteString("{\n")
	w.WriteString("\t\"Cells\": ")
	bytes, err := json.MarshalIndent(s, "\t", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	w.WriteString("\n}\n")

	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

type cfgoDailyTasksTableSlice []*DailyTasksTableCfg

func (x cfgoDailyTasksTableSlice) Len() int           { return len(x) }
func (x cfgoDailyTasksTableSlice) Less(i, j int) bool { return x[i].Id < x[j].Id }
func (x cfgoDailyTasksTableSlice) Swap(i, j int)      { x[i], x[j] = x[j], x[i] }

func (t *DailyTasksTable) LoadJsonByPath(jsonPath string, configs *Configs) error {
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[DailyTasksTable]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*DailyTasksTableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[DailyTasksTable]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		if err = t.Put(k, v); err != nil {
			return fmt.Errorf("[DailyTasksTable]put record failed, path=%s, err=[%w]", jsonPath, err)
		}
	}
	if err = t.setupIndexes(); err != nil {
		return fmt.Errorf("[DailyTasksTable]setup indexes failed, path=%s, err=[%w]", jsonPath, err)
	}
	return nil
}

func (t DailyTasksTable) saveMeta(dir string) error {
	return t.SaveMetaWithSuffix(dir, "")
}

func (t DailyTasksTable) SaveMetaWithSuffix(dir string, suffix string) error {
	fbPlus := false
	td := &TableData{}
	if err := json.Unmarshal(string2Bytes(DailyTasksTableJsonContent), td); err != nil {
		return fmt.Errorf("[client]unmarshal json failed, err=[%w]", err)
	}
	if len(suffix) > 0 {
		td.FileName = td.FileName + suffix
		td.TableName = td.TableName + suffix
	}
	path := filepath.Join(dir, td.FileName+".fbs")
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	defer f.Close()

	tplStr := FlatBufferTableTpl
	if fbPlus {
		tplStr = FlatBufferPlusTableTpl
	}
	tpl, err := template.New("table_schema").Parse(tplStr)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}

	if err = tpl.Execute(f, td); err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	return nil
}

var DailyTasksTableJsonContent string = `{
		"FileName": "DailyTasksTable",
		"ConstTable": false,
		"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
		"TableName": "DailyTasksTable",
		"Key": {
			"FieldName": "Id",
			"FieldType": "int32"
		},
		"Fields": [
			{
				"FieldName": "ReqFunction",
				"FieldType": "int32"
			},
			{
				"FieldName": "TaskType",
				"FieldType": "TaskType = Login"
			},
			{
				"FieldName": "TaskCounterType",
				"FieldType": "TaskCounterType = Reset"
			},
			{
				"FieldName": "Formula",
				"FieldType": "string"
			},
			{
				"FieldName": "Value",
				"FieldType": "int32"
			},
			{
				"FieldName": "GoTo",
				"FieldType": "int32"
			},
			{
				"FieldName": "Desc",
				"FieldType": "string"
			},
			{
				"FieldName": "Param",
				"FieldType": "string"
			},
			{
				"FieldName": "RewardType",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "RewardValue",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "Score",
				"FieldType": "int32"
			}
		]
	}`
