// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type IapDungeonFundTableCfg struct {
	Id              int32               `json:"Id"`           // Id
	StringId        string              `json:"StringId"`     // StringId
	Unlock          int32               `json:"Unlock"`       // 前置功能
	UnlockRef       *FunctionTableCfg   `json:"-"`            // 前置功能
	Type            DungeonType         `json:"Type"`         // 副本类型
	Stage           int32               `json:"Stage"`        // 阶段
	IapPackageId    int32               `json:"IapPackageId"` // 内购商品id
	IapPackageIdRef *IapPackageTableCfg `json:"-"`            // 内购商品id
	Limit           PurchaseLimitType   `json:"Limit"`        // 限购类型
	Times           int32               `json:"Times"`        // 限购次数
}

func NewIapDungeonFundTableCfg() *IapDungeonFundTableCfg {
	return &IapDungeonFundTableCfg{
		Id:              0,
		StringId:        "",
		Unlock:          0,
		UnlockRef:       nil,
		Type:            DungeonType(enumDefaultValue),
		Stage:           0,
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
	}
}

func NewMockIapDungeonFundTableCfg() *IapDungeonFundTableCfg {
	return &IapDungeonFundTableCfg{
		Id:              0,
		StringId:        "",
		Unlock:          0,
		UnlockRef:       nil,
		Type:            DungeonType(enumDefaultValue),
		Stage:           0,
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
	}
}

type IapDungeonFundTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*IapDungeonFundTableCfg
	localIds         map[int32]struct{}
}

func NewIapDungeonFundTable(configs *Configs) *IapDungeonFundTable {
	return &IapDungeonFundTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*IapDungeonFundTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *IapDungeonFundTable) Get(key int32) *IapDungeonFundTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapDungeonFundTable) GetAll() map[int32]*IapDungeonFundTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapDungeonFundTable) put(key int32, value *IapDungeonFundTableCfg, local bool) *IapDungeonFundTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *IapDungeonFundTable) putFromInheritedTable(key int32, value *IapDungeonFundTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[IapDungeonFundTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *IapDungeonFundTable) Put(key int32, value *IapDungeonFundTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[IapDungeonFundTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *IapDungeonFundTable) PutAll(m map[int32]*IapDungeonFundTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapDungeonFundTable) Range(f func(v *IapDungeonFundTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapDungeonFundTable) Filter(filterFuncs ...func(v *IapDungeonFundTableCfg) bool) map[int32]*IapDungeonFundTableCfg {
	filtered := map[int32]*IapDungeonFundTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapDungeonFundTable) FilterSlice(filterFuncs ...func(v *IapDungeonFundTableCfg) bool) []*IapDungeonFundTableCfg {
	filtered := []*IapDungeonFundTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapDungeonFundTable) FilterKeys(filterFuncs ...func(v *IapDungeonFundTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapDungeonFundTable) satisfied(v *IapDungeonFundTableCfg, filterFuncs ...func(v *IapDungeonFundTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapDungeonFundTable) setupIndexes() error {
	return nil
}

func (t *IapDungeonFundTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapDungeonFundTableCfg) bindRefs(c *Configs) {
	r.UnlockRef = c.FunctionTable.Get(r.Unlock)
	r.IapPackageIdRef = c.IapPackageTable.Get(r.IapPackageId)
}

func (t *IapDungeonFundTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[IapDungeonFundTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewIapDungeonFundTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapDungeonFundTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[IapDungeonFundTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// Unlock
		if record[t.getIndexInCsv("Unlock")] == "" {
			recordCfg.Unlock = 0
		} else {
			var err error
			recordCfg.Unlock, err = configs.FunctionTable.getIdByRef(record[t.getIndexInCsv("Unlock")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [IapDungeonFundTable]unmarshal csv record failed, varName=Unlock, type=ref@FunctionTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Unlock")], err)
				} else {
					return fmt.Errorf("[IapDungeonFundTable]unmarshal csv record failed, varName=Unlock, type=ref@FunctionTable, value=%s, err:[%s]", record[t.getIndexInCsv("Unlock")], err)
				}
			}
		}
		// Type
		{
			if record[t.getIndexInCsv("Type")] == "" {
				recordCfg.Type = DungeonType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseDungeonType(record[t.getIndexInCsv("Type")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapDungeonFundTable]unmarshal csv record failed, varName=Type, type=enum@DungeonType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Type")], err)
					} else {
						return fmt.Errorf("[IapDungeonFundTable]unmarshal csv record failed, varName=Type, type=enum@DungeonType, value=%s, err:[%s]", record[t.getIndexInCsv("Type")], err)
					}
				}
				recordCfg.Type = cfgoEnum
			}
		}
		// Stage
		{
			if record[t.getIndexInCsv("Stage")] == "" {
				recordCfg.Stage = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Stage")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapDungeonFundTable]unmarshal csv record failed, varName=Stage, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Stage")], err)
					} else {
						return fmt.Errorf("[IapDungeonFundTable]unmarshal csv record failed, varName=Stage, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Stage")], err)
					}
				}
				recordCfg.Stage = int32(cfgoInt)
			}
		}
		// IapPackageId
		if record[t.getIndexInCsv("IapPackageId")] == "" {
			recordCfg.IapPackageId = 0
		} else {
			var err error
			recordCfg.IapPackageId, err = configs.IapPackageTable.getIdByRef(record[t.getIndexInCsv("IapPackageId")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [IapDungeonFundTable]unmarshal csv record failed, varName=IapPackageId, type=ref@IapPackageTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("IapPackageId")], err)
				} else {
					return fmt.Errorf("[IapDungeonFundTable]unmarshal csv record failed, varName=IapPackageId, type=ref@IapPackageTable, value=%s, err:[%s]", record[t.getIndexInCsv("IapPackageId")], err)
				}
			}
		}
		// Limit
		{
			if record[t.getIndexInCsv("Limit")] == "" {
				recordCfg.Limit = PurchaseLimitType(enumDefaultValue)
			} else {
				cfgoEnum, err := parsePurchaseLimitType(record[t.getIndexInCsv("Limit")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapDungeonFundTable]unmarshal csv record failed, varName=Limit, type=enum@PurchaseLimitType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Limit")], err)
					} else {
						return fmt.Errorf("[IapDungeonFundTable]unmarshal csv record failed, varName=Limit, type=enum@PurchaseLimitType, value=%s, err:[%s]", record[t.getIndexInCsv("Limit")], err)
					}
				}
				recordCfg.Limit = cfgoEnum
			}
		}
		// Times
		{
			if record[t.getIndexInCsv("Times")] == "" {
				recordCfg.Times = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Times")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapDungeonFundTable]unmarshal csv record failed, varName=Times, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Times")], err)
					} else {
						return fmt.Errorf("[IapDungeonFundTable]unmarshal csv record failed, varName=Times, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Times")], err)
					}
				}
				recordCfg.Times = int32(cfgoInt)
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [IapDungeonFundTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[IapDungeonFundTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *IapDungeonFundTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "IapDungeonFundTable.csv") && (!strings.HasPrefix(fileName, "IapDungeonFundTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for IapDungeonFundTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[IapDungeonFundTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[IapDungeonFundTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[IapDungeonFundTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[IapDungeonFundTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[IapDungeonFundTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[IapDungeonFundTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[IapDungeonFundTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[IapDungeonFundTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [IapDungeonFundTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *IapDungeonFundTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[IapDungeonFundTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [IapDungeonFundTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *IapDungeonFundTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[IapDungeonFundTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *IapDungeonFundTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[IapDungeonFundTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[IapDungeonFundTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
