// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type HeroLevelTableCfg struct {
	Id        int32           `json:"Id"`        // Id
	PlanID    HeroLevelUpPlan `json:"PlanID"`    // 升级方案
	ReqDesc   string          `json:"ReqDesc"`   // 前置描述
	HeroLevel int32           `json:"HeroLevel"` // 英雄等级
	IsMax     bool            `json:"IsMax"`     // 是否最大等级
	LevelUp   []*CostKVS      `json:"LevelUp"`   // 升级消耗
	Power     int32           `json:"Power"`     // 戰力
	Attr      *AttrStr        `json:"Attr"`      // 属性
}

func NewHeroLevelTableCfg() *HeroLevelTableCfg {
	return &HeroLevelTableCfg{
		Id:        0,
		PlanID:    HeroLevelUpPlan(enumDefaultValue),
		ReqDesc:   "",
		HeroLevel: 0,
		IsMax:     false,
		LevelUp:   []*CostKVS{},
		Power:     0,
		Attr:      NewAttrStr(),
	}
}

type HeroLevelTable struct {
	records  map[int32]*HeroLevelTableCfg
	localIds map[int32]struct{}
}

func NewHeroLevelTable() *HeroLevelTable {
	return &HeroLevelTable{
		records:  map[int32]*HeroLevelTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *HeroLevelTable) Get(key int32) *HeroLevelTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *HeroLevelTable) GetAll() map[int32]*HeroLevelTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *HeroLevelTable) put(key int32, value *HeroLevelTableCfg, local bool) *HeroLevelTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *HeroLevelTable) Range(f func(v *HeroLevelTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *HeroLevelTable) Filter(filterFuncs ...func(v *HeroLevelTableCfg) bool) map[int32]*HeroLevelTableCfg {
	filtered := map[int32]*HeroLevelTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *HeroLevelTable) FilterSlice(filterFuncs ...func(v *HeroLevelTableCfg) bool) []*HeroLevelTableCfg {
	filtered := []*HeroLevelTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *HeroLevelTable) FilterKeys(filterFuncs ...func(v *HeroLevelTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *HeroLevelTable) satisfied(v *HeroLevelTableCfg, filterFuncs ...func(v *HeroLevelTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *HeroLevelTable) setupIndexes() error {
	return nil
}

func (t *HeroLevelTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *HeroLevelTableCfg) bindRefs(c *Configs) {
	for _, e := range r.LevelUp {
		e.bindRefs(c)
	}
	r.Attr.bindRefs(c)
}
