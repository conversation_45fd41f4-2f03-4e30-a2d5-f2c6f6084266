package rank

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"fmt"
	"github.com/go-redis/redis"
	"strconv"
	"time"
)

type PlayerPower struct {
	Uid   int64 `json:"uid"`
	Power int64 `json:"levelId"`
}

func SetPowerScore(uid int64, power int64) error {
	TimeScore := fmt.Sprintf("%09d", RANK_TIME_END-time.Now().Second())
	scoreStr := strconv.Itoa(int(power)) + "." + TimeScore
	score, err := strconv.ParseFloat(scoreStr, 64)
	if err != nil {
		return err
	}

	rdb := GetRankClient()
	currentScore, err := rdb.ZScore(RANK_USER_POWER, strconv.Itoa(int(uid))).Result()
	if err != nil && err != redis.Nil {
		panic(err) // 处理错误
	}
	if err == redis.Nil || score > currentScore {
		rdb.ZAdd(RANK_USER_POWER, redis.Z{
			Score:  score,
			Member: strconv.Itoa(int(uid)),
		})
	}
	return nil
}

func GetPowerRank(uid int64, num int64) ([]PlayerPower, error) {
	rdb := GetRankClient()
	val, err := rdb.ZRevRangeWithScores(RANK_USER_POWER, 0, num).Result() // 获取所有用户的排行，分数从高到低
	if err != nil {
		return nil, err
	}
	retInfos := []PlayerPower{}
	for _, z := range val {
		uidStr := z.Member.(string)
		power := int64(z.Score)
		var playerLevelStruct PlayerPower
		playerLevelStruct.Uid, err = strconv.ParseInt(uidStr, 10, 64)
		playerLevelStruct.Power = power

		if err == nil {
			retInfos = append(retInfos, playerLevelStruct)
		}

	}
	return retInfos, nil
}

func GetPowerRankInfoList(ctx context.Context, uid int64, num int64) []*wrpc.PowerRankInfo {
	rankInfos, err := GetPowerRank(uid, num)
	if err != nil {
		return nil
	}
	retRankList := []*wrpc.PowerRankInfo{}
	for i, rankInfo := range rankInfos {
		rankUser, _ := model.GetUserModel(ctx, rankInfo.Uid)
		unlock := model.LockUser(ctx, rankInfo.Uid)
		curLevelInfo := &wrpc.PowerRankInfo{
			Uid:   rankInfo.Uid,
			Rank:  int32(i + 1),
			Power: rankInfo.Power,
			Icon:  rankUser.AvatarConfigId(),
			Name:  rankUser.Name(),
		}
		unlock()
		retRankList = append(retRankList, curLevelInfo)
	}
	return retRankList
}
