package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/dungeon"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 设置副本英雄上阵
func (_ KnightsHandlers) SetDungeonHeroBattlePosHandler(ctx *wctx.Context, req *wrpc.SetDungeonHeroBattlePosRequest) (*wrpc.SetDungeonHeroBattlePosReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		err = dungeon.SetDungeonHeroBattle(ctx, req.UID, req.DungeonType, req.PosId, req.HeroId)
		rawResp = &wrpc.SetDungeonHeroBattlePosReply{
			Result: err == nil,
		}
	})
	return rawResp.(*wrpc.SetDungeonHeroBattlePosReply), err
}
