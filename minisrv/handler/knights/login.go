package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/g"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	user2 "bitbucket.org/kingsgroup/gog-knights/minisrv/module/user"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/system"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"time"
)

func (_ KnightsHandlers) LoginHandler(ctx *wctx.Context, req *wrpc.LoginRequest) (*wrpc.LoginReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		user, isNewPlayer := user2.LoginIn(ctx, int64(system.KingdomId), req.UID, req.AccountId)
		deviceModel, _, _ := model.GetUserDevice(ctx, req.UID)
		if deviceModel.Ip() != "" {
			err = user2.CheckIP(ctx, deviceModel.Ip())
			if err != nil {
				return
			}
		}
		unlock := user.RLock()
		user.SetChatChannel(ctx, req.UID)
		ui1 := user.Snapshoot().(*minirpc.UserInfo)
		unlock()
		buildings, _ := model.ExportAllBuidings(ctx, req.UID)
		heroLottery, _ := model.ExportLotteryModel(ctx, req.UID)
		heroInfos, _ := model.ExportHeros(ctx, req.UID)
		consumables, _ := model.ExportAllConsumables(ctx, req.UID)
		userBenefits, _ := model.ExportAllUserBenefits(ctx, req.UID)
		jobs, _ := model.ExportAllJobs(ctx, req.UID)
		troops, _ := model.ExportAllTroops(ctx, req.UID)
		researches, _ := model.ExportAllResearches(ctx, req.UID)
		mainTasks, _ := model.ExportAllMainTasks(ctx, req.UID)
		mapEvent, _ := model.GetMapEvent(ctx, req.UID)
		heroSkills, _ := model.ExportHeroSkills(ctx, req.UID)
		dave, _ := model.GetDave(ctx, req.UID)
		userDatas, _ := model.ExportUserData(ctx, req.UID)
		mainLineStage, _ := model.GetMainLineStage(ctx, req.UID)
		rtmTokenString, _ := g.ClientRtm.GetToken(req.UID)
		iapBuytimes, _ := model.GetUserIapBuyTimes(ctx, req.UID)
		lordEquip, _ := model.ExportLordEquips(ctx, req.UID)
		lordGem, _ := model.ExportLordGems(ctx, req.UID)
		lordGemRandom, _ := model.ExportLordGemRandomModel(ctx, req.UID)
		dungeon := model.ExportDungeon(ctx, req.UID)
		functionOpen, _ := model.GetFunctionOpenModel(ctx, req.UID)
		newbieGuide, _ := model.GetNewBieGuildModel(ctx, req.UID)
		activityTasks, _ := model.ExportAllActivityTasks(ctx, req.UID)
		firstCharge, _ := model.ExportAllFirstCharge(ctx, req.UID)
		growthFund, _ := model.ExportAllGrowthFund(ctx, req.UID)
		monthCard, _ := model.ExportAllMonthCard(ctx, req.UID)
		regularPack, _ := model.ExportRegularPackModel(ctx, req.UID)
		achievementTask, _ := model.ExportAllAchievementTaskModels(ctx, req.UID)
		var test1 = wrpc.UserData{
			Ui1:             ui1,
			Building:        buildings,
			HeroLottery:     heroLottery,
			Hero:            heroInfos,
			Consumables:     consumables,
			UserBenefits:    userBenefits,
			UserJob:         jobs,
			UserTroop:       troops,
			Research:        researches,
			MainTask:        mainTasks,
			MapEvent:        mapEvent.Snapshoot().(*minirpc.MapEvent),
			HeroSkill:       heroSkills,
			Dave:            dave.Snapshoot().(*minirpc.Dave),
			UserData:        userDatas,
			MainLineStage:   mainLineStage.Snapshoot().(*minirpc.MainLineStage),
			CurServerTime:   time.Now().Unix(),
			IsNewPlayer:     isNewPlayer,
			RtmToken:        rtmTokenString,
			UserIapBuyLog:   iapBuytimes.Snapshoot().(*minirpc.UserIapBuyTimes),
			LordEquip:       lordEquip,
			LordGem:         lordGem,
			LordGemRandom:   lordGemRandom,
			Dungeon:         dungeon,
			FunctionOpen:    functionOpen.Snapshoot().(*minirpc.FunctionOpen),
			NewbieGuide:     newbieGuide.Snapshoot().(*minirpc.NewbieGuide),
			ActivityTask:    activityTasks,
			FirstCharge:     firstCharge,
			GrowthFund:      growthFund,
			MonthCard:       monthCard,
			RegularPack:     regularPack,
			AchievementTask: achievementTask,
		}

		replay := wrpc.LoginReply{
			Result: &test1,
		}
		rawResp = &replay
		err = nil
	})
	return rawResp.(*wrpc.LoginReply), err
}
