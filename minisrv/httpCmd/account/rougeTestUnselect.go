package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/main_line_stage"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"math/rand"
	"net/http"
	"sort"
	"strconv"
	"time"
)

func init() {
	http_post.AddPostHandler("rougeTestUnselect", rougeTestUnselect)
}
func rougeTestUnselect(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid")     // 获取查询参数q的值
		countStr := values.Get("count") // 获取查询参数q的值
		if uidStr == "" {
			fmt.Fprintf(w, "need uid")
			return
		}
		if countStr == "" {
			fmt.Fprintf(w, "need countStr")
			return
		}
		uid, _ := strconv.ParseInt(uidStr, 10, 64)
		count, _ := strconv.ParseInt(countStr, 10, 64)
		unlock := model.LockUser(ctx, uid)
		defer unlock()
		//heroId, _ := strconv.ParseInt(heroIdStr, 10, 32)
		model.AddItem(ctx, uid, constDef.ITEM_ENERGY_BASE, 5, bi.ItemFlowReason_START_STAGE, strconv.Itoa(int(2)), "")
		main_line_stage.StartMainStage(ctx, uid, 2, false)
		rand.Seed(time.Now().UnixNano())
		stage, _ := model.GetMainLineStage(ctx, uid)
		for i := 0; i < int(count); i++ {
			selectIds := main_line_stage.RefreshRougeByKillMonster(ctx, uid, w)
			if selectIds == nil || len(selectIds) == 0 {
				break
			}
			main_line_stage.SelectEliteRougeSkill(ctx, uid)
			fmt.Fprintf(w, "技能刷新")
			fmt.Fprintf(w, "%v\n", selectIds)
			curSelectId := stage.CurSelectCardIds()
			var testCardIds []int32
			for _, v := range curSelectId {
				testCardIds = append(testCardIds, v)
			}
			heroIds := []int32{}
			for _, v := range curSelectId {
				line := cfg_mgr.Cfg.RougeTabTable.Get(v)
				if line != nil && line.RougeTabType == servercfg.RougeTabType_ConfigTab {
					rougeTabGroupLine := cfg_mgr.Cfg.RougeTabGroupTable.Get(line.RougeTabGroup)
					if rougeTabGroupLine != nil {
						heroIds = append(heroIds, rougeTabGroupLine.Hero)

					}
				}
			}

			cardIdCounts := map[int32]int32{}
			for _, v := range heroIds {
				//添加填进去的卡
				heroModel, _ := model.GetHero(ctx, uid, v)
				if heroModel != nil {
					geneLine := cfg_mgr.Cfg.HeroGeneTable.Get(heroModel.GeneConfigId())
					if geneLine != nil && geneLine.PVEPassiveSkillEffect != nil {
						for _, j := range geneLine.PVEPassiveSkillEffect {
							heroSkillEffectLine := cfg_mgr.Cfg.HeroSkillEffectTable.Get(j)
							if heroSkillEffectLine != nil {
								if heroSkillEffectLine.SkillEffectType == servercfg.HeroSkillEffectType_GetTab { //直接放入
									if heroSkillEffectLine.RougeTab != 0 {
										cardIdCounts[heroSkillEffectLine.RougeTab]++
										testCardIds = append(testCardIds, heroSkillEffectLine.RougeTab)
									}
								}
							}
						}
					}
				}

			}

			sort.Slice(testCardIds, func(i, j int) bool {
				return testCardIds[i] < testCardIds[j]
			})
			fmt.Fprintf(w, "当前所有技能")
			fmt.Fprintf(w, "%v\n", testCardIds)
			fmt.Fprintf(w, "本次完成------------------------------------------------\n")
		}

	})

}
