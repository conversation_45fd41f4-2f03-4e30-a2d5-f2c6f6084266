package alliance

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/g"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/misc/push"
	"context"
	"crypto/rand"
	"encoding/json"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/ds"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"go.mongodb.org/mongo-driver/bson"
	"math/big"
	"sort"
	"strconv"
	"time"
)

func genAcronym(ctx context.Context) string {
	for t := 0; t < 10; t++ {
		letters := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
		result := make([]byte, 3)
		for i := 0; i < 3; i++ {
			n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(letters))))
			result[i] = letters[n.Int64()]
		}
		filter := bson.D{{"acronym", string(result)}}
		alliance, _ := orm.Find[*model.Alliance](ctx, filter)
		if alliance == nil {
			return string(result)
		}
	}
	return ""
}

// CreateAlliance 创建联盟
func CreateAlliance(ctx context.Context, uid int64, recruitSetting int32, name string, flatBase int32, flagEmblem int32, isFree bool) (*wrpc.AllianceInfoRet, error) {
	userInfo, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userInfo)

	// 计算消耗
	cost := make(map[string]int64)
	if isFree {
		if userInfo.FreeCreateAllianceTimes()+1 > int32(cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_GUILD_BUILD_FREE_CNT)) {
			return nil, kdmerr.SysInvalidArguments.CastErrorf("free time is more than config")
		}
	} else {
		amount := cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_GUILD_BUILD_DIAMOND_CNT)
		err := consumable.DeductItem(ctx, uid, constDef.ITEM_DIAMOND, amount, bi.ItemFlowReason_Alliance_Create, "", util.GenerateSequenceString())
		if err != nil {
			return nil, err
		}
		cost[strconv.FormatInt(int64(constDef.ITEM_DIAMOND), 10)] = amount
	}

	userInfo.SetFreeCreateAllianceTimes(ctx, userInfo.FreeCreateAllianceTimes()+1)
	orm.Unlock(userInfo)

	acronym := genAcronym(ctx)
	alliance, _ := model.CreateAllianceModel(ctx, uid, recruitSetting, name, flatBase, flagEmblem, acronym)
	allianceMember, _ := model.JoinAlliance(ctx, uid, alliance.AllianceId(), constDef.Guild_rank_5)
	updateAllianceData(ctx, alliance.AllianceId())
	allianceMemberInfo, _ := getAllianceMemberInfo(ctx, uid, allianceMember.Step())

	// 打点
	costJsonData, _ := json.Marshal(cost)
	logger.LogCustom(ctx, uid, "alliance", logger.NewDetail().
		Put("action", "found").
		Put("uid", strconv.FormatInt(uid, 10)).
		Put("alliance_id", strconv.FormatInt(alliance.AllianceId(), 10)).
		Put("alliance_name", name).
		Put("acronym", acronym).
		Put("cost", string(costJsonData)).
		Put("join_config", recruitSetting+1)) // 转换为1、2的配置

	return &wrpc.AllianceInfoRet{
		Alliance:           alliance.Snapshoot().(*minirpc.Alliance),
		AllianceMembers:    []*minirpc.AllianceMember{allianceMember.Snapshoot().(*minirpc.AllianceMember)},
		AllianceMemberInfo: []*wrpc.AllianceMemberInfo{allianceMemberInfo},
	}, nil
}
func GenAllianceQuest(ctx context.Context, uid int64) error {
	allianceTaskLines := cfg_mgr.Cfg.GuildTaskTable.GetAll()
	for _, v := range allianceTaskLines {
		quests, err := orm.Get[*model.AllianceTask](ctx, uid, v.Id)
		if err != nil {
			return kdmerr.SysDBError.WrapError(err)
		}
		if quests != nil {
			quests.SetProgress(ctx, 0)
			quests.SetStatus(ctx, int32(minirpc.QuestActivity))
			continue
		}
		orm.Create[*model.AllianceTask](ctx, &minirpc.AllianceTask{
			Uid:      uid,
			QuestId:  v.Id,
			Type:     int32(v.TaskType),
			Progress: 0,
			Status:   int32(minirpc.QuestActivity),
		})
	}
	userData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeAllianceTaskChest))
	userData.SetValue(ctx, 0)
	userData.SetContent(ctx, map[int32]int64{})
	return nil
}
func GetAllianceQuest(ctx context.Context, uid int64) ([]*model.AllianceTask, *model.UserData) {
	refreshData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeAllianceTaskRefresh))
	lastRefreshtime := refreshData.Value()
	if !util.IsSameDay(time.Unix(lastRefreshtime, 0), time.Now()) {
		GenAllianceQuest(ctx, uid)
		refreshData.SetValue(ctx, time.Now().Unix())
	}
	allianceQuests, _ := model.GetAllAllianceTaskModel(ctx, uid)
	userData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeAllianceTaskRefresh))
	return allianceQuests, userData
}

// GetAllianceInfo 获取联盟信息
func GetAllianceInfo(ctx context.Context, uid int64) *wrpc.AllianceInfoRet {
	userInfo, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userInfo)
	allianceId := userInfo.AllianceId()
	orm.Unlock(userInfo)
	if allianceId == 0 {
		return nil
	}
	alliance, _ := model.GetAllianceModel(ctx, allianceId)
	if alliance == nil {
		allianceMember := model.GetAllianceMember(ctx, uid, allianceId)
		if allianceMember != nil {
			allianceMember.Delete(ctx)
		}
		userInfo.SetAllianceId(ctx, 0)
		return nil
	}

	updateAllianceData(ctx, allianceId)
	allianceMembers := model.ExportAllAllianceMembers(ctx, allianceId)
	bossAllianceMember := model.GetAllianceMember(ctx, alliance.BossUid(), allianceId)
	bossInfo, _ := getAllianceMemberInfo(ctx, bossAllianceMember.Uid(), bossAllianceMember.Step())
	refreshAllianceShop(ctx, allianceId)
	shopInfos, _ := model.ExportAllianceShops(ctx, allianceId, uid)
	allianceQuests, userData := GetAllianceQuest(ctx, uid)
	var quests []*minirpc.AllianceTask
	for _, v := range allianceQuests {
		quests = append(quests, v.Snapshoot().(*minirpc.AllianceTask))
	}
	return &wrpc.AllianceInfoRet{
		Alliance:           alliance.Snapshoot().(*minirpc.Alliance),
		AllianceMembers:    allianceMembers,
		AllianceMemberInfo: []*wrpc.AllianceMemberInfo{bossInfo},
		AllianceShop:       shopInfos,
		AllianceTask:       quests,
		AllianceChest:      userData.Snapshoot().(*minirpc.UserData),
	}
}

// ApplyJoinAlliance JoinAlliance 加入联盟
func ApplyJoinAlliance(ctx context.Context, uid int64, allianceId int64) (*wrpc.AllianceInfoRet, error) {
	alliance, _ := model.GetAllianceModel(ctx, allianceId)
	if alliance == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("alliance id is error")
	}
	allianceLine := cfg_mgr.Cfg.GuildLevelTable.Get(alliance.Level())
	allMembers := model.ExportAllAllianceMembers(ctx, allianceId)
	if allianceLine != nil && allianceLine.Member <= int32(len(allMembers)) {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("alliance member more than limit")
	}

	if alliance.RecruitSetting() == 0 { // 不需要申请
		allianceMember, _ := model.JoinAlliance(ctx, uid, allianceId, constDef.Guild_rank_1)
		updateAllianceData(ctx, allianceId)
		userInfo, _ := model.GetUserModel(ctx, uid)
		orm.Lock(userInfo)
		userInfo.SetAllianceAppList(ctx, map[int64]int64{})
		orm.Unlock(userInfo)

		// 打点 - 直接加入联盟
		logger.LogCustom(ctx, uid, "alliance", logger.NewDetail().
			Put("action", "join").
			Put("uid", strconv.FormatInt(uid, 10)).
			Put("alliance_id", strconv.FormatInt(allianceId, 10)).
			Put("type", 1)) // 1表示直接加入

		bossAllianceMember := model.GetAllianceMember(ctx, alliance.BossUid(), allianceId)
		bossInfo, _ := getAllianceMemberInfo(ctx, bossAllianceMember.Uid(), bossAllianceMember.Step())
		refreshAllianceShop(ctx, allianceId)
		shopInfos, _ := model.ExportAllianceShops(ctx, allianceId, uid)
		allianceQuests, userData := GetAllianceQuest(ctx, uid)
		var quests []*minirpc.AllianceTask
		for _, v := range allianceQuests {
			quests = append(quests, v.Snapshoot().(*minirpc.AllianceTask))
		}
		return &wrpc.AllianceInfoRet{
			Alliance:           alliance.Snapshoot().(*minirpc.Alliance),
			AllianceMembers:    []*minirpc.AllianceMember{allianceMember.Snapshoot().(*minirpc.AllianceMember)},
			AllianceMemberInfo: []*wrpc.AllianceMemberInfo{bossInfo},
			AllianceShop:       shopInfos,
			AllianceTask:       quests,
			AllianceChest:      userData.Snapshoot().(*minirpc.UserData),
		}, nil
	} else { // 需要申请
		// 打点 - 申请加入联盟
		logger.LogCustom(ctx, uid, "alliance", logger.NewDetail().
			Put("action", "apply").
			Put("uid", strconv.FormatInt(uid, 10)).
			Put("alliance_id", strconv.FormatInt(allianceId, 10)).
			Put("type", 1)) // 1表示申请

		applyList := alliance.ApplicationList()
		applyList = append(applyList, uid)
		now := time.Now().Unix()
		userInfo, _ := model.GetUserModel(ctx, uid)
		unlock := model.LockUser(ctx, uid)
		list := userInfo.AllianceAppList()
		if list == nil {
			list = map[int64]int64{}
		}
		list[allianceId] = now
		userInfo.SetAllianceAppList(ctx, list)
		unlock()
		alliance.SetApplicationList(ctx, applyList)
		allianceMember := model.GetAllianceMember(ctx, alliance.BossUid(), allianceId)
		allianceMemberInfo, _ := getAllianceMemberInfo(ctx, alliance.BossUid(), allianceMember.Step())

		// 给管理层推送
		allianceMembers := model.ExportAllAllianceMembers(ctx, allianceId)
		for _, v := range allianceMembers {
			if checkPermission(v.Step, constDef.ManageJoinApplication) {
				pushData := push.NewNotificationWithPayload(minirpc.PushCmdType_Apply_Join_Alliance, &minirpc.UserPush{Uid: uid})
				destUserInfo, _ := model.GetUserModel(ctx, v.Uid)
				orm.Lock(destUserInfo)
				ds.SyncCustom(ctx, pushData, []ds.Observer{destUserInfo})
				orm.Unlock(destUserInfo)
			}
		}
		return &wrpc.AllianceInfoRet{
			Alliance:           alliance.Snapshoot().(*minirpc.Alliance),
			AllianceMembers:    []*minirpc.AllianceMember{allianceMember.Snapshoot().(*minirpc.AllianceMember)},
			AllianceMemberInfo: []*wrpc.AllianceMemberInfo{allianceMemberInfo},
		}, nil
	}
}

func getAllianceMemberInfo(ctx context.Context, uid int64, step int32) (*wrpc.AllianceMemberInfo, error) {
	unlock := model.LockUser(ctx, uid)
	userInfo, _ := model.GetUserModel(ctx, uid)
	stage, _ := model.GetMainLineStage(ctx, uid)
	finishTime := int32(0)
	stageFinishStatus := stage.StageFinishStatus()
	if stageFinishStatus != nil {
		curStatus := stageFinishStatus[stage.UnlockStageId()-1]
		if curStatus != nil {
			finishTime = curStatus.MaxTime
		}
	}
	memberInfo := wrpc.AllianceMemberInfo{
		Uid:            uid,
		StageId:        stage.UnlockStageId() - 1,
		Power:          userInfo.Power(),
		Name:           userInfo.Name(),
		AvatarConfigId: userInfo.AvatarConfigId(),
		StepId:         step,
		FinishTime:     finishTime,
	}
	unlock()
	return &memberInfo, nil
}

// GetAllianceMembersInfos 获取联盟成员信息
func GetAllianceMembersInfos(ctx context.Context, uid int64) ([]*wrpc.AllianceMemberInfo, error) {
	userModel, _ := model.GetUserModel(ctx, uid)
	if userModel.AllianceId() == 0 {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("alliance id is nil")
	}
	allianceId := userModel.AllianceId()
	members := model.ExportAllAllianceMembers(ctx, allianceId)
	var ret []*wrpc.AllianceMemberInfo
	for _, member := range members {
		memberInfo, _ := getAllianceMemberInfo(ctx, member.Uid, member.Step)
		ret = append(ret, memberInfo)
	}
	return ret, nil
}

// LeaveAlliance 退出联盟
func LeaveAlliance(ctx context.Context, uid int64) error {
	userInfo, _ := model.GetUserModel(ctx, uid)
	unlock := model.LockUser(ctx, uid)
	defer unlock()

	allianceId := userInfo.AllianceId()
	allianceMember := model.GetAllianceMember(ctx, uid, allianceId)
	if allianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("user not in alliance")
	}
	if !checkPermission(allianceMember.Step(), constDef.ExitGuild) {
		return kdmerr.SysInvalidArguments.CastErrorf("user have no permission")
	}

	// 打点 - 自行退出联盟
	logger.LogCustom(ctx, uid, "alliance", logger.NewDetail().
		Put("action", "leave").
		Put("alliance_id", strconv.FormatInt(allianceId, 10)).
		Put("type", 1). // 1表示自行退出
		Put("uid", strconv.FormatInt(uid, 10)).
		Put("level", strconv.FormatInt(int64(allianceMember.Step()), 10)).
		Put("target_uid", strconv.FormatInt(uid, 10)).
		Put("target_level", strconv.FormatInt(int64(allianceMember.Step()), 10)))

	g.ClientRtm.DelGroupMembers(allianceId, []int64{uid})
	allianceMember.Delete(ctx)
	userInfo.SetAllianceId(ctx, 0)
	return nil
}

// EditAllianceName 修改联盟名称
func EditAllianceName(ctx context.Context, uid int64, allianceId int64, name string, isFree bool) error {
	allianceMember := model.GetAllianceMember(ctx, uid, allianceId)
	if allianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("player not in this alliance")
	}
	if !checkPermission(allianceMember.Step(), constDef.ChangeGuildName) {
		return kdmerr.SysInvalidArguments.CastErrorf("player have not permission")
	}
	allianceModel, _ := model.GetAllianceModel(ctx, allianceId)
	if isFree && allianceModel.FreeChangeNameTimes() >= int32(cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_GUILD_NAME_CHANGE_FREE_CNT)) {
		return kdmerr.SysInvalidArguments.CastErrorf("have no free times")
	}
	if !isFree {
		amount := cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_GUILD_RENAME_DIAMOND_CNT)
		err := consumable.DeductItem(ctx, uid, constDef.ITEM_DIAMOND, amount, bi.ItemFlowReason_Alliance_ChangeName, "", util.GenerateSequenceString())
		if err != nil {
			return err
		}
	} else {
		allianceModel.SetFreeChangeNameTimes(ctx, allianceModel.FreeChangeNameTimes()+1)
	}
	allianceModel.SetName(ctx, name)

	// 打点 - 修改联盟名称
	logger.LogCustom(ctx, uid, "alliance", logger.NewDetail().
		Put("action", "member_change").
		Put("alliance_id", strconv.FormatInt(allianceId, 10)).
		Put("type", 3). // 3表示联盟改名
		Put("uid", strconv.FormatInt(uid, 10)).
		Put("level", strconv.FormatInt(int64(allianceMember.Step()), 10)).
		Put("target_uid", "null").
		Put("target_level", "null"))

	return nil
}

// EditAllianceAcronym 修改联盟简称
func EditAllianceAcronym(ctx context.Context, uid int64, allianceId int64, acronym string, isFree bool) error {
	allianceMember := model.GetAllianceMember(ctx, uid, allianceId)
	if allianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("player not in this alliance")
	}
	if !checkPermission(allianceMember.Step(), constDef.ChangeGuildShortName) {
		return kdmerr.SysInvalidArguments.CastErrorf("player have not permission")
	}
	allianceModel, _ := model.GetAllianceModel(ctx, allianceId)
	if isFree && allianceModel.FreeChangeAcronymTimes() >= int32(cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_GUILD_SHORT_NAME_CHANGE_FREE_CNT)) {
		return kdmerr.SysInvalidArguments.CastErrorf("have no free times")
	}
	if !isFree {
		amount := cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_GUILD_RESHORTNAME_DIAMOND_CNT)
		err := consumable.DeductItem(ctx, uid, constDef.ITEM_DIAMOND, amount, bi.ItemFlowReason_Alliance_ChangeName, "", util.GenerateSequenceString())
		if err != nil {
			return err
		}
	} else {
		allianceModel.SetFreeChangeAcronymTimes(ctx, allianceModel.FreeChangeAcronymTimes()+1)
	}
	allianceModel.SetAcronym(ctx, acronym)

	// 打点 - 修改联盟简称
	logger.LogCustom(ctx, uid, "alliance", logger.NewDetail().
		Put("action", "member_change").
		Put("alliance_id", strconv.FormatInt(allianceId, 10)).
		Put("type", 4). // 4表示变更简称
		Put("uid", strconv.FormatInt(uid, 10)).
		Put("level", strconv.FormatInt(int64(allianceMember.Step()), 10)).
		Put("target_uid", "null").
		Put("target_level", "null"))

	return nil
}

// EditAllianceNotice 修改联盟公告
func EditAllianceNotice(ctx context.Context, uid int64, allianceId int64, notice string) error {
	allianceMember := model.GetAllianceMember(ctx, uid, allianceId)
	if allianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("player not in this alliance")
	}
	if !checkPermission(allianceMember.Step(), constDef.EditNotice) {
		return kdmerr.SysInvalidArguments.CastErrorf("player have not permission")
	}
	allianceModel, _ := model.GetAllianceModel(ctx, allianceId)
	allianceModel.SetNotice(ctx, notice)

	// 打点 - 修改联盟公告
	logger.LogCustom(ctx, uid, "alliance", logger.NewDetail().
		Put("action", "member_change").
		Put("alliance_id", strconv.FormatInt(allianceId, 10)).
		Put("type", 7). // 7表示变更联盟公告
		Put("uid", strconv.FormatInt(uid, 10)).
		Put("level", strconv.FormatInt(int64(allianceMember.Step()), 10)).
		Put("target_uid", "null").
		Put("target_level", "null"))

	return nil
}

// EditRecruitSetting 变更招募设定
func EditRecruitSetting(ctx context.Context, uid int64, allianceId int64, status int32, powerCondition int32, maxStageCondition int32) error {
	allianceMember := model.GetAllianceMember(ctx, uid, allianceId)
	if allianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("player not in this alliance")
	}
	if !checkPermission(allianceMember.Step(), constDef.ChangeRecruitSetting) {
		return kdmerr.SysInvalidArguments.CastErrorf("player have not permission")
	}
	allianceModel, _ := model.GetAllianceModel(ctx, allianceId)
	allianceModel.SetRecruitSetting(ctx, status)
	allianceModel.SetPowerCondition(ctx, powerCondition)
	allianceModel.SetMaxStageCondition(ctx, maxStageCondition)

	// 打点 - 变更招募设定
	logger.LogCustom(ctx, uid, "alliance", logger.NewDetail().
		Put("action", "member_change").
		Put("alliance_id", strconv.FormatInt(allianceId, 10)).
		Put("type", 5). // 5表示变更招募设定
		Put("uid", strconv.FormatInt(uid, 10)).
		Put("level", strconv.FormatInt(int64(allianceMember.Step()), 10)).
		Put("target_uid", "null").
		Put("target_level", "null"))

	return nil
}

// EditAllianceStepName 变更阶级头衔
func EditAllianceStepName(ctx context.Context, uid int64, allianceId int64, stepName map[int32]string) error {
	allianceMember := model.GetAllianceMember(ctx, uid, allianceId)
	if allianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("player not in this alliance")
	}
	allianceModel, _ := model.GetAllianceModel(ctx, allianceId)
	cur := allianceModel.StepName()
	if cur == nil {
		cur = map[int32]string{}
	}
	for step, name := range stepName {
		stepLine := cfg_mgr.Cfg.GuildRankTable.Get(step)
		if stepLine == nil {
			return kdmerr.SysInvalidArguments.CastErrorf("step id is error")
		}

		if !checkPermission(allianceMember.Step(), constDef.ChangeRankTitle) {
			return kdmerr.SysInvalidArguments.CastErrorf("player have not permission")
		}

		cur[step] = name
	}

	allianceModel.SetStepName(ctx, cur)
	return nil
}

// EditAllianceFlag 自定义旗帜
func EditAllianceFlag(ctx context.Context, uid int64, allianceId int64, flagBase int32, flagEmblem int32) error {
	allianceMember := model.GetAllianceMember(ctx, uid, allianceId)
	if allianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("player not in this alliance")
	}

	if !checkPermission(allianceMember.Step(), constDef.ChangeGuildFlag) {
		return kdmerr.SysInvalidArguments.CastErrorf("player have not permission")
	}
	allianceModel, _ := model.GetAllianceModel(ctx, allianceId)
	allianceModel.SetFlagEmblem(ctx, flagEmblem)
	allianceModel.SetFlagBase(ctx, flagBase)
	return nil
}

// TransferPresident 转让会长
func TransferPresident(ctx context.Context, uid int64, destUid int64) error {
	userInfo, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userInfo)
	allianceId := userInfo.AllianceId()
	orm.Unlock(userInfo)
	srcAllianceMember := model.GetAllianceMember(ctx, uid, allianceId)
	if srcAllianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("player not in this alliance")
	}
	destUserInfo, _ := model.GetUserModel(ctx, destUid)
	orm.Lock(destUserInfo)
	if destUserInfo.AllianceId() != allianceId {
		return kdmerr.SysInvalidArguments.CastErrorf("dest player not in this alliance")
	}
	if !checkPermission(srcAllianceMember.Step(), constDef.TransferPresident) {
		return kdmerr.SysInvalidArguments.CastErrorf("player not is rank5")
	}
	orm.Unlock(destUserInfo)
	destAllianceMember := model.GetAllianceMember(ctx, destUid, allianceId)
	if destAllianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("dest player not in this alliance")
	}
	srcAllianceMember.SetStep(ctx, constDef.Guild_rank_1)
	destAllianceMember.SetStep(ctx, constDef.Guild_rank_5)
	allianceModel, _ := model.GetAllianceModel(ctx, allianceId)
	allianceModel.SetBossUid(ctx, destUid)

	// 打点 - 转让会长
	logger.LogCustom(ctx, uid, "alliance", logger.NewDetail().
		Put("action", "member_change").
		Put("alliance_id", strconv.FormatInt(allianceId, 10)).
		Put("type", 1). // 1表示换盟主
		Put("uid", strconv.FormatInt(uid, 10)).
		Put("level", strconv.FormatInt(int64(constDef.Guild_rank_5), 10)). // 原盟主当前等级
		Put("target_uid", strconv.FormatInt(destUid, 10)).
		Put("target_level", strconv.FormatInt(int64(constDef.Guild_rank_1), 10))) // 新盟主当前等级

	return nil
}

// RemoveMember 移除成员
func RemoveMember(ctx context.Context, uid int64, destUid int64) error {
	userInfo, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userInfo)

	allianceId := userInfo.AllianceId()
	orm.Unlock(userInfo)
	srcAllianceMember := model.GetAllianceMember(ctx, uid, allianceId)
	if srcAllianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("player not in this alliance")
	}
	destUserInfo, _ := model.GetUserModel(ctx, destUid)
	orm.Lock(destUserInfo)
	defer orm.Unlock(destUserInfo)
	if destUserInfo.AllianceId() != allianceId {
		return kdmerr.SysInvalidArguments.CastErrorf("dest player not in this alliance")
	}

	if !checkPermission(srcAllianceMember.Step(), constDef.RemoveMember) {
		return kdmerr.SysInvalidArguments.CastErrorf("player not is rank5")
	}
	destAllianceMember := model.GetAllianceMember(ctx, destUid, allianceId)
	if destAllianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("dest player not in this alliance")
	}
	g.ClientRtm.DelGroupMembers(allianceId, []int64{destUid})
	// 打点 - 成员被踢出联盟
	logger.LogCustom(ctx, destUid, "alliance", logger.NewDetail().
		Put("action", "leave").
		Put("alliance_id", strconv.FormatInt(allianceId, 10)).
		Put("type", 2). // 2表示被踢
		Put("uid", strconv.FormatInt(uid, 10)).
		Put("level", strconv.FormatInt(int64(srcAllianceMember.Step()), 10)).
		Put("target_uid", strconv.FormatInt(destUid, 10)).
		Put("target_level", strconv.FormatInt(int64(destAllianceMember.Step()), 10)))
	destAllianceMember.Delete(ctx)
	destUserInfo.SetAllianceId(ctx, 0)

	return nil
}

// ChangeMemberStep 变更成员阶级
func ChangeMemberStep(ctx context.Context, uid int64, destUid int64, step int32) error {
	userInfo, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userInfo)
	defer orm.Unlock(userInfo)
	allianceId := userInfo.AllianceId()
	srcAllianceMember := model.GetAllianceMember(ctx, uid, allianceId)
	if srcAllianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("player not in this alliance")
	}
	destUserInfo, _ := model.GetUserModel(ctx, destUid)
	orm.Lock(destUserInfo)
	defer orm.Unlock(destUserInfo)
	if destUserInfo.AllianceId() != allianceId {
		return kdmerr.SysInvalidArguments.CastErrorf("dest player not in this alliance")
	}
	if !checkPermission(srcAllianceMember.Step(), constDef.ChangeMemberRank) {
		return kdmerr.SysInvalidArguments.CastErrorf("player not is rank5")
	}
	destAllianceMember := model.GetAllianceMember(ctx, destUid, allianceId)
	if destAllianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("dest player not in this alliance")
	}
	if srcAllianceMember.Step() <= step {
		return kdmerr.SysInvalidArguments.CastErrorf("src player step is low")
	}
	destAllianceMember.SetStep(ctx, step)

	// 打点 - 变更成员阶级
	logger.LogCustom(ctx, uid, "alliance", logger.NewDetail().
		Put("action", "member_change").
		Put("alliance_id", strconv.FormatInt(allianceId, 10)).
		Put("type", 6). // 6表示变更阶级
		Put("uid", strconv.FormatInt(uid, 10)).
		Put("level", strconv.FormatInt(int64(srcAllianceMember.Step()), 10)).
		Put("target_uid", strconv.FormatInt(destUid, 10)).
		Put("target_level", strconv.FormatInt(int64(step), 10)))

	return nil
}

func checkPermission(step int32, permissionId int32) bool {
	permissionLine := cfg_mgr.Cfg.GuildPermissionTable.Get(permissionId)
	for _, v := range permissionLine.GuildRank {
		if step == v {
			return true
		}
	}
	return false
}

func GetAllianceAppList(ctx context.Context, uid int64) ([]*wrpc.AllianceMemberInfo, error) {
	userInfo, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userInfo)
	allianceId := userInfo.AllianceId()
	orm.Unlock(userInfo)
	if allianceId == 0 {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("player alliance is nil")
	}
	allianceMember := model.GetAllianceMember(ctx, uid, allianceId)
	if !checkPermission(allianceMember.Step(), constDef.ManageJoinApplication) {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("have no permission")
	}
	allianceModel, _ := model.GetAllianceModel(ctx, allianceId)
	appList := allianceModel.ApplicationList()
	if appList == nil {
		return nil, nil
	}
	var ret []*wrpc.AllianceMemberInfo
	sort.Slice(appList, func(i, j int) bool {
		return appList[i] < appList[j] // 从小到大
	})
	for _, member := range appList {
		memberUserInfo, _ := model.GetUserModel(ctx, member)
		orm.Lock(memberUserInfo)
		stage, _ := model.GetMainLineStage(ctx, member)
		finishTime := int32(0)
		stageFinishStatus := stage.StageFinishStatus()
		if stageFinishStatus != nil {
			curStatus := stageFinishStatus[stage.UnlockStageId()-1]
			if curStatus != nil {
				finishTime = curStatus.MaxTime
			}
		}
		memberInfo := wrpc.AllianceMemberInfo{
			Uid:            member,
			StageId:        stage.UnlockStageId() - 1,
			Power:          memberUserInfo.Power(),
			Name:           memberUserInfo.Name(),
			AvatarConfigId: memberUserInfo.AvatarConfigId(),
			FinishTime:     finishTime,
		}
		orm.Unlock(memberUserInfo)
		ret = append(ret, &memberInfo)
	}
	return ret, nil
}

// HandleAllianceApp 处理联盟申请
func HandleAllianceApp(ctx context.Context, uid int64, destUid int64, isAgree bool) error {
	userInfo, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userInfo)
	allianceId := userInfo.AllianceId()
	orm.Unlock(userInfo)
	srcAllianceMember := model.GetAllianceMember(ctx, uid, allianceId)
	if srcAllianceMember == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("player not in this alliance")
	}
	destUserInfo, _ := model.GetUserModel(ctx, destUid)
	orm.Lock(destUserInfo)
	defer orm.Unlock(destUserInfo)
	if destUserInfo.AllianceId() != 0 {
		return kdmerr.SysInvalidArguments.CastErrorf("dest player have in other alliance")
	}
	//检测联盟在不在对方申请列表中
	destUserAppList := destUserInfo.AllianceAppList()
	flag := false
	for k, _ := range destUserAppList {
		if k == allianceId {
			flag = true
		}
	}
	if !flag {
		return kdmerr.SysInvalidArguments.CastErrorf("dest user is not in app list")
	}
	if srcAllianceMember.Step() != constDef.Guild_rank_5 {
		return kdmerr.SysInvalidArguments.CastErrorf("player not is rank5")
	}
	if !checkPermission(srcAllianceMember.Step(), constDef.ManageJoinApplication) {
		return kdmerr.SysInvalidArguments.CastErrorf("have no permission")
	}
	allianceModel, _ := model.GetAllianceModel(ctx, allianceId)
	appList := allianceModel.ApplicationList()
	for k, v := range appList {
		if v == destUid {
			flag = true
			appList = append(appList[:k], appList[k+1:]...)
		}
	}
	if !flag {
		return kdmerr.SysInvalidArguments.CastErrorf("dest player not in application list")
	}
	allianceModel.SetApplicationList(ctx, appList)
	if isAgree {
		_, err := model.JoinAlliance(ctx, destUid, allianceId, constDef.Guild_rank_1)
		updateAllianceData(ctx, allianceId)
		if err != nil {
			return err
		}
		destUserInfo.SetAllianceAppList(ctx, map[int64]int64{})
	} else {
		list := destUserInfo.AllianceAppList()
		if list == nil {
			list = map[int64]int64{}
		}
		delete(list, allianceId)
		destUserInfo.SetAllianceAppList(ctx, list)
	}
	pushData := push.NewNotificationWithPayload(minirpc.PushCmdType_AGREE_JOIN_ALLIANCE, &minirpc.AgreeJoinAlliance{AllianceId: allianceId, Name: allianceModel.Name(), Acronym: allianceModel.Acronym(), IsAgree: isAgree})
	ds.SyncCustom(ctx, pushData, []ds.Observer{destUserInfo})
	return nil
}

// DisbandAlliance 解散联盟
func DisbandAlliance(ctx context.Context, uid int64) error {
	userInfo, _ := model.GetUserModel(ctx, uid)
	orm.Lock(userInfo)
	defer orm.Unlock(userInfo)
	if userInfo.AllianceId() == 0 {
		return kdmerr.SysInvalidArguments.CastErrorf("alliance id is error")
	}
	allianceModel, _ := model.GetAllianceModel(ctx, userInfo.AllianceId())
	allianceMember := model.GetAllianceMember(ctx, uid, userInfo.AllianceId())
	if !checkPermission(allianceMember.Step(), constDef.DisbandGuild) {
		return kdmerr.SysInvalidArguments.CastErrorf("have no permission")
	}
	allianceModel.Delete(ctx)
	allianceMembers, _ := orm.GetAll[*model.AllianceMember](ctx, userInfo.AllianceId())
	for _, v := range allianceMembers {
		v.Delete(ctx)
	}
	return nil
}

func GetAllianceList(ctx context.Context, uid int64) (*wrpc.GetAllianceListRet, error) {
	allianceList, _ := orm.GetAll[*model.Alliance](ctx)
	if allianceList == nil {
		return nil, nil
	}
	var ret []*minirpc.Alliance
	for _, v := range allianceList {
		updateAllianceData(ctx, v.AllianceId())
		ret = append(ret, v.Snapshoot().(*minirpc.Alliance))
	}
	return &wrpc.GetAllianceListRet{
		Alliance: ret,
	}, nil
}

func CancerJoinAlliance(ctx context.Context, uid int64, allianceId int64) error {
	allianceModel, _ := model.GetAllianceModel(ctx, allianceId)
	appList := allianceModel.ApplicationList()

	var newList []int64
	for _, v := range appList {
		if v != uid {
			newList = append(newList, v)
		}
	}
	appList = newList
	userInfo, _ := model.GetUserModel(ctx, uid)
	unlock := model.LockUser(ctx, uid)
	defer unlock()
	allianceModel.SetApplicationList(ctx, appList)
	userAppList := userInfo.AllianceAppList()
	delete(userAppList, allianceId)
	userInfo.SetAllianceAppList(ctx, userAppList)
	return nil
}

// updateAllianceData 更新联盟数据和排行榜
func updateAllianceData(ctx context.Context, allianceId int64) {
	alliance, _ := model.GetAllianceModel(ctx, allianceId)
	if alliance == nil {
		return
	}

	// 更新联盟成员信息
	alliance.UpdateAllianceMember(ctx)

	// 更新公会战力排行榜
	rank.UpdateGuildPowerRank(ctx, allianceId, alliance.Power())
}
