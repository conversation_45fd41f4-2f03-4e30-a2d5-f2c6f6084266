// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"bufio"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (c *Configs) SetDataVersion(str string) error {
	if len(str) <= 0 {
		return nil
	}
	c.dataVersion = str
	return nil
}

func (c *Configs) AddVersionExtraInfos(str string) error {
	if len(str) <= 0 {
		return nil
	}
	keyValues := strings.Split(str, "|")
	if len(keyValues) <= 0 {
		return nil
	}
	for i := 0; i < len(keyValues); i += 2 {
		k := keyValues[i]
		if len(k) <= 0 {
			return fmt.Errorf("[version]key cannot be nil, str=%s", str)
		}
		v := ""
		if i+1 < len(keyValues) {
			v = keyValues[i+1]
		}
		c.versionExtraInfos[k] = v
	}
	return nil
}

func (c *Configs) saveVersionJson(dir string) error {
	jsonPath := filepath.Join(dir, "version.json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[version]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	versionInfo := &struct {
		CfgoVersion       string
		CfgoCommitHash    string
		MetaVersion       string
		DataVersion       string
		VersionExtraInfos map[string]string
	}{
		CfgoVersion:       c.cfgoVersion,
		CfgoCommitHash:    c.cfgoCommitHash,
		MetaVersion:       c.metaVersion,
		DataVersion:       c.dataVersion,
		VersionExtraInfos: c.versionExtraInfos,
	}
	w := bufio.NewWriter(f)
	bytes, err := json.MarshalIndent(versionInfo, "", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

func (c *Configs) saveMockDataJson(dir string) error {
	jsonPath := filepath.Join(dir, "mock_data.json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[version]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	mockData := &struct {
		Data map[string]string
	}{
		Data: make(map[string]string),
	}
	{
		m := NewMockAchievementTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["AchievementTable"] = string(bytes)
	}
	{
		m := NewMockActivityTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ActivityTable"] = string(bytes)
	}
	{
		m := NewMockArenaBotTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ArenaBotTable"] = string(bytes)
	}
	{
		m := NewMockArenaChallengeRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ArenaChallengeRewardTable"] = string(bytes)
	}
	{
		m := NewMockArenaDailyRankRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ArenaDailyRankRewardTable"] = string(bytes)
	}
	{
		m := NewMockArenaExtraChallengeCntTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ArenaExtraChallengeCntTable"] = string(bytes)
	}
	{
		m := NewMockArenaMatchTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ArenaMatchTable"] = string(bytes)
	}
	{
		m := NewMockArenaRefreshTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ArenaRefreshTable"] = string(bytes)
	}
	{
		m := NewMockArenaScoreTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ArenaScoreTable"] = string(bytes)
	}
	{
		m := NewMockArenaShopTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ArenaShopTable"] = string(bytes)
	}
	{
		m := NewMockArenaWeeklyRankRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ArenaWeeklyRankRewardTable"] = string(bytes)
	}
	{
		m := NewMockAttributeHierarchyTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["AttributeHierarchyTable"] = string(bytes)
	}
	{
		m := NewMockAvatarFrameTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["AvatarFrameTable"] = string(bytes)
	}
	{
		m := NewMockAvatarTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["AvatarTable"] = string(bytes)
	}
	{
		m := NewMockBattleAttributeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["BattleAttributeTable"] = string(bytes)
	}
	{
		m := NewMockBattleModelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["BattleModelTable"] = string(bytes)
	}
	{
		m := NewMockBenefitsCalcJustShowTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["BenefitsCalcJustShowTable"] = string(bytes)
	}
	{
		m := NewMockBenefitsCalcTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["BenefitsCalcTable"] = string(bytes)
	}
	{
		m := NewMockBenefitsTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["BenefitsTable"] = string(bytes)
	}
	{
		m := NewMockBlackShopTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["BlackShopTable"] = string(bytes)
	}
	{
		m := NewMockBossDungeonRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["BossDungeonRewardTable"] = string(bytes)
	}
	{
		m := NewMockBossDungeonTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["BossDungeonTable"] = string(bytes)
	}
	{
		m := NewMockChapterLevelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ChapterLevelTable"] = string(bytes)
	}
	{
		m := NewMockChapterTaskMainTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ChapterTaskMainTable"] = string(bytes)
	}
	{
		m := NewMockChapterTaskTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ChapterTaskTable"] = string(bytes)
	}
	{
		m := NewMockDailyTasksScoreTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["DailyTasksScoreTable"] = string(bytes)
	}
	{
		m := NewMockDailyTasksTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["DailyTasksTable"] = string(bytes)
	}
	{
		m := NewMockDaveLevelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["DaveLevelTable"] = string(bytes)
	}
	{
		m := NewMockDropGroupTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["DropGroupTable"] = string(bytes)
	}
	{
		m := NewMockDropMainTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["DropMainTable"] = string(bytes)
	}
	{
		m := NewMockDungeonChapterLevelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["DungeonChapterLevelTable"] = string(bytes)
	}
	{
		m := NewMockDungeonCoinLevelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["DungeonCoinLevelTable"] = string(bytes)
	}
	{
		m := NewMockDungeonGeneLevelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["DungeonGeneLevelTable"] = string(bytes)
	}
	{
		m := NewMockDungeonLordEquipLevelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["DungeonLordEquipLevelTable"] = string(bytes)
	}
	{
		m := NewMockDungeonRefreshTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["DungeonRefreshTable"] = string(bytes)
	}
	{
		m := NewMockDungeonSunshineLevelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["DungeonSunshineLevelTable"] = string(bytes)
	}
	{
		m := NewMockDungeonTypeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["DungeonTypeTable"] = string(bytes)
	}
	{
		m := NewMockEliteDungeonRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["EliteDungeonRewardTable"] = string(bytes)
	}
	{
		m := NewMockEliteDungeonTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["EliteDungeonTable"] = string(bytes)
	}
	{
		m := NewMockFunctionPreviewTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["FunctionPreviewTable"] = string(bytes)
	}
	{
		m := NewMockFunctionTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["FunctionTable"] = string(bytes)
	}
	{
		m := NewMockGameConfigsCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["GameConfigs"] = string(bytes)
	}
	{
		m := NewMockGemAffixQualityTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["GemAffixQualityTable"] = string(bytes)
	}
	{
		m := NewMockGemQualityTypeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["GemQualityTypeTable"] = string(bytes)
	}
	{
		m := NewMockGoToTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["GoToTable"] = string(bytes)
	}
	{
		m := NewMockGuildFlagTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["GuildFlagTable"] = string(bytes)
	}
	{
		m := NewMockGuildHaggleTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["GuildHaggleTable"] = string(bytes)
	}
	{
		m := NewMockGuildLevelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["GuildLevelTable"] = string(bytes)
	}
	{
		m := NewMockGuildPermissionTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["GuildPermissionTable"] = string(bytes)
	}
	{
		m := NewMockGuildRankTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["GuildRankTable"] = string(bytes)
	}
	{
		m := NewMockGuildShopTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["GuildShopTable"] = string(bytes)
	}
	{
		m := NewMockGuildTaskTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["GuildTaskTable"] = string(bytes)
	}
	{
		m := NewMockGuildTasksScoreTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["GuildTasksScoreTable"] = string(bytes)
	}
	{
		m := NewMockHeroBondsTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroBondsTable"] = string(bytes)
	}
	{
		m := NewMockHeroCareerTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroCareerTable"] = string(bytes)
	}
	{
		m := NewMockHeroConfigTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroConfigTable"] = string(bytes)
	}
	{
		m := NewMockHeroElementTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroElementTable"] = string(bytes)
	}
	{
		m := NewMockHeroFragmentTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroFragmentTable"] = string(bytes)
	}
	{
		m := NewMockHeroGeneFragmentTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroGeneFragmentTable"] = string(bytes)
	}
	{
		m := NewMockHeroGeneTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroGeneTable"] = string(bytes)
	}
	{
		m := NewMockHeroLevelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroLevelTable"] = string(bytes)
	}
	{
		m := NewMockHeroLotteryGroupTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroLotteryGroupTable"] = string(bytes)
	}
	{
		m := NewMockHeroLotteryMustTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroLotteryMustTable"] = string(bytes)
	}
	{
		m := NewMockHeroLotteryRandomGroupTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroLotteryRandomGroupTable"] = string(bytes)
	}
	{
		m := NewMockHeroLotteryRandomTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroLotteryRandomTable"] = string(bytes)
	}
	{
		m := NewMockHeroQualityTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroQualityTable"] = string(bytes)
	}
	{
		m := NewMockHeroRestrainTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroRestrainTable"] = string(bytes)
	}
	{
		m := NewMockHeroSkillAttrTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroSkillAttrTable"] = string(bytes)
	}
	{
		m := NewMockHeroSkillAwakeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroSkillAwakeTable"] = string(bytes)
	}
	{
		m := NewMockHeroSkillBuffTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroSkillBuffTable"] = string(bytes)
	}
	{
		m := NewMockHeroSkillBuffTypeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroSkillBuffTypeTable"] = string(bytes)
	}
	{
		m := NewMockHeroSkillEffectTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroSkillEffectTable"] = string(bytes)
	}
	{
		m := NewMockHeroSkillGroupTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroSkillGroupTable"] = string(bytes)
	}
	{
		m := NewMockHeroSkillTypeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroSkillTypeTable"] = string(bytes)
	}
	{
		m := NewMockHeroStarTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroStarTable"] = string(bytes)
	}
	{
		m := NewMockHeroTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroTable"] = string(bytes)
	}
	{
		m := NewMockHeroTypeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["HeroTypeTable"] = string(bytes)
	}
	{
		m := NewMockIap1stTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["Iap1stTable"] = string(bytes)
	}
	{
		m := NewMockIap2XTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["Iap2XTable"] = string(bytes)
	}
	{
		m := NewMockIapAdFreeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapAdFreeTable"] = string(bytes)
	}
	{
		m := NewMockIapBPTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapBPTable"] = string(bytes)
	}
	{
		m := NewMockIapBpRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapBpRewardTable"] = string(bytes)
	}
	{
		m := NewMockIapDailySaleFreeRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapDailySaleFreeRewardTable"] = string(bytes)
	}
	{
		m := NewMockIapDailySaleRewardGroupTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapDailySaleRewardGroupTable"] = string(bytes)
	}
	{
		m := NewMockIapDailySaleRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapDailySaleRewardTable"] = string(bytes)
	}
	{
		m := NewMockIapDailySaleTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapDailySaleTable"] = string(bytes)
	}
	{
		m := NewMockIapDealTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapDealTable"] = string(bytes)
	}
	{
		m := NewMockIapDungeonFundRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapDungeonFundRewardTable"] = string(bytes)
	}
	{
		m := NewMockIapDungeonFundTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapDungeonFundTable"] = string(bytes)
	}
	{
		m := NewMockIapLevelFundRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapLevelFundRewardTable"] = string(bytes)
	}
	{
		m := NewMockIapLevelFundTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapLevelFundTable"] = string(bytes)
	}
	{
		m := NewMockIapLifeCardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapLifeCardTable"] = string(bytes)
	}
	{
		m := NewMockIapMonthCardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapMonthCardTable"] = string(bytes)
	}
	{
		m := NewMockIapOrderCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapOrder"] = string(bytes)
	}
	{
		m := NewMockIapPackageDiamondShopTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapPackageDiamondShopTable"] = string(bytes)
	}
	{
		m := NewMockIapPackageRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapPackageRewardTable"] = string(bytes)
	}
	{
		m := NewMockIapPackageTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapPackageTable"] = string(bytes)
	}
	{
		m := NewMockIapPriceTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapPriceTable"] = string(bytes)
	}
	{
		m := NewMockIapRegularPackGroupTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapRegularPackGroupTable"] = string(bytes)
	}
	{
		m := NewMockIapRegularPackTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapRegularPackTable"] = string(bytes)
	}
	{
		m := NewMockIapShopMallTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapShopMallTable"] = string(bytes)
	}
	{
		m := NewMockIapSignRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapSignRewardTable"] = string(bytes)
	}
	{
		m := NewMockIapSignTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapSignTable"] = string(bytes)
	}
	{
		m := NewMockIapTriggerPackGroupTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapTriggerPackGroupTable"] = string(bytes)
	}
	{
		m := NewMockIapTriggerPackTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapTriggerPackTable"] = string(bytes)
	}
	{
		m := NewMockIapTurnPackTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IapTurnPackTable"] = string(bytes)
	}
	{
		m := NewMockIdleMonsterTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IdleMonsterTable"] = string(bytes)
	}
	{
		m := NewMockIdleRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IdleRewardTable"] = string(bytes)
	}
	{
		m := NewMockIdleRewardTimeCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["IdleRewardTime"] = string(bytes)
	}
	{
		m := NewMockItemQualityTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ItemQualityTable"] = string(bytes)
	}
	{
		m := NewMockItemSourceTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ItemSourceTable"] = string(bytes)
	}
	{
		m := NewMockItemTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ItemTable"] = string(bytes)
	}
	{
		m := NewMockLTCRechargeScoreTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LTCRechargeScoreTable"] = string(bytes)
	}
	{
		m := NewMockLTCRechargeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LTCRechargeTable"] = string(bytes)
	}
	{
		m := NewMockLanguageCnTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LanguageCnTable"] = string(bytes)
	}
	{
		m := NewMockLevelShopTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LevelShopTable"] = string(bytes)
	}
	{
		m := NewMockLoginOpenTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LoginOpenTable"] = string(bytes)
	}
	{
		m := NewMockLordEquipGradeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordEquipGradeTable"] = string(bytes)
	}
	{
		m := NewMockLordEquipGradeTypeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordEquipGradeTypeTable"] = string(bytes)
	}
	{
		m := NewMockLordEquipSlotsTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordEquipSlotsTable"] = string(bytes)
	}
	{
		m := NewMockLordEquipTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordEquipTable"] = string(bytes)
	}
	{
		m := NewMockLordEquipTypeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordEquipTypeTable"] = string(bytes)
	}
	{
		m := NewMockLordGemCraftTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordGemCraftTable"] = string(bytes)
	}
	{
		m := NewMockLordGemDropCntTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordGemDropCntTable"] = string(bytes)
	}
	{
		m := NewMockLordGemDropQualityTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordGemDropQualityTable"] = string(bytes)
	}
	{
		m := NewMockLordGemRandomGroupChanceTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordGemRandomGroupChanceTable"] = string(bytes)
	}
	{
		m := NewMockLordGemRandomGroupMustTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordGemRandomGroupMustTable"] = string(bytes)
	}
	{
		m := NewMockLordGemRandomGroupTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordGemRandomGroupTable"] = string(bytes)
	}
	{
		m := NewMockLordGemRandomRewardGroupTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordGemRandomRewardGroupTable"] = string(bytes)
	}
	{
		m := NewMockLordGemReforgeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordGemReforgeTable"] = string(bytes)
	}
	{
		m := NewMockLordGemTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["LordGemTable"] = string(bytes)
	}
	{
		m := NewMockMailTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MailTable"] = string(bytes)
	}
	{
		m := NewMockMainChapterLevelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MainChapterLevelTable"] = string(bytes)
	}
	{
		m := NewMockMainChapterTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MainChapterTable"] = string(bytes)
	}
	{
		m := NewMockMainLevelPassRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MainLevelPassRewardTable"] = string(bytes)
	}
	{
		m := NewMockMainLevelRangeDmgTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MainLevelRangeDmgTable"] = string(bytes)
	}
	{
		m := NewMockMainLevelRewardRatioTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MainLevelRewardRatioTable"] = string(bytes)
	}
	{
		m := NewMockMainLevelRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MainLevelRewardTable"] = string(bytes)
	}
	{
		m := NewMockMainLevelRogueRewardWeightTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MainLevelRogueRewardWeightTable"] = string(bytes)
	}
	{
		m := NewMockMainLevelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MainLevelTable"] = string(bytes)
	}
	{
		m := NewMockMainLineTasksTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MainLineTasksTable"] = string(bytes)
	}
	{
		m := NewMockMapEventBuffTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MapEventBuffTable"] = string(bytes)
	}
	{
		m := NewMockMapEventMonsterGroupTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MapEventMonsterGroupTable"] = string(bytes)
	}
	{
		m := NewMockMapEventMonsterTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MapEventMonsterTable"] = string(bytes)
	}
	{
		m := NewMockMapEventObstacleTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MapEventObstacleTable"] = string(bytes)
	}
	{
		m := NewMockMapEventPropTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MapEventPropTable"] = string(bytes)
	}
	{
		m := NewMockMapEventRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MapEventRewardTable"] = string(bytes)
	}
	{
		m := NewMockMapEventSkillTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MapEventSkillTable"] = string(bytes)
	}
	{
		m := NewMockMapEventTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MapEventTable"] = string(bytes)
	}
	{
		m := NewMockMapRefreshMonsterEventTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MapRefreshMonsterEventTable"] = string(bytes)
	}
	{
		m := NewMockModifierTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ModifierTable"] = string(bytes)
	}
	{
		m := NewMockMonsterCareerTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MonsterCareerTable"] = string(bytes)
	}
	{
		m := NewMockMonsterGradeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MonsterGradeTable"] = string(bytes)
	}
	{
		m := NewMockMonsterPosTypeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MonsterPosTypeTable"] = string(bytes)
	}
	{
		m := NewMockMonsterPreviewSchemeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MonsterPreviewSchemeTable"] = string(bytes)
	}
	{
		m := NewMockMonsterSkillTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MonsterSkillTable"] = string(bytes)
	}
	{
		m := NewMockMonsterTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MonsterTable"] = string(bytes)
	}
	{
		m := NewMockMonsterTypeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["MonsterTypeTable"] = string(bytes)
	}
	{
		m := NewMockNewbieTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["NewbieTable"] = string(bytes)
	}
	{
		m := NewMockNpcDialogueTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["NpcDialogueTable"] = string(bytes)
	}
	{
		m := NewMockPhotovoltaicTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["PhotovoltaicTable"] = string(bytes)
	}
	{
		m := NewMockPresetsTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["PresetsTable"] = string(bytes)
	}
	{
		m := NewMockRankMainTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["RankMainTable"] = string(bytes)
	}
	{
		m := NewMockRankRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["RankRewardTable"] = string(bytes)
	}
	{
		m := NewMockRougeNameCnCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["RougeNameCn"] = string(bytes)
	}
	{
		m := NewMockRougeRefreshTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["RougeRefreshTable"] = string(bytes)
	}
	{
		m := NewMockRougeTabEffectTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["RougeTabEffectTable"] = string(bytes)
	}
	{
		m := NewMockRougeTabGroupRandomTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["RougeTabGroupRandomTable"] = string(bytes)
	}
	{
		m := NewMockRougeTabGroupTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["RougeTabGroupTable"] = string(bytes)
	}
	{
		m := NewMockRougeTabNewbieTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["RougeTabNewbieTable"] = string(bytes)
	}
	{
		m := NewMockRougeTabTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["RougeTabTable"] = string(bytes)
	}
	{
		m := NewMockRougeWeightCoefCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["RougeWeightCoef"] = string(bytes)
	}
	{
		m := NewMockSelectChestGroupTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["SelectChestGroupTable"] = string(bytes)
	}
	{
		m := NewMockSevenDayTasksScoreTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["SevenDayTasksScoreTable"] = string(bytes)
	}
	{
		m := NewMockSevenDayTasksTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["SevenDayTasksTable"] = string(bytes)
	}
	{
		m := NewMockShopTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["ShopTable"] = string(bytes)
	}
	{
		m := NewMockSign7TableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["Sign7Table"] = string(bytes)
	}
	{
		m := NewMockSkillDmgTypeTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["SkillDmgTypeTable"] = string(bytes)
	}
	{
		m := NewMockTowerAILevelTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["TowerAILevelTable"] = string(bytes)
	}
	{
		m := NewMockTowerAITableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["TowerAITable"] = string(bytes)
	}
	{
		m := NewMockTowerTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["TowerTable"] = string(bytes)
	}
	{
		m := NewMockTurnRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["TurnRewardTable"] = string(bytes)
	}
	{
		m := NewMockTurnScoreRewardTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["TurnScoreRewardTable"] = string(bytes)
	}
	{
		m := NewMockTurnTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["TurnTable"] = string(bytes)
	}
	{
		m := NewMockVehicleTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["VehicleTable"] = string(bytes)
	}
	{
		m := NewMockVipFreeExpTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["VipFreeExpTable"] = string(bytes)
	}
	{
		m := NewMockVipTableCfg()
		bytes, err := json.MarshalIndent(m, "", "\t")
		if err != nil {
			return err
		}
		mockData.Data["VipTable"] = string(bytes)
	}

	w := bufio.NewWriter(f)
	bytes, err := json.MarshalIndent(mockData, "", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

func (c *Configs) saveEnumJson(dir string) error {
	jsonPath := filepath.Join(dir, "enum.json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[version]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	w := bufio.NewWriter(f)
	w.WriteString(enumJsonContent)
	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

func (c *Configs) SaveJson(dir string) error {
	// 删除所有.json文件
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	for _, file := range files {
		fileName := file.Name()
		if strings.HasSuffix(fileName, ".json") {
			filepath.Join(dir, fileName)
			if err = os.Remove(filepath.Join(dir, fileName)); err != nil {
				return err
			}
		}
	}
	// save version
	if err = c.saveVersionJson(dir); err != nil {
		return err
	}
	// save mock data
	if err = c.saveMockDataJson(dir); err != nil {
		return err
	}
	// save enum
	if err = c.saveEnumJson(dir); err != nil {
		return err
	}
	// save all tables
	if err = c.AchievementTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ActivityTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ArenaBotTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ArenaChallengeRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ArenaDailyRankRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ArenaExtraChallengeCntTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ArenaMatchTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ArenaRefreshTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ArenaScoreTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ArenaShopTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ArenaWeeklyRankRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.AttributeHierarchyTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.AvatarFrameTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.AvatarTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.BattleAttributeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.BattleModelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.BenefitsCalcJustShowTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.BenefitsCalcTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.BenefitsTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.BlackShopTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.BossDungeonRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.BossDungeonTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ChapterLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ChapterTaskMainTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ChapterTaskTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.DailyTasksScoreTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.DailyTasksTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.DaveLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.DropGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.DropMainTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.DungeonChapterLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.DungeonCoinLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.DungeonGeneLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.DungeonLordEquipLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.DungeonRefreshTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.DungeonSunshineLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.DungeonTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.EliteDungeonRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.EliteDungeonTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.FunctionPreviewTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.FunctionTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.GameConfigs.saveJson(dir); err != nil {
		return err
	}
	if err = c.GemAffixQualityTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.GemQualityTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.GoToTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.GuildFlagTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.GuildHaggleTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.GuildLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.GuildPermissionTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.GuildRankTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.GuildShopTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.GuildTaskTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.GuildTasksScoreTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroBondsTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroCareerTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroConfigTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroElementTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroFragmentTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroGeneFragmentTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroGeneTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroLotteryGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroLotteryMustTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroLotteryRandomGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroLotteryRandomTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroQualityTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroRestrainTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroSkillAttrTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroSkillAwakeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroSkillBuffTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroSkillBuffTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroSkillEffectTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroSkillGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroSkillTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroStarTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.HeroTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.Iap1stTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.Iap2XTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapAdFreeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapBPTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapBpRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapDailySaleFreeRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapDailySaleRewardGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapDailySaleRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapDailySaleTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapDealTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapDungeonFundRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapDungeonFundTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapLevelFundRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapLevelFundTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapLifeCardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapMonthCardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapOrder.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapPackageDiamondShopTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapPackageRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapPackageTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapPriceTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapRegularPackGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapRegularPackTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapShopMallTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapSignRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapSignTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapTriggerPackGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapTriggerPackTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IapTurnPackTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IdleMonsterTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IdleRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.IdleRewardTime.saveJson(dir); err != nil {
		return err
	}
	if err = c.ItemQualityTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ItemSourceTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ItemTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LTCRechargeScoreTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LTCRechargeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LanguageCnTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LevelShopTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LoginOpenTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordEquipGradeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordEquipGradeTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordEquipSlotsTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordEquipTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordEquipTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordGemCraftTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordGemDropCntTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordGemDropQualityTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupChanceTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupMustTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordGemRandomRewardGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordGemReforgeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.LordGemTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MailTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MainChapterLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MainChapterTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MainLevelPassRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MainLevelRangeDmgTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MainLevelRewardRatioTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MainLevelRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MainLevelRogueRewardWeightTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MainLevelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MainLineTasksTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MapEventBuffTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MapEventMonsterGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MapEventMonsterTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MapEventObstacleTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MapEventPropTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MapEventRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MapEventSkillTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MapEventTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MapRefreshMonsterEventTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ModifierTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MonsterCareerTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MonsterGradeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MonsterPosTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MonsterPreviewSchemeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MonsterSkillTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MonsterTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.MonsterTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.NewbieTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.NpcDialogueTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.PhotovoltaicTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.PresetsTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.RankMainTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.RankRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.RougeNameCn.saveJson(dir); err != nil {
		return err
	}
	if err = c.RougeRefreshTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.RougeTabEffectTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.RougeTabGroupRandomTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.RougeTabGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.RougeTabNewbieTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.RougeTabTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.RougeWeightCoef.saveJson(dir); err != nil {
		return err
	}
	if err = c.SelectChestGroupTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.SevenDayTasksScoreTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.SevenDayTasksTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.ShopTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.Sign7Table.saveJson(dir); err != nil {
		return err
	}
	if err = c.SkillDmgTypeTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.TowerAILevelTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.TowerAITable.saveJson(dir); err != nil {
		return err
	}
	if err = c.TowerTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.TurnRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.TurnScoreRewardTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.TurnTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.VehicleTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.VipFreeExpTable.saveJson(dir); err != nil {
		return err
	}
	if err = c.VipTable.saveJson(dir); err != nil {
		return err
	}

	return nil
}

var enumJsonContent string = `{
	"Enums": [
		{
			"EnumName": "AttrDefaultType",
			"Cases": [
				{
					"CaseName": "Number",
					"CaseValue": 1
				},
				{
					"CaseName": "Model",
					"CaseValue": 2
				},
				{
					"CaseName": "Buff",
					"CaseValue": 3
				},
				{
					"CaseName": "Effect",
					"CaseValue": 4
				}
			]
		},
		{
			"EnumName": "BagType",
			"Cases": [
				{
					"CaseName": "Plants",
					"CaseValue": 1
				},
				{
					"CaseName": "Consumption",
					"CaseValue": 2
				},
				{
					"CaseName": "Materials",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "BenefitCalcFormula",
			"Cases": [
				{
					"CaseName": "Formula1",
					"CaseValue": 1
				},
				{
					"CaseName": "Formula2",
					"CaseValue": 2
				},
				{
					"CaseName": "Formula3",
					"CaseValue": 3
				},
				{
					"CaseName": "Formula4",
					"CaseValue": 4
				},
				{
					"CaseName": "Formula5",
					"CaseValue": 5
				},
				{
					"CaseName": "Formula6",
					"CaseValue": 6
				},
				{
					"CaseName": "Formula7",
					"CaseValue": 7
				},
				{
					"CaseName": "Formula8",
					"CaseValue": 8
				},
				{
					"CaseName": "Formula9",
					"CaseValue": 9
				}
			]
		},
		{
			"EnumName": "BuffOverlyingType",
			"Cases": [
				{
					"CaseName": "NoOverlying",
					"CaseValue": 1
				},
				{
					"CaseName": "TimeOverlying",
					"CaseValue": 2
				},
				{
					"CaseName": "EffectOverlying",
					"CaseValue": 3
				},
				{
					"CaseName": "EffectCover",
					"CaseValue": 4
				},
				{
					"CaseName": "EffectReplace",
					"CaseValue": 5
				},
				{
					"CaseName": "EffectMultiple",
					"CaseValue": 6
				}
			]
		},
		{
			"EnumName": "BuffTarget",
			"Cases": [
				{
					"CaseName": "Own",
					"CaseValue": 1
				},
				{
					"CaseName": "OwnSide",
					"CaseValue": 2
				},
				{
					"CaseName": "OwnSideHeroDefense",
					"CaseValue": 3
				},
				{
					"CaseName": "OwnSideHeroRanged",
					"CaseValue": 4
				},
				{
					"CaseName": "OwnSideHeroSupport",
					"CaseValue": 5
				},
				{
					"CaseName": "OwnSideHeroFront",
					"CaseValue": 6
				},
				{
					"CaseName": "OwnSideHeroBehind",
					"CaseValue": 7
				},
				{
					"CaseName": "OwnSideHeroHPLowest",
					"CaseValue": 8
				},
				{
					"CaseName": "Opposite",
					"CaseValue": 9
				},
				{
					"CaseName": "OppositeSide",
					"CaseValue": 10
				},
				{
					"CaseName": "OppositeSideHeroDefense",
					"CaseValue": 11
				},
				{
					"CaseName": "OppositeSideHeroRanged",
					"CaseValue": 12
				},
				{
					"CaseName": "OppositeSideHeroSupport",
					"CaseValue": 13
				},
				{
					"CaseName": "OppoSideHeroFront",
					"CaseValue": 14
				},
				{
					"CaseName": "OppoSideHeroBehind",
					"CaseValue": 15
				},
				{
					"CaseName": "OppositeSideHeroHpLowest",
					"CaseValue": 16
				},
				{
					"CaseName": "PreSkillEffectTarget",
					"CaseValue": 17
				},
				{
					"CaseName": "PreSkillEffectTargetElseBoss",
					"CaseValue": 18
				},
				{
					"CaseName": "Boss",
					"CaseValue": 19
				},
				{
					"CaseName": "Vehicle",
					"CaseValue": 20
				},
				{
					"CaseName": "OwnForward",
					"CaseValue": 21
				},
				{
					"CaseName": "OwnSideHeroMagic",
					"CaseValue": 22
				},
				{
					"CaseName": "OwnSideHeroSuperPowers",
					"CaseValue": 23
				},
				{
					"CaseName": "OwnSideHeroTech",
					"CaseValue": 24
				},
				{
					"CaseName": "OppositeSideHeroMagic",
					"CaseValue": 25
				},
				{
					"CaseName": "OppositeSideHeroSuperPowers",
					"CaseValue": 26
				},
				{
					"CaseName": "OppositeSideHeroTech",
					"CaseValue": 27
				},
				{
					"CaseName": "OwnSideHeroAtkHighest",
					"CaseValue": 28
				},
				{
					"CaseName": "OwnSideHeroBehindMagic",
					"CaseValue": 29
				},
				{
					"CaseName": "OwnSideHeroBehindTech",
					"CaseValue": 30
				},
				{
					"CaseName": "OwnSideHeroFrontDefenseRandom",
					"CaseValue": 31
				}
			]
		},
		{
			"EnumName": "BuffType",
			"Cases": [
				{
					"CaseName": "Buff",
					"CaseValue": 1
				},
				{
					"CaseName": "Debuff",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "CorrectType",
			"Cases": [
				{
					"CaseName": "Overlying",
					"CaseValue": 1
				},
				{
					"CaseName": "Cover",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "DailyOrWeekly",
			"Cases": [
				{
					"CaseName": "Daily",
					"CaseValue": 1
				},
				{
					"CaseName": "Weekly",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "DungeonType",
			"Cases": [
				{
					"CaseName": "CoinDungeon",
					"CaseValue": 1
				},
				{
					"CaseName": "GeneDungeon",
					"CaseValue": 2
				},
				{
					"CaseName": "LordEquipDungeon",
					"CaseValue": 3
				},
				{
					"CaseName": "SunshineDungeon",
					"CaseValue": 4
				}
			]
		},
		{
			"EnumName": "GemAffixQuality",
			"Cases": [
				{
					"CaseName": "GemAffixQuality1",
					"CaseValue": 1
				},
				{
					"CaseName": "GemAffixQuality2",
					"CaseValue": 2
				},
				{
					"CaseName": "GemAffixQuality3",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "GemQualityType",
			"Cases": [
				{
					"CaseName": "GemQualityType1",
					"CaseValue": 1
				},
				{
					"CaseName": "GemQualityType2",
					"CaseValue": 2
				},
				{
					"CaseName": "GemQualityType3",
					"CaseValue": 3
				},
				{
					"CaseName": "GemQualityType4",
					"CaseValue": 4
				},
				{
					"CaseName": "GemQualityType5",
					"CaseValue": 5
				},
				{
					"CaseName": "GemQualityType6",
					"CaseValue": 6
				},
				{
					"CaseName": "GemQualityType7",
					"CaseValue": 7
				}
			]
		},
		{
			"EnumName": "GuildFlagType",
			"Cases": [
				{
					"CaseName": "Base",
					"CaseValue": 1
				},
				{
					"CaseName": "Badge",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "GuildPermission",
			"Cases": [
				{
					"CaseName": "ChangeGuildFlag",
					"CaseValue": 1
				},
				{
					"CaseName": "ChangeGuildShortName",
					"CaseValue": 2
				},
				{
					"CaseName": "ChangeGuildName",
					"CaseValue": 3
				},
				{
					"CaseName": "EditNotice",
					"CaseValue": 4
				},
				{
					"CaseName": "ChangeRecruitSetting",
					"CaseValue": 5
				},
				{
					"CaseName": "ManageJoinApplication",
					"CaseValue": 6
				},
				{
					"CaseName": "DisbandGuild",
					"CaseValue": 7
				},
				{
					"CaseName": "TransferPresident",
					"CaseValue": 8
				},
				{
					"CaseName": "RemoveMember",
					"CaseValue": 9
				},
				{
					"CaseName": "ChangeMemberRank",
					"CaseValue": 10
				},
				{
					"CaseName": "ViewMemberInfo",
					"CaseValue": 11
				},
				{
					"CaseName": "ChangeRankTitle",
					"CaseValue": 12
				},
				{
					"CaseName": "ChangeLanguage",
					"CaseValue": 13
				},
				{
					"CaseName": "ExitGuild",
					"CaseValue": 14
				}
			]
		},
		{
			"EnumName": "GuildRank",
			"Cases": [
				{
					"CaseName": "Rank5",
					"CaseValue": 1
				},
				{
					"CaseName": "Rank4",
					"CaseValue": 2
				},
				{
					"CaseName": "Rank3",
					"CaseValue": 3
				},
				{
					"CaseName": "Rank2",
					"CaseValue": 4
				},
				{
					"CaseName": "Rank1",
					"CaseValue": 5
				}
			]
		},
		{
			"EnumName": "HeroCareer",
			"Cases": [
				{
					"CaseName": "HeroDefense",
					"CaseValue": 1
				},
				{
					"CaseName": "HeroRanged",
					"CaseValue": 2
				},
				{
					"CaseName": "HeroSupport",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "HeroConfig",
			"Cases": [
				{
					"CaseName": "HeroConfig1",
					"CaseValue": 1
				},
				{
					"CaseName": "HeroConfig2",
					"CaseValue": 2
				},
				{
					"CaseName": "HeroConfig3",
					"CaseValue": 3
				},
				{
					"CaseName": "HeroConfig4",
					"CaseValue": 4
				},
				{
					"CaseName": "HeroConfig5",
					"CaseValue": 5
				}
			]
		},
		{
			"EnumName": "HeroLevelUpPlan",
			"Cases": [
				{
					"CaseName": "HeroLevelPlanLegendaryDefense",
					"CaseValue": 1
				},
				{
					"CaseName": "HeroLevelPlanLegendaryRanged",
					"CaseValue": 2
				},
				{
					"CaseName": "HeroLevelPlanLegendarySupport",
					"CaseValue": 3
				},
				{
					"CaseName": "HeroLevelPlanEpicDefense",
					"CaseValue": 4
				},
				{
					"CaseName": "HeroLevelPlanEpicRanged",
					"CaseValue": 5
				},
				{
					"CaseName": "HeroLevelPlanEpicSupport",
					"CaseValue": 6
				},
				{
					"CaseName": "HeroLevelPlanRareDefense",
					"CaseValue": 7
				},
				{
					"CaseName": "HeroLevelPlanRareRanged",
					"CaseValue": 8
				},
				{
					"CaseName": "HeroLevelPlanRareSupport",
					"CaseValue": 9
				}
			]
		},
		{
			"EnumName": "HeroQuality",
			"Cases": [
				{
					"CaseName": "HeroLegendary",
					"CaseValue": 1
				},
				{
					"CaseName": "HeroEpic",
					"CaseValue": 2
				},
				{
					"CaseName": "HeroRare",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "HeroSkillBuffType",
			"Cases": [
				{
					"CaseName": "Benefit",
					"CaseValue": 1
				},
				{
					"CaseName": "Silent",
					"CaseValue": 2
				},
				{
					"CaseName": "Stun",
					"CaseValue": 3
				},
				{
					"CaseName": "Paralysis",
					"CaseValue": 4
				},
				{
					"CaseName": "Sleep",
					"CaseValue": 5
				},
				{
					"CaseName": "Bind",
					"CaseValue": 6
				},
				{
					"CaseName": "Immortal",
					"CaseValue": 7
				},
				{
					"CaseName": "Veil",
					"CaseValue": 8
				},
				{
					"CaseName": "Stealth",
					"CaseValue": 9
				},
				{
					"CaseName": "Curse",
					"CaseValue": 10
				},
				{
					"CaseName": "Dot_bleed",
					"CaseValue": 11
				},
				{
					"CaseName": "Dot_poison",
					"CaseValue": 12
				},
				{
					"CaseName": "Dot_frostbite",
					"CaseValue": 13
				},
				{
					"CaseName": "Dot_burn",
					"CaseValue": 14
				},
				{
					"CaseName": "Block",
					"CaseValue": 15
				},
				{
					"CaseName": "Unrevive",
					"CaseValue": 16
				},
				{
					"CaseName": "EternalSlumber",
					"CaseValue": 17
				},
				{
					"CaseName": "Tense",
					"CaseValue": 18
				},
				{
					"CaseName": "Immunity",
					"CaseValue": 19
				},
				{
					"CaseName": "Shield",
					"CaseValue": 20
				},
				{
					"CaseName": "HalfAsleep",
					"CaseValue": 21
				},
				{
					"CaseName": "Nightmare",
					"CaseValue": 22
				},
				{
					"CaseName": "LifeSteal",
					"CaseValue": 23
				},
				{
					"CaseName": "Revive",
					"CaseValue": 24
				},
				{
					"CaseName": "HpRecovery",
					"CaseValue": 25
				},
				{
					"CaseName": "SkillSwitch",
					"CaseValue": 26
				},
				{
					"CaseName": "Taunted",
					"CaseValue": 27
				},
				{
					"CaseName": "Dmg",
					"CaseValue": 28
				},
				{
					"CaseName": "RemoveBuff",
					"CaseValue": 29
				},
				{
					"CaseName": "OnSideExplosion",
					"CaseValue": 30
				},
				{
					"CaseName": "Frozen",
					"CaseValue": 31
				},
				{
					"CaseName": "Repel",
					"CaseValue": 32
				},
				{
					"CaseName": "Pull",
					"CaseValue": 33
				},
				{
					"CaseName": "LightingStruck",
					"CaseValue": 34
				},
				{
					"CaseName": "Vulnerability",
					"CaseValue": 35
				},
				{
					"CaseName": "Lame",
					"CaseValue": 36
				},
				{
					"CaseName": "Rampage",
					"CaseValue": 37
				},
				{
					"CaseName": "BuffDelay",
					"CaseValue": 38
				},
				{
					"CaseName": "CoolingOff",
					"CaseValue": 39
				},
				{
					"CaseName": "RecoveryDown",
					"CaseValue": 40
				},
				{
					"CaseName": "Armour",
					"CaseValue": 41
				},
				{
					"CaseName": "FrozenHpRecovery",
					"CaseValue": 42
				},
				{
					"CaseName": "ElectrostaticSputtering",
					"CaseValue": 43
				},
				{
					"CaseName": "InjuryHealing",
					"CaseValue": 44
				},
				{
					"CaseName": "Summon",
					"CaseValue": 45
				},
				{
					"CaseName": "CrabWalk",
					"CaseValue": 46
				},
				{
					"CaseName": "Invulnerable",
					"CaseValue": 47
				},
				{
					"CaseName": "Split",
					"CaseValue": 48
				},
				{
					"CaseName": "Trigger",
					"CaseValue": 49
				},
				{
					"CaseName": "Excavation",
					"CaseValue": 50
				},
				{
					"CaseName": "TombStone",
					"CaseValue": 51
				},
				{
					"CaseName": "InstantDeath",
					"CaseValue": 52
				},
				{
					"CaseName": "RangeRampage",
					"CaseValue": 53
				},
				{
					"CaseName": "Dot_wind",
					"CaseValue": 54
				},
				{
					"CaseName": "ConditionTrigger",
					"CaseValue": 55
				},
				{
					"CaseName": "Immolate",
					"CaseValue": 56
				},
				{
					"CaseName": "HpSwitch",
					"CaseValue": 57
				},
				{
					"CaseName": "StepingStone",
					"CaseValue": 58
				},
				{
					"CaseName": "EDER",
					"CaseValue": 59
				},
				{
					"CaseName": "Shippuden",
					"CaseValue": 60
				}
			]
		},
		{
			"EnumName": "HeroSkillDmgType",
			"Cases": [
				{
					"CaseName": "DmgValue",
					"CaseValue": 1
				},
				{
					"CaseName": "DmgRatio",
					"CaseValue": 2
				},
				{
					"CaseName": "MaxHpPerDmg",
					"CaseValue": 3
				},
				{
					"CaseName": "CurHpPerDmg",
					"CaseValue": 4
				},
				{
					"CaseName": "LossHpPerDmg",
					"CaseValue": 5
				},
				{
					"CaseName": "InheritedSkillRatio",
					"CaseValue": 6
				}
			]
		},
		{
			"EnumName": "HeroSkillEffectType",
			"Cases": [
				{
					"CaseName": "Buff",
					"CaseValue": 1
				},
				{
					"CaseName": "GuideLaser",
					"CaseValue": 2
				},
				{
					"CaseName": "BulletFire",
					"CaseValue": 3
				},
				{
					"CaseName": "IceRose",
					"CaseValue": 4
				},
				{
					"CaseName": "BulletWind",
					"CaseValue": 5
				},
				{
					"CaseName": "Laser",
					"CaseValue": 6
				},
				{
					"CaseName": "ElectricFierce",
					"CaseValue": 7
				},
				{
					"CaseName": "Airdrop",
					"CaseValue": 8
				},
				{
					"CaseName": "ElectricArc",
					"CaseValue": 9
				},
				{
					"CaseName": "BulletIce",
					"CaseValue": 10
				},
				{
					"CaseName": "DragonFlame",
					"CaseValue": 11
				},
				{
					"CaseName": "Car",
					"CaseValue": 12
				},
				{
					"CaseName": "Shrapnel",
					"CaseValue": 13
				},
				{
					"CaseName": "Cyclone",
					"CaseValue": 14
				},
				{
					"CaseName": "BulletPea",
					"CaseValue": 15
				},
				{
					"CaseName": "HandSword",
					"CaseValue": 16
				},
				{
					"CaseName": "Missile",
					"CaseValue": 17
				},
				{
					"CaseName": "MeleeAtk",
					"CaseValue": 18
				},
				{
					"CaseName": "SummonMonster",
					"CaseValue": 19
				},
				{
					"CaseName": "Aoe",
					"CaseValue": 20
				},
				{
					"CaseName": "UnlockTabLevelLimit",
					"CaseValue": 21
				},
				{
					"CaseName": "GetTab",
					"CaseValue": 22
				},
				{
					"CaseName": "UnlockTab",
					"CaseValue": 23
				},
				{
					"CaseName": "HomelanderLaser",
					"CaseValue": 24
				},
				{
					"CaseName": "ReplacedTab",
					"CaseValue": 25
				},
				{
					"CaseName": "BossEarLaser",
					"CaseValue": 26
				},
				{
					"CaseName": "BossMouseLaser",
					"CaseValue": 27
				},
				{
					"CaseName": "Boss2",
					"CaseValue": 28
				},
				{
					"CaseName": "MultipleMeleeAtk",
					"CaseValue": 29
				},
				{
					"CaseName": "SuicideBombing",
					"CaseValue": 30
				},
				{
					"CaseName": "Phase2Boss1",
					"CaseValue": 31
				},
				{
					"CaseName": "Phase2Boss6",
					"CaseValue": 32
				}
			]
		},
		{
			"EnumName": "HeroSkillHpRecoveryType",
			"Cases": [
				{
					"CaseName": "HpRecoveryValue",
					"CaseValue": 1
				},
				{
					"CaseName": "MaxHpPerRecovery",
					"CaseValue": 2
				},
				{
					"CaseName": "CurHpPerRecovery",
					"CaseValue": 3
				},
				{
					"CaseName": "LossHpPerRecovery",
					"CaseValue": 4
				},
				{
					"CaseName": "DmgPerRecovery",
					"CaseValue": 5
				}
			]
		},
		{
			"EnumName": "HeroSkillRangePolygon",
			"Cases": [
				{
					"CaseName": "Rectangle",
					"CaseValue": 1
				},
				{
					"CaseName": "Circular",
					"CaseValue": 2
				},
				{
					"CaseName": "Sector",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "HeroSkillType",
			"Cases": [
				{
					"CaseName": "HitSkill",
					"CaseValue": 1
				},
				{
					"CaseName": "NegativeSkill",
					"CaseValue": 2
				},
				{
					"CaseName": "GiftSkill",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "HeroStarUpPlan",
			"Cases": [
				{
					"CaseName": "HeroStarPlanLegendary",
					"CaseValue": 1
				},
				{
					"CaseName": "HeroStarPlanEpic",
					"CaseValue": 2
				},
				{
					"CaseName": "HeroStarPlanRare",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "HeroType",
			"Cases": [
				{
					"CaseName": "Magic",
					"CaseValue": 1
				},
				{
					"CaseName": "SuperPowers",
					"CaseValue": 2
				},
				{
					"CaseName": "Tech",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "IapBoothType",
			"Cases": [
				{
					"CaseName": "DiamondShop",
					"CaseValue": 1
				},
				{
					"CaseName": "DailySale",
					"CaseValue": 2
				},
				{
					"CaseName": "RegularPack",
					"CaseValue": 3
				},
				{
					"CaseName": "RegularBp",
					"CaseValue": 4
				},
				{
					"CaseName": "NoAds",
					"CaseValue": 5
				},
				{
					"CaseName": "2X",
					"CaseValue": 6
				},
				{
					"CaseName": "MonthCard",
					"CaseValue": 7
				},
				{
					"CaseName": "Fund",
					"CaseValue": 8
				},
				{
					"CaseName": "Sign",
					"CaseValue": 9
				},
				{
					"CaseName": "TurnTable",
					"CaseValue": 10
				},
				{
					"CaseName": "Sign7",
					"CaseValue": 11
				},
				{
					"CaseName": "Life",
					"CaseValue": 12
				}
			]
		},
		{
			"EnumName": "IapPackageType",
			"Cases": [
				{
					"CaseName": "Diamond",
					"CaseValue": 1
				},
				{
					"CaseName": "First",
					"CaseValue": 2
				},
				{
					"CaseName": "MonthCard",
					"CaseValue": 3
				},
				{
					"CaseName": "Fund",
					"CaseValue": 4
				},
				{
					"CaseName": "Regular",
					"CaseValue": 5
				},
				{
					"CaseName": "DailySale",
					"CaseValue": 6
				},
				{
					"CaseName": "AdFree",
					"CaseValue": 7
				},
				{
					"CaseName": "Life",
					"CaseValue": 8
				},
				{
					"CaseName": "SignBp",
					"CaseValue": 9
				},
				{
					"CaseName": "TurnActivity",
					"CaseValue": 10
				},
				{
					"CaseName": "Trigger",
					"CaseValue": 11
				},
				{
					"CaseName": "CoinFund",
					"CaseValue": 12
				},
				{
					"CaseName": "GeneFund",
					"CaseValue": 13
				},
				{
					"CaseName": "LordEquipFund",
					"CaseValue": 14
				},
				{
					"CaseName": "SunshineFund",
					"CaseValue": 15
				},
				{
					"CaseName": "Vip",
					"CaseValue": 16
				}
			]
		},
		{
			"EnumName": "ItemType",
			"Cases": [
				{
					"CaseName": "Chest",
					"CaseValue": 1
				},
				{
					"CaseName": "ChestSelfSelect",
					"CaseValue": 2
				},
				{
					"CaseName": "Diamond",
					"CaseValue": 3
				},
				{
					"CaseName": "Doughnut",
					"CaseValue": 4
				},
				{
					"CaseName": "SunShine",
					"CaseValue": 5
				},
				{
					"CaseName": "SummonCard",
					"CaseValue": 6
				},
				{
					"CaseName": "SeedBagCommon",
					"CaseValue": 7
				},
				{
					"CaseName": "SeedBagRare",
					"CaseValue": 8
				},
				{
					"CaseName": "SeedBagEpic",
					"CaseValue": 9
				},
				{
					"CaseName": "SeedBagLegendary",
					"CaseValue": 10
				},
				{
					"CaseName": "SeedBagMyth",
					"CaseValue": 11
				},
				{
					"CaseName": "LegendarySkillBook",
					"CaseValue": 12
				},
				{
					"CaseName": "EpicSkillBook",
					"CaseValue": 13
				},
				{
					"CaseName": "RareSkillBook",
					"CaseValue": 14
				},
				{
					"CaseName": "UniversalLegendaryHeroFragment",
					"CaseValue": 15
				},
				{
					"CaseName": "UniversalEpicHeroFragment",
					"CaseValue": 16
				},
				{
					"CaseName": "UniversalRareHeroFragment",
					"CaseValue": 17
				},
				{
					"CaseName": "RandomLegendaryHeroFragment",
					"CaseValue": 18
				},
				{
					"CaseName": "RandomEpicHeroFragment",
					"CaseValue": 19
				},
				{
					"CaseName": "RandomRareHeroFragment",
					"CaseValue": 20
				},
				{
					"CaseName": "Hero",
					"CaseValue": 21
				},
				{
					"CaseName": "HeroFragment",
					"CaseValue": 22
				},
				{
					"CaseName": "Avatar",
					"CaseValue": 23
				},
				{
					"CaseName": "AvatarFrame",
					"CaseValue": 24
				},
				{
					"CaseName": "RougeExp",
					"CaseValue": 25
				},
				{
					"CaseName": "SkillBook",
					"CaseValue": 26
				},
				{
					"CaseName": "Energy",
					"CaseValue": 27
				},
				{
					"CaseName": "HeroGeneFragment",
					"CaseValue": 28
				},
				{
					"CaseName": "Coin",
					"CaseValue": 29
				},
				{
					"CaseName": "HeroGeneralGeneFragment",
					"CaseValue": 30
				},
				{
					"CaseName": "GemRandom",
					"CaseValue": 31
				},
				{
					"CaseName": "Gem",
					"CaseValue": 32
				},
				{
					"CaseName": "GemReforge",
					"CaseValue": 33
				},
				{
					"CaseName": "LordEquipManual",
					"CaseValue": 34
				},
				{
					"CaseName": "LordEquipRandomManual",
					"CaseValue": 35
				},
				{
					"CaseName": "GemDraw",
					"CaseValue": 36
				},
				{
					"CaseName": "GuildExp",
					"CaseValue": 37
				},
				{
					"CaseName": "GuildCoin",
					"CaseValue": 38
				},
				{
					"CaseName": "ArenaCoin",
					"CaseValue": 39
				},
				{
					"CaseName": "TowerKey",
					"CaseValue": 40
				},
				{
					"CaseName": "CoinDungeonKey",
					"CaseValue": 41
				},
				{
					"CaseName": "GeneDungeonKey",
					"CaseValue": 42
				},
				{
					"CaseName": "LordEquipDungenKey",
					"CaseValue": 43
				},
				{
					"CaseName": "LordEquipGradeUpManual",
					"CaseValue": 44
				},
				{
					"CaseName": "RegularBpKey1",
					"CaseValue": 45
				},
				{
					"CaseName": "FreeAd",
					"CaseValue": 46
				},
				{
					"CaseName": "2X",
					"CaseValue": 47
				},
				{
					"CaseName": "SignKey1",
					"CaseValue": 48
				},
				{
					"CaseName": "FundKey1",
					"CaseValue": 49
				},
				{
					"CaseName": "MonthCard1",
					"CaseValue": 50
				},
				{
					"CaseName": "MonthCard2",
					"CaseValue": 51
				},
				{
					"CaseName": "turntablecoin",
					"CaseValue": 52
				},
				{
					"CaseName": "LordEquipMaterial",
					"CaseValue": 53
				},
				{
					"CaseName": "HeroQualityUp",
					"CaseValue": 54
				},
				{
					"CaseName": "SevenDayTasksScore",
					"CaseValue": 55
				},
				{
					"CaseName": "StarUpCommonItem",
					"CaseValue": 56
				},
				{
					"CaseName": "SunShineDungenKey",
					"CaseValue": 57
				},
				{
					"CaseName": "Vip",
					"CaseValue": 58
				}
			]
		},
		{
			"EnumName": "LaserTarget",
			"Cases": [
				{
					"CaseName": "Opposite",
					"CaseValue": 1
				},
				{
					"CaseName": "OppositeSideHeroDefense",
					"CaseValue": 2
				},
				{
					"CaseName": "OppositeSideHeroRanged",
					"CaseValue": 3
				},
				{
					"CaseName": "OppositeSideHeroSupport",
					"CaseValue": 4
				},
				{
					"CaseName": "OppoSideHeroFront",
					"CaseValue": 5
				},
				{
					"CaseName": "OppoSideHeroBehind",
					"CaseValue": 6
				},
				{
					"CaseName": "OppositeSideHeroHpLowest",
					"CaseValue": 7
				},
				{
					"CaseName": "PreSkillEffectTarget",
					"CaseValue": 8
				},
				{
					"CaseName": "PreSkillEffectTargetElseBoss",
					"CaseValue": 9
				},
				{
					"CaseName": "Boss",
					"CaseValue": 10
				},
				{
					"CaseName": "OwnForward",
					"CaseValue": 11
				}
			]
		},
		{
			"EnumName": "LevelType",
			"Cases": [
				{
					"CaseName": "TowerDefense",
					"CaseValue": 1
				},
				{
					"CaseName": "ParkOur",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "LogicType",
			"Cases": [
				{
					"CaseName": "And",
					"CaseValue": 1
				},
				{
					"CaseName": "Or",
					"CaseValue": 2
				},
				{
					"CaseName": "Invert",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "LordEquipGradeType",
			"Cases": [
				{
					"CaseName": "LordEquipGrade1",
					"CaseValue": 1
				},
				{
					"CaseName": "LordEquipGrade2",
					"CaseValue": 2
				},
				{
					"CaseName": "LordEquipGrade3",
					"CaseValue": 3
				},
				{
					"CaseName": "LordEquipGrade4",
					"CaseValue": 4
				},
				{
					"CaseName": "LordEquipGrade5",
					"CaseValue": 5
				},
				{
					"CaseName": "LordEquipGrade6",
					"CaseValue": 6
				},
				{
					"CaseName": "LordEquipGrade7",
					"CaseValue": 7
				}
			]
		},
		{
			"EnumName": "LordEquipType",
			"Cases": [
				{
					"CaseName": "LordEquipType1",
					"CaseValue": 1
				},
				{
					"CaseName": "LordEquipType2",
					"CaseValue": 2
				},
				{
					"CaseName": "LordEquipType3",
					"CaseValue": 3
				},
				{
					"CaseName": "LordEquipType4",
					"CaseValue": 4
				},
				{
					"CaseName": "LordEquipType5",
					"CaseValue": 5
				},
				{
					"CaseName": "LordEquipType6",
					"CaseValue": 6
				}
			]
		},
		{
			"EnumName": "MapEventType",
			"Cases": [
				{
					"CaseName": "Monster",
					"CaseValue": 1
				},
				{
					"CaseName": "Prop",
					"CaseValue": 2
				},
				{
					"CaseName": "Buff",
					"CaseValue": 3
				},
				{
					"CaseName": "Obstacle",
					"CaseValue": 4
				},
				{
					"CaseName": "Reward",
					"CaseValue": 5
				}
			]
		},
		{
			"EnumName": "MissileTarget",
			"Cases": [
				{
					"CaseName": "Opposite",
					"CaseValue": 1
				},
				{
					"CaseName": "OppositeSideHeroDefense",
					"CaseValue": 2
				},
				{
					"CaseName": "OppositeSideHeroRanged",
					"CaseValue": 3
				},
				{
					"CaseName": "OppositeSideHeroSupport",
					"CaseValue": 4
				},
				{
					"CaseName": "OppoSideHeroFront",
					"CaseValue": 5
				},
				{
					"CaseName": "OppoSideHeroBehind",
					"CaseValue": 6
				},
				{
					"CaseName": "OppositeSideHeroHpLowest",
					"CaseValue": 7
				},
				{
					"CaseName": "PreSkillEffectTarget",
					"CaseValue": 8
				},
				{
					"CaseName": "PreSkillEffectTargetElseBoss",
					"CaseValue": 9
				},
				{
					"CaseName": "Boss",
					"CaseValue": 10
				}
			]
		},
		{
			"EnumName": "MonsterCareerType",
			"Cases": [
				{
					"CaseName": "Melee",
					"CaseValue": 1
				},
				{
					"CaseName": "Ranger",
					"CaseValue": 2
				},
				{
					"CaseName": "Tank",
					"CaseValue": 3
				},
				{
					"CaseName": "Assassin",
					"CaseValue": 4
				},
				{
					"CaseName": "AirMelee",
					"CaseValue": 5
				},
				{
					"CaseName": "AirRanger",
					"CaseValue": 6
				},
				{
					"CaseName": "Suicide",
					"CaseValue": 7
				},
				{
					"CaseName": "LongRanger",
					"CaseValue": 8
				}
			]
		},
		{
			"EnumName": "MonsterGrade",
			"Cases": [
				{
					"CaseName": "Common",
					"CaseValue": 1
				},
				{
					"CaseName": "Elite",
					"CaseValue": 2
				},
				{
					"CaseName": "Boss",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "MonsterPosType",
			"Cases": [
				{
					"CaseName": "Ground",
					"CaseValue": 1
				},
				{
					"CaseName": "Air",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "MonsterRefreshType",
			"Cases": [
				{
					"CaseName": "InitialRefresh",
					"CaseValue": 1
				},
				{
					"CaseName": "DelayRefresh",
					"CaseValue": 2
				},
				{
					"CaseName": "UpstreamDeathCntRefresh",
					"CaseValue": 3
				},
				{
					"CaseName": "PassPlotsRefresh",
					"CaseValue": 4
				},
				{
					"CaseName": "AfterRefreshing",
					"CaseValue": 5
				},
				{
					"CaseName": "AfterTheGameStarts",
					"CaseValue": 6
				}
			]
		},
		{
			"EnumName": "PurchaseLimitType",
			"Cases": [
				{
					"CaseName": "DailyLimit",
					"CaseValue": 1
				},
				{
					"CaseName": "WeeklyLimit",
					"CaseValue": 2
				},
				{
					"CaseName": "MonthlyLimit",
					"CaseValue": 3
				},
				{
					"CaseName": "LifeLimit",
					"CaseValue": 4
				},
				{
					"CaseName": "UnLimit",
					"CaseValue": 5
				}
			]
		},
		{
			"EnumName": "RougeTabType",
			"Cases": [
				{
					"CaseName": "EffectTab",
					"CaseValue": 1
				},
				{
					"CaseName": "UnlockTab",
					"CaseValue": 2
				},
				{
					"CaseName": "ConfigTab",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "ShopType",
			"Cases": [
				{
					"CaseName": "GuildShop",
					"CaseValue": 1
				},
				{
					"CaseName": "ArenaShop",
					"CaseValue": 2
				},
				{
					"CaseName": "BlackShop",
					"CaseValue": 3
				},
				{
					"CaseName": "LevelShop",
					"CaseValue": 4
				}
			]
		},
		{
			"EnumName": "SkillAttrOverlyingType",
			"Cases": [
				{
					"CaseName": "AddOverlying",
					"CaseValue": 1
				},
				{
					"CaseName": "MulOverlying",
					"CaseValue": 2
				},
				{
					"CaseName": "EnumOverlying",
					"CaseValue": 3
				},
				{
					"CaseName": "NoOverlying",
					"CaseValue": 4
				}
			]
		},
		{
			"EnumName": "SkillDmgType",
			"Cases": [
				{
					"CaseName": "Electrical",
					"CaseValue": 1
				},
				{
					"CaseName": "Wind",
					"CaseValue": 2
				},
				{
					"CaseName": "Light",
					"CaseValue": 3
				},
				{
					"CaseName": "Fire",
					"CaseValue": 4
				},
				{
					"CaseName": "Ice",
					"CaseValue": 5
				},
				{
					"CaseName": "Physical",
					"CaseValue": 6
				}
			]
		},
		{
			"EnumName": "SkillType",
			"Cases": [
				{
					"CaseName": "ActiveSkill",
					"CaseValue": 1
				},
				{
					"CaseName": "PassiveSkill",
					"CaseValue": 2
				},
				{
					"CaseName": "AuraSkill",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "TaskCounterType",
			"Cases": [
				{
					"CaseName": "Reset",
					"CaseValue": 1
				},
				{
					"CaseName": "Total",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "TaskType",
			"Cases": [
				{
					"CaseName": "Login",
					"CaseValue": 1
				},
				{
					"CaseName": "TotalLogin",
					"CaseValue": 2
				},
				{
					"CaseName": "LevelBegin",
					"CaseValue": 3
				},
				{
					"CaseName": "LevelPass",
					"CaseValue": 4
				},
				{
					"CaseName": "LevelPassTo",
					"CaseValue": 5
				},
				{
					"CaseName": "ItemBurn",
					"CaseValue": 6
				},
				{
					"CaseName": "TotalItemBurn",
					"CaseValue": 7
				},
				{
					"CaseName": "HeroLevelUp",
					"CaseValue": 8
				},
				{
					"CaseName": "HeroLevelUpTo",
					"CaseValue": 9
				},
				{
					"CaseName": "HeroStarUp",
					"CaseValue": 10
				},
				{
					"CaseName": "HeroStarUpTo",
					"CaseValue": 11
				},
				{
					"CaseName": "HeroSkillUp",
					"CaseValue": 12
				},
				{
					"CaseName": "HeroSkillUpTo",
					"CaseValue": 13
				},
				{
					"CaseName": "HeroGeneUp",
					"CaseValue": 14
				},
				{
					"CaseName": "HeroGeneUpTo",
					"CaseValue": 15
				},
				{
					"CaseName": "KillMonster",
					"CaseValue": 16
				},
				{
					"CaseName": "TotalKillMonster",
					"CaseValue": 17
				},
				{
					"CaseName": "claim_idle_reward",
					"CaseValue": 18
				},
				{
					"CaseName": "claim_pass_level_reward",
					"CaseValue": 19
				},
				{
					"CaseName": "Chat",
					"CaseValue": 20
				},
				{
					"CaseName": "Nigger",
					"CaseValue": 21
				},
				{
					"CaseName": "Rename",
					"CaseValue": 22
				},
				{
					"CaseName": "Avatar",
					"CaseValue": 23
				},
				{
					"CaseName": "JoinGuild",
					"CaseValue": 24
				},
				{
					"CaseName": "Sweep",
					"CaseValue": 25
				},
				{
					"CaseName": "HeroSummon",
					"CaseValue": 26
				},
				{
					"CaseName": "HeroConfig",
					"CaseValue": 27
				},
				{
					"CaseName": "LordEquipLvlUp",
					"CaseValue": 28
				},
				{
					"CaseName": "LordEquipLvlUpTo",
					"CaseValue": 29
				},
				{
					"CaseName": "GemCraft",
					"CaseValue": 30
				},
				{
					"CaseName": "GemSummon",
					"CaseValue": 31
				},
				{
					"CaseName": "Shopping",
					"CaseValue": 32
				},
				{
					"CaseName": "DungeonChallenge",
					"CaseValue": 33
				},
				{
					"CaseName": "DungeonSweep",
					"CaseValue": 34
				},
				{
					"CaseName": "ArenaChallenge",
					"CaseValue": 35
				},
				{
					"CaseName": "TotalActivateHero",
					"CaseValue": 36
				},
				{
					"CaseName": "TotalQualityHero",
					"CaseValue": 37
				},
				{
					"CaseName": "TotalMainStar",
					"CaseValue": 38
				},
				{
					"CaseName": "DailyScore",
					"CaseValue": 39
				},
				{
					"CaseName": "WeeklyScore",
					"CaseValue": 40
				},
				{
					"CaseName": "DungeonLevel",
					"CaseValue": 41
				},
				{
					"CaseName": "GemQuality",
					"CaseValue": 42
				},
				{
					"CaseName": "LordEquipGrade",
					"CaseValue": 43
				},
				{
					"CaseName": "LordEquipGradeTo",
					"CaseValue": 44
				},
				{
					"CaseName": "EnergyFactory",
					"CaseValue": 45
				},
				{
					"CaseName": "TotalHeroLevelUp",
					"CaseValue": 46
				},
				{
					"CaseName": "TotalHeroStarUp",
					"CaseValue": 47
				},
				{
					"CaseName": "TotalHeroSkillUp",
					"CaseValue": 48
				},
				{
					"CaseName": "TotalHeroGeneUp",
					"CaseValue": 49
				},
				{
					"CaseName": "TotalHeroSummon",
					"CaseValue": 50
				},
				{
					"CaseName": "TotalGemSummon",
					"CaseValue": 51
				},
				{
					"CaseName": "TotalGemCraft",
					"CaseValue": 52
				},
				{
					"CaseName": "TotalLordEquipLvlUp",
					"CaseValue": 53
				},
				{
					"CaseName": "TotalLordEquipGrade",
					"CaseValue": 54
				},
				{
					"CaseName": "CompleteTask",
					"CaseValue": 55
				},
				{
					"CaseName": "TotalGemSummon_1",
					"CaseValue": 56
				},
				{
					"CaseName": "TotalGemSummon_2",
					"CaseValue": 57
				},
				{
					"CaseName": "ChapterTaskComplete",
					"CaseValue": 58
				}
			]
		},
		{
			"EnumName": "TriggerPackType",
			"Cases": [
				{
					"CaseName": "LevelPass",
					"CaseValue": 1
				},
				{
					"CaseName": "GemDraw",
					"CaseValue": 2
				},
				{
					"CaseName": "HeroSummon",
					"CaseValue": 3
				}
			]
		}
	]
}`
