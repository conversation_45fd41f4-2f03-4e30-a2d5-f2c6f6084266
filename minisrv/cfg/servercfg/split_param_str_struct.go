// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type SplitParamStr struct {
	MonsterType    []int32            `json:"MonsterType"` // 怪物类型
	MonsterTypeRef []*MonsterTableCfg `json:"-"`           // 怪物类型
	MonsterCnt     []int32            `json:"MonsterCnt"`  // 怪物数量
	Ratio          []float32          `json:"Ratio"`       // 攻防血占比
}

func NewSplitParamStr() *SplitParamStr {
	return &SplitParamStr{
		MonsterType:    []int32{},
		MonsterTypeRef: []*MonsterTableCfg{},
		MonsterCnt:     []int32{},
		Ratio:          []float32{},
	}
}

func (s *SplitParamStr) bindRefs(c *Configs) {
	for _, e := range s.MonsterType {
		cfgoRefRecord := c.MonsterTable.Get(e)
		s.MonsterTypeRef = append(s.MonsterTypeRef, cfgoRefRecord)
	}
}
