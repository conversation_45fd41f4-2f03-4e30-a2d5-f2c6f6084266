package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
	"strconv"
)

func init() {
	http_post.AddPostHandler("addItem", addItem)
}
func addItem(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid") // 获取查询参数q的值
		if uidStr != "" {
			uid, _ := strconv.ParseInt(uidStr, 10, 64)
			//添加物品
			itemIdStr := values.Get("itemId")       // 获取查询参数q的值
			itemCountStr := values.Get("itemCount") // 获取查询参数q的值
			seq := util.GenerateSequenceString()
			itemId, _ := strconv.ParseInt(itemIdStr, 10, 32)
			if itemIdStr != "" && itemCountStr != "" {
				if itemCountStr == "clear" {
					itemCount := model.GetConsumableQuantity(ctx, uid, int32(itemId))
					consumable.DeductItem(ctx, uid, int32(itemId), itemCount, bi.ItemFlowReason_TEST, "", seq)
				} else {

					itemCount, _ := strconv.ParseInt(itemCountStr, 10, 64)
					consumable.AddItem(ctx, uid, int32(itemId), itemCount, bi.ItemFlowReason_TEST, "", seq)
				}

			}

			itemModels, _ := model.ExportAllConsumables(ctx, uid)
			for _, v := range itemModels {
				fmt.Fprintf(w, "uid : %s, item : %s, count : %s \n", v.Uid, v.ConfigId, v.Quantity)
			}
			return
		}
		fmt.Fprintf(w, "no uid")
	})

}
