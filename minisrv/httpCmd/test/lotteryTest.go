package test

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/hero"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
	"strconv"
)

func init() {
	http_post.AddPostHandler("lotteryTest", lotteryTest)
}
func lotteryTest(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid")     // 获取查询参数q的值
		countStr := values.Get("count") // 获取查询参数q的值
		if uidStr == "" {
			fmt.Fprintf(w, "need uid")
			return
		}
		if countStr == "" {
			fmt.Fprintf(w, "need countStr")
			return
		}
		uid, _ := strconv.ParseInt(uidStr, 10, 64)
		count, _ := strconv.ParseInt(countStr, 10, 64)
		unlock := model.LockUser(ctx, uid)
		defer unlock()
		cost := map[int32]int64{}
		biRewards := map[int32]int64{}
		for i := 0; i < int(count); i++ {
			model.AddItem(ctx, uid, 4, 10, bi.ItemFlowReason_TEST, "", "")
			hero.HeroLottery(ctx, uid, 1, false, &cost, &biRewards)
		}
		fmt.Fprintf(w, "%v", biRewards)

	})

}
