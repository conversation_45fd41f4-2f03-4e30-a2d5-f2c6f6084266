package day7quest

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
	"strconv"
	"time"
)

func init() {
	quest.QuestCheckFuncMap[int32(GetActivityType())] = GetActivityInfo
}

func GetActivityInfo(ctx context.Context, uid int64) *wrpc.ActivityInfo {
	user, _ := model.GetUserModel(ctx, uid)
	startTime := user.Ctime().Time()
	//获取startTime当天的0点
	startTime = time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, startTime.Location())
	endTime := startTime.Add(14 * 24 * time.Hour)
	if endTime.Before(time.Now()) {
		userData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeDay7NurseMailSend))
		if userData.Value() == 0 {
			SendNurseMail(ctx, uid)
			userData.SetValue(ctx, 1)
		}
		return nil
	}
	return &wrpc.ActivityInfo{
		StartTime:    startTime.Unix(),
		PublicTime:   startTime.Add(12 * 24 * time.Hour).Unix(),
		EndTime:      startTime.Add(14 * 24 * time.Hour).Unix(),
		Status:       1,
		ActivityName: "七日任务",
		ActivityType: constDef.ActivityTypeDay7Quest,
		ActivityId:   1,
	}
}

// 发送保姆邮件
func SendNurseMail(ctx context.Context, uid int64) {
	chestLines := cfg_mgr.Cfg.SevenDayTasksScoreTable.FilterSlice(func(v *servercfg.SevenDayTasksScoreTableCfg) bool {
		return true
	})

	// 获取用户已领取的宝箱记录
	userData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeDay7QuestChest))
	collectedChests := userData.Content()
	if collectedChests == nil {
		collectedChests = make(map[int32]int64)
	}

	// 构建邮件附件，包含所有未领取的宝箱奖励
	attachment := make(map[int32]int64)
	mailLine := cfg_mgr.Cfg.MailTable.Get(constDef.MAIL_SEVENDAYTASK_REISSUE)
	// 检查用户选择的奖励
	selectId := int32(1)
	selectUserData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeDay7QuestSelect))
	if selectUserData.Value() > 0 {
		selectId = int32(selectUserData.Value())
	}

	// 遍历所有宝箱，将未领取的宝箱奖励添加到附件中
	hasUnclaimedRewards := false
	for _, chestLine := range chestLines {
		// 如果宝箱已经领取，跳过
		if collectedChests[chestLine.Id] > 0 {
			continue
		}
		//检查道具是否充足
		if model.GetConsumableQuantity(ctx, uid, chestLine.ScoreType) < int64(chestLine.ScoreValue) {
			continue
		}

		// 添加基础奖励
		for _, reward := range chestLine.Reward {
			attachment[reward.RewardType] += int64(reward.RewardValue)
			hasUnclaimedRewards = true
		}

		// 添加选择性奖励
		if chestLine.Day7RewardRef != nil {
			if len(chestLine.Day7RewardRef.Reward) >= int(selectId) {
				selectReawrd := chestLine.Day7RewardRef.Reward[selectId-1]
				if selectReawrd != nil {
					attachment[selectReawrd.RewardType] += int64(selectReawrd.RewardValue)
					hasUnclaimedRewards = true
				}
			}

		}

		// 标记该宝箱为已领取
		collectedChests[chestLine.Id] = 1
	}

	// 2. 处理未领取的任务奖励
	taskLines := cfg_mgr.Cfg.SevenDayTasksTable.GetAll()

	// 遍历所有任务，将未领取的任务奖励添加到附件中
	for _, taskLine := range taskLines {
		// 获取任务完成状态
		quest, err := model.GetActivityTaskModel(ctx, uid, taskLine.Id)
		if err != nil || quest == nil {
			continue
		}

		// 只处理已完成但未领取奖励的任务
		if quest.Status() == int32(minirpc.QuestComplete) {
			// 添加任务奖励
			for i := range taskLine.RewardType {
				attachment[taskLine.RewardType[i]] += int64(taskLine.RewardValue[i])
				hasUnclaimedRewards = true
			}

			// 标记任务为已领取奖励
			quest.SetStatus(ctx, int32(minirpc.QuestRewarded))
		}
	}

	//attachment中去掉 id 为111的道具
	delete(attachment, constDef.ITEM_7_DAY_TASKS_SCORE)

	// 如果有未领取的奖励，发送邮件并更新用户数据
	if hasUnclaimedRewards {
		// 获取当前时间并设置邮件过期时间（7天后）
		expireTime := time.Now().Unix() + 7*24*60*60

		// 发送邮件
		userInfo, _ := model.GetUserModel(ctx, uid)
		bodyAttr := []string{userInfo.Name()}
		model.SendSystemMail(ctx, uid, 1, mailLine.Title, mailLine.Subject, mailLine.Image, attachment, expireTime, nil, bodyAttr)

		// 更新用户已领取的宝箱记录
		userData.SetContent(ctx, collectedChests)
	}
}

func GetActivityType() minirpc.ActivityType {
	return minirpc.ActivityTypeDay7Quest
}

func InitQuest(ctx context.Context, uid int64) {
	lines := cfg_mgr.Cfg.SevenDayTasksTable.GetAll()
	for _, line := range lines {
		quest, _ := model.GetActivityTaskModel(ctx, uid, line.Id)
		if quest == nil {
			orm.Create[*model.ActivityTask](ctx, &minirpc.ActivityTask{
				Uid:          uid,
				QuestId:      line.Id,
				Type:         int32(line.TaskType),
				Progress:     0,
				Status:       int32(minirpc.QuestActivity),
				ActivityType: int32(GetActivityType()),
			})
		}
	}
}

func CollectDay7Reward(ctx context.Context, uid int64, questId int32) ([]*wrpc.Rewards, error) {
	activityInfo := GetActivityInfo(ctx, uid)
	if activityInfo == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("activity is not active")
	}

	quest, err := model.GetActivityTaskModel(ctx, uid, questId)
	if err != nil {
		return nil, kdmerr.SysDBError.WrapError(err)
	}
	if quest == nil || quest.Status() != int32(minirpc.QuestComplete) {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("quest is error")
	}
	line := cfg_mgr.Cfg.SevenDayTasksTable.Get(questId)
	if line == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("quest is error")
	}
	if activityInfo.StartTime+int64((line.Day-1)*24*60*60) > time.Now().Unix() {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("quest is not open")
	}
	quest.SetStatus(ctx, int32(minirpc.QuestRewarded))
	var retRewards []*wrpc.Rewards
	rewardMap := make(map[int32]int32)
	for k, _ := range line.RewardType {
		model.AddItem(ctx, uid, line.RewardType[k], int64(line.RewardValue[k]), bi.ItemFlowReason_Day7_Quest, strconv.Itoa(int(questId)), util.GenerateSequenceString())
		retRewards = append(retRewards, &wrpc.Rewards{ItemId: line.RewardType[k], ItemValue: int64(line.RewardValue[k])})
		rewardMap[line.RewardType[k]] = line.RewardValue[k]
	}
	/**
	info
	"活动开启时间（开启时间时间戳）
	任务所处开放天数，
	任务类别（子类）"
	*/
	info := map[string]string{}
	info["startTime"] = strconv.Itoa(int(time.Now().Unix()))
	info["day"] = strconv.Itoa(int(line.Day))
	info["type"] = line.TaskType.String()
	infoJson, _ := json.Marshal(info)
	// 记录奖励领取日志
	logger.LogCustom(ctx, uid, "common_task_group", logger.NewDetail().
		Put("action", "get_task_rewards").
		Put("group_type", 1003). // 联盟任务类型
		Put("group_id", line.Id).
		Put("task_id", questId).
		Put("rewards", util.FormatRewards(rewardMap)).
		Put("info", string(infoJson)))
	return retRewards, nil
}

func CollectDay7Chest(ctx context.Context, uid int64, chestId int32) ([]*wrpc.Rewards, error) {
	chestLine := cfg_mgr.Cfg.SevenDayTasksScoreTable.Get(chestId)

	userData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeDay7QuestChest))
	content := userData.Content()
	if content == nil {
		content = make(map[int32]int64)
	}
	if chestLine == nil || content[chestId] > 0 {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("chest id is error")
	}
	if model.GetConsumableQuantity(ctx, uid, chestLine.ScoreType) < int64(chestLine.ScoreValue) {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("chest id is error")
	}
	//consumable.DeductItem(ctx, uid, chestLine.ScoreType, int64(chestLine.ScoreValue), bi.ItemFlowReason_Daily_Task_Reward, strconv.Itoa(int(chestId)), util.GenerateSequenceString())
	retRewards := []*wrpc.Rewards{}
	content[chestId] = 1
	userData.SetContent(ctx, content)
	// 构建奖励map用于日志记录
	rewardMap := make(map[int32]int32)
	for k, _ := range chestLine.Reward {
		model.AddItem(ctx, uid, chestLine.Reward[k].RewardType, int64(chestLine.Reward[k].RewardValue), bi.ItemFlowReason_Day7_Quest_Chest, strconv.Itoa(int(chestId)), util.GenerateSequenceString())
		retRewards = append(retRewards, &wrpc.Rewards{ItemId: chestLine.Reward[k].RewardType, ItemValue: int64(chestLine.Reward[k].RewardValue)})
		rewardMap[chestLine.Reward[k].RewardType] = chestLine.Reward[k].RewardValue
	}
	if chestLine.Day7RewardRef != nil {
		selectId := int32(1)
		selectUserData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeDay7QuestSelect))
		if selectUserData.Value() > 0 {
			selectId = int32(selectUserData.Value())
		}

		if len(chestLine.Day7RewardRef.Reward) >= int(selectId) {
			selectReawrd := chestLine.Day7RewardRef.Reward[selectId-1]
			if selectReawrd != nil {
				model.AddItem(ctx, uid, selectReawrd.RewardType, int64(selectReawrd.RewardValue), bi.ItemFlowReason_Day7_Quest_Chest, strconv.Itoa(int(chestId)), util.GenerateSequenceString())
				retRewards = append(retRewards, &wrpc.Rewards{ItemId: selectReawrd.RewardType, ItemValue: int64(selectReawrd.RewardValue)})
				rewardMap[selectReawrd.RewardType] = selectReawrd.RewardValue
			}
		}

	}

	logger.LogCustom(ctx, uid, "common_task_group", logger.NewDetail().
		Put("action", "get_step_rewards").
		Put("group_type", 1001). // 每日任务组
		Put("group_id", chestLine.Id).
		Put("step_id", strconv.Itoa(int(chestId))).
		Put("cur_step", strconv.Itoa(int(userData.Value()))).
		Put("rewards", util.FormatRewards(rewardMap)))

	return retRewards, nil
}

func SelectDay7Reward(ctx context.Context, uid int64, selectId int32) error {
	userData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeDay7QuestSelect))
	userData.SetValue(ctx, int64(selectId))
	return nil
}
