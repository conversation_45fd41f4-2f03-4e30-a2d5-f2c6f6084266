{"1001": {"Id": 1001, "StringId": "hero_3_0_name", "Key": "hero_3_0_name"}, "1002": {"Id": 1002, "StringId": "hero_3_1_name", "Key": "hero_3_1_name"}, "1003": {"Id": 1003, "StringId": "hero_3_2_name", "Key": "hero_3_2_name"}, "1004": {"Id": 1004, "StringId": "hero_3_3_name", "Key": "hero_3_3_name"}, "1005": {"Id": 1005, "StringId": "hero_3_4_name", "Key": "hero_3_4_name"}, "1006": {"Id": 1006, "StringId": "hero_3_5_name", "Key": "hero_3_5_name"}, "1007": {"Id": 1007, "StringId": "hero_2_0_name", "Key": "hero_2_0_name"}, "1008": {"Id": 1008, "StringId": "hero_2_1_name", "Key": "hero_2_1_name"}, "1009": {"Id": 1009, "StringId": "hero_2_2_name", "Key": "hero_2_2_name"}, "1010": {"Id": 1010, "StringId": "hero_2_3_name", "Key": "hero_2_3_name"}, "1011": {"Id": 1011, "StringId": "hero_2_4_name", "Key": "hero_2_4_name"}, "1012": {"Id": 1012, "StringId": "hero_2_5_name", "Key": "hero_2_5_name"}, "1013": {"Id": 1013, "StringId": "hero_2_6_name", "Key": "hero_2_6_name"}, "1014": {"Id": 1014, "StringId": "hero_1_0_name", "Key": "hero_1_0_name"}, "1015": {"Id": 1015, "StringId": "hero_1_1_name", "Key": "hero_1_1_name"}, "1016": {"Id": 1016, "StringId": "hero_defense_name", "Key": "hero_defense_name"}, "1017": {"Id": 1017, "StringId": "hero_ranged_name", "Key": "hero_ranged_name"}, "1018": {"Id": 1018, "StringId": "hero_support_name", "Key": "hero_support_name"}, "1019": {"Id": 1019, "StringId": "hero_defense_desc", "Key": "hero_defense_desc"}, "1020": {"Id": 1020, "StringId": "hero_ranged_desc", "Key": "hero_ranged_desc"}, "1021": {"Id": 1021, "StringId": "hero_support_desc", "Key": "hero_support_desc"}, "1022": {"Id": 1022, "StringId": "hero_type_magic_name", "Key": "hero_type_magic_name"}, "1023": {"Id": 1023, "StringId": "hero_type_superpower_name", "Key": "hero_type_superpower_name"}, "1024": {"Id": 1024, "StringId": "hero_type_tech_name", "Key": "hero_type_tech_name"}, "1025": {"Id": 1025, "StringId": "hero_legendary_name", "Key": "hero_legendary_name"}, "1026": {"Id": 1026, "StringId": "hero_epic_name", "Key": "hero_epic_name"}, "1027": {"Id": 1027, "StringId": "hero_rare_name", "Key": "hero_rare_name"}, "1028": {"Id": 1028, "StringId": "skill_dmg_type_electrical_name", "Key": "skill_dmg_type_electrical_name"}, "1029": {"Id": 1029, "StringId": "skill_dmg_type_wind_name", "Key": "skill_dmg_type_wind_name"}, "1030": {"Id": 1030, "StringId": "skill_dmg_type_light_name", "Key": "skill_dmg_type_light_name"}, "1031": {"Id": 1031, "StringId": "skill_dmg_type_fire_name", "Key": "skill_dmg_type_fire_name"}, "1032": {"Id": 1032, "StringId": "skill_dmg_type_ice_name", "Key": "skill_dmg_type_ice_name"}, "1033": {"Id": 1033, "StringId": "skill_dmg_type_physical_name", "Key": "skill_dmg_type_physical_name"}, "1034": {"Id": 1034, "StringId": "skill_dmg_type_electrical_desc", "Key": "skill_dmg_type_electrical_desc"}, "1035": {"Id": 1035, "StringId": "skill_dmg_type_wind_desc", "Key": "skill_dmg_type_wind_desc"}, "1036": {"Id": 1036, "StringId": "skill_dmg_type_light_desc", "Key": "skill_dmg_type_light_desc"}, "1037": {"Id": 1037, "StringId": "skill_dmg_type_fire_desc", "Key": "skill_dmg_type_fire_desc"}, "1038": {"Id": 1038, "StringId": "skill_dmg_type_ice_desc", "Key": "skill_dmg_type_ice_desc"}, "1039": {"Id": 1039, "StringId": "skill_dmg_type_physical_desc", "Key": "skill_dmg_type_physical_desc"}, "1040": {"Id": 1040, "StringId": "hit_skill_name", "Key": "hit_skill_name"}, "1041": {"Id": 1041, "StringId": "negative_skill_name", "Key": "negative_skill_name"}, "1042": {"Id": 1042, "StringId": "gift_skill_name", "Key": "gift_skill_name"}, "1043": {"Id": 1043, "StringId": "hero_3_0_hit_skill_group_name", "Key": "hero_3_0_hit_skill_group_name"}, "1044": {"Id": 1044, "StringId": "hero_3_0_passive_skill_group_name", "Key": "hero_3_0_passive_skill_group_name"}, "1045": {"Id": 1045, "StringId": "hero_3_0_gift_skill_group_name", "Key": "hero_3_0_gift_skill_group_name"}, "1046": {"Id": 1046, "StringId": "hero_3_1_hit_skill_group_name", "Key": "hero_3_1_hit_skill_group_name"}, "1047": {"Id": 1047, "StringId": "hero_3_1_passive_skill_group_name", "Key": "hero_3_1_passive_skill_group_name"}, "1048": {"Id": 1048, "StringId": "hero_3_1_gift_skill_group_name", "Key": "hero_3_1_gift_skill_group_name"}, "1049": {"Id": 1049, "StringId": "hero_3_2_hit_skill_group_name", "Key": "hero_3_2_hit_skill_group_name"}, "1050": {"Id": 1050, "StringId": "hero_3_2_passive_skill_group_name", "Key": "hero_3_2_passive_skill_group_name"}, "1051": {"Id": 1051, "StringId": "hero_3_2_gift_skill_group_name", "Key": "hero_3_2_gift_skill_group_name"}, "1052": {"Id": 1052, "StringId": "hero_3_3_hit_skill_group_name", "Key": "hero_3_3_hit_skill_group_name"}, "1053": {"Id": 1053, "StringId": "hero_3_3_passive_skill_group_name", "Key": "hero_3_3_passive_skill_group_name"}, "1054": {"Id": 1054, "StringId": "hero_3_3_gift_skill_group_name", "Key": "hero_3_3_gift_skill_group_name"}, "1055": {"Id": 1055, "StringId": "hero_3_4_hit_skill_group_name", "Key": "hero_3_4_hit_skill_group_name"}, "1056": {"Id": 1056, "StringId": "hero_3_4_passive_skill_group_name", "Key": "hero_3_4_passive_skill_group_name"}, "1057": {"Id": 1057, "StringId": "hero_3_4_gift_skill_group_name", "Key": "hero_3_4_gift_skill_group_name"}, "1058": {"Id": 1058, "StringId": "hero_3_5_hit_skill_group_name", "Key": "hero_3_5_hit_skill_group_name"}, "1059": {"Id": 1059, "StringId": "hero_3_5_passive_skill_group_name", "Key": "hero_3_5_passive_skill_group_name"}, "1060": {"Id": 1060, "StringId": "hero_3_5_gift_skill_group_name", "Key": "hero_3_5_gift_skill_group_name"}, "1061": {"Id": 1061, "StringId": "hero_2_0_hit_skill_group_name", "Key": "hero_2_0_hit_skill_group_name"}, "1062": {"Id": 1062, "StringId": "hero_2_0_passive_skill_group_name", "Key": "hero_2_0_passive_skill_group_name"}, "1063": {"Id": 1063, "StringId": "hero_2_0_gift_skill_group_name", "Key": "hero_2_0_gift_skill_group_name"}, "1064": {"Id": 1064, "StringId": "hero_2_1_hit_skill_group_name", "Key": "hero_2_1_hit_skill_group_name"}, "1065": {"Id": 1065, "StringId": "hero_2_1_passive_skill_group_name", "Key": "hero_2_1_passive_skill_group_name"}, "1066": {"Id": 1066, "StringId": "hero_2_1_gift_skill_group_name", "Key": "hero_2_1_gift_skill_group_name"}, "1067": {"Id": 1067, "StringId": "hero_2_2_hit_skill_group_name", "Key": "hero_2_2_hit_skill_group_name"}, "1068": {"Id": 1068, "StringId": "hero_2_2_passive_skill_group_name", "Key": "hero_2_2_passive_skill_group_name"}, "1069": {"Id": 1069, "StringId": "hero_2_2_gift_skill_group_name", "Key": "hero_2_2_gift_skill_group_name"}, "1070": {"Id": 1070, "StringId": "hero_2_3_hit_skill_group_name", "Key": "hero_2_3_hit_skill_group_name"}, "1071": {"Id": 1071, "StringId": "hero_2_3_passive_skill_group_name", "Key": "hero_2_3_passive_skill_group_name"}, "1072": {"Id": 1072, "StringId": "hero_2_3_gift_skill_group_name", "Key": "hero_2_3_gift_skill_group_name"}, "1073": {"Id": 1073, "StringId": "hero_2_4_hit_skill_group_name", "Key": "hero_2_4_hit_skill_group_name"}, "1074": {"Id": 1074, "StringId": "hero_2_4_passive_skill_group_name", "Key": "hero_2_4_passive_skill_group_name"}, "1075": {"Id": 1075, "StringId": "hero_2_4_gift_skill_group_name", "Key": "hero_2_4_gift_skill_group_name"}, "1076": {"Id": 1076, "StringId": "hero_2_5_hit_skill_group_name", "Key": "hero_2_5_hit_skill_group_name"}, "1077": {"Id": 1077, "StringId": "hero_2_5_passive_skill_group_name", "Key": "hero_2_5_passive_skill_group_name"}, "1078": {"Id": 1078, "StringId": "hero_2_5_gift_skill_group_name", "Key": "hero_2_5_gift_skill_group_name"}, "1079": {"Id": 1079, "StringId": "hero_2_6_hit_skill_group_name", "Key": "hero_2_6_hit_skill_group_name"}, "1080": {"Id": 1080, "StringId": "hero_2_6_passive_skill_group_name", "Key": "hero_2_6_passive_skill_group_name"}, "1081": {"Id": 1081, "StringId": "hero_2_6_gift_skill_group_name", "Key": "hero_2_6_gift_skill_group_name"}, "1082": {"Id": 1082, "StringId": "hero_1_0_hit_skill_group_name", "Key": "hero_1_0_hit_skill_group_name"}, "1083": {"Id": 1083, "StringId": "hero_1_0_gift_skill_group_name", "Key": "hero_1_0_gift_skill_group_name"}, "1084": {"Id": 1084, "StringId": "hero_1_1_hit_skill_group_name", "Key": "hero_1_1_hit_skill_group_name"}, "1085": {"Id": 1085, "StringId": "hero_1_1_gift_skill_group_name", "Key": "hero_1_1_gift_skill_group_name"}, "1086": {"Id": 1086, "StringId": "hero_3_0_hit_skill_desc", "Key": "hero_3_0_hit_skill_desc"}, "1087": {"Id": 1087, "StringId": "hero_3_0_negative_skill_desc", "Key": "hero_3_0_negative_skill_desc"}, "1088": {"Id": 1088, "StringId": "hero_3_1_hit_skill_desc", "Key": "hero_3_1_hit_skill_desc"}, "1089": {"Id": 1089, "StringId": "hero_3_1_negative_skill_desc", "Key": "hero_3_1_negative_skill_desc"}, "1090": {"Id": 1090, "StringId": "hero_3_2_hit_skill_desc", "Key": "hero_3_2_hit_skill_desc"}, "1091": {"Id": 1091, "StringId": "hero_3_2_negative_skill_desc", "Key": "hero_3_2_negative_skill_desc"}, "1092": {"Id": 1092, "StringId": "hero_3_3_hit_skill_desc", "Key": "hero_3_3_hit_skill_desc"}, "1093": {"Id": 1093, "StringId": "hero_3_3_negative_skill_desc", "Key": "hero_3_3_negative_skill_desc"}, "1094": {"Id": 1094, "StringId": "hero_3_4_hit_skill_desc", "Key": "hero_3_4_hit_skill_desc"}, "1095": {"Id": 1095, "StringId": "hero_3_4_negative_skill_desc", "Key": "hero_3_4_negative_skill_desc"}, "1096": {"Id": 1096, "StringId": "hero_3_5_hit_skill_desc", "Key": "hero_3_5_hit_skill_desc"}, "1097": {"Id": 1097, "StringId": "hero_3_5_negative_skill_desc", "Key": "hero_3_5_negative_skill_desc"}, "1098": {"Id": 1098, "StringId": "hero_2_0_hit_skill_desc", "Key": "hero_2_0_hit_skill_desc"}, "1099": {"Id": 1099, "StringId": "hero_2_0_negative_skill_desc", "Key": "hero_2_0_negative_skill_desc"}, "1100": {"Id": 1100, "StringId": "hero_2_1_hit_skill_desc", "Key": "hero_2_1_hit_skill_desc"}, "1101": {"Id": 1101, "StringId": "hero_2_1_negative_skill_desc", "Key": "hero_2_1_negative_skill_desc"}, "1102": {"Id": 1102, "StringId": "hero_2_2_hit_skill_desc", "Key": "hero_2_2_hit_skill_desc"}, "1103": {"Id": 1103, "StringId": "hero_2_2_negative_skill_desc", "Key": "hero_2_2_negative_skill_desc"}, "1104": {"Id": 1104, "StringId": "hero_2_3_hit_skill_desc", "Key": "hero_2_3_hit_skill_desc"}, "1105": {"Id": 1105, "StringId": "hero_2_3_negative_skill_desc", "Key": "hero_2_3_negative_skill_desc"}, "1106": {"Id": 1106, "StringId": "hero_2_4_hit_skill_desc", "Key": "hero_2_4_hit_skill_desc"}, "1107": {"Id": 1107, "StringId": "hero_2_4_negative_skill_desc", "Key": "hero_2_4_negative_skill_desc"}, "1108": {"Id": 1108, "StringId": "hero_2_5_hit_skill_desc", "Key": "hero_2_5_hit_skill_desc"}, "1109": {"Id": 1109, "StringId": "hero_2_5_negative_skill_desc", "Key": "hero_2_5_negative_skill_desc"}, "1110": {"Id": 1110, "StringId": "hero_2_6_hit_skill_desc", "Key": "hero_2_6_hit_skill_desc"}, "1111": {"Id": 1111, "StringId": "hero_2_6_negative_skill_desc", "Key": "hero_2_6_negative_skill_desc"}, "1112": {"Id": 1112, "StringId": "hero_1_0_hit_skill_desc", "Key": "hero_1_0_hit_skill_desc"}, "1113": {"Id": 1113, "StringId": "hero_1_1_hit_skill_desc", "Key": "hero_1_1_hit_skill_desc"}, "1114": {"Id": 1114, "StringId": "hero_legend_gift_skill_desc", "Key": "hero_legend_gift_skill_desc"}, "1115": {"Id": 1115, "StringId": "hero_epic_gift_skill_desc", "Key": "hero_epic_gift_skill_desc"}, "1116": {"Id": 1116, "StringId": "hero_rare_gift_skill_desc", "Key": "hero_rare_gift_skill_desc"}, "1117": {"Id": 1117, "StringId": "hero_hit_skill_extra_1_desc", "Key": "hero_hit_skill_extra_1_desc"}, "1118": {"Id": 1118, "StringId": "hero_hit_skill_extra_2_desc", "Key": "hero_hit_skill_extra_2_desc"}, "1119": {"Id": 1119, "StringId": "hero_passive_skill_extra_1_desc", "Key": "hero_passive_skill_extra_1_desc"}, "1120": {"Id": 1120, "StringId": "hero_passive_skill_extra_2_desc", "Key": "hero_passive_skill_extra_2_desc"}, "1121": {"Id": 1121, "StringId": "hero_passive_skill_extra_3_desc", "Key": "hero_passive_skill_extra_3_desc"}, "1122": {"Id": 1122, "StringId": "hero_passive_skill_extra_4_desc", "Key": "hero_passive_skill_extra_4_desc"}, "1123": {"Id": 1123, "StringId": "attribute_hierarchy_base_name", "Key": "attribute_hierarchy_base_name"}, "1124": {"Id": 1124, "StringId": "attribute_hierarchy_atk_name", "Key": "attribute_hierarchy_atk_name"}, "1125": {"Id": 1125, "StringId": "attribute_hierarchy_def_name", "Key": "attribute_hierarchy_def_name"}, "1126": {"Id": 1126, "StringId": "hero_star_0_name", "Key": "hero_star_0_name"}, "1127": {"Id": 1127, "StringId": "hero_star_1_name", "Key": "hero_star_1_name"}, "1128": {"Id": 1128, "StringId": "hero_star_2_name", "Key": "hero_star_2_name"}, "1129": {"Id": 1129, "StringId": "hero_star_3_name", "Key": "hero_star_3_name"}, "1130": {"Id": 1130, "StringId": "hero_star_4_name", "Key": "hero_star_4_name"}, "1131": {"Id": 1131, "StringId": "hero_star_5_name", "Key": "hero_star_5_name"}, "1132": {"Id": 1132, "StringId": "hero_star_6_name", "Key": "hero_star_6_name"}, "1133": {"Id": 1133, "StringId": "hero_star_7_name", "Key": "hero_star_7_name"}, "1134": {"Id": 1134, "StringId": "hero_star_8_name", "Key": "hero_star_8_name"}, "1135": {"Id": 1135, "StringId": "hero_star_9_name", "Key": "hero_star_9_name"}, "1136": {"Id": 1136, "StringId": "hero_star_10_name", "Key": "hero_star_10_name"}, "1137": {"Id": 1137, "StringId": "hero_star_11_name", "Key": "hero_star_11_name"}, "1138": {"Id": 1138, "StringId": "hero_star_12_name", "Key": "hero_star_12_name"}, "1139": {"Id": 1139, "StringId": "hero_star_13_name", "Key": "hero_star_13_name"}, "1140": {"Id": 1140, "StringId": "hero_star_14_name", "Key": "hero_star_14_name"}, "1141": {"Id": 1141, "StringId": "hero_star_15_name", "Key": "hero_star_15_name"}, "1142": {"Id": 1142, "StringId": "hero_star_16_name", "Key": "hero_star_16_name"}, "1143": {"Id": 1143, "StringId": "hero_star_17_name", "Key": "hero_star_17_name"}, "1144": {"Id": 1144, "StringId": "hero_star_18_name", "Key": "hero_star_18_name"}, "1145": {"Id": 1145, "StringId": "hero_star_19_name", "Key": "hero_star_19_name"}, "1146": {"Id": 1146, "StringId": "hero_star_20_name", "Key": "hero_star_20_name"}, "1147": {"Id": 1147, "StringId": "hero_star_21_name", "Key": "hero_star_21_name"}, "1148": {"Id": 1148, "StringId": "hero_star_22_name", "Key": "hero_star_22_name"}, "1149": {"Id": 1149, "StringId": "hero_star_23_name", "Key": "hero_star_23_name"}, "1150": {"Id": 1150, "StringId": "hero_star_24_name", "Key": "hero_star_24_name"}, "1151": {"Id": 1151, "StringId": "hero_star_25_name", "Key": "hero_star_25_name"}, "1152": {"Id": 1152, "StringId": "<PERSON><PERSON><PERSON><PERSON>", "Key": "<PERSON><PERSON><PERSON><PERSON>"}, "1153": {"Id": 1153, "StringId": "hero<PERSON><PERSON>th", "Key": "hero<PERSON><PERSON>th"}, "1154": {"Id": 1154, "StringId": "heroDefense", "Key": "heroDefense"}, "1155": {"Id": 1155, "StringId": "iap_package_first_charge_1_title", "Key": "iap_package_first_charge_1_title"}, "1156": {"Id": 1156, "StringId": "iap_package_first_charge_2_title", "Key": "iap_package_first_charge_2_title"}, "1157": {"Id": 1157, "StringId": "iap_package_first_charge_3_title", "Key": "iap_package_first_charge_3_title"}, "1158": {"Id": 1158, "StringId": "iap_package_first_charge_4_title", "Key": "iap_package_first_charge_4_title"}, "1159": {"Id": 1159, "StringId": "iap_package_first_charge_5_title", "Key": "iap_package_first_charge_5_title"}, "1160": {"Id": 1160, "StringId": "iap_package_first_charge_6_title", "Key": "iap_package_first_charge_6_title"}, "1161": {"Id": 1161, "StringId": "hero_config_1_unlocked_desc", "Key": "hero_config_1_unlocked_desc"}, "1162": {"Id": 1162, "StringId": "hero_bonds_1_title", "Key": "hero_bonds_1_title"}, "1163": {"Id": 1163, "StringId": "hero_bonds_2_title", "Key": "hero_bonds_2_title"}, "1164": {"Id": 1164, "StringId": "hero_bonds_3_title", "Key": "hero_bonds_3_title"}, "1165": {"Id": 1165, "StringId": "hero_bonds_4_title", "Key": "hero_bonds_4_title"}, "1166": {"Id": 1166, "StringId": "hero_bonds_1_desc", "Key": "hero_bonds_1_desc"}, "1167": {"Id": 1167, "StringId": "hero_bonds_2_desc", "Key": "hero_bonds_2_desc"}, "1168": {"Id": 1168, "StringId": "hero_bonds_3_desc", "Key": "hero_bonds_3_desc"}, "1169": {"Id": 1169, "StringId": "hero_bonds_4_desc", "Key": "hero_bonds_4_desc"}, "1170": {"Id": 1170, "StringId": "hero_restrain_1_desc", "Key": "hero_restrain_1_desc"}, "1171": {"Id": 1171, "StringId": "hero_restrain_2_desc", "Key": "hero_restrain_2_desc"}, "1172": {"Id": 1172, "StringId": "hero_restrain_3_desc", "Key": "hero_restrain_3_desc"}, "1173": {"Id": 1173, "StringId": "hero_fire_improve_all", "Key": "hero_fire_improve_all"}, "1174": {"Id": 1174, "StringId": "hero_ice_improve_all", "Key": "hero_ice_improve_all"}, "1175": {"Id": 1175, "StringId": "hero_wind_improve_all", "Key": "hero_wind_improve_all"}, "1176": {"Id": 1176, "StringId": "hero_physical_improve_all", "Key": "hero_physical_improve_all"}, "1177": {"Id": 1177, "StringId": "hero_light_improve_all", "Key": "hero_light_improve_all"}, "1178": {"Id": 1178, "StringId": "hero_electrical_improve_all", "Key": "hero_electrical_improve_all"}, "2001": {"Id": 2001, "StringId": "rouge_tab_group_1_0_name", "Key": "rouge_tab_group_1_0_name"}, "2002": {"Id": 2002, "StringId": "rouge_tab_group_1_1_name", "Key": "rouge_tab_group_1_1_name"}, "2003": {"Id": 2003, "StringId": "rouge_tab_group_1_2_name", "Key": "rouge_tab_group_1_2_name"}, "2004": {"Id": 2004, "StringId": "rouge_tab_group_1_3_name", "Key": "rouge_tab_group_1_3_name"}, "2005": {"Id": 2005, "StringId": "rouge_tab_group_1_4_name", "Key": "rouge_tab_group_1_4_name"}, "2006": {"Id": 2006, "StringId": "rouge_tab_group_1_5_name", "Key": "rouge_tab_group_1_5_name"}, "2007": {"Id": 2007, "StringId": "rouge_tab_group_1_7_name", "Key": "rouge_tab_group_1_7_name"}, "2008": {"Id": 2008, "StringId": "rouge_tab_group_1_12_name", "Key": "rouge_tab_group_1_12_name"}, "2009": {"Id": 2009, "StringId": "rouge_tab_group_1_13_name", "Key": "rouge_tab_group_1_13_name"}, "2010": {"Id": 2010, "StringId": "rouge_tab_group_1_14_name", "Key": "rouge_tab_group_1_14_name"}, "2011": {"Id": 2011, "StringId": "rouge_tab_group_2_0_name", "Key": "rouge_tab_group_2_0_name"}, "2012": {"Id": 2012, "StringId": "rouge_tab_group_2_1_name", "Key": "rouge_tab_group_2_1_name"}, "2013": {"Id": 2013, "StringId": "rouge_tab_group_2_2_name", "Key": "rouge_tab_group_2_2_name"}, "2014": {"Id": 2014, "StringId": "rouge_tab_group_2_3_name", "Key": "rouge_tab_group_2_3_name"}, "2015": {"Id": 2015, "StringId": "rouge_tab_group_2_4_name", "Key": "rouge_tab_group_2_4_name"}, "2016": {"Id": 2016, "StringId": "rouge_tab_group_2_5_name", "Key": "rouge_tab_group_2_5_name"}, "2017": {"Id": 2017, "StringId": "rouge_tab_group_2_6_name", "Key": "rouge_tab_group_2_6_name"}, "2018": {"Id": 2018, "StringId": "rouge_tab_group_2_7_name", "Key": "rouge_tab_group_2_7_name"}, "2019": {"Id": 2019, "StringId": "rouge_tab_group_2_8_name", "Key": "rouge_tab_group_2_8_name"}, "2020": {"Id": 2020, "StringId": "rouge_tab_group_2_9_name", "Key": "rouge_tab_group_2_9_name"}, "2021": {"Id": 2021, "StringId": "rouge_tab_group_2_10_name", "Key": "rouge_tab_group_2_10_name"}, "2022": {"Id": 2022, "StringId": "rouge_tab_group_2_11_name", "Key": "rouge_tab_group_2_11_name"}, "2023": {"Id": 2023, "StringId": "rouge_tab_group_3_0_name", "Key": "rouge_tab_group_3_0_name"}, "2024": {"Id": 2024, "StringId": "rouge_tab_group_3_1_name", "Key": "rouge_tab_group_3_1_name"}, "2025": {"Id": 2025, "StringId": "rouge_tab_group_3_2_name", "Key": "rouge_tab_group_3_2_name"}, "2026": {"Id": 2026, "StringId": "rouge_tab_group_3_3_name", "Key": "rouge_tab_group_3_3_name"}, "2027": {"Id": 2027, "StringId": "rouge_tab_group_3_4_name", "Key": "rouge_tab_group_3_4_name"}, "2028": {"Id": 2028, "StringId": "rouge_tab_group_3_5_name", "Key": "rouge_tab_group_3_5_name"}, "2029": {"Id": 2029, "StringId": "rouge_tab_group_3_6_name", "Key": "rouge_tab_group_3_6_name"}, "2030": {"Id": 2030, "StringId": "rouge_tab_group_3_7_name", "Key": "rouge_tab_group_3_7_name"}, "2031": {"Id": 2031, "StringId": "rouge_tab_group_3_8_name", "Key": "rouge_tab_group_3_8_name"}, "2032": {"Id": 2032, "StringId": "rouge_tab_group_3_9_name", "Key": "rouge_tab_group_3_9_name"}, "2033": {"Id": 2033, "StringId": "rouge_tab_group_3_10_name", "Key": "rouge_tab_group_3_10_name"}, "2034": {"Id": 2034, "StringId": "rouge_tab_group_3_11_name", "Key": "rouge_tab_group_3_11_name"}, "2035": {"Id": 2035, "StringId": "rouge_tab_group_3_12_name", "Key": "rouge_tab_group_3_12_name"}, "2036": {"Id": 2036, "StringId": "rouge_tab_group_3_13_name", "Key": "rouge_tab_group_3_13_name"}, "2037": {"Id": 2037, "StringId": "rouge_tab_group_3_14_name", "Key": "rouge_tab_group_3_14_name"}, "2038": {"Id": 2038, "StringId": "rouge_tab_group_3_15_name", "Key": "rouge_tab_group_3_15_name"}, "2039": {"Id": 2039, "StringId": "rouge_tab_group_3_16_name", "Key": "rouge_tab_group_3_16_name"}, "2040": {"Id": 2040, "StringId": "rouge_tab_group_3_17_name", "Key": "rouge_tab_group_3_17_name"}, "2041": {"Id": 2041, "StringId": "rouge_tab_group_3_18_name", "Key": "rouge_tab_group_3_18_name"}, "2042": {"Id": 2042, "StringId": "rouge_tab_group_3_19_name", "Key": "rouge_tab_group_3_19_name"}, "2043": {"Id": 2043, "StringId": "rouge_tab_group_3_26_name", "Key": "rouge_tab_group_3_26_name"}, "2044": {"Id": 2044, "StringId": "rouge_tab_group_3_27_name", "Key": "rouge_tab_group_3_27_name"}, "2045": {"Id": 2045, "StringId": "rouge_tab_group_4_0_name", "Key": "rouge_tab_group_4_0_name"}, "2046": {"Id": 2046, "StringId": "rouge_tab_group_4_1_name", "Key": "rouge_tab_group_4_1_name"}, "2047": {"Id": 2047, "StringId": "rouge_tab_group_4_2_name", "Key": "rouge_tab_group_4_2_name"}, "2048": {"Id": 2048, "StringId": "rouge_tab_group_4_3_name", "Key": "rouge_tab_group_4_3_name"}, "2049": {"Id": 2049, "StringId": "rouge_tab_group_4_4_name", "Key": "rouge_tab_group_4_4_name"}, "2050": {"Id": 2050, "StringId": "rouge_tab_group_4_5_name", "Key": "rouge_tab_group_4_5_name"}, "2051": {"Id": 2051, "StringId": "rouge_tab_group_4_6_name", "Key": "rouge_tab_group_4_6_name"}, "2052": {"Id": 2052, "StringId": "rouge_tab_group_4_7_name", "Key": "rouge_tab_group_4_7_name"}, "2053": {"Id": 2053, "StringId": "rouge_tab_group_4_8_name", "Key": "rouge_tab_group_4_8_name"}, "2054": {"Id": 2054, "StringId": "rouge_tab_group_4_9_name", "Key": "rouge_tab_group_4_9_name"}, "2055": {"Id": 2055, "StringId": "rouge_tab_group_4_10_name", "Key": "rouge_tab_group_4_10_name"}, "2056": {"Id": 2056, "StringId": "rouge_tab_group_4_13_name", "Key": "rouge_tab_group_4_13_name"}, "2057": {"Id": 2057, "StringId": "rouge_tab_group_4_14_name", "Key": "rouge_tab_group_4_14_name"}, "2058": {"Id": 2058, "StringId": "rouge_tab_group_4_19_name", "Key": "rouge_tab_group_4_19_name"}, "2059": {"Id": 2059, "StringId": "rouge_tab_group_5_0_name", "Key": "rouge_tab_group_5_0_name"}, "2060": {"Id": 2060, "StringId": "rouge_tab_group_5_1_name", "Key": "rouge_tab_group_5_1_name"}, "2061": {"Id": 2061, "StringId": "rouge_tab_group_5_2_name", "Key": "rouge_tab_group_5_2_name"}, "2062": {"Id": 2062, "StringId": "rouge_tab_group_5_3_name", "Key": "rouge_tab_group_5_3_name"}, "2063": {"Id": 2063, "StringId": "rouge_tab_group_5_4_name", "Key": "rouge_tab_group_5_4_name"}, "2064": {"Id": 2064, "StringId": "rouge_tab_group_5_5_name", "Key": "rouge_tab_group_5_5_name"}, "2065": {"Id": 2065, "StringId": "rouge_tab_group_5_6_name", "Key": "rouge_tab_group_5_6_name"}, "2066": {"Id": 2066, "StringId": "rouge_tab_group_5_9_name", "Key": "rouge_tab_group_5_9_name"}, "2067": {"Id": 2067, "StringId": "rouge_tab_group_5_11_name", "Key": "rouge_tab_group_5_11_name"}, "2068": {"Id": 2068, "StringId": "rouge_tab_group_5_13_name", "Key": "rouge_tab_group_5_13_name"}, "2069": {"Id": 2069, "StringId": "rouge_tab_group_6_0_name", "Key": "rouge_tab_group_6_0_name"}, "2070": {"Id": 2070, "StringId": "rouge_tab_group_6_1_name", "Key": "rouge_tab_group_6_1_name"}, "2071": {"Id": 2071, "StringId": "rouge_tab_group_6_2_name", "Key": "rouge_tab_group_6_2_name"}, "2072": {"Id": 2072, "StringId": "rouge_tab_group_6_3_name", "Key": "rouge_tab_group_6_3_name"}, "2073": {"Id": 2073, "StringId": "rouge_tab_group_6_4_name", "Key": "rouge_tab_group_6_4_name"}, "2074": {"Id": 2074, "StringId": "rouge_tab_group_6_5_name", "Key": "rouge_tab_group_6_5_name"}, "2075": {"Id": 2075, "StringId": "rouge_tab_group_6_6_name", "Key": "rouge_tab_group_6_6_name"}, "2076": {"Id": 2076, "StringId": "rouge_tab_group_6_7_name", "Key": "rouge_tab_group_6_7_name"}, "2077": {"Id": 2077, "StringId": "rouge_tab_group_6_10_name", "Key": "rouge_tab_group_6_10_name"}, "2078": {"Id": 2078, "StringId": "rouge_tab_group_6_12_name", "Key": "rouge_tab_group_6_12_name"}, "2079": {"Id": 2079, "StringId": "rouge_tab_group_6_15_name", "Key": "rouge_tab_group_6_15_name"}, "2080": {"Id": 2080, "StringId": "rouge_tab_group_6_16_name", "Key": "rouge_tab_group_6_16_name"}, "2081": {"Id": 2081, "StringId": "rouge_tab_group_6_17_name", "Key": "rouge_tab_group_6_17_name"}, "2082": {"Id": 2082, "StringId": "rouge_tab_group_7_0_name", "Key": "rouge_tab_group_7_0_name"}, "2083": {"Id": 2083, "StringId": "rouge_tab_group_7_1_name", "Key": "rouge_tab_group_7_1_name"}, "2084": {"Id": 2084, "StringId": "rouge_tab_group_7_2_name", "Key": "rouge_tab_group_7_2_name"}, "2085": {"Id": 2085, "StringId": "rouge_tab_group_7_3_name", "Key": "rouge_tab_group_7_3_name"}, "2086": {"Id": 2086, "StringId": "rouge_tab_group_7_4_name", "Key": "rouge_tab_group_7_4_name"}, "2087": {"Id": 2087, "StringId": "rouge_tab_group_7_5_name", "Key": "rouge_tab_group_7_5_name"}, "2088": {"Id": 2088, "StringId": "rouge_tab_group_7_6_name", "Key": "rouge_tab_group_7_6_name"}, "2089": {"Id": 2089, "StringId": "rouge_tab_group_7_7_name", "Key": "rouge_tab_group_7_7_name"}, "2090": {"Id": 2090, "StringId": "rouge_tab_group_7_8_name", "Key": "rouge_tab_group_7_8_name"}, "2091": {"Id": 2091, "StringId": "rouge_tab_group_7_9_name", "Key": "rouge_tab_group_7_9_name"}, "2092": {"Id": 2092, "StringId": "rouge_tab_group_7_12_name", "Key": "rouge_tab_group_7_12_name"}, "2093": {"Id": 2093, "StringId": "rouge_tab_group_8_0_name", "Key": "rouge_tab_group_8_0_name"}, "2094": {"Id": 2094, "StringId": "rouge_tab_group_8_1_name", "Key": "rouge_tab_group_8_1_name"}, "2095": {"Id": 2095, "StringId": "rouge_tab_group_8_2_name", "Key": "rouge_tab_group_8_2_name"}, "2096": {"Id": 2096, "StringId": "rouge_tab_group_8_3_name", "Key": "rouge_tab_group_8_3_name"}, "2097": {"Id": 2097, "StringId": "rouge_tab_group_8_4_name", "Key": "rouge_tab_group_8_4_name"}, "2098": {"Id": 2098, "StringId": "rouge_tab_group_8_5_name", "Key": "rouge_tab_group_8_5_name"}, "2099": {"Id": 2099, "StringId": "rouge_tab_group_8_6_name", "Key": "rouge_tab_group_8_6_name"}, "2100": {"Id": 2100, "StringId": "rouge_tab_group_8_8_name", "Key": "rouge_tab_group_8_8_name"}, "2101": {"Id": 2101, "StringId": "rouge_tab_group_8_10_name", "Key": "rouge_tab_group_8_10_name"}, "2102": {"Id": 2102, "StringId": "rouge_tab_group_8_13_name", "Key": "rouge_tab_group_8_13_name"}, "2103": {"Id": 2103, "StringId": "rouge_tab_group_8_14_name", "Key": "rouge_tab_group_8_14_name"}, "2104": {"Id": 2104, "StringId": "rouge_tab_group_8_15_name", "Key": "rouge_tab_group_8_15_name"}, "2105": {"Id": 2105, "StringId": "rouge_tab_group_9_0_name", "Key": "rouge_tab_group_9_0_name"}, "2106": {"Id": 2106, "StringId": "rouge_tab_group_9_1_name", "Key": "rouge_tab_group_9_1_name"}, "2107": {"Id": 2107, "StringId": "rouge_tab_group_9_2_name", "Key": "rouge_tab_group_9_2_name"}, "2108": {"Id": 2108, "StringId": "rouge_tab_group_9_3_name", "Key": "rouge_tab_group_9_3_name"}, "2109": {"Id": 2109, "StringId": "rouge_tab_group_9_4_name", "Key": "rouge_tab_group_9_4_name"}, "2110": {"Id": 2110, "StringId": "rouge_tab_group_9_5_name", "Key": "rouge_tab_group_9_5_name"}, "2111": {"Id": 2111, "StringId": "rouge_tab_group_9_6_name", "Key": "rouge_tab_group_9_6_name"}, "2112": {"Id": 2112, "StringId": "rouge_tab_group_9_8_name", "Key": "rouge_tab_group_9_8_name"}, "2113": {"Id": 2113, "StringId": "rouge_tab_group_9_9_name", "Key": "rouge_tab_group_9_9_name"}, "2114": {"Id": 2114, "StringId": "rouge_tab_group_9_14_name", "Key": "rouge_tab_group_9_14_name"}, "2115": {"Id": 2115, "StringId": "rouge_tab_group_10_0_name", "Key": "rouge_tab_group_10_0_name"}, "2116": {"Id": 2116, "StringId": "rouge_tab_group_10_1_name", "Key": "rouge_tab_group_10_1_name"}, "2117": {"Id": 2117, "StringId": "rouge_tab_group_10_2_name", "Key": "rouge_tab_group_10_2_name"}, "2118": {"Id": 2118, "StringId": "rouge_tab_group_10_3_name", "Key": "rouge_tab_group_10_3_name"}, "2119": {"Id": 2119, "StringId": "rouge_tab_group_10_4_name", "Key": "rouge_tab_group_10_4_name"}, "2120": {"Id": 2120, "StringId": "rouge_tab_group_10_5_name", "Key": "rouge_tab_group_10_5_name"}, "2121": {"Id": 2121, "StringId": "rouge_tab_group_10_6_name", "Key": "rouge_tab_group_10_6_name"}, "2122": {"Id": 2122, "StringId": "rouge_tab_group_10_7_name", "Key": "rouge_tab_group_10_7_name"}, "2123": {"Id": 2123, "StringId": "rouge_tab_group_10_8_name", "Key": "rouge_tab_group_10_8_name"}, "2124": {"Id": 2124, "StringId": "rouge_tab_group_10_9_name", "Key": "rouge_tab_group_10_9_name"}, "2125": {"Id": 2125, "StringId": "rouge_tab_group_10_11_name", "Key": "rouge_tab_group_10_11_name"}, "2126": {"Id": 2126, "StringId": "rouge_tab_group_10_12_name", "Key": "rouge_tab_group_10_12_name"}, "2127": {"Id": 2127, "StringId": "rouge_tab_group_10_13_name", "Key": "rouge_tab_group_10_13_name"}, "2128": {"Id": 2128, "StringId": "rouge_tab_group_10_18_name", "Key": "rouge_tab_group_10_18_name"}, "2129": {"Id": 2129, "StringId": "rouge_tab_group_10_19_name", "Key": "rouge_tab_group_10_19_name"}, "2130": {"Id": 2130, "StringId": "rouge_tab_group_11_0_name", "Key": "rouge_tab_group_11_0_name"}, "2131": {"Id": 2131, "StringId": "rouge_tab_group_11_1_name", "Key": "rouge_tab_group_11_1_name"}, "2132": {"Id": 2132, "StringId": "rouge_tab_group_11_2_name", "Key": "rouge_tab_group_11_2_name"}, "2133": {"Id": 2133, "StringId": "rouge_tab_group_11_3_name", "Key": "rouge_tab_group_11_3_name"}, "2134": {"Id": 2134, "StringId": "rouge_tab_group_11_4_name", "Key": "rouge_tab_group_11_4_name"}, "2135": {"Id": 2135, "StringId": "rouge_tab_group_11_5_name", "Key": "rouge_tab_group_11_5_name"}, "2136": {"Id": 2136, "StringId": "rouge_tab_group_11_6_name", "Key": "rouge_tab_group_11_6_name"}, "2137": {"Id": 2137, "StringId": "rouge_tab_group_11_7_name", "Key": "rouge_tab_group_11_7_name"}, "2138": {"Id": 2138, "StringId": "rouge_tab_group_11_10_name", "Key": "rouge_tab_group_11_10_name"}, "2139": {"Id": 2139, "StringId": "rouge_tab_group_11_12_name", "Key": "rouge_tab_group_11_12_name"}, "2140": {"Id": 2140, "StringId": "rouge_tab_group_11_16_name", "Key": "rouge_tab_group_11_16_name"}, "2141": {"Id": 2141, "StringId": "rouge_tab_group_12_0_name", "Key": "rouge_tab_group_12_0_name"}, "2142": {"Id": 2142, "StringId": "rouge_tab_group_12_1_name", "Key": "rouge_tab_group_12_1_name"}, "2143": {"Id": 2143, "StringId": "rouge_tab_group_12_2_name", "Key": "rouge_tab_group_12_2_name"}, "2144": {"Id": 2144, "StringId": "rouge_tab_group_12_3_name", "Key": "rouge_tab_group_12_3_name"}, "2145": {"Id": 2145, "StringId": "rouge_tab_group_12_4_name", "Key": "rouge_tab_group_12_4_name"}, "2146": {"Id": 2146, "StringId": "rouge_tab_group_12_5_name", "Key": "rouge_tab_group_12_5_name"}, "2147": {"Id": 2147, "StringId": "rouge_tab_group_12_6_name", "Key": "rouge_tab_group_12_6_name"}, "2148": {"Id": 2148, "StringId": "rouge_tab_group_12_7_name", "Key": "rouge_tab_group_12_7_name"}, "2149": {"Id": 2149, "StringId": "rouge_tab_group_12_8_name", "Key": "rouge_tab_group_12_8_name"}, "2150": {"Id": 2150, "StringId": "rouge_tab_group_12_9_name", "Key": "rouge_tab_group_12_9_name"}, "2151": {"Id": 2151, "StringId": "rouge_tab_group_12_10_name", "Key": "rouge_tab_group_12_10_name"}, "2152": {"Id": 2152, "StringId": "rouge_tab_group_12_11_name", "Key": "rouge_tab_group_12_11_name"}, "2153": {"Id": 2153, "StringId": "rouge_tab_group_12_12_name", "Key": "rouge_tab_group_12_12_name"}, "2154": {"Id": 2154, "StringId": "rouge_tab_group_12_14_name", "Key": "rouge_tab_group_12_14_name"}, "2155": {"Id": 2155, "StringId": "rouge_tab_group_12_16_name", "Key": "rouge_tab_group_12_16_name"}, "2156": {"Id": 2156, "StringId": "rouge_tab_group_12_18_name", "Key": "rouge_tab_group_12_18_name"}, "2157": {"Id": 2157, "StringId": "rouge_tab_group_12_19_name", "Key": "rouge_tab_group_12_19_name"}, "2158": {"Id": 2158, "StringId": "rouge_tab_group_13_0_name", "Key": "rouge_tab_group_13_0_name"}, "2159": {"Id": 2159, "StringId": "rouge_tab_group_13_1_name", "Key": "rouge_tab_group_13_1_name"}, "2160": {"Id": 2160, "StringId": "rouge_tab_group_13_2_name", "Key": "rouge_tab_group_13_2_name"}, "2161": {"Id": 2161, "StringId": "rouge_tab_group_13_3_name", "Key": "rouge_tab_group_13_3_name"}, "2162": {"Id": 2162, "StringId": "rouge_tab_group_13_4_name", "Key": "rouge_tab_group_13_4_name"}, "2163": {"Id": 2163, "StringId": "rouge_tab_group_13_5_name", "Key": "rouge_tab_group_13_5_name"}, "2164": {"Id": 2164, "StringId": "rouge_tab_group_13_6_name", "Key": "rouge_tab_group_13_6_name"}, "2165": {"Id": 2165, "StringId": "rouge_tab_group_13_8_name", "Key": "rouge_tab_group_13_8_name"}, "2166": {"Id": 2166, "StringId": "rouge_tab_group_13_9_name", "Key": "rouge_tab_group_13_9_name"}, "2167": {"Id": 2167, "StringId": "rouge_tab_group_14_0_name", "Key": "rouge_tab_group_14_0_name"}, "2168": {"Id": 2168, "StringId": "rouge_tab_group_14_1_name", "Key": "rouge_tab_group_14_1_name"}, "2169": {"Id": 2169, "StringId": "rouge_tab_group_14_2_name", "Key": "rouge_tab_group_14_2_name"}, "2170": {"Id": 2170, "StringId": "rouge_tab_group_14_3_name", "Key": "rouge_tab_group_14_3_name"}, "2171": {"Id": 2171, "StringId": "rouge_tab_group_14_4_name", "Key": "rouge_tab_group_14_4_name"}, "2172": {"Id": 2172, "StringId": "rouge_tab_group_14_5_name", "Key": "rouge_tab_group_14_5_name"}, "2173": {"Id": 2173, "StringId": "rouge_tab_group_14_6_name", "Key": "rouge_tab_group_14_6_name"}, "2174": {"Id": 2174, "StringId": "rouge_tab_group_14_7_name", "Key": "rouge_tab_group_14_7_name"}, "2175": {"Id": 2175, "StringId": "rouge_tab_group_14_8_name", "Key": "rouge_tab_group_14_8_name"}, "2176": {"Id": 2176, "StringId": "rouge_tab_group_14_9_name", "Key": "rouge_tab_group_14_9_name"}, "2177": {"Id": 2177, "StringId": "rouge_tab_group_14_11_name", "Key": "rouge_tab_group_14_11_name"}, "2178": {"Id": 2178, "StringId": "rouge_tab_group_14_17_name", "Key": "rouge_tab_group_14_17_name"}, "2179": {"Id": 2179, "StringId": "rouge_tab_group_14_18_name", "Key": "rouge_tab_group_14_18_name"}, "2180": {"Id": 2180, "StringId": "rouge_tab_group_15_0_name", "Key": "rouge_tab_group_15_0_name"}, "2181": {"Id": 2181, "StringId": "rouge_tab_group_15_1_name", "Key": "rouge_tab_group_15_1_name"}, "2182": {"Id": 2182, "StringId": "rouge_tab_group_15_2_name", "Key": "rouge_tab_group_15_2_name"}, "2183": {"Id": 2183, "StringId": "rouge_tab_group_15_3_name", "Key": "rouge_tab_group_15_3_name"}, "2184": {"Id": 2184, "StringId": "rouge_tab_group_15_4_name", "Key": "rouge_tab_group_15_4_name"}, "2185": {"Id": 2185, "StringId": "rouge_tab_group_15_5_name", "Key": "rouge_tab_group_15_5_name"}, "2186": {"Id": 2186, "StringId": "rouge_tab_group_15_6_name", "Key": "rouge_tab_group_15_6_name"}, "2187": {"Id": 2187, "StringId": "rouge_tab_group_15_7_name", "Key": "rouge_tab_group_15_7_name"}, "2188": {"Id": 2188, "StringId": "rouge_tab_group_15_10_name", "Key": "rouge_tab_group_15_10_name"}, "2189": {"Id": 2189, "StringId": "rouge_tab_group_15_12_name", "Key": "rouge_tab_group_15_12_name"}, "2190": {"Id": 2190, "StringId": "rouge_tab_group_1_0_desc", "Key": "rouge_tab_group_1_0_desc"}, "2191": {"Id": 2191, "StringId": "rouge_tab_group_1_1_desc", "Key": "rouge_tab_group_1_1_desc"}, "2192": {"Id": 2192, "StringId": "rouge_tab_group_1_2_desc", "Key": "rouge_tab_group_1_2_desc"}, "2193": {"Id": 2193, "StringId": "rouge_tab_group_1_3_desc", "Key": "rouge_tab_group_1_3_desc"}, "2194": {"Id": 2194, "StringId": "rouge_tab_group_1_4_desc", "Key": "rouge_tab_group_1_4_desc"}, "2195": {"Id": 2195, "StringId": "rouge_tab_group_1_5_desc", "Key": "rouge_tab_group_1_5_desc"}, "2196": {"Id": 2196, "StringId": "rouge_tab_group_1_7_desc", "Key": "rouge_tab_group_1_7_desc"}, "2197": {"Id": 2197, "StringId": "rouge_tab_group_1_12_desc", "Key": "rouge_tab_group_1_12_desc"}, "2198": {"Id": 2198, "StringId": "rouge_tab_group_1_13_desc", "Key": "rouge_tab_group_1_13_desc"}, "2199": {"Id": 2199, "StringId": "rouge_tab_group_1_14_desc", "Key": "rouge_tab_group_1_14_desc"}, "2200": {"Id": 2200, "StringId": "rouge_tab_group_2_0_desc", "Key": "rouge_tab_group_2_0_desc"}, "2201": {"Id": 2201, "StringId": "rouge_tab_group_2_1_desc", "Key": "rouge_tab_group_2_1_desc"}, "2202": {"Id": 2202, "StringId": "rouge_tab_group_2_2_desc", "Key": "rouge_tab_group_2_2_desc"}, "2203": {"Id": 2203, "StringId": "rouge_tab_group_2_3_desc", "Key": "rouge_tab_group_2_3_desc"}, "2204": {"Id": 2204, "StringId": "rouge_tab_group_2_4_desc", "Key": "rouge_tab_group_2_4_desc"}, "2205": {"Id": 2205, "StringId": "rouge_tab_group_2_5_desc", "Key": "rouge_tab_group_2_5_desc"}, "2206": {"Id": 2206, "StringId": "rouge_tab_group_2_6_desc", "Key": "rouge_tab_group_2_6_desc"}, "2207": {"Id": 2207, "StringId": "rouge_tab_group_2_7_desc", "Key": "rouge_tab_group_2_7_desc"}, "2208": {"Id": 2208, "StringId": "rouge_tab_group_2_8_desc", "Key": "rouge_tab_group_2_8_desc"}, "2209": {"Id": 2209, "StringId": "rouge_tab_group_2_9_desc", "Key": "rouge_tab_group_2_9_desc"}, "2210": {"Id": 2210, "StringId": "rouge_tab_group_2_10_desc", "Key": "rouge_tab_group_2_10_desc"}, "2211": {"Id": 2211, "StringId": "rouge_tab_group_2_11_desc", "Key": "rouge_tab_group_2_11_desc"}, "2212": {"Id": 2212, "StringId": "rouge_tab_group_3_0_desc", "Key": "rouge_tab_group_3_0_desc"}, "2213": {"Id": 2213, "StringId": "rouge_tab_group_3_1_desc", "Key": "rouge_tab_group_3_1_desc"}, "2214": {"Id": 2214, "StringId": "rouge_tab_group_3_2_desc", "Key": "rouge_tab_group_3_2_desc"}, "2215": {"Id": 2215, "StringId": "rouge_tab_group_3_3_desc", "Key": "rouge_tab_group_3_3_desc"}, "2216": {"Id": 2216, "StringId": "rouge_tab_group_3_4_desc", "Key": "rouge_tab_group_3_4_desc"}, "2217": {"Id": 2217, "StringId": "rouge_tab_group_3_5_desc", "Key": "rouge_tab_group_3_5_desc"}, "2218": {"Id": 2218, "StringId": "rouge_tab_group_3_6_desc", "Key": "rouge_tab_group_3_6_desc"}, "2219": {"Id": 2219, "StringId": "rouge_tab_group_3_7_desc", "Key": "rouge_tab_group_3_7_desc"}, "2220": {"Id": 2220, "StringId": "rouge_tab_group_3_8_desc", "Key": "rouge_tab_group_3_8_desc"}, "2221": {"Id": 2221, "StringId": "rouge_tab_group_3_9_desc", "Key": "rouge_tab_group_3_9_desc"}, "2222": {"Id": 2222, "StringId": "rouge_tab_group_3_10_desc", "Key": "rouge_tab_group_3_10_desc"}, "2223": {"Id": 2223, "StringId": "rouge_tab_group_3_11_desc", "Key": "rouge_tab_group_3_11_desc"}, "2224": {"Id": 2224, "StringId": "rouge_tab_group_3_12_desc", "Key": "rouge_tab_group_3_12_desc"}, "2225": {"Id": 2225, "StringId": "rouge_tab_group_3_13_desc", "Key": "rouge_tab_group_3_13_desc"}, "2226": {"Id": 2226, "StringId": "rouge_tab_group_3_14_desc", "Key": "rouge_tab_group_3_14_desc"}, "2227": {"Id": 2227, "StringId": "rouge_tab_group_3_15_desc", "Key": "rouge_tab_group_3_15_desc"}, "2228": {"Id": 2228, "StringId": "rouge_tab_group_3_16_desc", "Key": "rouge_tab_group_3_16_desc"}, "2229": {"Id": 2229, "StringId": "rouge_tab_group_3_17_desc", "Key": "rouge_tab_group_3_17_desc"}, "2230": {"Id": 2230, "StringId": "rouge_tab_group_3_18_desc", "Key": "rouge_tab_group_3_18_desc"}, "2231": {"Id": 2231, "StringId": "rouge_tab_group_3_19_desc", "Key": "rouge_tab_group_3_19_desc"}, "2232": {"Id": 2232, "StringId": "rouge_tab_group_3_26_desc", "Key": "rouge_tab_group_3_26_desc"}, "2233": {"Id": 2233, "StringId": "rouge_tab_group_3_27_desc", "Key": "rouge_tab_group_3_27_desc"}, "2234": {"Id": 2234, "StringId": "rouge_tab_group_4_0_desc", "Key": "rouge_tab_group_4_0_desc"}, "2235": {"Id": 2235, "StringId": "rouge_tab_group_4_1_desc", "Key": "rouge_tab_group_4_1_desc"}, "2236": {"Id": 2236, "StringId": "rouge_tab_group_4_2_desc", "Key": "rouge_tab_group_4_2_desc"}, "2237": {"Id": 2237, "StringId": "rouge_tab_group_4_3_desc", "Key": "rouge_tab_group_4_3_desc"}, "2238": {"Id": 2238, "StringId": "rouge_tab_group_4_4_desc", "Key": "rouge_tab_group_4_4_desc"}, "2239": {"Id": 2239, "StringId": "rouge_tab_group_4_5_desc", "Key": "rouge_tab_group_4_5_desc"}, "2240": {"Id": 2240, "StringId": "rouge_tab_group_4_6_desc", "Key": "rouge_tab_group_4_6_desc"}, "2241": {"Id": 2241, "StringId": "rouge_tab_group_4_7_desc", "Key": "rouge_tab_group_4_7_desc"}, "2242": {"Id": 2242, "StringId": "rouge_tab_group_4_8_desc", "Key": "rouge_tab_group_4_8_desc"}, "2243": {"Id": 2243, "StringId": "rouge_tab_group_4_9_desc", "Key": "rouge_tab_group_4_9_desc"}, "2244": {"Id": 2244, "StringId": "rouge_tab_group_4_10_desc", "Key": "rouge_tab_group_4_10_desc"}, "2245": {"Id": 2245, "StringId": "rouge_tab_group_4_13_desc", "Key": "rouge_tab_group_4_13_desc"}, "2246": {"Id": 2246, "StringId": "rouge_tab_group_4_14_desc", "Key": "rouge_tab_group_4_14_desc"}, "2247": {"Id": 2247, "StringId": "rouge_tab_group_4_19_desc", "Key": "rouge_tab_group_4_19_desc"}, "2248": {"Id": 2248, "StringId": "rouge_tab_group_5_0_desc", "Key": "rouge_tab_group_5_0_desc"}, "2249": {"Id": 2249, "StringId": "rouge_tab_group_5_1_desc", "Key": "rouge_tab_group_5_1_desc"}, "2250": {"Id": 2250, "StringId": "rouge_tab_group_5_2_desc", "Key": "rouge_tab_group_5_2_desc"}, "2251": {"Id": 2251, "StringId": "rouge_tab_group_5_3_desc", "Key": "rouge_tab_group_5_3_desc"}, "2252": {"Id": 2252, "StringId": "rouge_tab_group_5_4_desc", "Key": "rouge_tab_group_5_4_desc"}, "2253": {"Id": 2253, "StringId": "rouge_tab_group_5_5_desc", "Key": "rouge_tab_group_5_5_desc"}, "2254": {"Id": 2254, "StringId": "rouge_tab_group_5_6_desc", "Key": "rouge_tab_group_5_6_desc"}, "2255": {"Id": 2255, "StringId": "rouge_tab_group_5_9_desc", "Key": "rouge_tab_group_5_9_desc"}, "2256": {"Id": 2256, "StringId": "rouge_tab_group_5_11_desc", "Key": "rouge_tab_group_5_11_desc"}, "2257": {"Id": 2257, "StringId": "rouge_tab_group_5_13_desc", "Key": "rouge_tab_group_5_13_desc"}, "2258": {"Id": 2258, "StringId": "rouge_tab_group_6_0_desc", "Key": "rouge_tab_group_6_0_desc"}, "2259": {"Id": 2259, "StringId": "rouge_tab_group_6_1_desc", "Key": "rouge_tab_group_6_1_desc"}, "2260": {"Id": 2260, "StringId": "rouge_tab_group_6_2_desc", "Key": "rouge_tab_group_6_2_desc"}, "2261": {"Id": 2261, "StringId": "rouge_tab_group_6_3_desc", "Key": "rouge_tab_group_6_3_desc"}, "2262": {"Id": 2262, "StringId": "rouge_tab_group_6_4_desc", "Key": "rouge_tab_group_6_4_desc"}, "2263": {"Id": 2263, "StringId": "rouge_tab_group_6_5_desc", "Key": "rouge_tab_group_6_5_desc"}, "2264": {"Id": 2264, "StringId": "rouge_tab_group_6_6_desc", "Key": "rouge_tab_group_6_6_desc"}, "2265": {"Id": 2265, "StringId": "rouge_tab_group_6_7_desc", "Key": "rouge_tab_group_6_7_desc"}, "2266": {"Id": 2266, "StringId": "rouge_tab_group_6_10_desc", "Key": "rouge_tab_group_6_10_desc"}, "2267": {"Id": 2267, "StringId": "rouge_tab_group_6_12_desc", "Key": "rouge_tab_group_6_12_desc"}, "2268": {"Id": 2268, "StringId": "rouge_tab_group_6_15_desc", "Key": "rouge_tab_group_6_15_desc"}, "2269": {"Id": 2269, "StringId": "rouge_tab_group_6_16_desc", "Key": "rouge_tab_group_6_16_desc"}, "2270": {"Id": 2270, "StringId": "rouge_tab_group_6_17_desc", "Key": "rouge_tab_group_6_17_desc"}, "2271": {"Id": 2271, "StringId": "rouge_tab_group_7_0_desc", "Key": "rouge_tab_group_7_0_desc"}, "2272": {"Id": 2272, "StringId": "rouge_tab_group_7_1_desc", "Key": "rouge_tab_group_7_1_desc"}, "2273": {"Id": 2273, "StringId": "rouge_tab_group_7_2_desc", "Key": "rouge_tab_group_7_2_desc"}, "2274": {"Id": 2274, "StringId": "rouge_tab_group_7_3_desc", "Key": "rouge_tab_group_7_3_desc"}, "2275": {"Id": 2275, "StringId": "rouge_tab_group_7_4_desc", "Key": "rouge_tab_group_7_4_desc"}, "2276": {"Id": 2276, "StringId": "rouge_tab_group_7_5_desc", "Key": "rouge_tab_group_7_5_desc"}, "2277": {"Id": 2277, "StringId": "rouge_tab_group_7_6_desc", "Key": "rouge_tab_group_7_6_desc"}, "2278": {"Id": 2278, "StringId": "rouge_tab_group_7_7_desc", "Key": "rouge_tab_group_7_7_desc"}, "2279": {"Id": 2279, "StringId": "rouge_tab_group_7_8_desc", "Key": "rouge_tab_group_7_8_desc"}, "2280": {"Id": 2280, "StringId": "rouge_tab_group_7_9_desc", "Key": "rouge_tab_group_7_9_desc"}, "2281": {"Id": 2281, "StringId": "rouge_tab_group_7_12_desc", "Key": "rouge_tab_group_7_12_desc"}, "2282": {"Id": 2282, "StringId": "rouge_tab_group_8_0_desc", "Key": "rouge_tab_group_8_0_desc"}, "2283": {"Id": 2283, "StringId": "rouge_tab_group_8_1_desc", "Key": "rouge_tab_group_8_1_desc"}, "2284": {"Id": 2284, "StringId": "rouge_tab_group_8_2_desc", "Key": "rouge_tab_group_8_2_desc"}, "2285": {"Id": 2285, "StringId": "rouge_tab_group_8_3_desc", "Key": "rouge_tab_group_8_3_desc"}, "2286": {"Id": 2286, "StringId": "rouge_tab_group_8_4_desc", "Key": "rouge_tab_group_8_4_desc"}, "2287": {"Id": 2287, "StringId": "rouge_tab_group_8_5_desc", "Key": "rouge_tab_group_8_5_desc"}, "2288": {"Id": 2288, "StringId": "rouge_tab_group_8_6_desc", "Key": "rouge_tab_group_8_6_desc"}, "2289": {"Id": 2289, "StringId": "rouge_tab_group_8_8_desc", "Key": "rouge_tab_group_8_8_desc"}, "2290": {"Id": 2290, "StringId": "rouge_tab_group_8_10_desc", "Key": "rouge_tab_group_8_10_desc"}, "2291": {"Id": 2291, "StringId": "rouge_tab_group_8_13_desc", "Key": "rouge_tab_group_8_13_desc"}, "2292": {"Id": 2292, "StringId": "rouge_tab_group_8_14_desc", "Key": "rouge_tab_group_8_14_desc"}, "2293": {"Id": 2293, "StringId": "rouge_tab_group_8_15_desc", "Key": "rouge_tab_group_8_15_desc"}, "2294": {"Id": 2294, "StringId": "rouge_tab_group_9_0_desc", "Key": "rouge_tab_group_9_0_desc"}, "2295": {"Id": 2295, "StringId": "rouge_tab_group_9_1_desc", "Key": "rouge_tab_group_9_1_desc"}, "2296": {"Id": 2296, "StringId": "rouge_tab_group_9_2_desc", "Key": "rouge_tab_group_9_2_desc"}, "2297": {"Id": 2297, "StringId": "rouge_tab_group_9_3_desc", "Key": "rouge_tab_group_9_3_desc"}, "2298": {"Id": 2298, "StringId": "rouge_tab_group_9_4_desc", "Key": "rouge_tab_group_9_4_desc"}, "2299": {"Id": 2299, "StringId": "rouge_tab_group_9_5_desc", "Key": "rouge_tab_group_9_5_desc"}, "2300": {"Id": 2300, "StringId": "rouge_tab_group_9_6_desc", "Key": "rouge_tab_group_9_6_desc"}, "2301": {"Id": 2301, "StringId": "rouge_tab_group_9_8_desc", "Key": "rouge_tab_group_9_8_desc"}, "2302": {"Id": 2302, "StringId": "rouge_tab_group_9_9_desc", "Key": "rouge_tab_group_9_9_desc"}, "2303": {"Id": 2303, "StringId": "rouge_tab_group_9_14_desc", "Key": "rouge_tab_group_9_14_desc"}, "2304": {"Id": 2304, "StringId": "rouge_tab_group_10_0_desc", "Key": "rouge_tab_group_10_0_desc"}, "2305": {"Id": 2305, "StringId": "rouge_tab_group_10_1_desc", "Key": "rouge_tab_group_10_1_desc"}, "2306": {"Id": 2306, "StringId": "rouge_tab_group_10_2_desc", "Key": "rouge_tab_group_10_2_desc"}, "2307": {"Id": 2307, "StringId": "rouge_tab_group_10_3_desc", "Key": "rouge_tab_group_10_3_desc"}, "2308": {"Id": 2308, "StringId": "rouge_tab_group_10_4_desc", "Key": "rouge_tab_group_10_4_desc"}, "2309": {"Id": 2309, "StringId": "rouge_tab_group_10_5_desc", "Key": "rouge_tab_group_10_5_desc"}, "2310": {"Id": 2310, "StringId": "rouge_tab_group_10_6_desc", "Key": "rouge_tab_group_10_6_desc"}, "2311": {"Id": 2311, "StringId": "rouge_tab_group_10_7_desc", "Key": "rouge_tab_group_10_7_desc"}, "2312": {"Id": 2312, "StringId": "rouge_tab_group_10_8_desc", "Key": "rouge_tab_group_10_8_desc"}, "2313": {"Id": 2313, "StringId": "rouge_tab_group_10_9_desc", "Key": "rouge_tab_group_10_9_desc"}, "2314": {"Id": 2314, "StringId": "rouge_tab_group_10_11_desc", "Key": "rouge_tab_group_10_11_desc"}, "2315": {"Id": 2315, "StringId": "rouge_tab_group_10_12_desc", "Key": "rouge_tab_group_10_12_desc"}, "2316": {"Id": 2316, "StringId": "rouge_tab_group_10_13_desc", "Key": "rouge_tab_group_10_13_desc"}, "2317": {"Id": 2317, "StringId": "rouge_tab_group_10_18_desc", "Key": "rouge_tab_group_10_18_desc"}, "2318": {"Id": 2318, "StringId": "rouge_tab_group_10_19_desc", "Key": "rouge_tab_group_10_19_desc"}, "2319": {"Id": 2319, "StringId": "rouge_tab_group_11_0_desc", "Key": "rouge_tab_group_11_0_desc"}, "2320": {"Id": 2320, "StringId": "rouge_tab_group_11_1_desc", "Key": "rouge_tab_group_11_1_desc"}, "2321": {"Id": 2321, "StringId": "rouge_tab_group_11_2_desc", "Key": "rouge_tab_group_11_2_desc"}, "2322": {"Id": 2322, "StringId": "rouge_tab_group_11_3_desc", "Key": "rouge_tab_group_11_3_desc"}, "2323": {"Id": 2323, "StringId": "rouge_tab_group_11_4_desc", "Key": "rouge_tab_group_11_4_desc"}, "2324": {"Id": 2324, "StringId": "rouge_tab_group_11_5_desc", "Key": "rouge_tab_group_11_5_desc"}, "2325": {"Id": 2325, "StringId": "rouge_tab_group_11_6_desc", "Key": "rouge_tab_group_11_6_desc"}, "2326": {"Id": 2326, "StringId": "rouge_tab_group_11_7_desc", "Key": "rouge_tab_group_11_7_desc"}, "2327": {"Id": 2327, "StringId": "rouge_tab_group_11_10_desc", "Key": "rouge_tab_group_11_10_desc"}, "2328": {"Id": 2328, "StringId": "rouge_tab_group_11_12_desc", "Key": "rouge_tab_group_11_12_desc"}, "2329": {"Id": 2329, "StringId": "rouge_tab_group_11_16_desc", "Key": "rouge_tab_group_11_16_desc"}, "2330": {"Id": 2330, "StringId": "rouge_tab_group_12_0_desc", "Key": "rouge_tab_group_12_0_desc"}, "2331": {"Id": 2331, "StringId": "rouge_tab_group_12_1_desc", "Key": "rouge_tab_group_12_1_desc"}, "2332": {"Id": 2332, "StringId": "rouge_tab_group_12_2_desc", "Key": "rouge_tab_group_12_2_desc"}, "2333": {"Id": 2333, "StringId": "rouge_tab_group_12_3_desc", "Key": "rouge_tab_group_12_3_desc"}, "2334": {"Id": 2334, "StringId": "rouge_tab_group_12_4_desc", "Key": "rouge_tab_group_12_4_desc"}, "2335": {"Id": 2335, "StringId": "rouge_tab_group_12_5_desc", "Key": "rouge_tab_group_12_5_desc"}, "2336": {"Id": 2336, "StringId": "rouge_tab_group_12_6_desc", "Key": "rouge_tab_group_12_6_desc"}, "2337": {"Id": 2337, "StringId": "rouge_tab_group_12_7_desc", "Key": "rouge_tab_group_12_7_desc"}, "2338": {"Id": 2338, "StringId": "rouge_tab_group_12_8_desc", "Key": "rouge_tab_group_12_8_desc"}, "2339": {"Id": 2339, "StringId": "rouge_tab_group_12_9_desc", "Key": "rouge_tab_group_12_9_desc"}, "2340": {"Id": 2340, "StringId": "rouge_tab_group_12_10_desc", "Key": "rouge_tab_group_12_10_desc"}, "2341": {"Id": 2341, "StringId": "rouge_tab_group_12_11_desc", "Key": "rouge_tab_group_12_11_desc"}, "2342": {"Id": 2342, "StringId": "rouge_tab_group_12_12_desc", "Key": "rouge_tab_group_12_12_desc"}, "2343": {"Id": 2343, "StringId": "rouge_tab_group_12_14_desc", "Key": "rouge_tab_group_12_14_desc"}, "2344": {"Id": 2344, "StringId": "rouge_tab_group_12_16_desc", "Key": "rouge_tab_group_12_16_desc"}, "2345": {"Id": 2345, "StringId": "rouge_tab_group_12_18_desc", "Key": "rouge_tab_group_12_18_desc"}, "2346": {"Id": 2346, "StringId": "rouge_tab_group_12_19_desc", "Key": "rouge_tab_group_12_19_desc"}, "2347": {"Id": 2347, "StringId": "rouge_tab_group_13_0_desc", "Key": "rouge_tab_group_13_0_desc"}, "2348": {"Id": 2348, "StringId": "rouge_tab_group_13_1_desc", "Key": "rouge_tab_group_13_1_desc"}, "2349": {"Id": 2349, "StringId": "rouge_tab_group_13_2_desc", "Key": "rouge_tab_group_13_2_desc"}, "2350": {"Id": 2350, "StringId": "rouge_tab_group_13_3_desc", "Key": "rouge_tab_group_13_3_desc"}, "2351": {"Id": 2351, "StringId": "rouge_tab_group_13_4_desc", "Key": "rouge_tab_group_13_4_desc"}, "2352": {"Id": 2352, "StringId": "rouge_tab_group_13_5_desc", "Key": "rouge_tab_group_13_5_desc"}, "2353": {"Id": 2353, "StringId": "rouge_tab_group_13_6_desc", "Key": "rouge_tab_group_13_6_desc"}, "2354": {"Id": 2354, "StringId": "rouge_tab_group_13_8_desc", "Key": "rouge_tab_group_13_8_desc"}, "2355": {"Id": 2355, "StringId": "rouge_tab_group_13_9_desc", "Key": "rouge_tab_group_13_9_desc"}, "2356": {"Id": 2356, "StringId": "rouge_tab_group_14_0_desc", "Key": "rouge_tab_group_14_0_desc"}, "2357": {"Id": 2357, "StringId": "rouge_tab_group_14_1_desc", "Key": "rouge_tab_group_14_1_desc"}, "2358": {"Id": 2358, "StringId": "rouge_tab_group_14_2_desc", "Key": "rouge_tab_group_14_2_desc"}, "2359": {"Id": 2359, "StringId": "rouge_tab_group_14_3_desc", "Key": "rouge_tab_group_14_3_desc"}, "2360": {"Id": 2360, "StringId": "rouge_tab_group_14_4_desc", "Key": "rouge_tab_group_14_4_desc"}, "2361": {"Id": 2361, "StringId": "rouge_tab_group_14_5_desc", "Key": "rouge_tab_group_14_5_desc"}, "2362": {"Id": 2362, "StringId": "rouge_tab_group_14_6_desc", "Key": "rouge_tab_group_14_6_desc"}, "2363": {"Id": 2363, "StringId": "rouge_tab_group_14_7_desc", "Key": "rouge_tab_group_14_7_desc"}, "2364": {"Id": 2364, "StringId": "rouge_tab_group_14_8_desc", "Key": "rouge_tab_group_14_8_desc"}, "2365": {"Id": 2365, "StringId": "rouge_tab_group_14_9_desc", "Key": "rouge_tab_group_14_9_desc"}, "2366": {"Id": 2366, "StringId": "rouge_tab_group_14_11_desc", "Key": "rouge_tab_group_14_11_desc"}, "2367": {"Id": 2367, "StringId": "rouge_tab_group_14_17_desc", "Key": "rouge_tab_group_14_17_desc"}, "2368": {"Id": 2368, "StringId": "rouge_tab_group_14_18_desc", "Key": "rouge_tab_group_14_18_desc"}, "2369": {"Id": 2369, "StringId": "rouge_tab_group_15_0_desc", "Key": "rouge_tab_group_15_0_desc"}, "2370": {"Id": 2370, "StringId": "rouge_tab_group_15_1_desc", "Key": "rouge_tab_group_15_1_desc"}, "2371": {"Id": 2371, "StringId": "rouge_tab_group_15_2_desc", "Key": "rouge_tab_group_15_2_desc"}, "2372": {"Id": 2372, "StringId": "rouge_tab_group_15_3_desc", "Key": "rouge_tab_group_15_3_desc"}, "2373": {"Id": 2373, "StringId": "rouge_tab_group_15_4_desc", "Key": "rouge_tab_group_15_4_desc"}, "2374": {"Id": 2374, "StringId": "rouge_tab_group_15_5_desc", "Key": "rouge_tab_group_15_5_desc"}, "2375": {"Id": 2375, "StringId": "rouge_tab_group_15_6_desc", "Key": "rouge_tab_group_15_6_desc"}, "2376": {"Id": 2376, "StringId": "rouge_tab_group_15_7_desc", "Key": "rouge_tab_group_15_7_desc"}, "2377": {"Id": 2377, "StringId": "rouge_tab_group_15_10_desc", "Key": "rouge_tab_group_15_10_desc"}, "2378": {"Id": 2378, "StringId": "rouge_tab_group_15_12_desc", "Key": "rouge_tab_group_15_12_desc"}, "2379": {"Id": 2379, "StringId": "hero_3_0_rouge_skill_1_desc", "Key": "hero_3_0_rouge_skill_1_desc"}, "2380": {"Id": 2380, "StringId": "hero_3_0_rouge_skill_2_desc", "Key": "hero_3_0_rouge_skill_2_desc"}, "2381": {"Id": 2381, "StringId": "hero_3_0_rouge_skill_3_desc", "Key": "hero_3_0_rouge_skill_3_desc"}, "2382": {"Id": 2382, "StringId": "hero_3_0_rouge_skill_4_desc", "Key": "hero_3_0_rouge_skill_4_desc"}, "2383": {"Id": 2383, "StringId": "hero_3_0_rouge_skill_5_desc", "Key": "hero_3_0_rouge_skill_5_desc"}, "2384": {"Id": 2384, "StringId": "hero_3_0_rouge_skill_6_desc", "Key": "hero_3_0_rouge_skill_6_desc"}, "2385": {"Id": 2385, "StringId": "hero_3_0_rouge_skill_7_desc", "Key": "hero_3_0_rouge_skill_7_desc"}, "2386": {"Id": 2386, "StringId": "hero_3_0_rouge_skill_8_desc", "Key": "hero_3_0_rouge_skill_8_desc"}, "2387": {"Id": 2387, "StringId": "hero_3_1_rouge_skill_1_desc", "Key": "hero_3_1_rouge_skill_1_desc"}, "2388": {"Id": 2388, "StringId": "hero_3_1_rouge_skill_2_desc", "Key": "hero_3_1_rouge_skill_2_desc"}, "2389": {"Id": 2389, "StringId": "hero_3_1_rouge_skill_3_desc", "Key": "hero_3_1_rouge_skill_3_desc"}, "2390": {"Id": 2390, "StringId": "hero_3_1_rouge_skill_4_desc", "Key": "hero_3_1_rouge_skill_4_desc"}, "2391": {"Id": 2391, "StringId": "hero_3_1_rouge_skill_5_desc", "Key": "hero_3_1_rouge_skill_5_desc"}, "2392": {"Id": 2392, "StringId": "hero_3_1_rouge_skill_6_desc", "Key": "hero_3_1_rouge_skill_6_desc"}, "2393": {"Id": 2393, "StringId": "hero_3_1_rouge_skill_7_desc", "Key": "hero_3_1_rouge_skill_7_desc"}, "2394": {"Id": 2394, "StringId": "hero_3_1_rouge_skill_8_desc", "Key": "hero_3_1_rouge_skill_8_desc"}, "2395": {"Id": 2395, "StringId": "hero_3_2_rouge_skill_1_desc", "Key": "hero_3_2_rouge_skill_1_desc"}, "2396": {"Id": 2396, "StringId": "hero_3_2_rouge_skill_2_desc", "Key": "hero_3_2_rouge_skill_2_desc"}, "2397": {"Id": 2397, "StringId": "hero_3_2_rouge_skill_3_desc", "Key": "hero_3_2_rouge_skill_3_desc"}, "2398": {"Id": 2398, "StringId": "hero_3_2_rouge_skill_4_desc", "Key": "hero_3_2_rouge_skill_4_desc"}, "2399": {"Id": 2399, "StringId": "hero_3_2_rouge_skill_5_desc", "Key": "hero_3_2_rouge_skill_5_desc"}, "2400": {"Id": 2400, "StringId": "hero_3_2_rouge_skill_6_desc", "Key": "hero_3_2_rouge_skill_6_desc"}, "2401": {"Id": 2401, "StringId": "hero_3_2_rouge_skill_7_desc", "Key": "hero_3_2_rouge_skill_7_desc"}, "2402": {"Id": 2402, "StringId": "hero_3_2_rouge_skill_8_desc", "Key": "hero_3_2_rouge_skill_8_desc"}, "2403": {"Id": 2403, "StringId": "hero_3_3_rouge_skill_1_desc", "Key": "hero_3_3_rouge_skill_1_desc"}, "2404": {"Id": 2404, "StringId": "hero_3_3_rouge_skill_2_desc", "Key": "hero_3_3_rouge_skill_2_desc"}, "2405": {"Id": 2405, "StringId": "hero_3_3_rouge_skill_3_desc", "Key": "hero_3_3_rouge_skill_3_desc"}, "2406": {"Id": 2406, "StringId": "hero_3_3_rouge_skill_4_desc", "Key": "hero_3_3_rouge_skill_4_desc"}, "2407": {"Id": 2407, "StringId": "hero_3_3_rouge_skill_5_desc", "Key": "hero_3_3_rouge_skill_5_desc"}, "2408": {"Id": 2408, "StringId": "hero_3_3_rouge_skill_6_desc", "Key": "hero_3_3_rouge_skill_6_desc"}, "2409": {"Id": 2409, "StringId": "hero_3_3_rouge_skill_7_desc", "Key": "hero_3_3_rouge_skill_7_desc"}, "2410": {"Id": 2410, "StringId": "hero_3_3_rouge_skill_8_desc", "Key": "hero_3_3_rouge_skill_8_desc"}, "2411": {"Id": 2411, "StringId": "hero_3_4_rouge_skill_1_desc", "Key": "hero_3_4_rouge_skill_1_desc"}, "2412": {"Id": 2412, "StringId": "hero_3_4_rouge_skill_2_desc", "Key": "hero_3_4_rouge_skill_2_desc"}, "2413": {"Id": 2413, "StringId": "hero_3_4_rouge_skill_3_desc", "Key": "hero_3_4_rouge_skill_3_desc"}, "2414": {"Id": 2414, "StringId": "hero_3_4_rouge_skill_4_desc", "Key": "hero_3_4_rouge_skill_4_desc"}, "2415": {"Id": 2415, "StringId": "hero_3_4_rouge_skill_5_desc", "Key": "hero_3_4_rouge_skill_5_desc"}, "2416": {"Id": 2416, "StringId": "hero_3_4_rouge_skill_6_desc", "Key": "hero_3_4_rouge_skill_6_desc"}, "2417": {"Id": 2417, "StringId": "hero_3_4_rouge_skill_7_desc", "Key": "hero_3_4_rouge_skill_7_desc"}, "2418": {"Id": 2418, "StringId": "hero_3_4_rouge_skill_8_desc", "Key": "hero_3_4_rouge_skill_8_desc"}, "2419": {"Id": 2419, "StringId": "hero_3_5_rouge_skill_1_desc", "Key": "hero_3_5_rouge_skill_1_desc"}, "2420": {"Id": 2420, "StringId": "hero_3_5_rouge_skill_2_desc", "Key": "hero_3_5_rouge_skill_2_desc"}, "2421": {"Id": 2421, "StringId": "hero_3_5_rouge_skill_3_desc", "Key": "hero_3_5_rouge_skill_3_desc"}, "2422": {"Id": 2422, "StringId": "hero_3_5_rouge_skill_4_desc", "Key": "hero_3_5_rouge_skill_4_desc"}, "2423": {"Id": 2423, "StringId": "hero_3_5_rouge_skill_5_desc", "Key": "hero_3_5_rouge_skill_5_desc"}, "2424": {"Id": 2424, "StringId": "hero_3_5_rouge_skill_6_desc", "Key": "hero_3_5_rouge_skill_6_desc"}, "2425": {"Id": 2425, "StringId": "hero_3_5_rouge_skill_7_desc", "Key": "hero_3_5_rouge_skill_7_desc"}, "2426": {"Id": 2426, "StringId": "hero_3_5_rouge_skill_8_desc", "Key": "hero_3_5_rouge_skill_8_desc"}, "2427": {"Id": 2427, "StringId": "hero_2_0_rouge_skill_1_desc", "Key": "hero_2_0_rouge_skill_1_desc"}, "2428": {"Id": 2428, "StringId": "hero_2_0_rouge_skill_2_desc", "Key": "hero_2_0_rouge_skill_2_desc"}, "2429": {"Id": 2429, "StringId": "hero_2_0_rouge_skill_3_desc", "Key": "hero_2_0_rouge_skill_3_desc"}, "2430": {"Id": 2430, "StringId": "hero_2_0_rouge_skill_4_desc", "Key": "hero_2_0_rouge_skill_4_desc"}, "2431": {"Id": 2431, "StringId": "hero_2_0_rouge_skill_5_desc", "Key": "hero_2_0_rouge_skill_5_desc"}, "2432": {"Id": 2432, "StringId": "hero_2_0_rouge_skill_6_desc", "Key": "hero_2_0_rouge_skill_6_desc"}, "2433": {"Id": 2433, "StringId": "hero_2_0_rouge_skill_7_desc", "Key": "hero_2_0_rouge_skill_7_desc"}, "2434": {"Id": 2434, "StringId": "hero_2_0_rouge_skill_8_desc", "Key": "hero_2_0_rouge_skill_8_desc"}, "2435": {"Id": 2435, "StringId": "hero_2_1_rouge_skill_1_desc", "Key": "hero_2_1_rouge_skill_1_desc"}, "2436": {"Id": 2436, "StringId": "hero_2_1_rouge_skill_2_desc", "Key": "hero_2_1_rouge_skill_2_desc"}, "2437": {"Id": 2437, "StringId": "hero_2_1_rouge_skill_3_desc", "Key": "hero_2_1_rouge_skill_3_desc"}, "2438": {"Id": 2438, "StringId": "hero_2_1_rouge_skill_4_desc", "Key": "hero_2_1_rouge_skill_4_desc"}, "2439": {"Id": 2439, "StringId": "hero_2_1_rouge_skill_5_desc", "Key": "hero_2_1_rouge_skill_5_desc"}, "2440": {"Id": 2440, "StringId": "hero_2_1_rouge_skill_6_desc", "Key": "hero_2_1_rouge_skill_6_desc"}, "2441": {"Id": 2441, "StringId": "hero_2_1_rouge_skill_7_desc", "Key": "hero_2_1_rouge_skill_7_desc"}, "2442": {"Id": 2442, "StringId": "hero_2_1_rouge_skill_8_desc", "Key": "hero_2_1_rouge_skill_8_desc"}, "2443": {"Id": 2443, "StringId": "hero_2_2_rouge_skill_1_desc", "Key": "hero_2_2_rouge_skill_1_desc"}, "2444": {"Id": 2444, "StringId": "hero_2_2_rouge_skill_2_desc", "Key": "hero_2_2_rouge_skill_2_desc"}, "2445": {"Id": 2445, "StringId": "hero_2_2_rouge_skill_3_desc", "Key": "hero_2_2_rouge_skill_3_desc"}, "2446": {"Id": 2446, "StringId": "hero_2_2_rouge_skill_4_desc", "Key": "hero_2_2_rouge_skill_4_desc"}, "2447": {"Id": 2447, "StringId": "hero_2_2_rouge_skill_5_desc", "Key": "hero_2_2_rouge_skill_5_desc"}, "2448": {"Id": 2448, "StringId": "hero_2_2_rouge_skill_6_desc", "Key": "hero_2_2_rouge_skill_6_desc"}, "2449": {"Id": 2449, "StringId": "hero_2_2_rouge_skill_7_desc", "Key": "hero_2_2_rouge_skill_7_desc"}, "2450": {"Id": 2450, "StringId": "hero_2_2_rouge_skill_8_desc", "Key": "hero_2_2_rouge_skill_8_desc"}, "2451": {"Id": 2451, "StringId": "hero_2_3_rouge_skill_1_desc", "Key": "hero_2_3_rouge_skill_1_desc"}, "2452": {"Id": 2452, "StringId": "hero_2_3_rouge_skill_2_desc", "Key": "hero_2_3_rouge_skill_2_desc"}, "2453": {"Id": 2453, "StringId": "hero_2_3_rouge_skill_3_desc", "Key": "hero_2_3_rouge_skill_3_desc"}, "2454": {"Id": 2454, "StringId": "hero_2_3_rouge_skill_4_desc", "Key": "hero_2_3_rouge_skill_4_desc"}, "2455": {"Id": 2455, "StringId": "hero_2_3_rouge_skill_5_desc", "Key": "hero_2_3_rouge_skill_5_desc"}, "2456": {"Id": 2456, "StringId": "hero_2_3_rouge_skill_6_desc", "Key": "hero_2_3_rouge_skill_6_desc"}, "2457": {"Id": 2457, "StringId": "hero_2_3_rouge_skill_7_desc", "Key": "hero_2_3_rouge_skill_7_desc"}, "2458": {"Id": 2458, "StringId": "hero_2_3_rouge_skill_8_desc", "Key": "hero_2_3_rouge_skill_8_desc"}, "2459": {"Id": 2459, "StringId": "hero_2_4_rouge_skill_1_desc", "Key": "hero_2_4_rouge_skill_1_desc"}, "2460": {"Id": 2460, "StringId": "hero_2_4_rouge_skill_2_desc", "Key": "hero_2_4_rouge_skill_2_desc"}, "2461": {"Id": 2461, "StringId": "hero_2_4_rouge_skill_3_desc", "Key": "hero_2_4_rouge_skill_3_desc"}, "2462": {"Id": 2462, "StringId": "hero_2_4_rouge_skill_4_desc", "Key": "hero_2_4_rouge_skill_4_desc"}, "2463": {"Id": 2463, "StringId": "hero_2_4_rouge_skill_5_desc", "Key": "hero_2_4_rouge_skill_5_desc"}, "2464": {"Id": 2464, "StringId": "hero_2_4_rouge_skill_6_desc", "Key": "hero_2_4_rouge_skill_6_desc"}, "2465": {"Id": 2465, "StringId": "hero_2_4_rouge_skill_7_desc", "Key": "hero_2_4_rouge_skill_7_desc"}, "2466": {"Id": 2466, "StringId": "hero_2_4_rouge_skill_8_desc", "Key": "hero_2_4_rouge_skill_8_desc"}, "2467": {"Id": 2467, "StringId": "hero_2_5_rouge_skill_1_desc", "Key": "hero_2_5_rouge_skill_1_desc"}, "2468": {"Id": 2468, "StringId": "hero_2_5_rouge_skill_2_desc", "Key": "hero_2_5_rouge_skill_2_desc"}, "2469": {"Id": 2469, "StringId": "hero_2_5_rouge_skill_3_desc", "Key": "hero_2_5_rouge_skill_3_desc"}, "2470": {"Id": 2470, "StringId": "hero_2_5_rouge_skill_4_desc", "Key": "hero_2_5_rouge_skill_4_desc"}, "2471": {"Id": 2471, "StringId": "hero_2_5_rouge_skill_5_desc", "Key": "hero_2_5_rouge_skill_5_desc"}, "2472": {"Id": 2472, "StringId": "hero_2_5_rouge_skill_6_desc", "Key": "hero_2_5_rouge_skill_6_desc"}, "2473": {"Id": 2473, "StringId": "hero_2_5_rouge_skill_7_desc", "Key": "hero_2_5_rouge_skill_7_desc"}, "2474": {"Id": 2474, "StringId": "hero_2_5_rouge_skill_8_desc", "Key": "hero_2_5_rouge_skill_8_desc"}, "2475": {"Id": 2475, "StringId": "hero_2_6_rouge_skill_1_desc", "Key": "hero_2_6_rouge_skill_1_desc"}, "2476": {"Id": 2476, "StringId": "hero_2_6_rouge_skill_2_desc", "Key": "hero_2_6_rouge_skill_2_desc"}, "2477": {"Id": 2477, "StringId": "hero_2_6_rouge_skill_3_desc", "Key": "hero_2_6_rouge_skill_3_desc"}, "2478": {"Id": 2478, "StringId": "hero_2_6_rouge_skill_4_desc", "Key": "hero_2_6_rouge_skill_4_desc"}, "2479": {"Id": 2479, "StringId": "hero_2_6_rouge_skill_5_desc", "Key": "hero_2_6_rouge_skill_5_desc"}, "2480": {"Id": 2480, "StringId": "hero_2_6_rouge_skill_6_desc", "Key": "hero_2_6_rouge_skill_6_desc"}, "2481": {"Id": 2481, "StringId": "hero_2_6_rouge_skill_7_desc", "Key": "hero_2_6_rouge_skill_7_desc"}, "2482": {"Id": 2482, "StringId": "hero_2_6_rouge_skill_8_desc", "Key": "hero_2_6_rouge_skill_8_desc"}, "2483": {"Id": 2483, "StringId": "hero_1_0_rouge_skill_1_desc", "Key": "hero_1_0_rouge_skill_1_desc"}, "2484": {"Id": 2484, "StringId": "hero_1_0_rouge_skill_2_desc", "Key": "hero_1_0_rouge_skill_2_desc"}, "2485": {"Id": 2485, "StringId": "hero_1_0_rouge_skill_3_desc", "Key": "hero_1_0_rouge_skill_3_desc"}, "2486": {"Id": 2486, "StringId": "hero_1_0_rouge_skill_4_desc", "Key": "hero_1_0_rouge_skill_4_desc"}, "2487": {"Id": 2487, "StringId": "hero_1_0_rouge_skill_5_desc", "Key": "hero_1_0_rouge_skill_5_desc"}, "2488": {"Id": 2488, "StringId": "hero_1_0_rouge_skill_6_desc", "Key": "hero_1_0_rouge_skill_6_desc"}, "2489": {"Id": 2489, "StringId": "hero_1_0_rouge_skill_7_desc", "Key": "hero_1_0_rouge_skill_7_desc"}, "2490": {"Id": 2490, "StringId": "hero_1_0_rouge_skill_8_desc", "Key": "hero_1_0_rouge_skill_8_desc"}, "2491": {"Id": 2491, "StringId": "hero_1_1_rouge_skill_1_desc", "Key": "hero_1_1_rouge_skill_1_desc"}, "2492": {"Id": 2492, "StringId": "hero_1_1_rouge_skill_2_desc", "Key": "hero_1_1_rouge_skill_2_desc"}, "2493": {"Id": 2493, "StringId": "hero_1_1_rouge_skill_3_desc", "Key": "hero_1_1_rouge_skill_3_desc"}, "2494": {"Id": 2494, "StringId": "hero_1_1_rouge_skill_4_desc", "Key": "hero_1_1_rouge_skill_4_desc"}, "2495": {"Id": 2495, "StringId": "hero_1_1_rouge_skill_5_desc", "Key": "hero_1_1_rouge_skill_5_desc"}, "2496": {"Id": 2496, "StringId": "hero_1_1_rouge_skill_6_desc", "Key": "hero_1_1_rouge_skill_6_desc"}, "2497": {"Id": 2497, "StringId": "hero_1_1_rouge_skill_7_desc", "Key": "hero_1_1_rouge_skill_7_desc"}, "2498": {"Id": 2498, "StringId": "hero_1_1_rouge_skill_8_desc", "Key": "hero_1_1_rouge_skill_8_desc"}, "2499": {"Id": 2499, "StringId": "hero_rouge_skill_passive_title", "Key": "hero_rouge_skill_passive_title"}, "2500": {"Id": 2500, "StringId": "hero_rouge_skill_tab_title", "Key": "hero_rouge_skill_tab_title"}, "2501": {"Id": 2501, "StringId": "rouge_tab_10001_name", "Key": "rouge_tab_10001_name"}, "2502": {"Id": 2502, "StringId": "rouge_tab_10002_name", "Key": "rouge_tab_10002_name"}, "2503": {"Id": 2503, "StringId": "rouge_tab_10003_name", "Key": "rouge_tab_10003_name"}, "2504": {"Id": 2504, "StringId": "rouge_tab_10001_desc", "Key": "rouge_tab_10001_desc"}, "2505": {"Id": 2505, "StringId": "rouge_tab_10002_desc", "Key": "rouge_tab_10002_desc"}, "2506": {"Id": 2506, "StringId": "rouge_tab_10003_desc", "Key": "rouge_tab_10003_desc"}, "2507": {"Id": 2507, "StringId": "rouge_explain_1", "Key": "rouge_explain_1"}, "2508": {"Id": 2508, "StringId": "rouge_explain_2", "Key": "rouge_explain_2"}, "2509": {"Id": 2509, "StringId": "rouge_explain_3", "Key": "rouge_explain_3"}, "2510": {"Id": 2510, "StringId": "rouge_explain_4", "Key": "rouge_explain_4"}, "2511": {"Id": 2511, "StringId": "rouge_explain_5", "Key": "rouge_explain_5"}, "2512": {"Id": 2512, "StringId": "rouge_explain_6", "Key": "rouge_explain_6"}, "2513": {"Id": 2513, "StringId": "rouge_explain_7", "Key": "rouge_explain_7"}, "2514": {"Id": 2514, "StringId": "rouge_explain_8", "Key": "rouge_explain_8"}, "2515": {"Id": 2515, "StringId": "rouge_explain_9", "Key": "rouge_explain_9"}, "2516": {"Id": 2516, "StringId": "rouge_explain_10", "Key": "rouge_explain_10"}, "2517": {"Id": 2517, "StringId": "rouge_explain_11", "Key": "rouge_explain_11"}, "2518": {"Id": 2518, "StringId": "rouge_explain_12", "Key": "rouge_explain_12"}, "2519": {"Id": 2519, "StringId": "rouge_explain_13", "Key": "rouge_explain_13"}, "2520": {"Id": 2520, "StringId": "rouge_explain_14", "Key": "rouge_explain_14"}, "2521": {"Id": 2521, "StringId": "rouge_explain_15", "Key": "rouge_explain_15"}, "2522": {"Id": 2522, "StringId": "rouge_explain_16", "Key": "rouge_explain_16"}, "2523": {"Id": 2523, "StringId": "rouge_explain_17", "Key": "rouge_explain_17"}, "2601": {"Id": 2601, "StringId": "hero_3_0_gift_break_through_1", "Key": "hero_3_0_gift_break_through_1"}, "2602": {"Id": 2602, "StringId": "hero_3_0_gift_break_through_2", "Key": "hero_3_0_gift_break_through_2"}, "2603": {"Id": 2603, "StringId": "hero_3_0_gift_break_through_3", "Key": "hero_3_0_gift_break_through_3"}, "2604": {"Id": 2604, "StringId": "hero_3_0_gift_break_through_4", "Key": "hero_3_0_gift_break_through_4"}, "2605": {"Id": 2605, "StringId": "hero_3_1_gift_break_through_1", "Key": "hero_3_1_gift_break_through_1"}, "2606": {"Id": 2606, "StringId": "hero_3_1_gift_break_through_2", "Key": "hero_3_1_gift_break_through_2"}, "2607": {"Id": 2607, "StringId": "hero_3_1_gift_break_through_3", "Key": "hero_3_1_gift_break_through_3"}, "2608": {"Id": 2608, "StringId": "hero_3_1_gift_break_through_4", "Key": "hero_3_1_gift_break_through_4"}, "2609": {"Id": 2609, "StringId": "hero_3_2_gift_break_through_1", "Key": "hero_3_2_gift_break_through_1"}, "2610": {"Id": 2610, "StringId": "hero_3_2_gift_break_through_2", "Key": "hero_3_2_gift_break_through_2"}, "2611": {"Id": 2611, "StringId": "hero_3_2_gift_break_through_3", "Key": "hero_3_2_gift_break_through_3"}, "2612": {"Id": 2612, "StringId": "hero_3_2_gift_break_through_4", "Key": "hero_3_2_gift_break_through_4"}, "2613": {"Id": 2613, "StringId": "hero_3_3_gift_break_through_1", "Key": "hero_3_3_gift_break_through_1"}, "2614": {"Id": 2614, "StringId": "hero_3_3_gift_break_through_2", "Key": "hero_3_3_gift_break_through_2"}, "2615": {"Id": 2615, "StringId": "hero_3_3_gift_break_through_3", "Key": "hero_3_3_gift_break_through_3"}, "2616": {"Id": 2616, "StringId": "hero_3_3_gift_break_through_4", "Key": "hero_3_3_gift_break_through_4"}, "2617": {"Id": 2617, "StringId": "hero_3_4_gift_break_through_1", "Key": "hero_3_4_gift_break_through_1"}, "2618": {"Id": 2618, "StringId": "hero_3_4_gift_break_through_2", "Key": "hero_3_4_gift_break_through_2"}, "2619": {"Id": 2619, "StringId": "hero_3_4_gift_break_through_3", "Key": "hero_3_4_gift_break_through_3"}, "2620": {"Id": 2620, "StringId": "hero_3_4_gift_break_through_4", "Key": "hero_3_4_gift_break_through_4"}, "2621": {"Id": 2621, "StringId": "hero_3_5_gift_break_through_1", "Key": "hero_3_5_gift_break_through_1"}, "2622": {"Id": 2622, "StringId": "hero_3_5_gift_break_through_2", "Key": "hero_3_5_gift_break_through_2"}, "2623": {"Id": 2623, "StringId": "hero_3_5_gift_break_through_3", "Key": "hero_3_5_gift_break_through_3"}, "2624": {"Id": 2624, "StringId": "hero_3_5_gift_break_through_4", "Key": "hero_3_5_gift_break_through_4"}, "2625": {"Id": 2625, "StringId": "hero_2_0_gift_break_through_1", "Key": "hero_2_0_gift_break_through_1"}, "2626": {"Id": 2626, "StringId": "hero_2_0_gift_break_through_2", "Key": "hero_2_0_gift_break_through_2"}, "2627": {"Id": 2627, "StringId": "hero_2_0_gift_break_through_3", "Key": "hero_2_0_gift_break_through_3"}, "2628": {"Id": 2628, "StringId": "hero_2_0_gift_break_through_4", "Key": "hero_2_0_gift_break_through_4"}, "2629": {"Id": 2629, "StringId": "hero_2_1_gift_break_through_1", "Key": "hero_2_1_gift_break_through_1"}, "2630": {"Id": 2630, "StringId": "hero_2_1_gift_break_through_2", "Key": "hero_2_1_gift_break_through_2"}, "2631": {"Id": 2631, "StringId": "hero_2_1_gift_break_through_3", "Key": "hero_2_1_gift_break_through_3"}, "2632": {"Id": 2632, "StringId": "hero_2_1_gift_break_through_4", "Key": "hero_2_1_gift_break_through_4"}, "2633": {"Id": 2633, "StringId": "hero_2_2_gift_break_through_1", "Key": "hero_2_2_gift_break_through_1"}, "2634": {"Id": 2634, "StringId": "hero_2_2_gift_break_through_2", "Key": "hero_2_2_gift_break_through_2"}, "2635": {"Id": 2635, "StringId": "hero_2_2_gift_break_through_3", "Key": "hero_2_2_gift_break_through_3"}, "2636": {"Id": 2636, "StringId": "hero_2_2_gift_break_through_4", "Key": "hero_2_2_gift_break_through_4"}, "2637": {"Id": 2637, "StringId": "hero_2_3_gift_break_through_1", "Key": "hero_2_3_gift_break_through_1"}, "2638": {"Id": 2638, "StringId": "hero_2_3_gift_break_through_2", "Key": "hero_2_3_gift_break_through_2"}, "2639": {"Id": 2639, "StringId": "hero_2_3_gift_break_through_3", "Key": "hero_2_3_gift_break_through_3"}, "2640": {"Id": 2640, "StringId": "hero_2_3_gift_break_through_4", "Key": "hero_2_3_gift_break_through_4"}, "2641": {"Id": 2641, "StringId": "hero_2_4_gift_break_through_1", "Key": "hero_2_4_gift_break_through_1"}, "2642": {"Id": 2642, "StringId": "hero_2_4_gift_break_through_2", "Key": "hero_2_4_gift_break_through_2"}, "2643": {"Id": 2643, "StringId": "hero_2_4_gift_break_through_3", "Key": "hero_2_4_gift_break_through_3"}, "2644": {"Id": 2644, "StringId": "hero_2_4_gift_break_through_4", "Key": "hero_2_4_gift_break_through_4"}, "2645": {"Id": 2645, "StringId": "hero_2_5_gift_break_through_1", "Key": "hero_2_5_gift_break_through_1"}, "2646": {"Id": 2646, "StringId": "hero_2_5_gift_break_through_2", "Key": "hero_2_5_gift_break_through_2"}, "2647": {"Id": 2647, "StringId": "hero_2_5_gift_break_through_3", "Key": "hero_2_5_gift_break_through_3"}, "2648": {"Id": 2648, "StringId": "hero_2_5_gift_break_through_4", "Key": "hero_2_5_gift_break_through_4"}, "2649": {"Id": 2649, "StringId": "hero_2_6_gift_break_through_1", "Key": "hero_2_6_gift_break_through_1"}, "2650": {"Id": 2650, "StringId": "hero_2_6_gift_break_through_2", "Key": "hero_2_6_gift_break_through_2"}, "2651": {"Id": 2651, "StringId": "hero_2_6_gift_break_through_3", "Key": "hero_2_6_gift_break_through_3"}, "2652": {"Id": 2652, "StringId": "hero_2_6_gift_break_through_4", "Key": "hero_2_6_gift_break_through_4"}, "2653": {"Id": 2653, "StringId": "hero_1_0_gift_break_through_1", "Key": "hero_1_0_gift_break_through_1"}, "2654": {"Id": 2654, "StringId": "hero_1_0_gift_break_through_2", "Key": "hero_1_0_gift_break_through_2"}, "2655": {"Id": 2655, "StringId": "hero_1_0_gift_break_through_3", "Key": "hero_1_0_gift_break_through_3"}, "2656": {"Id": 2656, "StringId": "hero_1_0_gift_break_through_4", "Key": "hero_1_0_gift_break_through_4"}, "2657": {"Id": 2657, "StringId": "hero_1_1_gift_break_through_1", "Key": "hero_1_1_gift_break_through_1"}, "2658": {"Id": 2658, "StringId": "hero_1_1_gift_break_through_2", "Key": "hero_1_1_gift_break_through_2"}, "2659": {"Id": 2659, "StringId": "hero_1_1_gift_break_through_3", "Key": "hero_1_1_gift_break_through_3"}, "2660": {"Id": 2660, "StringId": "hero_1_1_gift_break_through_4", "Key": "hero_1_1_gift_break_through_4"}, "3001": {"Id": 3001, "StringId": "curtain_buff_dmg_up_5%_name", "Key": "curtain_buff_dmg_up_5%_name"}, "3002": {"Id": 3002, "StringId": "curtain_buff_dmg_down_5%_name", "Key": "curtain_buff_dmg_down_5%_name"}, "3003": {"Id": 3003, "StringId": "level_1_1_1_obstacle_1_name", "Key": "level_1_1_1_obstacle_1_name"}, "3004": {"Id": 3004, "StringId": "level_1_1_1_obstacle_2_name", "Key": "level_1_1_1_obstacle_2_name"}, "3005": {"Id": 3005, "StringId": "map_event_prop_1_name", "Key": "map_event_prop_1_name"}, "3006": {"Id": 3006, "StringId": "map_event_prop_2_name", "Key": "map_event_prop_2_name"}, "3007": {"Id": 3007, "StringId": "map_event_prop_3_name", "Key": "map_event_prop_3_name"}, "3008": {"Id": 3008, "StringId": "map_event_prop_4_name", "Key": "map_event_prop_4_name"}, "3009": {"Id": 3009, "StringId": "map_event_prop_5_name", "Key": "map_event_prop_5_name"}, "3010": {"Id": 3010, "StringId": "map_event_obstacle_1_skill_desc", "Key": "map_event_obstacle_1_skill_desc"}, "3011": {"Id": 3011, "StringId": "map_event_obstacle_2_skill_desc", "Key": "map_event_obstacle_2_skill_desc"}, "3012": {"Id": 3012, "StringId": "map_event_prop_1_skill_desc", "Key": "map_event_prop_1_skill_desc"}, "3013": {"Id": 3013, "StringId": "map_event_prop_2_skill_desc", "Key": "map_event_prop_2_skill_desc"}, "3014": {"Id": 3014, "StringId": "map_event_prop_3_skill_desc", "Key": "map_event_prop_3_skill_desc"}, "3015": {"Id": 3015, "StringId": "map_event_prop_4_skill_desc", "Key": "map_event_prop_4_skill_desc"}, "3016": {"Id": 3016, "StringId": "map_event_prop_5_skill_desc", "Key": "map_event_prop_5_skill_desc"}, "3017": {"Id": 3017, "StringId": "map_event_buff_1_skill_desc", "Key": "map_event_buff_1_skill_desc"}, "3018": {"Id": 3018, "StringId": "map_event_buff_2_skill_desc", "Key": "map_event_buff_2_skill_desc"}, "3019": {"Id": 3019, "StringId": "chapter_1_name", "Key": "chapter_1_name"}, "3020": {"Id": 3020, "StringId": "chapter_2_name", "Key": "chapter_2_name"}, "3021": {"Id": 3021, "StringId": "chapter_3_name", "Key": "chapter_3_name"}, "3022": {"Id": 3022, "StringId": "chapter_4_name", "Key": "chapter_4_name"}, "3023": {"Id": 3023, "StringId": "chapter_5_name", "Key": "chapter_5_name"}, "3024": {"Id": 3024, "StringId": "chapter_6_name", "Key": "chapter_6_name"}, "3025": {"Id": 3025, "StringId": "chapter_7_name", "Key": "chapter_7_name"}, "3026": {"Id": 3026, "StringId": "chapter_8_name", "Key": "chapter_8_name"}, "3027": {"Id": 3027, "StringId": "chapter_9_name", "Key": "chapter_9_name"}, "3028": {"Id": 3028, "StringId": "chapter_10_name", "Key": "chapter_10_name"}, "3029": {"Id": 3029, "StringId": "chapter_11_name", "Key": "chapter_11_name"}, "3030": {"Id": 3030, "StringId": "chapter_12_name", "Key": "chapter_12_name"}, "3031": {"Id": 3031, "StringId": "chapter_13_name", "Key": "chapter_13_name"}, "3032": {"Id": 3032, "StringId": "chapter_14_name", "Key": "chapter_14_name"}, "3033": {"Id": 3033, "StringId": "chapter_15_name", "Key": "chapter_15_name"}, "3034": {"Id": 3034, "StringId": "chapter_3_2_name", "Key": "chapter_3_2_name"}, "3035": {"Id": 3035, "StringId": "chapter_6_2_name", "Key": "chapter_6_2_name"}, "3036": {"Id": 3036, "StringId": "chapter_9_2_name", "Key": "chapter_9_2_name"}, "3037": {"Id": 3037, "StringId": "chapter_12_2_name", "Key": "chapter_12_2_name"}, "3038": {"Id": 3038, "StringId": "chapter_15_2_name", "Key": "chapter_15_2_name"}, "3039": {"Id": 3039, "StringId": "chapter_16_name", "Key": "chapter_16_name"}, "3040": {"Id": 3040, "StringId": "chapter_17_name", "Key": "chapter_17_name"}, "3041": {"Id": 3041, "StringId": "chapter_18_name", "Key": "chapter_18_name"}, "3042": {"Id": 3042, "StringId": "chapter_19_name", "Key": "chapter_19_name"}, "3043": {"Id": 3043, "StringId": "chapter_20_name", "Key": "chapter_20_name"}, "3044": {"Id": 3044, "StringId": "chapter_21_name", "Key": "chapter_21_name"}, "3045": {"Id": 3045, "StringId": "chapter_22_name", "Key": "chapter_22_name"}, "3046": {"Id": 3046, "StringId": "chapter_23_name", "Key": "chapter_23_name"}, "3047": {"Id": 3047, "StringId": "chapter_24_name", "Key": "chapter_24_name"}, "3048": {"Id": 3048, "StringId": "chapter_25_name", "Key": "chapter_25_name"}, "3049": {"Id": 3049, "StringId": "chapter_26_name", "Key": "chapter_26_name"}, "3050": {"Id": 3050, "StringId": "chapter_27_name", "Key": "chapter_27_name"}, "3051": {"Id": 3051, "StringId": "chapter_28_name", "Key": "chapter_28_name"}, "3052": {"Id": 3052, "StringId": "chapter_29_name", "Key": "chapter_29_name"}, "3053": {"Id": 3053, "StringId": "chapter_30_name", "Key": "chapter_30_name"}, "3054": {"Id": 3054, "StringId": "chapter_31_name", "Key": "chapter_31_name"}, "3055": {"Id": 3055, "StringId": "chapter_32_name", "Key": "chapter_32_name"}, "3056": {"Id": 3056, "StringId": "chapter_33_name", "Key": "chapter_33_name"}, "3057": {"Id": 3057, "StringId": "chapter_34_name", "Key": "chapter_34_name"}, "3058": {"Id": 3058, "StringId": "chapter_35_name", "Key": "chapter_35_name"}, "3059": {"Id": 3059, "StringId": "chapter_36_name", "Key": "chapter_36_name"}, "3060": {"Id": 3060, "StringId": "chapter_37_name", "Key": "chapter_37_name"}, "3061": {"Id": 3061, "StringId": "chapter_38_name", "Key": "chapter_38_name"}, "3062": {"Id": 3062, "StringId": "chapter_39_name", "Key": "chapter_39_name"}, "3063": {"Id": 3063, "StringId": "chapter_40_name", "Key": "chapter_40_name"}, "3064": {"Id": 3064, "StringId": "chapter_41_name", "Key": "chapter_41_name"}, "3065": {"Id": 3065, "StringId": "chapter_42_name", "Key": "chapter_42_name"}, "3066": {"Id": 3066, "StringId": "chapter_43_name", "Key": "chapter_43_name"}, "3067": {"Id": 3067, "StringId": "chapter_44_name", "Key": "chapter_44_name"}, "3068": {"Id": 3068, "StringId": "chapter_45_name", "Key": "chapter_45_name"}, "3069": {"Id": 3069, "StringId": "chapter_46_name", "Key": "chapter_46_name"}, "3070": {"Id": 3070, "StringId": "chapter_47_name", "Key": "chapter_47_name"}, "3071": {"Id": 3071, "StringId": "chapter_48_name", "Key": "chapter_48_name"}, "3072": {"Id": 3072, "StringId": "chapter_49_name", "Key": "chapter_49_name"}, "3073": {"Id": 3073, "StringId": "chapter_50_name", "Key": "chapter_50_name"}, "3074": {"Id": 3074, "StringId": "chapter_51_name", "Key": "chapter_51_name"}, "3075": {"Id": 3075, "StringId": "chapter_52_name", "Key": "chapter_52_name"}, "3076": {"Id": 3076, "StringId": "chapter_53_name", "Key": "chapter_53_name"}, "3077": {"Id": 3077, "StringId": "chapter_54_name", "Key": "chapter_54_name"}, "3078": {"Id": 3078, "StringId": "chapter_55_name", "Key": "chapter_55_name"}, "3079": {"Id": 3079, "StringId": "chapter_56_name", "Key": "chapter_56_name"}, "3080": {"Id": 3080, "StringId": "chapter_57_name", "Key": "chapter_57_name"}, "3081": {"Id": 3081, "StringId": "chapter_58_name", "Key": "chapter_58_name"}, "3082": {"Id": 3082, "StringId": "chapter_59_name", "Key": "chapter_59_name"}, "3083": {"Id": 3083, "StringId": "chapter_60_name", "Key": "chapter_60_name"}, "3099": {"Id": 3099, "StringId": "chapter_61_name", "Key": "chapter_61_name"}, "3100": {"Id": 3100, "StringId": "chapter_62_name", "Key": "chapter_62_name"}, "3101": {"Id": 3101, "StringId": "chapter_63_name", "Key": "chapter_63_name"}, "3102": {"Id": 3102, "StringId": "chapter_64_name", "Key": "chapter_64_name"}, "3103": {"Id": 3103, "StringId": "chapter_65_name", "Key": "chapter_65_name"}, "3104": {"Id": 3104, "StringId": "chapter_66_name", "Key": "chapter_66_name"}, "3105": {"Id": 3105, "StringId": "chapter_67_name", "Key": "chapter_67_name"}, "3106": {"Id": 3106, "StringId": "chapter_68_name", "Key": "chapter_68_name"}, "3107": {"Id": 3107, "StringId": "chapter_69_name", "Key": "chapter_69_name"}, "3108": {"Id": 3108, "StringId": "chapter_70_name", "Key": "chapter_70_name"}, "3109": {"Id": 3109, "StringId": "chapter_71_name", "Key": "chapter_71_name"}, "3110": {"Id": 3110, "StringId": "chapter_72_name", "Key": "chapter_72_name"}, "3111": {"Id": 3111, "StringId": "chapter_73_name", "Key": "chapter_73_name"}, "3112": {"Id": 3112, "StringId": "chapter_74_name", "Key": "chapter_74_name"}, "3113": {"Id": 3113, "StringId": "chapter_75_name", "Key": "chapter_75_name"}, "3114": {"Id": 3114, "StringId": "chapter_76_name", "Key": "chapter_76_name"}, "3115": {"Id": 3115, "StringId": "chapter_77_name", "Key": "chapter_77_name"}, "3116": {"Id": 3116, "StringId": "chapter_78_name", "Key": "chapter_78_name"}, "3117": {"Id": 3117, "StringId": "chapter_79_name", "Key": "chapter_79_name"}, "3118": {"Id": 3118, "StringId": "chapter_80_name", "Key": "chapter_80_name"}, "3119": {"Id": 3119, "StringId": "chapter_81_name", "Key": "chapter_81_name"}, "3120": {"Id": 3120, "StringId": "chapter_82_name", "Key": "chapter_82_name"}, "3121": {"Id": 3121, "StringId": "chapter_83_name", "Key": "chapter_83_name"}, "3122": {"Id": 3122, "StringId": "chapter_84_name", "Key": "chapter_84_name"}, "3123": {"Id": 3123, "StringId": "chapter_85_name", "Key": "chapter_85_name"}, "3124": {"Id": 3124, "StringId": "chapter_86_name", "Key": "chapter_86_name"}, "3125": {"Id": 3125, "StringId": "chapter_87_name", "Key": "chapter_87_name"}, "3126": {"Id": 3126, "StringId": "chapter_88_name", "Key": "chapter_88_name"}, "3127": {"Id": 3127, "StringId": "chapter_89_name", "Key": "chapter_89_name"}, "3128": {"Id": 3128, "StringId": "chapter_90_name", "Key": "chapter_90_name"}, "3129": {"Id": 3129, "StringId": "chapter_91_name", "Key": "chapter_91_name"}, "3130": {"Id": 3130, "StringId": "chapter_92_name", "Key": "chapter_92_name"}, "3131": {"Id": 3131, "StringId": "chapter_93_name", "Key": "chapter_93_name"}, "3132": {"Id": 3132, "StringId": "chapter_94_name", "Key": "chapter_94_name"}, "3133": {"Id": 3133, "StringId": "chapter_95_name", "Key": "chapter_95_name"}, "3134": {"Id": 3134, "StringId": "chapter_96_name", "Key": "chapter_96_name"}, "3135": {"Id": 3135, "StringId": "chapter_97_name", "Key": "chapter_97_name"}, "3136": {"Id": 3136, "StringId": "chapter_98_name", "Key": "chapter_98_name"}, "3137": {"Id": 3137, "StringId": "chapter_99_name", "Key": "chapter_99_name"}, "3138": {"Id": 3138, "StringId": "chapter_100_name", "Key": "chapter_100_name"}, "3139": {"Id": 3139, "StringId": "dungeon_coin_name", "Key": "dungeon_coin_name"}, "3140": {"Id": 3140, "StringId": "dungeon_gene_name", "Key": "dungeon_gene_name"}, "3141": {"Id": 3141, "StringId": "dungeon_lord_equip_name", "Key": "dungeon_lord_equip_name"}, "3142": {"Id": 3142, "StringId": "dungeon_coin_desc", "Key": "dungeon_coin_desc"}, "3143": {"Id": 3143, "StringId": "dungeon_gene_desc", "Key": "dungeon_gene_desc"}, "3144": {"Id": 3144, "StringId": "dungeon_lord_equip_desc", "Key": "dungeon_lord_equip_desc"}, "3145": {"Id": 3145, "StringId": "chapter_101_name", "Key": "chapter_101_name"}, "3146": {"Id": 3146, "StringId": "chapter_102_name", "Key": "chapter_102_name"}, "3147": {"Id": 3147, "StringId": "chapter_103_name", "Key": "chapter_103_name"}, "3148": {"Id": 3148, "StringId": "dungeon_sunshine_name", "Key": "dungeon_sunshine_name"}, "3149": {"Id": 3149, "StringId": "dungeon_sunshine_desc", "Key": "dungeon_coin_desc"}, "4001": {"Id": 4001, "StringId": "diamond_name", "Key": "diamond_name"}, "4002": {"Id": 4002, "StringId": "sunshine_name", "Key": "sunshine_name"}, "4003": {"Id": 4003, "StringId": "item_coin_name", "Key": "item_coin_name"}, "4004": {"Id": 4004, "StringId": "item_summon_card_name", "Key": "item_summon_card_name"}, "4005": {"Id": 4005, "StringId": "rouge_exp_name", "Key": "rouge_exp_name"}, "4006": {"Id": 4006, "StringId": "item_skill_book_name", "Key": "item_skill_book_name"}, "4007": {"Id": 4007, "StringId": "item_energy_name", "Key": "item_energy_name"}, "4008": {"Id": 4008, "StringId": "diamond_desc", "Key": "diamond_desc"}, "4009": {"Id": 4009, "StringId": "sunshine_desc", "Key": "sunshine_desc"}, "4010": {"Id": 4010, "StringId": "item_coin_desc", "Key": "item_coin_desc"}, "4011": {"Id": 4011, "StringId": "item_summon_card_desc", "Key": "item_summon_card_desc"}, "4012": {"Id": 4012, "StringId": "rouge_exp_desc", "Key": "rouge_exp_desc"}, "4013": {"Id": 4013, "StringId": "item_skill_book_desc", "Key": "item_skill_book_desc"}, "4014": {"Id": 4014, "StringId": "item_energy_desc", "Key": "item_energy_desc"}, "4015": {"Id": 4015, "StringId": "item_hero_3_0_name", "Key": "item_hero_3_0_name"}, "4016": {"Id": 4016, "StringId": "item_hero_3_1_name", "Key": "item_hero_3_1_name"}, "4017": {"Id": 4017, "StringId": "item_hero_3_2_name", "Key": "item_hero_3_2_name"}, "4018": {"Id": 4018, "StringId": "item_hero_3_3_name", "Key": "item_hero_3_3_name"}, "4019": {"Id": 4019, "StringId": "item_hero_3_4_name", "Key": "item_hero_3_4_name"}, "4020": {"Id": 4020, "StringId": "item_hero_3_5_name", "Key": "item_hero_3_5_name"}, "4021": {"Id": 4021, "StringId": "item_hero_2_0_name", "Key": "item_hero_2_0_name"}, "4022": {"Id": 4022, "StringId": "item_hero_2_1_name", "Key": "item_hero_2_1_name"}, "4023": {"Id": 4023, "StringId": "item_hero_2_2_name", "Key": "item_hero_2_2_name"}, "4024": {"Id": 4024, "StringId": "item_hero_2_3_name", "Key": "item_hero_2_3_name"}, "4025": {"Id": 4025, "StringId": "item_hero_2_4_name", "Key": "item_hero_2_4_name"}, "4026": {"Id": 4026, "StringId": "item_hero_2_5_name", "Key": "item_hero_2_5_name"}, "4027": {"Id": 4027, "StringId": "item_hero_2_6_name", "Key": "item_hero_2_6_name"}, "4028": {"Id": 4028, "StringId": "item_hero_1_0_name", "Key": "item_hero_1_0_name"}, "4029": {"Id": 4029, "StringId": "item_hero_1_1_name", "Key": "item_hero_1_1_name"}, "4030": {"Id": 4030, "StringId": "item_hero_3_0_fragment_name", "Key": "item_hero_3_0_fragment_name"}, "4031": {"Id": 4031, "StringId": "item_hero_3_1_fragment_name", "Key": "item_hero_3_1_fragment_name"}, "4032": {"Id": 4032, "StringId": "item_hero_3_2_fragment_name", "Key": "item_hero_3_2_fragment_name"}, "4033": {"Id": 4033, "StringId": "item_hero_3_3_fragment_name", "Key": "item_hero_3_3_fragment_name"}, "4034": {"Id": 4034, "StringId": "item_hero_3_4_fragment_name", "Key": "item_hero_3_4_fragment_name"}, "4035": {"Id": 4035, "StringId": "item_hero_3_5_fragment_name", "Key": "item_hero_3_5_fragment_name"}, "4036": {"Id": 4036, "StringId": "item_hero_2_0_fragment_name", "Key": "item_hero_2_0_fragment_name"}, "4037": {"Id": 4037, "StringId": "item_hero_2_1_fragment_name", "Key": "item_hero_2_1_fragment_name"}, "4038": {"Id": 4038, "StringId": "item_hero_2_2_fragment_name", "Key": "item_hero_2_2_fragment_name"}, "4039": {"Id": 4039, "StringId": "item_hero_2_3_fragment_name", "Key": "item_hero_2_3_fragment_name"}, "4040": {"Id": 4040, "StringId": "item_hero_2_4_fragment_name", "Key": "item_hero_2_4_fragment_name"}, "4041": {"Id": 4041, "StringId": "item_hero_2_5_fragment_name", "Key": "item_hero_2_5_fragment_name"}, "4042": {"Id": 4042, "StringId": "item_hero_2_6_fragment_name", "Key": "item_hero_2_6_fragment_name"}, "4043": {"Id": 4043, "StringId": "item_hero_1_0_fragment_name", "Key": "item_hero_1_0_fragment_name"}, "4044": {"Id": 4044, "StringId": "item_hero_1_1_fragment_name", "Key": "item_hero_1_1_fragment_name"}, "4045": {"Id": 4045, "StringId": "item_hero_3_0_fragment_desc", "Key": "item_hero_3_0_fragment_desc"}, "4046": {"Id": 4046, "StringId": "item_hero_3_1_fragment_desc", "Key": "item_hero_3_1_fragment_desc"}, "4047": {"Id": 4047, "StringId": "item_hero_3_2_fragment_desc", "Key": "item_hero_3_2_fragment_desc"}, "4048": {"Id": 4048, "StringId": "item_hero_3_3_fragment_desc", "Key": "item_hero_3_3_fragment_desc"}, "4049": {"Id": 4049, "StringId": "item_hero_3_4_fragment_desc", "Key": "item_hero_3_4_fragment_desc"}, "4050": {"Id": 4050, "StringId": "item_hero_3_5_fragment_desc", "Key": "item_hero_3_5_fragment_desc"}, "4051": {"Id": 4051, "StringId": "item_hero_2_0_fragment_desc", "Key": "item_hero_2_0_fragment_desc"}, "4052": {"Id": 4052, "StringId": "item_hero_2_1_fragment_desc", "Key": "item_hero_2_1_fragment_desc"}, "4053": {"Id": 4053, "StringId": "item_hero_2_2_fragment_desc", "Key": "item_hero_2_2_fragment_desc"}, "4054": {"Id": 4054, "StringId": "item_hero_2_3_fragment_desc", "Key": "item_hero_2_3_fragment_desc"}, "4055": {"Id": 4055, "StringId": "item_hero_2_4_fragment_desc", "Key": "item_hero_2_4_fragment_desc"}, "4056": {"Id": 4056, "StringId": "item_hero_2_5_fragment_desc", "Key": "item_hero_2_5_fragment_desc"}, "4057": {"Id": 4057, "StringId": "item_hero_2_6_fragment_desc", "Key": "item_hero_2_6_fragment_desc"}, "4058": {"Id": 4058, "StringId": "item_hero_1_0_fragment_desc", "Key": "item_hero_1_0_fragment_desc"}, "4059": {"Id": 4059, "StringId": "item_hero_1_1_fragment_desc", "Key": "item_hero_1_1_fragment_desc"}, "4060": {"Id": 4060, "StringId": "universal_legendary_hero_fragment_name", "Key": "universal_legendary_hero_fragment_name"}, "4061": {"Id": 4061, "StringId": "universal_epic_hero_fragment_name", "Key": "universal_epic_hero_fragment_name"}, "4062": {"Id": 4062, "StringId": "universal_rare_hero_fragment_name", "Key": "universal_rare_hero_fragment_name"}, "4063": {"Id": 4063, "StringId": "random_legendary_hero_fragment_name", "Key": "random_legendary_hero_fragment_name"}, "4064": {"Id": 4064, "StringId": "random_epic_hero_fragment_name", "Key": "random_epic_hero_fragment_name"}, "4065": {"Id": 4065, "StringId": "random_rare_hero_fragment_name", "Key": "random_rare_hero_fragment_name"}, "4066": {"Id": 4066, "StringId": "universal_legendary_hero_fragment_desc", "Key": "universal_legendary_hero_fragment_desc"}, "4067": {"Id": 4067, "StringId": "universal_epic_hero_fragment_desc", "Key": "universal_epic_hero_fragment_desc"}, "4068": {"Id": 4068, "StringId": "universal_rare_hero_fragment_desc", "Key": "universal_rare_hero_fragment_desc"}, "4069": {"Id": 4069, "StringId": "random_legendary_hero_fragment_desc", "Key": "random_legendary_hero_fragment_desc"}, "4070": {"Id": 4070, "StringId": "random_epic_hero_fragment_desc", "Key": "random_epic_hero_fragment_desc"}, "4071": {"Id": 4071, "StringId": "random_rare_hero_fragment_desc", "Key": "random_rare_hero_fragment_desc"}, "4072": {"Id": 4072, "StringId": "item_hero_3_0_avatar_name", "Key": "item_hero_3_0_avatar_name"}, "4073": {"Id": 4073, "StringId": "item_hero_3_1_avatar_name", "Key": "item_hero_3_1_avatar_name"}, "4074": {"Id": 4074, "StringId": "item_hero_3_2_avatar_name", "Key": "item_hero_3_2_avatar_name"}, "4075": {"Id": 4075, "StringId": "item_hero_3_3_avatar_name", "Key": "item_hero_3_3_avatar_name"}, "4076": {"Id": 4076, "StringId": "item_hero_3_4_avatar_name", "Key": "item_hero_3_4_avatar_name"}, "4077": {"Id": 4077, "StringId": "item_hero_3_5_avatar_name", "Key": "item_hero_3_5_avatar_name"}, "4078": {"Id": 4078, "StringId": "item_hero_2_0_avatar_name", "Key": "item_hero_2_0_avatar_name"}, "4079": {"Id": 4079, "StringId": "item_hero_2_1_avatar_name", "Key": "item_hero_2_1_avatar_name"}, "4080": {"Id": 4080, "StringId": "item_hero_2_2_avatar_name", "Key": "item_hero_2_2_avatar_name"}, "4081": {"Id": 4081, "StringId": "item_hero_2_3_avatar_name", "Key": "item_hero_2_3_avatar_name"}, "4082": {"Id": 4082, "StringId": "item_hero_2_4_avatar_name", "Key": "item_hero_2_4_avatar_name"}, "4083": {"Id": 4083, "StringId": "item_hero_2_5_avatar_name", "Key": "item_hero_2_5_avatar_name"}, "4084": {"Id": 4084, "StringId": "item_hero_2_6_avatar_name", "Key": "item_hero_2_6_avatar_name"}, "4085": {"Id": 4085, "StringId": "item_hero_1_0_avatar_name", "Key": "item_hero_1_0_avatar_name"}, "4086": {"Id": 4086, "StringId": "item_hero_1_1_avatar_name", "Key": "item_hero_1_1_avatar_name"}, "4087": {"Id": 4087, "StringId": "item_avatar_frame_1_name", "Key": "item_avatar_frame_1_name"}, "4088": {"Id": 4088, "StringId": "item_hero_3_0_gene_fragment_name", "Key": "item_hero_3_0_gene_fragment_name"}, "4089": {"Id": 4089, "StringId": "item_hero_3_1_gene_fragment_name", "Key": "item_hero_3_1_gene_fragment_name"}, "4090": {"Id": 4090, "StringId": "item_hero_3_2_gene_fragment_name", "Key": "item_hero_3_2_gene_fragment_name"}, "4091": {"Id": 4091, "StringId": "item_hero_3_3_gene_fragment_name", "Key": "item_hero_3_3_gene_fragment_name"}, "4092": {"Id": 4092, "StringId": "item_hero_3_4_gene_fragment_name", "Key": "item_hero_3_4_gene_fragment_name"}, "4093": {"Id": 4093, "StringId": "item_hero_3_5_gene_fragment_name", "Key": "item_hero_3_5_gene_fragment_name"}, "4094": {"Id": 4094, "StringId": "item_hero_2_0_gene_fragment_name", "Key": "item_hero_2_0_gene_fragment_name"}, "4095": {"Id": 4095, "StringId": "item_hero_2_1_gene_fragment_name", "Key": "item_hero_2_1_gene_fragment_name"}, "4096": {"Id": 4096, "StringId": "item_hero_2_2_gene_fragment_name", "Key": "item_hero_2_2_gene_fragment_name"}, "4097": {"Id": 4097, "StringId": "item_hero_2_3_gene_fragment_name", "Key": "item_hero_2_3_gene_fragment_name"}, "4098": {"Id": 4098, "StringId": "item_hero_2_4_gene_fragment_name", "Key": "item_hero_2_4_gene_fragment_name"}, "4099": {"Id": 4099, "StringId": "item_hero_2_5_gene_fragment_name", "Key": "item_hero_2_5_gene_fragment_name"}, "4100": {"Id": 4100, "StringId": "item_hero_2_6_gene_fragment_name", "Key": "item_hero_2_6_gene_fragment_name"}, "4101": {"Id": 4101, "StringId": "item_hero_1_0_gene_fragment_name", "Key": "item_hero_1_0_gene_fragment_name"}, "4102": {"Id": 4102, "StringId": "item_hero_1_1_gene_fragment_name", "Key": "item_hero_1_1_gene_fragment_name"}, "4103": {"Id": 4103, "StringId": "item_hero_general_gene_fragment_name", "Key": "item_hero_general_gene_fragment_name"}, "4104": {"Id": 4104, "StringId": "item_hero_3_0_gene_fragment_desc", "Key": "item_hero_3_0_gene_fragment_desc"}, "4105": {"Id": 4105, "StringId": "item_hero_3_1_gene_fragment_desc", "Key": "item_hero_3_1_gene_fragment_desc"}, "4106": {"Id": 4106, "StringId": "item_hero_3_2_gene_fragment_desc", "Key": "item_hero_3_2_gene_fragment_desc"}, "4107": {"Id": 4107, "StringId": "item_hero_3_3_gene_fragment_desc", "Key": "item_hero_3_3_gene_fragment_desc"}, "4108": {"Id": 4108, "StringId": "item_hero_3_4_gene_fragment_desc", "Key": "item_hero_3_4_gene_fragment_desc"}, "4109": {"Id": 4109, "StringId": "item_hero_3_5_gene_fragment_desc", "Key": "item_hero_3_5_gene_fragment_desc"}, "4110": {"Id": 4110, "StringId": "item_hero_2_0_gene_fragment_desc", "Key": "item_hero_2_0_gene_fragment_desc"}, "4111": {"Id": 4111, "StringId": "item_hero_2_1_gene_fragment_desc", "Key": "item_hero_2_1_gene_fragment_desc"}, "4112": {"Id": 4112, "StringId": "item_hero_2_2_gene_fragment_desc", "Key": "item_hero_2_2_gene_fragment_desc"}, "4113": {"Id": 4113, "StringId": "item_hero_2_3_gene_fragment_desc", "Key": "item_hero_2_3_gene_fragment_desc"}, "4114": {"Id": 4114, "StringId": "item_hero_2_4_gene_fragment_desc", "Key": "item_hero_2_4_gene_fragment_desc"}, "4115": {"Id": 4115, "StringId": "item_hero_2_5_gene_fragment_desc", "Key": "item_hero_2_5_gene_fragment_desc"}, "4116": {"Id": 4116, "StringId": "item_hero_2_6_gene_fragment_desc", "Key": "item_hero_2_6_gene_fragment_desc"}, "4117": {"Id": 4117, "StringId": "item_hero_1_0_gene_fragment_desc", "Key": "item_hero_1_0_gene_fragment_desc"}, "4118": {"Id": 4118, "StringId": "item_hero_1_1_gene_fragment_desc", "Key": "item_hero_1_1_gene_fragment_desc"}, "4119": {"Id": 4119, "StringId": "item_hero_general_gene_fragment_desc", "Key": "item_hero_general_gene_fragment_desc"}, "4120": {"Id": 4120, "StringId": "item_source_level_name", "Key": "item_source_level_name"}, "4121": {"Id": 4121, "StringId": "item_source_summon_name", "Key": "item_source_summon_name"}, "4122": {"Id": 4122, "StringId": "item_source_idle_reward_name", "Key": "item_source_idle_reward_name"}, "4123": {"Id": 4123, "StringId": "item_source_photovoltaic_name", "Key": "item_source_photovoltaic_name"}, "4124": {"Id": 4124, "StringId": "item_source_level_desc", "Key": "item_source_level_desc"}, "4125": {"Id": 4125, "StringId": "item_source_summon_desc", "Key": "item_source_summon_desc"}, "4126": {"Id": 4126, "StringId": "item_source_idle_reward_desc", "Key": "item_source_idle_reward_desc"}, "4127": {"Id": 4127, "StringId": "item_source_photovoltaic_desc", "Key": "item_source_photovoltaic_desc"}, "4128": {"Id": 4128, "StringId": "item_gem_random_rare_name", "Key": "item_gem_random_rare_name"}, "4129": {"Id": 4129, "StringId": "item_gem_random_epic_name", "Key": "item_gem_random_epic_name"}, "4130": {"Id": 4130, "StringId": "item_gem_random_legendary_name", "Key": "item_gem_random_legendary_name"}, "4131": {"Id": 4131, "StringId": "item_gem_random_rare_desc", "Key": "item_gem_random_rare_desc"}, "4132": {"Id": 4132, "StringId": "item_gem_random_epic_desc", "Key": "item_gem_random_epic_desc"}, "4133": {"Id": 4133, "StringId": "item_gem_random_legendary_desc", "Key": "item_gem_random_legendary_desc"}, "4134": {"Id": 4134, "StringId": "item_gem_reforge_name", "Key": "item_gem_reforge_name"}, "4135": {"Id": 4135, "StringId": "item_lord_equip_1_level_up_name", "Key": "item_lord_equip_1_level_up_name"}, "4136": {"Id": 4136, "StringId": "item_lord_equip_2_level_up_name", "Key": "item_lord_equip_2_level_up_name"}, "4137": {"Id": 4137, "StringId": "item_lord_equip_3_level_up_name", "Key": "item_lord_equip_3_level_up_name"}, "4138": {"Id": 4138, "StringId": "item_lord_equip_4_level_up_name", "Key": "item_lord_equip_4_level_up_name"}, "4139": {"Id": 4139, "StringId": "item_lord_equip_5_level_up_name", "Key": "item_lord_equip_5_level_up_name"}, "4140": {"Id": 4140, "StringId": "item_lord_equip_6_level_up_name", "Key": "item_lord_equip_6_level_up_name"}, "4141": {"Id": 4141, "StringId": "item_lord_equip_level_up_name", "Key": "item_lord_equip_level_up_name"}, "4142": {"Id": 4142, "StringId": "item_lord_equip_1_level_up_desc", "Key": "item_lord_equip_1_level_up_desc"}, "4143": {"Id": 4143, "StringId": "item_lord_equip_2_level_up_desc", "Key": "item_lord_equip_2_level_up_desc"}, "4144": {"Id": 4144, "StringId": "item_lord_equip_3_level_up_desc", "Key": "item_lord_equip_3_level_up_desc"}, "4145": {"Id": 4145, "StringId": "item_lord_equip_4_level_up_desc", "Key": "item_lord_equip_4_level_up_desc"}, "4146": {"Id": 4146, "StringId": "item_lord_equip_5_level_up_desc", "Key": "item_lord_equip_5_level_up_desc"}, "4147": {"Id": 4147, "StringId": "item_lord_equip_6_level_up_desc", "Key": "item_lord_equip_6_level_up_desc"}, "4148": {"Id": 4148, "StringId": "item_lord_equip_level_up_desc", "Key": "item_lord_equip_level_up_desc"}, "4149": {"Id": 4149, "StringId": "item_gem_draw_common_name", "Key": "item_gem_draw_common_name"}, "4150": {"Id": 4150, "StringId": "item_gem_draw_senior_name", "Key": "item_gem_draw_senior_name"}, "4151": {"Id": 4151, "StringId": "item_gem_reforge_desc", "Key": "item_gem_reforge_desc"}, "4152": {"Id": 4152, "StringId": "item_gem_draw_common_desc", "Key": "item_gem_draw_common_desc"}, "4153": {"Id": 4153, "StringId": "item_gem_draw_senior_desc", "Key": "item_gem_draw_senior_desc"}, "4154": {"Id": 4154, "StringId": "item_guild_exp_name", "Key": "item_guild_exp_name"}, "4155": {"Id": 4155, "StringId": "item_guild_coin_name", "Key": "item_guild_coin_name"}, "4156": {"Id": 4156, "StringId": "item_daily_task_score_desc", "Key": "item_daily_task_score_desc"}, "4157": {"Id": 4157, "StringId": "item_guild_exp_desc", "Key": "item_guild_exp_desc"}, "4158": {"Id": 4158, "StringId": "item_guild_task_score_desc", "Key": "item_guild_task_score_desc"}, "4159": {"Id": 4159, "StringId": "item_guild_coin_desc", "Key": "item_guild_coin_desc"}, "4160": {"Id": 4160, "StringId": "item_arena_coin_name", "Key": "item_arena_coin_name"}, "4161": {"Id": 4161, "StringId": "item_arena_coin_desc", "Key": "item_arena_coin_desc"}, "4162": {"Id": 4162, "StringId": "item_tower_key_name", "Key": "item_tower_key_name"}, "4163": {"Id": 4163, "StringId": "item_tower_key_desc", "Key": "item_tower_key_desc"}, "4164": {"Id": 4164, "StringId": "item_coin_dungeon_key_name", "Key": "item_coin_dungeon_key_name"}, "4165": {"Id": 4165, "StringId": "item_gene_dungeon_key_name", "Key": "item_gene_dungeon_key_name"}, "4166": {"Id": 4166, "StringId": "item_lord_equip_dungeon_key_name", "Key": "item_lord_equip_dungeon_key_name"}, "4167": {"Id": 4167, "StringId": "item_coin_dungeon_key_desc", "Key": "item_coin_dungeon_key_desc"}, "4168": {"Id": 4168, "StringId": "item_gene_dungeon_key_desc", "Key": "item_gene_dungeon_key_desc"}, "4169": {"Id": 4169, "StringId": "item_lord_equip_dungeon_key_desc", "Key": "item_lord_equip_dungeon_key_desc"}, "4170": {"Id": 4170, "StringId": "item_lord_equip_grade_up_name", "Key": "item_lord_equip_grade_up_name"}, "4171": {"Id": 4171, "StringId": "item_lord_equip_grade_up_desc", "Key": "item_lord_equip_grade_up_desc"}, "4172": {"Id": 4172, "StringId": "item_hero_quality_up_name", "Key": "item_hero_quality_up_name"}, "4173": {"Id": 4173, "StringId": "item_hero_quality_up_desc", "Key": "item_hero_quality_up_desc"}, "4174": {"Id": 4174, "StringId": "item_free_ad_privilege_name", "Key": "item_free_ad_privilege_name"}, "4175": {"Id": 4175, "StringId": "item_2X_privilege_name", "Key": "item_2X_privilege_name"}, "4176": {"Id": 4176, "StringId": "item_free_ad_privilege_desc", "Key": "item_free_ad_privilege_desc"}, "4177": {"Id": 4177, "StringId": "item_2X_privilege_desc", "Key": "item_2X_privilege_desc"}, "4178": {"Id": 4178, "StringId": "item_turn_table_coin_name", "Key": "item_turn_table_coin_name"}, "4179": {"Id": 4179, "StringId": "item_turn_table_coin_desc", "Key": "item_turn_table_coin_desc"}, "4180": {"Id": 4180, "StringId": "item_gem_1_name", "Key": "item_gem_1_name"}, "4181": {"Id": 4181, "StringId": "item_gem_2_name", "Key": "item_gem_2_name"}, "4182": {"Id": 4182, "StringId": "item_gem_3_name", "Key": "item_gem_3_name"}, "4183": {"Id": 4183, "StringId": "item_gem_4_name", "Key": "item_gem_4_name"}, "4184": {"Id": 4184, "StringId": "item_gem_5_name", "Key": "item_gem_5_name"}, "4185": {"Id": 4185, "StringId": "item_gem_6_name", "Key": "item_gem_6_name"}, "4186": {"Id": 4186, "StringId": "item_gem_7_name", "Key": "item_gem_7_name"}, "4187": {"Id": 4187, "StringId": "item_7_day_tasks_score_name", "Key": "item_7_day_tasks_score_name"}, "4188": {"Id": 4188, "StringId": "item_legendary_hero_select_chest_name", "Key": "item_legendary_hero_select_chest"}, "4189": {"Id": 4189, "StringId": "item_7_day_tasks_avatar_frame", "Key": "item_7_day_tasks_avatar_frame"}, "4190": {"Id": 4190, "StringId": "item_7_day_tasks_score_desc", "Key": "item_7_day_tasks_score_desc"}, "4191": {"Id": 4191, "StringId": "item_legendary_hero_select_chest_desc", "Key": "item_legendary_hero_select_chest_desc"}, "4192": {"Id": 4192, "StringId": "item_7_day_tasks_avatar_frame_desc", "Key": "item_7_day_tasks_avatar_frame_desc"}, "4193": {"Id": 4193, "StringId": "item_gem_random_rare_chest_name", "Key": "item_gem_random_rare_chest_name"}, "4194": {"Id": 4194, "StringId": "item_gem_random_epic_chest_name", "Key": "item_gem_random_epic_chest_name"}, "4195": {"Id": 4195, "StringId": "item_gem_random_rare_chest_desc", "Key": "item_gem_random_rare_chest_desc"}, "4196": {"Id": 4196, "StringId": "item_gem_random_epic_chest_desc", "Key": "item_gem_random_epic_chest_desc"}, "4197": {"Id": 4197, "StringId": "item_gem_random_legendary_chest_name", "Key": "item_gem_random_legendary_chest_name"}, "4198": {"Id": 4198, "StringId": "item_gem_random_legendary_chest_desc", "Key": "item_gem_random_legendary_chest_desc"}, "4199": {"Id": 4199, "StringId": "item_gem_random_seed1_name", "Key": "item_gem_random_seed1_name"}, "4200": {"Id": 4200, "StringId": "item_gem_random_seed1_desc", "Key": "item_gem_random_seed1_desc"}, "4201": {"Id": 4201, "StringId": "item_gem_random_seed2_name", "Key": "item_gem_random_seed2_name"}, "4202": {"Id": 4202, "StringId": "item_gem_random_seed2_desc", "Key": "item_gem_random_seed2_desc"}, "4203": {"Id": 4203, "StringId": "item_gem_random_seed3_name", "Key": "item_gem_random_seed3_name"}, "4204": {"Id": 4204, "StringId": "item_gem_random_seed3_desc", "Key": "item_gem_random_seed3_desc"}, "4205": {"Id": 4205, "StringId": "item_hero_star_up_name", "Key": "item_hero_star_up_name"}, "4206": {"Id": 4206, "StringId": "item_hero_star_up_desc", "Key": "item_hero_star_up_desc"}, "4207": {"Id": 4207, "StringId": "item_gem_1_1_name", "Key": "item_gem_1_1_name"}, "4208": {"Id": 4208, "StringId": "item_gem_1_2_name", "Key": "item_gem_1_2_name"}, "4209": {"Id": 4209, "StringId": "item_gem_1_3_name", "Key": "item_gem_1_3_name"}, "4210": {"Id": 4210, "StringId": "item_gem_1_4_name", "Key": "item_gem_1_4_name"}, "4211": {"Id": 4211, "StringId": "item_gem_1_5_name", "Key": "item_gem_1_5_name"}, "4212": {"Id": 4212, "StringId": "item_gem_1_6_name", "Key": "item_gem_1_6_name"}, "4213": {"Id": 4213, "StringId": "item_gem_2_1_name", "Key": "item_gem_2_1_name"}, "4214": {"Id": 4214, "StringId": "item_gem_2_2_name", "Key": "item_gem_2_2_name"}, "4215": {"Id": 4215, "StringId": "item_gem_2_3_name", "Key": "item_gem_2_3_name"}, "4216": {"Id": 4216, "StringId": "item_gem_2_4_name", "Key": "item_gem_2_4_name"}, "4217": {"Id": 4217, "StringId": "item_gem_2_5_name", "Key": "item_gem_2_5_name"}, "4218": {"Id": 4218, "StringId": "item_gem_2_6_name", "Key": "item_gem_2_6_name"}, "4219": {"Id": 4219, "StringId": "item_gem_3_1_name", "Key": "item_gem_3_1_name"}, "4220": {"Id": 4220, "StringId": "item_gem_3_2_name", "Key": "item_gem_3_2_name"}, "4221": {"Id": 4221, "StringId": "item_gem_3_3_name", "Key": "item_gem_3_3_name"}, "4222": {"Id": 4222, "StringId": "item_gem_3_4_name", "Key": "item_gem_3_4_name"}, "4223": {"Id": 4223, "StringId": "item_gem_3_5_name", "Key": "item_gem_3_5_name"}, "4224": {"Id": 4224, "StringId": "item_gem_3_6_name", "Key": "item_gem_3_6_name"}, "4225": {"Id": 4225, "StringId": "item_gem_4_1_name", "Key": "item_gem_4_1_name"}, "4226": {"Id": 4226, "StringId": "item_gem_4_2_name", "Key": "item_gem_4_2_name"}, "4227": {"Id": 4227, "StringId": "item_gem_4_3_name", "Key": "item_gem_4_3_name"}, "4228": {"Id": 4228, "StringId": "item_gem_4_4_name", "Key": "item_gem_4_4_name"}, "4229": {"Id": 4229, "StringId": "item_gem_4_5_name", "Key": "item_gem_4_5_name"}, "4230": {"Id": 4230, "StringId": "item_gem_4_6_name", "Key": "item_gem_4_6_name"}, "4231": {"Id": 4231, "StringId": "item_gem_5_1_name", "Key": "item_gem_5_1_name"}, "4232": {"Id": 4232, "StringId": "item_gem_5_2_name", "Key": "item_gem_5_2_name"}, "4233": {"Id": 4233, "StringId": "item_gem_5_3_name", "Key": "item_gem_5_3_name"}, "4234": {"Id": 4234, "StringId": "item_gem_5_4_name", "Key": "item_gem_5_4_name"}, "4235": {"Id": 4235, "StringId": "item_gem_5_5_name", "Key": "item_gem_5_5_name"}, "4236": {"Id": 4236, "StringId": "item_gem_5_6_name", "Key": "item_gem_5_6_name"}, "4237": {"Id": 4237, "StringId": "item_gem_6_1_name", "Key": "item_gem_6_1_name"}, "4238": {"Id": 4238, "StringId": "item_gem_6_2_name", "Key": "item_gem_6_2_name"}, "4239": {"Id": 4239, "StringId": "item_gem_6_3_name", "Key": "item_gem_6_3_name"}, "4240": {"Id": 4240, "StringId": "item_gem_6_4_name", "Key": "item_gem_6_4_name"}, "4241": {"Id": 4241, "StringId": "item_gem_6_5_name", "Key": "item_gem_6_5_name"}, "4242": {"Id": 4242, "StringId": "item_gem_6_6_name", "Key": "item_gem_6_6_name"}, "4243": {"Id": 4243, "StringId": "item_gem_7_1_name", "Key": "item_gem_7_1_name"}, "4244": {"Id": 4244, "StringId": "item_gem_7_2_name", "Key": "item_gem_7_2_name"}, "4245": {"Id": 4245, "StringId": "item_gem_7_3_name", "Key": "item_gem_7_3_name"}, "4246": {"Id": 4246, "StringId": "item_gem_7_4_name", "Key": "item_gem_7_4_name"}, "4247": {"Id": 4247, "StringId": "item_gem_7_5_name", "Key": "item_gem_7_5_name"}, "4248": {"Id": 4248, "StringId": "item_gem_7_6_name", "Key": "item_gem_7_6_name"}, "4249": {"Id": 4249, "StringId": "regular_pack_hero_gene_up_title", "Key": "regular_pack_hero_gene_up_title"}, "4250": {"Id": 4250, "StringId": "item_random_hero_3_1_fragment_box_name", "Key": "item_random_hero_3_1_fragment_box_name"}, "4251": {"Id": 4251, "StringId": "item_random_hero_3_2_fragment_box_name", "Key": "item_random_hero_3_2_fragment_box_name"}, "4252": {"Id": 4252, "StringId": "item_random_hero_3_3_fragment_box_name", "Key": "item_random_hero_3_3_fragment_box_name"}, "4253": {"Id": 4253, "StringId": "item_random_hero_3_4_fragment_box_name", "Key": "item_random_hero_3_4_fragment_box_name"}, "4254": {"Id": 4254, "StringId": "item_random_hero_3_5_fragment_box_name", "Key": "item_random_hero_3_5_fragment_box_name"}, "4255": {"Id": 4255, "StringId": "item_random_hero_2_5_fragment_box_name", "Key": "item_random_hero_2_5_fragment_box_name"}, "5001": {"Id": 5001, "StringId": "hp_name", "Key": "hp_name"}, "5002": {"Id": 5002, "StringId": "atk_name", "Key": "atk_name"}, "5003": {"Id": 5003, "StringId": "def_name", "Key": "def_name"}, "5004": {"Id": 5004, "StringId": "crit_chance_name", "Key": "crit_chance_name"}, "5005": {"Id": 5005, "StringId": "crit_dmg_up_per_name", "Key": "crit_dmg_up_per_name"}, "5006": {"Id": 5006, "StringId": "cd_rate_name", "Key": "cd_rate_name"}, "5007": {"Id": 5007, "StringId": "atk_up_per_name", "Key": "atk_up_per_name"}, "5008": {"Id": 5008, "StringId": "dmg_up_per_name", "Key": "dmg_up_per_name"}, "5009": {"Id": 5009, "StringId": "crit_resist_chance_name", "Key": "crit_resist_chance_name"}, "5010": {"Id": 5010, "StringId": "crit_dmg_down_per_name", "Key": "crit_dmg_down_per_name"}, "5011": {"Id": 5011, "StringId": "be_dmg_down_per_name", "Key": "be_dmg_down_per_name"}, "5012": {"Id": 5012, "StringId": "def_up_per_name", "Key": "def_up_per_name"}, "5013": {"Id": 5013, "StringId": "hp_up_per_name", "Key": "hp_up_per_name"}, "5014": {"Id": 5014, "StringId": "hp_recover_name", "Key": "hp_recover_name"}, "5015": {"Id": 5015, "StringId": "benefit_hp_up_percent_name", "Key": "benefit_hp_up_percent_name"}, "5016": {"Id": 5016, "StringId": "benefit_atk_up_percent_name", "Key": "benefit_atk_up_percent_name"}, "5017": {"Id": 5017, "StringId": "benefit_def_up_percent_name", "Key": "benefit_def_up_percent_name"}, "5018": {"Id": 5018, "StringId": "benefit_crit_chance_up_percent_name", "Key": "benefit_crit_chance_up_percent_name"}, "5019": {"Id": 5019, "StringId": "benefit_crit_dmg_up_percent_name", "Key": "benefit_crit_dmg_up_percent_name"}, "5020": {"Id": 5020, "StringId": "benefit_crit_chance_resist_up_percent_name", "Key": "benefit_crit_chance_resist_up_percent_name"}, "5021": {"Id": 5021, "StringId": "benefit_crit_dmg_resist_up_percent_name", "Key": "benefit_crit_dmg_resist_up_percent_name"}, "5022": {"Id": 5022, "StringId": "benefit_dmg_up_percent_name", "Key": "benefit_dmg_up_percent_name"}, "5023": {"Id": 5023, "StringId": "benefit_dmg_resist_up_percent_name", "Key": "benefit_dmg_resist_up_percent_name"}, "5024": {"Id": 5024, "StringId": "benefit_cd_rate_up_percent_name", "Key": "benefit_cd_rate_up_percent_name"}, "5025": {"Id": 5025, "StringId": "benefit_hp_down_percent_name", "Key": "benefit_hp_down_percent_name"}, "5026": {"Id": 5026, "StringId": "benefit_atk_down_percent_name", "Key": "benefit_atk_down_percent_name"}, "5027": {"Id": 5027, "StringId": "benefit_def_down_percent_name", "Key": "benefit_def_down_percent_name"}, "5028": {"Id": 5028, "StringId": "benefit_crit_chance_down_percent_name", "Key": "benefit_crit_chance_down_percent_name"}, "5029": {"Id": 5029, "StringId": "benefit_crit_dmg_down_percent_name", "Key": "benefit_crit_dmg_down_percent_name"}, "5030": {"Id": 5030, "StringId": "benefit_crit_chance_resist_down_percent_name", "Key": "benefit_crit_chance_resist_down_percent_name"}, "5031": {"Id": 5031, "StringId": "benefit_crit_dmg_resist_down_percent_name", "Key": "benefit_crit_dmg_resist_down_percent_name"}, "5032": {"Id": 5032, "StringId": "benefit_dmg_down_percent_name", "Key": "benefit_dmg_down_percent_name"}, "5033": {"Id": 5033, "StringId": "benefit_dmg_resist_down_percent_name", "Key": "benefit_dmg_resist_down_percent_name"}, "5034": {"Id": 5034, "StringId": "benefit_cd_rate_down_percent_name", "Key": "benefit_cd_rate_down_percent_name"}, "5035": {"Id": 5035, "StringId": "benefit_move_speed_up_percent_name", "Key": "benefit_move_speed_up_percent_name"}, "5036": {"Id": 5036, "StringId": "benefit_move_speed_down_percent_name", "Key": "benefit_move_speed_down_percent_name"}, "5037": {"Id": 5037, "StringId": "beneift_hp_recovery_up_percent_name", "Key": "beneift_hp_recovery_up_percent_name"}, "5038": {"Id": 5038, "StringId": "beneift_hp_recovery_down_percent_name", "Key": "beneift_hp_recovery_down_percent_name"}, "5039": {"Id": 5039, "StringId": "benefit_cd_time_up_percent_name", "Key": "benefit_cd_time_up_percent_name"}, "5040": {"Id": 5040, "StringId": "benefit_cd_time_down_percent_name", "Key": "benefit_cd_time_down_percent_name"}, "5041": {"Id": 5041, "StringId": "benefit_all_hero_crit_dmg_up_percent_name", "Key": "benefit_all_hero_crit_dmg_up_percent_name"}, "5042": {"Id": 5042, "StringId": "benefit_skill_range_up_percent_name", "Key": "benefit_skill_range_up_percent_name"}, "5043": {"Id": 5043, "StringId": "benefit_skill_range_down_percent_name", "Key": "benefit_skill_range_down_percent_name"}, "5044": {"Id": 5044, "StringId": "benefit_fire_dmg_resist_up_percent_name", "Key": "benefit_fire_dmg_resist_up_percent_name"}, "5045": {"Id": 5045, "StringId": "benefit_fire_dmg_resist_down_percent_name", "Key": "benefit_fire_dmg_resist_down_percent_name"}, "5046": {"Id": 5046, "StringId": "benefit_electrical_dmg_resist_up_percent_name", "Key": "benefit_electrical_dmg_resist_up_percent_name"}, "5047": {"Id": 5047, "StringId": "benefit_electrical_dmg_resist_down_percent_name", "Key": "benefit_electrical_dmg_resist_down_percent_name"}, "5048": {"Id": 5048, "StringId": "benefit_wind_dmg_resist_up_percent_name", "Key": "benefit_wind_dmg_resist_up_percent_name"}, "5049": {"Id": 5049, "StringId": "benefit_wind_dmg_resist_down_percent_name", "Key": "benefit_wind_dmg_resist_down_percent_name"}, "5050": {"Id": 5050, "StringId": "benefit_light_dmg_resist_up_percent_name", "Key": "benefit_light_dmg_resist_up_percent_name"}, "5051": {"Id": 5051, "StringId": "benefit_light_dmg_resist_down_percent_name", "Key": "benefit_light_dmg_resist_down_percent_name"}, "5052": {"Id": 5052, "StringId": "benefit_ice_dmg_resist_up_percent_name", "Key": "benefit_ice_dmg_resist_up_percent_name"}, "5053": {"Id": 5053, "StringId": "benefit_ice_dmg_resist_down_percent_name", "Key": "benefit_ice_dmg_resist_down_percent_name"}, "5054": {"Id": 5054, "StringId": "benefit_physical_dmg_resist_up_percent_name", "Key": "benefit_physical_dmg_resist_up_percent_name"}, "5055": {"Id": 5055, "StringId": "benefit_physical_dmg_resist_down_percent_name", "Key": "benefit_physical_dmg_resist_down_percent_name"}, "5056": {"Id": 5056, "StringId": "benefit_level_coin_up_percent_name", "Key": "benefit_level_coin_up_percent_name"}, "5057": {"Id": 5057, "StringId": "benefit_monster_dmg_up_percent_name", "Key": "benefit_monster_dmg_up_percent_name"}, "5058": {"Id": 5058, "StringId": "benefit_monster_dmg_down_percent_name", "Key": "benefit_monster_dmg_down_percent_name"}, "5059": {"Id": 5059, "StringId": "benefit_monster_dmg_resist_up_percent_name", "Key": "benefit_monster_dmg_resist_up_percent_name"}, "5060": {"Id": 5060, "StringId": "benefit_monster_dmg_resist_down_percent_name", "Key": "benefit_monster_dmg_resist_down_percent_name"}, "5061": {"Id": 5061, "StringId": "benefit_ballistic_dmg_resist_up_percent_name", "Key": "benefit_ballistic_dmg_resist_up_percent_name"}, "5062": {"Id": 5062, "StringId": "benefit_ballistic_dmg_resist_down_percent_name", "Key": "benefit_ballistic_dmg_resist_down_percent_name"}, "5063": {"Id": 5063, "StringId": "benefit_hero_defense_atk_up_value_name", "Key": "benefit_hero_defense_atk_up_value_name"}, "5064": {"Id": 5064, "StringId": "benefit_hero_ranged_atk_up_value_name", "Key": "benefit_hero_ranged_atk_up_value_name"}, "5065": {"Id": 5065, "StringId": "benefit_hero_support_atk_up_value_name", "Key": "benefit_hero_support_atk_up_value_name"}, "5066": {"Id": 5066, "StringId": "benefit_hero_defense_def_up_value_name", "Key": "benefit_hero_defense_def_up_value_name"}, "5067": {"Id": 5067, "StringId": "benefit_hero_ranged_def_up_value_name", "Key": "benefit_hero_ranged_def_up_value_name"}, "5068": {"Id": 5068, "StringId": "benefit_hero_support_def_up_value_name", "Key": "benefit_hero_support_def_up_value_name"}, "5069": {"Id": 5069, "StringId": "benefit_hero_defense_hp_up_value_name", "Key": "benefit_hero_defense_hp_up_value_name"}, "5070": {"Id": 5070, "StringId": "benefit_hero_ranged_hp_up_value_name", "Key": "benefit_hero_ranged_hp_up_value_name"}, "5071": {"Id": 5071, "StringId": "benefit_hero_support_hp_up_value_name", "Key": "benefit_hero_support_hp_up_value_name"}, "5072": {"Id": 5072, "StringId": "benefit_hero_defense_atk_up_per_level_name", "Key": "benefit_hero_defense_atk_up_per_level_name"}, "5073": {"Id": 5073, "StringId": "benefit_hero_ranged_atk_up_per_level_name", "Key": "benefit_hero_ranged_atk_up_per_level_name"}, "5074": {"Id": 5074, "StringId": "benefit_hero_support_atk_up_per_level_name", "Key": "benefit_hero_support_atk_up_per_level_name"}, "5075": {"Id": 5075, "StringId": "benefit_hero_defense_def_up_per_level_name", "Key": "benefit_hero_defense_def_up_per_level_name"}, "5076": {"Id": 5076, "StringId": "benefit_hero_ranged_def_up_per_level_name", "Key": "benefit_hero_ranged_def_up_per_level_name"}, "5077": {"Id": 5077, "StringId": "benefit_hero_support_def_up_per_level_name", "Key": "benefit_hero_support_def_up_per_level_name"}, "5078": {"Id": 5078, "StringId": "benefit_hero_defense_hp_up_per_level_name", "Key": "benefit_hero_defense_hp_up_per_level_name"}, "5079": {"Id": 5079, "StringId": "benefit_hero_ranged_hp_up_per_level_name", "Key": "benefit_hero_ranged_hp_up_per_level_name"}, "5080": {"Id": 5080, "StringId": "benefit_hero_support_hp_up_per_level_name", "Key": "benefit_hero_support_hp_up_per_level_name"}, "5081": {"Id": 5081, "StringId": "benefit_fire_dmg_up_percent_name", "Key": "benefit_fire_dmg_up_percent_name"}, "5082": {"Id": 5082, "StringId": "benefit_fire_dmg_down_percent_name", "Key": "benefit_fire_dmg_down_percent_name"}, "5083": {"Id": 5083, "StringId": "benefit_electrical_dmg_up_percent_name", "Key": "benefit_electrical_dmg_up_percent_name"}, "5084": {"Id": 5084, "StringId": "benefit_electrical_dmg_down_percent_name", "Key": "benefit_electrical_dmg_down_percent_name"}, "5085": {"Id": 5085, "StringId": "benefit_wind_dmg_up_percent_name", "Key": "benefit_wind_dmg_up_percent_name"}, "5086": {"Id": 5086, "StringId": "benefit_wind_dmg_down_percent_name", "Key": "benefit_wind_dmg_down_percent_name"}, "5087": {"Id": 5087, "StringId": "benefit_light_dmg_up_percent_name", "Key": "benefit_light_dmg_up_percent_name"}, "5088": {"Id": 5088, "StringId": "benefit_light_dmg_down_percent_name", "Key": "benefit_light_dmg_down_percent_name"}, "5089": {"Id": 5089, "StringId": "benefit_ice_dmg_up_percent_name", "Key": "benefit_ice_dmg_up_percent_name"}, "5090": {"Id": 5090, "StringId": "benefit_ice_dmg_down_percent_name", "Key": "benefit_ice_dmg_down_percent_name"}, "5091": {"Id": 5091, "StringId": "benefit_physical_dmg_up_percent_name", "Key": "benefit_physical_dmg_up_percent_name"}, "5092": {"Id": 5092, "StringId": "benefit_physical_dmg_down_percent_name", "Key": "benefit_physical_dmg_down_percent_name"}, "5093": {"Id": 5093, "StringId": "benefit_ballistic_dmg_up_percent_name", "Key": "benefit_ballistic_dmg_up_percent_name"}, "5094": {"Id": 5094, "StringId": "benefit_ballistic_dmg_down_percent_name", "Key": "benefit_ballistic_dmg_down_percent_name"}, "6001": {"Id": 6001, "StringId": "monster_1_skill_desc", "Key": "monster_1_skill_desc"}, "6002": {"Id": 6002, "StringId": "monster_2_skill_desc", "Key": "monster_2_skill_desc"}, "6003": {"Id": 6003, "StringId": "monster_3_skill_desc", "Key": "monster_3_skill_desc"}, "6004": {"Id": 6004, "StringId": "monster_4_skill_desc", "Key": "monster_4_skill_desc"}, "6005": {"Id": 6005, "StringId": "monster_5_skill_1_desc", "Key": "monster_5_skill_1_desc"}, "6006": {"Id": 6006, "StringId": "monster_5_skill_2_desc", "Key": "monster_5_skill_2_desc"}, "6007": {"Id": 6007, "StringId": "monster_6_skill_desc", "Key": "monster_6_skill_desc"}, "6008": {"Id": 6008, "StringId": "boss_1_skill_1_desc", "Key": "boss_1_skill_1_desc"}, "6009": {"Id": 6009, "StringId": "boss_1_skill_2_desc", "Key": "boss_1_skill_2_desc"}, "6010": {"Id": 6010, "StringId": "boss_1_skill_3_desc", "Key": "boss_1_skill_3_desc"}, "6011": {"Id": 6011, "StringId": "boss_1_skill_4_desc", "Key": "boss_1_skill_4_desc"}, "6012": {"Id": 6012, "StringId": "boss_1_skill_5_desc", "Key": "boss_1_skill_5_desc"}, "6013": {"Id": 6013, "StringId": "monster_pos_type_1_name", "Key": "monster_pos_type_1_name"}, "6014": {"Id": 6014, "StringId": "monster_pos_type_2_name", "Key": "monster_pos_type_2_name"}, "6015": {"Id": 6015, "StringId": "monster_grade_common_name", "Key": "monster_grade_common_name"}, "6016": {"Id": 6016, "StringId": "monster_grade_elite_name", "Key": "monster_grade_elite_name"}, "6017": {"Id": 6017, "StringId": "monster_grade_boss_name", "Key": "monster_grade_boss_name"}, "6018": {"Id": 6018, "StringId": "monster_common_1_name", "Key": "monster_common_1_name"}, "6019": {"Id": 6019, "StringId": "monster_common_2_name", "Key": "monster_common_2_name"}, "6020": {"Id": 6020, "StringId": "monster_common_4_name", "Key": "monster_common_4_name"}, "6021": {"Id": 6021, "StringId": "monster_common_5_name", "Key": "monster_common_5_name"}, "6022": {"Id": 6022, "StringId": "monster_boss_1_name", "Key": "monster_boss_1_name"}, "6023": {"Id": 6023, "StringId": "monster_common_7_name", "Key": "monster_common_7_name"}, "6024": {"Id": 6024, "StringId": "monster_common_8_name", "Key": "monster_common_8_name"}, "6025": {"Id": 6025, "StringId": "monster_common_9_name", "Key": "monster_common_9_name"}, "6026": {"Id": 6026, "StringId": "monster_common_10_name", "Key": "monster_common_10_name"}, "6027": {"Id": 6027, "StringId": "monster_common_11_name", "Key": "monster_common_11_name"}, "6028": {"Id": 6028, "StringId": "monster_common_12_name", "Key": "monster_common_12_name"}, "6029": {"Id": 6029, "StringId": "monster_common_13_name", "Key": "monster_common_13_name"}, "6030": {"Id": 6030, "StringId": "monster_common_14_name", "Key": "monster_common_14_name"}, "6031": {"Id": 6031, "StringId": "monster_common_15_name", "Key": "monster_common_15_name"}, "6032": {"Id": 6032, "StringId": "monster_common_16_name", "Key": "monster_common_16_name"}, "6033": {"Id": 6033, "StringId": "monster_common_17_name", "Key": "monster_common_17_name"}, "6034": {"Id": 6034, "StringId": "monster_elites_1_name", "Key": "monster_elites_1_name"}, "6035": {"Id": 6035, "StringId": "monster_elites_2_name", "Key": "monster_elites_2_name"}, "6036": {"Id": 6036, "StringId": "monster_elites_3_name", "Key": "monster_elites_3_name"}, "6037": {"Id": 6037, "StringId": "monster_elites_4_name", "Key": "monster_elites_4_name"}, "6038": {"Id": 6038, "StringId": "monster_elites_5_name", "Key": "monster_elites_5_name"}, "6039": {"Id": 6039, "StringId": "monster_elites_6_name", "Key": "monster_elites_6_name"}, "6040": {"Id": 6040, "StringId": "monster_elites_7_name", "Key": "monster_elites_7_name"}, "6041": {"Id": 6041, "StringId": "monster_elites_8_name", "Key": "monster_elites_8_name"}, "6042": {"Id": 6042, "StringId": "monster_elites_9_name", "Key": "monster_elites_9_name"}, "6043": {"Id": 6043, "StringId": "monster_elites_10_name", "Key": "monster_elites_10_name"}, "6044": {"Id": 6044, "StringId": "monster_elites_11_name", "Key": "monster_elites_11_name"}, "6045": {"Id": 6045, "StringId": "monster_elites_12_name", "Key": "monster_elites_12_name"}, "6046": {"Id": 6046, "StringId": "monster_elites_13_name", "Key": "monster_elites_13_name"}, "6047": {"Id": 6047, "StringId": "monster_boss_2_name", "Key": "monster_boss_2_name"}, "6048": {"Id": 6048, "StringId": "monster_boss_3_name", "Key": "monster_boss_3_name"}, "6049": {"Id": 6049, "StringId": "monster_common_1_adv", "Key": "monster_common_1_adv"}, "6050": {"Id": 6050, "StringId": "monster_common_2_adv", "Key": "monster_common_2_adv"}, "6051": {"Id": 6051, "StringId": "monster_common_4_adv", "Key": "monster_common_4_adv"}, "6052": {"Id": 6052, "StringId": "monster_common_5_adv", "Key": "monster_common_5_adv"}, "6053": {"Id": 6053, "StringId": "monster_boss_1_adv", "Key": "monster_boss_1_adv"}, "6054": {"Id": 6054, "StringId": "monster_common_7_adv", "Key": "monster_common_7_adv"}, "6055": {"Id": 6055, "StringId": "monster_common_8_adv", "Key": "monster_common_8_adv"}, "6056": {"Id": 6056, "StringId": "monster_common_9_adv", "Key": "monster_common_9_adv"}, "6057": {"Id": 6057, "StringId": "monster_common_10_adv", "Key": "monster_common_10_adv"}, "6058": {"Id": 6058, "StringId": "monster_common_11_adv", "Key": "monster_common_11_adv"}, "6059": {"Id": 6059, "StringId": "monster_common_12_adv", "Key": "monster_common_12_adv"}, "6060": {"Id": 6060, "StringId": "monster_common_13_adv", "Key": "monster_common_13_adv"}, "6061": {"Id": 6061, "StringId": "monster_common_14_adv", "Key": "monster_common_14_adv"}, "6062": {"Id": 6062, "StringId": "monster_common_15_adv", "Key": "monster_common_15_adv"}, "6063": {"Id": 6063, "StringId": "monster_common_16_adv", "Key": "monster_common_16_adv"}, "6064": {"Id": 6064, "StringId": "monster_common_17_adv", "Key": "monster_common_17_adv"}, "6065": {"Id": 6065, "StringId": "monster_elites_1_adv", "Key": "monster_elites_1_adv"}, "6066": {"Id": 6066, "StringId": "monster_elites_2_adv", "Key": "monster_elites_2_adv"}, "6067": {"Id": 6067, "StringId": "monster_elites_3_adv", "Key": "monster_elites_3_adv"}, "6068": {"Id": 6068, "StringId": "monster_elites_4_adv", "Key": "monster_elites_4_adv"}, "6069": {"Id": 6069, "StringId": "monster_elites_5_adv", "Key": "monster_elites_5_adv"}, "6070": {"Id": 6070, "StringId": "monster_elites_6_adv", "Key": "monster_elites_6_adv"}, "6071": {"Id": 6071, "StringId": "monster_elites_7_adv", "Key": "monster_elites_7_adv"}, "6072": {"Id": 6072, "StringId": "monster_elites_8_adv", "Key": "monster_elites_8_adv"}, "6073": {"Id": 6073, "StringId": "monster_elites_9_adv", "Key": "monster_elites_9_adv"}, "6074": {"Id": 6074, "StringId": "monster_elites_10_adv", "Key": "monster_elites_10_adv"}, "6075": {"Id": 6075, "StringId": "monster_elites_11_adv", "Key": "monster_elites_11_adv"}, "6076": {"Id": 6076, "StringId": "monster_elites_12_adv", "Key": "monster_elites_12_adv"}, "6077": {"Id": 6077, "StringId": "monster_elites_13_adv", "Key": "monster_elites_13_adv"}, "6078": {"Id": 6078, "StringId": "monster_boss_2_adv", "Key": "monster_boss_2_adv"}, "6079": {"Id": 6079, "StringId": "monster_boss_3_adv", "Key": "monster_boss_3_adv"}, "6080": {"Id": 6080, "StringId": "monster_common_1_weak", "Key": "monster_common_1_weak"}, "6081": {"Id": 6081, "StringId": "monster_common_2_weak", "Key": "monster_common_2_weak"}, "6082": {"Id": 6082, "StringId": "monster_common_4_weak", "Key": "monster_common_4_weak"}, "6083": {"Id": 6083, "StringId": "monster_common_5_weak", "Key": "monster_common_5_weak"}, "6084": {"Id": 6084, "StringId": "monster_boss_1_weak", "Key": "monster_boss_1_weak"}, "6085": {"Id": 6085, "StringId": "monster_common_7_weak", "Key": "monster_common_7_weak"}, "6086": {"Id": 6086, "StringId": "monster_common_8_weak", "Key": "monster_common_8_weak"}, "6087": {"Id": 6087, "StringId": "monster_common_9_weak", "Key": "monster_common_9_weak"}, "6088": {"Id": 6088, "StringId": "monster_common_10_weak", "Key": "monster_common_10_weak"}, "6089": {"Id": 6089, "StringId": "monster_common_11_weak", "Key": "monster_common_11_weak"}, "6090": {"Id": 6090, "StringId": "monster_common_12_weak", "Key": "monster_common_12_weak"}, "6091": {"Id": 6091, "StringId": "monster_common_13_weak", "Key": "monster_common_13_weak"}, "6092": {"Id": 6092, "StringId": "monster_common_14_weak", "Key": "monster_common_14_weak"}, "6093": {"Id": 6093, "StringId": "monster_common_15_weak", "Key": "monster_common_15_weak"}, "6094": {"Id": 6094, "StringId": "monster_common_16_weak", "Key": "monster_common_16_weak"}, "6095": {"Id": 6095, "StringId": "monster_common_17_weak", "Key": "monster_common_17_weak"}, "6096": {"Id": 6096, "StringId": "monster_elites_1_weak", "Key": "monster_elites_1_weak"}, "6097": {"Id": 6097, "StringId": "monster_elites_2_weak", "Key": "monster_elites_2_weak"}, "6098": {"Id": 6098, "StringId": "monster_elites_3_weak", "Key": "monster_elites_3_weak"}, "6099": {"Id": 6099, "StringId": "monster_elites_4_weak", "Key": "monster_elites_4_weak"}, "6100": {"Id": 6100, "StringId": "monster_elites_5_weak", "Key": "monster_elites_5_weak"}, "6101": {"Id": 6101, "StringId": "monster_elites_6_weak", "Key": "monster_elites_6_weak"}, "6102": {"Id": 6102, "StringId": "monster_elites_7_weak", "Key": "monster_elites_7_weak"}, "6103": {"Id": 6103, "StringId": "monster_elites_8_weak", "Key": "monster_elites_8_weak"}, "6104": {"Id": 6104, "StringId": "monster_elites_9_weak", "Key": "monster_elites_9_weak"}, "6105": {"Id": 6105, "StringId": "monster_elites_10_weak", "Key": "monster_elites_10_weak"}, "6106": {"Id": 6106, "StringId": "monster_elites_11_weak", "Key": "monster_elites_11_weak"}, "6107": {"Id": 6107, "StringId": "monster_elites_12_weak", "Key": "monster_elites_12_weak"}, "6108": {"Id": 6108, "StringId": "monster_elites_13_weak", "Key": "monster_elites_13_weak"}, "6109": {"Id": 6109, "StringId": "monster_boss_2_weak", "Key": "monster_boss_2_weak"}, "6110": {"Id": 6110, "StringId": "monster_boss_3_weak", "Key": "monster_boss_3_weak"}, "6111": {"Id": 6111, "StringId": "Phase_2_monster_1_adv", "Key": "Phase_2_monster_1_adv"}, "6112": {"Id": 6112, "StringId": "Phase_2_monster_2_adv", "Key": "Phase_2_monster_2_adv"}, "6113": {"Id": 6113, "StringId": "Phase_2_monster_3_adv", "Key": "Phase_2_monster_3_adv"}, "6114": {"Id": 6114, "StringId": "Phase_2_monster_4_adv", "Key": "Phase_2_monster_4_adv"}, "6115": {"Id": 6115, "StringId": "Phase_2_monster_5_adv", "Key": "Phase_2_monster_5_adv"}, "6116": {"Id": 6116, "StringId": "Phase_2_monster_6_adv", "Key": "Phase_2_monster_6_adv"}, "6117": {"Id": 6117, "StringId": "Phase_2_monster_7_adv", "Key": "Phase_2_monster_7_adv"}, "6118": {"Id": 6118, "StringId": "Phase_2_monster_8_adv", "Key": "Phase_2_monster_8_adv"}, "6119": {"Id": 6119, "StringId": "Phase_2_monster_9_adv", "Key": "Phase_2_monster_9_adv"}, "6120": {"Id": 6120, "StringId": "Phase_2_monster_10_adv", "Key": "Phase_2_monster_10_adv"}, "6121": {"Id": 6121, "StringId": "Phase_2_monster_11_adv", "Key": "Phase_2_monster_11_adv"}, "6122": {"Id": 6122, "StringId": "Phase_2_monster_12_adv", "Key": "Phase_2_monster_12_adv"}, "6123": {"Id": 6123, "StringId": "Phase_2_monster_13_adv", "Key": "Phase_2_monster_13_adv"}, "6124": {"Id": 6124, "StringId": "Phase_2_monster_14_adv", "Key": "Phase_2_monster_14_adv"}, "6125": {"Id": 6125, "StringId": "Phase_2_monster_15_adv", "Key": "Phase_2_monster_15_adv"}, "6126": {"Id": 6126, "StringId": "Phase_2_monster_16_adv", "Key": "Phase_2_monster_16_adv"}, "6127": {"Id": 6127, "StringId": "Phase_2_monster_17_adv", "Key": "Phase_2_monster_17_adv"}, "6128": {"Id": 6128, "StringId": "Phase_2_monster_18_adv", "Key": "Phase_2_monster_18_adv"}, "6129": {"Id": 6129, "StringId": "Phase_2_monster_19_adv", "Key": "Phase_2_monster_19_adv"}, "6130": {"Id": 6130, "StringId": "Phase_2_monster_20_adv", "Key": "Phase_2_monster_20_adv"}, "6131": {"Id": 6131, "StringId": "Phase_2_monster_21_adv", "Key": "Phase_2_monster_21_adv"}, "6132": {"Id": 6132, "StringId": "Phase_2_elite_1_adv", "Key": "Phase_2_elite_1_adv"}, "6133": {"Id": 6133, "StringId": "Phase_2_elite_2_adv", "Key": "Phase_2_elite_2_adv"}, "6134": {"Id": 6134, "StringId": "Phase_2_elite_3_adv", "Key": "Phase_2_elite_3_adv"}, "6135": {"Id": 6135, "StringId": "Phase_2_elite_4_adv", "Key": "Phase_2_elite_4_adv"}, "6136": {"Id": 6136, "StringId": "Phase_2_elite_5_adv", "Key": "Phase_2_elite_5_adv"}, "6137": {"Id": 6137, "StringId": "Phase_2_elite_6_adv", "Key": "Phase_2_elite_6_adv"}, "6138": {"Id": 6138, "StringId": "Phase_2_elite_7_adv", "Key": "Phase_2_elite_7_adv"}, "6139": {"Id": 6139, "StringId": "Phase_2_elite_8_adv", "Key": "Phase_2_elite_8_adv"}, "6140": {"Id": 6140, "StringId": "Phase_2_elite_9_adv", "Key": "Phase_2_elite_9_adv"}, "6141": {"Id": 6141, "StringId": "Phase_2_elite_10_adv", "Key": "Phase_2_elite_10_adv"}, "6142": {"Id": 6142, "StringId": "Phase_2_elite_11_adv", "Key": "Phase_2_elite_11_adv"}, "6143": {"Id": 6143, "StringId": "Phase_2_elite_12_adv", "Key": "Phase_2_elite_12_adv"}, "6144": {"Id": 6144, "StringId": "Phase_2_elite_13_adv", "Key": "Phase_2_elite_13_adv"}, "6145": {"Id": 6145, "StringId": "Phase_2_elite_14_adv", "Key": "Phase_2_elite_14_adv"}, "6146": {"Id": 6146, "StringId": "Phase_2_elite_15_adv", "Key": "Phase_2_elite_15_adv"}, "6147": {"Id": 6147, "StringId": "Phase_2_elite_16_adv", "Key": "Phase_2_elite_16_adv"}, "6148": {"Id": 6148, "StringId": "Phase_2_elite_17_adv", "Key": "Phase_2_elite_17_adv"}, "6149": {"Id": 6149, "StringId": "Phase_2_elite_18_adv", "Key": "Phase_2_elite_18_adv"}, "6150": {"Id": 6150, "StringId": "Phase_2_elite_19_adv", "Key": "Phase_2_elite_19_adv"}, "6151": {"Id": 6151, "StringId": "Phase_2_elite_20_adv", "Key": "Phase_2_elite_20_adv"}, "6152": {"Id": 6152, "StringId": "Phase_2_elite_21_adv", "Key": "Phase_2_elite_21_adv"}, "6153": {"Id": 6153, "StringId": "Phase_2_elite_22_adv", "Key": "Phase_2_elite_22_adv"}, "6154": {"Id": 6154, "StringId": "Phase_2_elite_23_adv", "Key": "Phase_2_elite_23_adv"}, "6155": {"Id": 6155, "StringId": "Phase_2_elite_24_adv", "Key": "Phase_2_elite_24_adv"}, "6156": {"Id": 6156, "StringId": "Phase_2_elite_25_adv", "Key": "Phase_2_elite_25_adv"}, "6157": {"Id": 6157, "StringId": "Phase_2_elite_26_adv", "Key": "Phase_2_elite_26_adv"}, "6158": {"Id": 6158, "StringId": "Phase_2_elite_27_adv", "Key": "Phase_2_elite_27_adv"}, "6159": {"Id": 6159, "StringId": "Phase_2_boss_1_adv", "Key": "Phase_2_boss_1_adv"}, "6160": {"Id": 6160, "StringId": "Phase_2_boss_2_adv", "Key": "Phase_2_boss_2_adv"}, "6161": {"Id": 6161, "StringId": "Phase_2_boss_3_adv", "Key": "Phase_2_boss_3_adv"}, "6162": {"Id": 6162, "StringId": "Phase_2_boss_4_adv", "Key": "Phase_2_boss_4_adv"}, "6163": {"Id": 6163, "StringId": "Phase_2_boss_5_adv", "Key": "Phase_2_boss_5_adv"}, "6164": {"Id": 6164, "StringId": "Phase_2_boss_6_adv", "Key": "Phase_2_boss_6_adv"}, "6165": {"Id": 6165, "StringId": "Phase_2_boss_7_adv", "Key": "Phase_2_boss_7_adv"}, "6166": {"Id": 6166, "StringId": "Phase_2_boss_8_adv", "Key": "Phase_2_boss_8_adv"}, "6167": {"Id": 6167, "StringId": "Phase_2_boss_9_adv", "Key": "Phase_2_boss_9_adv"}, "6168": {"Id": 6168, "StringId": "Phase_2_monster_1_weak", "Key": "Phase_2_monster_1_weak"}, "6169": {"Id": 6169, "StringId": "Phase_2_monster_2_weak", "Key": "Phase_2_monster_2_weak"}, "6170": {"Id": 6170, "StringId": "Phase_2_monster_3_weak", "Key": "Phase_2_monster_3_weak"}, "6171": {"Id": 6171, "StringId": "Phase_2_monster_4_weak", "Key": "Phase_2_monster_4_weak"}, "6172": {"Id": 6172, "StringId": "Phase_2_monster_5_weak", "Key": "Phase_2_monster_5_weak"}, "6173": {"Id": 6173, "StringId": "Phase_2_monster_6_weak", "Key": "Phase_2_monster_6_weak"}, "6174": {"Id": 6174, "StringId": "Phase_2_monster_7_weak", "Key": "Phase_2_monster_7_weak"}, "6175": {"Id": 6175, "StringId": "Phase_2_monster_8_weak", "Key": "Phase_2_monster_8_weak"}, "6176": {"Id": 6176, "StringId": "Phase_2_monster_9_weak", "Key": "Phase_2_monster_9_weak"}, "6177": {"Id": 6177, "StringId": "Phase_2_monster_10_weak", "Key": "Phase_2_monster_10_weak"}, "6178": {"Id": 6178, "StringId": "Phase_2_monster_11_weak", "Key": "Phase_2_monster_11_weak"}, "6179": {"Id": 6179, "StringId": "Phase_2_monster_12_weak", "Key": "Phase_2_monster_12_weak"}, "6180": {"Id": 6180, "StringId": "Phase_2_monster_13_weak", "Key": "Phase_2_monster_13_weak"}, "6181": {"Id": 6181, "StringId": "Phase_2_monster_14_weak", "Key": "Phase_2_monster_14_weak"}, "6182": {"Id": 6182, "StringId": "Phase_2_monster_15_weak", "Key": "Phase_2_monster_15_weak"}, "6183": {"Id": 6183, "StringId": "Phase_2_monster_16_weak", "Key": "Phase_2_monster_16_weak"}, "6184": {"Id": 6184, "StringId": "Phase_2_monster_17_weak", "Key": "Phase_2_monster_17_weak"}, "6185": {"Id": 6185, "StringId": "Phase_2_monster_18_weak", "Key": "Phase_2_monster_18_weak"}, "6186": {"Id": 6186, "StringId": "Phase_2_monster_19_weak", "Key": "Phase_2_monster_19_weak"}, "6187": {"Id": 6187, "StringId": "Phase_2_monster_20_weak", "Key": "Phase_2_monster_20_weak"}, "6188": {"Id": 6188, "StringId": "Phase_2_monster_21_weak", "Key": "Phase_2_monster_21_weak"}, "6189": {"Id": 6189, "StringId": "Phase_2_elite_1_weak", "Key": "Phase_2_elite_1_weak"}, "6190": {"Id": 6190, "StringId": "Phase_2_elite_2_weak", "Key": "Phase_2_elite_2_weak"}, "6191": {"Id": 6191, "StringId": "Phase_2_elite_3_weak", "Key": "Phase_2_elite_3_weak"}, "6192": {"Id": 6192, "StringId": "Phase_2_elite_4_weak", "Key": "Phase_2_elite_4_weak"}, "6193": {"Id": 6193, "StringId": "Phase_2_elite_5_weak", "Key": "Phase_2_elite_5_weak"}, "6194": {"Id": 6194, "StringId": "Phase_2_elite_6_weak", "Key": "Phase_2_elite_6_weak"}, "6195": {"Id": 6195, "StringId": "Phase_2_elite_7_weak", "Key": "Phase_2_elite_7_weak"}, "6196": {"Id": 6196, "StringId": "Phase_2_elite_8_weak", "Key": "Phase_2_elite_8_weak"}, "6197": {"Id": 6197, "StringId": "Phase_2_elite_9_weak", "Key": "Phase_2_elite_9_weak"}, "6198": {"Id": 6198, "StringId": "Phase_2_elite_10_weak", "Key": "Phase_2_elite_10_weak"}, "6199": {"Id": 6199, "StringId": "Phase_2_elite_11_weak", "Key": "Phase_2_elite_11_weak"}, "6200": {"Id": 6200, "StringId": "Phase_2_elite_12_weak", "Key": "Phase_2_elite_12_weak"}, "6201": {"Id": 6201, "StringId": "Phase_2_elite_13_weak", "Key": "Phase_2_elite_13_weak"}, "6202": {"Id": 6202, "StringId": "Phase_2_elite_14_weak", "Key": "Phase_2_elite_14_weak"}, "6203": {"Id": 6203, "StringId": "Phase_2_elite_15_weak", "Key": "Phase_2_elite_15_weak"}, "6204": {"Id": 6204, "StringId": "Phase_2_elite_16_weak", "Key": "Phase_2_elite_16_weak"}, "6205": {"Id": 6205, "StringId": "Phase_2_elite_17_weak", "Key": "Phase_2_elite_17_weak"}, "6206": {"Id": 6206, "StringId": "Phase_2_elite_18_weak", "Key": "Phase_2_elite_18_weak"}, "6207": {"Id": 6207, "StringId": "Phase_2_elite_19_weak", "Key": "Phase_2_elite_19_weak"}, "6208": {"Id": 6208, "StringId": "Phase_2_elite_20_weak", "Key": "Phase_2_elite_20_weak"}, "6209": {"Id": 6209, "StringId": "Phase_2_elite_21_weak", "Key": "Phase_2_elite_21_weak"}, "6210": {"Id": 6210, "StringId": "Phase_2_elite_22_weak", "Key": "Phase_2_elite_22_weak"}, "6211": {"Id": 6211, "StringId": "Phase_2_elite_23_weak", "Key": "Phase_2_elite_23_weak"}, "6212": {"Id": 6212, "StringId": "Phase_2_elite_24_weak", "Key": "Phase_2_elite_24_weak"}, "6213": {"Id": 6213, "StringId": "Phase_2_elite_25_weak", "Key": "Phase_2_elite_25_weak"}, "6214": {"Id": 6214, "StringId": "Phase_2_elite_26_weak", "Key": "Phase_2_elite_26_weak"}, "6215": {"Id": 6215, "StringId": "Phase_2_elite_27_weak", "Key": "Phase_2_elite_27_weak"}, "6216": {"Id": 6216, "StringId": "Phase_2_boss_1_weak", "Key": "Phase_2_boss_1_weak"}, "6217": {"Id": 6217, "StringId": "Phase_2_boss_2_weak", "Key": "Phase_2_boss_2_weak"}, "6218": {"Id": 6218, "StringId": "Phase_2_boss_3_weak", "Key": "Phase_2_boss_3_weak"}, "6219": {"Id": 6219, "StringId": "Phase_2_boss_4_weak", "Key": "Phase_2_boss_4_weak"}, "6220": {"Id": 6220, "StringId": "Phase_2_boss_5_weak", "Key": "Phase_2_boss_5_weak"}, "6221": {"Id": 6221, "StringId": "Phase_2_boss_6_weak", "Key": "Phase_2_boss_6_weak"}, "6222": {"Id": 6222, "StringId": "Phase_2_boss_7_weak", "Key": "Phase_2_boss_7_weak"}, "6223": {"Id": 6223, "StringId": "Phase_2_boss_8_weak", "Key": "Phase_2_boss_8_weak"}, "6224": {"Id": 6224, "StringId": "Phase_2_boss_9_weak", "Key": "Phase_2_boss_9_weak"}, "6225": {"Id": 6225, "StringId": "Phase_2_monster_1_name", "Key": "Phase_2_monster_1_name"}, "6226": {"Id": 6226, "StringId": "Phase_2_monster_2_name", "Key": "Phase_2_monster_2_name"}, "6227": {"Id": 6227, "StringId": "Phase_2_monster_3_name", "Key": "Phase_2_monster_3_name"}, "6228": {"Id": 6228, "StringId": "Phase_2_monster_4_name", "Key": "Phase_2_monster_4_name"}, "6229": {"Id": 6229, "StringId": "Phase_2_monster_5_name", "Key": "Phase_2_monster_5_name"}, "6230": {"Id": 6230, "StringId": "Phase_2_monster_6_name", "Key": "Phase_2_monster_6_name"}, "6231": {"Id": 6231, "StringId": "Phase_2_monster_7_name", "Key": "Phase_2_monster_7_name"}, "6232": {"Id": 6232, "StringId": "Phase_2_monster_8_name", "Key": "Phase_2_monster_8_name"}, "6233": {"Id": 6233, "StringId": "Phase_2_monster_9_name", "Key": "Phase_2_monster_9_name"}, "6234": {"Id": 6234, "StringId": "Phase_2_monster_10_name", "Key": "Phase_2_monster_10_name"}, "6235": {"Id": 6235, "StringId": "Phase_2_monster_11_name", "Key": "Phase_2_monster_11_name"}, "6236": {"Id": 6236, "StringId": "Phase_2_monster_12_name", "Key": "Phase_2_monster_12_name"}, "6237": {"Id": 6237, "StringId": "Phase_2_monster_13_name", "Key": "Phase_2_monster_13_name"}, "6238": {"Id": 6238, "StringId": "Phase_2_monster_14_name", "Key": "Phase_2_monster_14_name"}, "6239": {"Id": 6239, "StringId": "Phase_2_monster_15_name", "Key": "Phase_2_monster_15_name"}, "6240": {"Id": 6240, "StringId": "Phase_2_monster_16_name", "Key": "Phase_2_monster_16_name"}, "6241": {"Id": 6241, "StringId": "Phase_2_elite_1_name", "Key": "Phase_2_elite_1_name"}, "6242": {"Id": 6242, "StringId": "Phase_2_elite_2_name", "Key": "Phase_2_elite_2_name"}, "6243": {"Id": 6243, "StringId": "Phase_2_elite_3_name", "Key": "Phase_2_elite_3_name"}, "6244": {"Id": 6244, "StringId": "Phase_2_elite_4_name", "Key": "Phase_2_elite_4_name"}, "6245": {"Id": 6245, "StringId": "Phase_2_elite_5_name", "Key": "Phase_2_elite_5_name"}, "6246": {"Id": 6246, "StringId": "Phase_2_elite_6_name", "Key": "Phase_2_elite_6_name"}, "6247": {"Id": 6247, "StringId": "Phase_2_elite_7_name", "Key": "Phase_2_elite_7_name"}, "6248": {"Id": 6248, "StringId": "Phase_2_elite_8_name", "Key": "Phase_2_elite_8_name"}, "6249": {"Id": 6249, "StringId": "Phase_2_elite_9_name", "Key": "Phase_2_elite_9_name"}, "6250": {"Id": 6250, "StringId": "Phase_2_elite_10_name", "Key": "Phase_2_elite_10_name"}, "6251": {"Id": 6251, "StringId": "Phase_2_elite_11_name", "Key": "Phase_2_elite_11_name"}, "6252": {"Id": 6252, "StringId": "Phase_2_elite_12_name", "Key": "Phase_2_elite_12_name"}, "6253": {"Id": 6253, "StringId": "Phase_2_elite_13_name", "Key": "Phase_2_elite_13_name"}, "6254": {"Id": 6254, "StringId": "Phase_2_elite_14_name", "Key": "Phase_2_elite_14_name"}, "6255": {"Id": 6255, "StringId": "Phase_2_elite_15_name", "Key": "Phase_2_elite_15_name"}, "6256": {"Id": 6256, "StringId": "Phase_2_elite_16_name", "Key": "Phase_2_elite_16_name"}, "6257": {"Id": 6257, "StringId": "Phase_2_elite_17_name", "Key": "Phase_2_elite_17_name"}, "6258": {"Id": 6258, "StringId": "Phase_2_elite_18_name", "Key": "Phase_2_elite_18_name"}, "6259": {"Id": 6259, "StringId": "Phase_2_elite_19_name", "Key": "Phase_2_elite_19_name"}, "6260": {"Id": 6260, "StringId": "Phase_2_elite_20_name", "Key": "Phase_2_elite_20_name"}, "6261": {"Id": 6261, "StringId": "Phase_2_elite_21_name", "Key": "Phase_2_elite_21_name"}, "6262": {"Id": 6262, "StringId": "Phase_2_elite_22_name", "Key": "Phase_2_elite_22_name"}, "6263": {"Id": 6263, "StringId": "Phase_2_boss_1_name", "Key": "Phase_2_boss_1_name"}, "6264": {"Id": 6264, "StringId": "Phase_2_boss_2_name", "Key": "Phase_2_boss_2_name"}, "6265": {"Id": 6265, "StringId": "Phase_2_boss_3_name", "Key": "Phase_2_boss_3_name"}, "6266": {"Id": 6266, "StringId": "Phase_2_boss_4_name", "Key": "Phase_2_boss_4_name"}, "6267": {"Id": 6267, "StringId": "Phase_2_boss_5_name", "Key": "Phase_2_boss_5_name"}, "6268": {"Id": 6268, "StringId": "Phase_2_boss_6_name", "Key": "Phase_2_boss_6_name"}, "6269": {"Id": 6269, "StringId": "Phase_2_boss_7_name", "Key": "Phase_2_boss_7_name"}, "6270": {"Id": 6270, "StringId": "Phase_2_boss_8_name", "Key": "Phase_2_boss_8_name"}, "6271": {"Id": 6271, "StringId": "Phase_2_boss_9_name", "Key": "Phase_2_boss_9_name"}, "6272": {"Id": 6272, "StringId": "monster_common_melee_1_name", "Key": "monster_common_melee_1_name"}, "6273": {"Id": 6273, "StringId": "monster_common_melee_2_name", "Key": "monster_common_melee_2_name"}, "6274": {"Id": 6274, "StringId": "monster_common_melee_3_name", "Key": "monster_common_melee_3_name"}, "6275": {"Id": 6275, "StringId": "monster_common_melee_4_name", "Key": "monster_common_melee_4_name"}, "6276": {"Id": 6276, "StringId": "monster_common_melee_5_name", "Key": "monster_common_melee_5_name"}, "6277": {"Id": 6277, "StringId": "monster_common_melee_6_name", "Key": "monster_common_melee_6_name"}, "6278": {"Id": 6278, "StringId": "monster_common_melee_7_name", "Key": "monster_common_melee_7_name"}, "6279": {"Id": 6279, "StringId": "monster_common_melee_8_name", "Key": "monster_common_melee_8_name"}, "6280": {"Id": 6280, "StringId": "monster_common_melee_9_name", "Key": "monster_common_melee_9_name"}, "6281": {"Id": 6281, "StringId": "monster_common_melee_10_name", "Key": "monster_common_melee_10_name"}, "6282": {"Id": 6282, "StringId": "monster_common_melee_11_name", "Key": "monster_common_melee_11_name"}, "6283": {"Id": 6283, "StringId": "monster_common_melee_12_name", "Key": "monster_common_melee_12_name"}, "6284": {"Id": 6284, "StringId": "monster_common_melee_13_name", "Key": "monster_common_melee_13_name"}, "6285": {"Id": 6285, "StringId": "monster_common_melee_14_name", "Key": "monster_common_melee_14_name"}, "6286": {"Id": 6286, "StringId": "monster_common_ranger_1_name", "Key": "monster_common_ranger_1_name"}, "6287": {"Id": 6287, "StringId": "monster_common_ranger_2_name", "Key": "monster_common_ranger_2_name"}, "6288": {"Id": 6288, "StringId": "monster_common_ranger_3_name", "Key": "monster_common_ranger_3_name"}, "6289": {"Id": 6289, "StringId": "monster_common_ranger_4_name", "Key": "monster_common_ranger_4_name"}, "6290": {"Id": 6290, "StringId": "monster_common_ranger_5_name", "Key": "monster_common_ranger_5_name"}, "6291": {"Id": 6291, "StringId": "monster_common_ranger_6_name", "Key": "monster_common_ranger_6_name"}, "6292": {"Id": 6292, "StringId": "monster_common_ranger_7_name", "Key": "monster_common_ranger_7_name"}, "6293": {"Id": 6293, "StringId": "monster_common_ranger_8_name", "Key": "monster_common_ranger_8_name"}, "6294": {"Id": 6294, "StringId": "monster_common_ranger_9_name", "Key": "monster_common_ranger_9_name"}, "6295": {"Id": 6295, "StringId": "monster_common_ranger_10_name", "Key": "monster_common_ranger_10_name"}, "6296": {"Id": 6296, "StringId": "monster_common_tank_1_name", "Key": "monster_common_tank_1_name"}, "6297": {"Id": 6297, "StringId": "monster_common_tank_2_name", "Key": "monster_common_tank_2_name"}, "6298": {"Id": 6298, "StringId": "monster_common_tank_3_name", "Key": "monster_common_tank_3_name"}, "6299": {"Id": 6299, "StringId": "monster_common_tank_4_name", "Key": "monster_common_tank_4_name"}, "6300": {"Id": 6300, "StringId": "monster_common_tank_5_name", "Key": "monster_common_tank_5_name"}, "6301": {"Id": 6301, "StringId": "monster_common_tank_6_name", "Key": "monster_common_tank_6_name"}, "6302": {"Id": 6302, "StringId": "monster_common_tank_7_name", "Key": "monster_common_tank_7_name"}, "6303": {"Id": 6303, "StringId": "monster_common_tank_8_name", "Key": "monster_common_tank_8_name"}, "6304": {"Id": 6304, "StringId": "monster_common_tank_9_name", "Key": "monster_common_tank_9_name"}, "6305": {"Id": 6305, "StringId": "monster_common_tank_10_name", "Key": "monster_common_tank_10_name"}, "6306": {"Id": 6306, "StringId": "monster_common_tank_11_name", "Key": "monster_common_tank_11_name"}, "6307": {"Id": 6307, "StringId": "monster_common_tank_12_name", "Key": "monster_common_tank_12_name"}, "6308": {"Id": 6308, "StringId": "monster_common_tank_13_name", "Key": "monster_common_tank_13_name"}, "6309": {"Id": 6309, "StringId": "monster_common_air_melee_1_name", "Key": "monster_common_air_melee_1_name"}, "6310": {"Id": 6310, "StringId": "monster_common_air_melee_2_name", "Key": "monster_common_air_melee_2_name"}, "6311": {"Id": 6311, "StringId": "monster_common_air_melee_3_name", "Key": "monster_common_air_melee_3_name"}, "6312": {"Id": 6312, "StringId": "monster_common_air_melee_4_name", "Key": "monster_common_air_melee_4_name"}, "6313": {"Id": 6313, "StringId": "monster_common_air_ranger_1_name", "Key": "monster_common_air_ranger_1_name"}, "6314": {"Id": 6314, "StringId": "monster_common_air_ranger_2_name", "Key": "monster_common_air_ranger_2_name"}, "6315": {"Id": 6315, "StringId": "monster_common_assassin_1_name", "Key": "monster_common_assassin_1_name"}, "6316": {"Id": 6316, "StringId": "monster_common_assassin_2_name", "Key": "monster_common_assassin_2_name"}, "6317": {"Id": 6317, "StringId": "monster_common_assassin_3_name", "Key": "monster_common_assassin_3_name"}, "6318": {"Id": 6318, "StringId": "monster_common_assassin_4_name", "Key": "monster_common_assassin_4_name"}, "6319": {"Id": 6319, "StringId": "monster_common_assassin_5_name", "Key": "monster_common_assassin_5_name"}, "6320": {"Id": 6320, "StringId": "monster_common_suicide_1_name", "Key": "monster_common_suicide_1_name"}, "6321": {"Id": 6321, "StringId": "monster_elite_melee_1_name", "Key": "monster_elite_melee_1_name"}, "6322": {"Id": 6322, "StringId": "monster_elite_melee_2_name", "Key": "monster_elite_melee_2_name"}, "6323": {"Id": 6323, "StringId": "monster_elite_melee_3_name", "Key": "monster_elite_melee_3_name"}, "6324": {"Id": 6324, "StringId": "monster_elite_melee_4_name", "Key": "monster_elite_melee_4_name"}, "6325": {"Id": 6325, "StringId": "monster_elite_melee_5_name", "Key": "monster_elite_melee_5_name"}, "6326": {"Id": 6326, "StringId": "monster_elite_melee_6_name", "Key": "monster_elite_melee_6_name"}, "6327": {"Id": 6327, "StringId": "monster_elite_melee_7_name", "Key": "monster_elite_melee_7_name"}, "6328": {"Id": 6328, "StringId": "monster_elite_melee_8_name", "Key": "monster_elite_melee_8_name"}, "6329": {"Id": 6329, "StringId": "monster_elite_melee_9_name", "Key": "monster_elite_melee_9_name"}, "6330": {"Id": 6330, "StringId": "monster_elite_melee_10_name", "Key": "monster_elite_melee_10_name"}, "6331": {"Id": 6331, "StringId": "monster_elite_melee_11_name", "Key": "monster_elite_melee_11_name"}, "6332": {"Id": 6332, "StringId": "monster_elite_melee_12_name", "Key": "monster_elite_melee_12_name"}, "6333": {"Id": 6333, "StringId": "monster_elite_ranger_1_name", "Key": "monster_elite_ranger_1_name"}, "6334": {"Id": 6334, "StringId": "monster_elite_ranger_2_name", "Key": "monster_elite_ranger_2_name"}, "6335": {"Id": 6335, "StringId": "monster_elite_ranger_3_name", "Key": "monster_elite_ranger_3_name"}, "6336": {"Id": 6336, "StringId": "monster_elite_ranger_4_name", "Key": "monster_elite_ranger_4_name"}, "6337": {"Id": 6337, "StringId": "monster_elite_ranger_5_name", "Key": "monster_elite_ranger_5_name"}, "6338": {"Id": 6338, "StringId": "monster_elite_ranger_6_name", "Key": "monster_elite_ranger_6_name"}, "6339": {"Id": 6339, "StringId": "monster_elite_ranger_7_name", "Key": "monster_elite_ranger_7_name"}, "6340": {"Id": 6340, "StringId": "monster_elite_ranger_8_name", "Key": "monster_elite_ranger_8_name"}, "6341": {"Id": 6341, "StringId": "monster_elite_ranger_9_name", "Key": "monster_elite_ranger_9_name"}, "6342": {"Id": 6342, "StringId": "monster_elite_ranger_10_name", "Key": "monster_elite_ranger_10_name"}, "6343": {"Id": 6343, "StringId": "monster_elite_tank_1_name", "Key": "monster_elite_tank_1_name"}, "6344": {"Id": 6344, "StringId": "monster_elite_tank_2_name", "Key": "monster_elite_tank_2_name"}, "6345": {"Id": 6345, "StringId": "monster_elite_tank_3_name", "Key": "monster_elite_tank_3_name"}, "6346": {"Id": 6346, "StringId": "monster_elite_tank_4_name", "Key": "monster_elite_tank_4_name"}, "6347": {"Id": 6347, "StringId": "monster_elite_tank_5_name", "Key": "monster_elite_tank_5_name"}, "6348": {"Id": 6348, "StringId": "monster_elite_tank_6_name", "Key": "monster_elite_tank_6_name"}, "6349": {"Id": 6349, "StringId": "monster_elite_tank_7_name", "Key": "monster_elite_tank_7_name"}, "6350": {"Id": 6350, "StringId": "monster_elite_tank_8_name", "Key": "monster_elite_tank_8_name"}, "6351": {"Id": 6351, "StringId": "monster_elite_tank_9_name", "Key": "monster_elite_tank_9_name"}, "6352": {"Id": 6352, "StringId": "monster_elite_tank_10_name", "Key": "monster_elite_tank_10_name"}, "6353": {"Id": 6353, "StringId": "monster_elite_tank_11_name", "Key": "monster_elite_tank_11_name"}, "6354": {"Id": 6354, "StringId": "monster_elite_tank_12_name", "Key": "monster_elite_tank_12_name"}, "6355": {"Id": 6355, "StringId": "monster_elite_tank_13_name", "Key": "monster_elite_tank_13_name"}, "6356": {"Id": 6356, "StringId": "monster_elite_air_melee_1_name", "Key": "monster_elite_air_melee_1_name"}, "6357": {"Id": 6357, "StringId": "monster_elite_air_melee_2_name", "Key": "monster_elite_air_melee_2_name"}, "6358": {"Id": 6358, "StringId": "monster_elite_air_melee_3_name", "Key": "monster_elite_air_melee_3_name"}, "6359": {"Id": 6359, "StringId": "monster_elite_air_melee_4_name", "Key": "monster_elite_air_melee_4_name"}, "6360": {"Id": 6360, "StringId": "monster_elite_air_ranger_1_name", "Key": "monster_elite_air_ranger_1_name"}, "6361": {"Id": 6361, "StringId": "monster_elite_air_ranger_2_name", "Key": "monster_elite_air_ranger_2_name"}, "6362": {"Id": 6362, "StringId": "monster_elite_assassin_1_name", "Key": "monster_elite_assassin_1_name"}, "6363": {"Id": 6363, "StringId": "monster_elite_suicide_1_name", "Key": "monster_elite_suicide_1_name"}, "6364": {"Id": 6364, "StringId": "monster_boss_melee_1_name", "Key": "monster_boss_melee_1_name"}, "6365": {"Id": 6365, "StringId": "monster_boss_melee_2_name", "Key": "monster_boss_melee_2_name"}, "6366": {"Id": 6366, "StringId": "monster_boss_ranger_1_name", "Key": "monster_boss_ranger_1_name"}, "6367": {"Id": 6367, "StringId": "monster_boss_ranger_2_name", "Key": "monster_boss_ranger_2_name"}, "6368": {"Id": 6368, "StringId": "monster_boss_ranger_3_name", "Key": "monster_boss_ranger_3_name"}, "6369": {"Id": 6369, "StringId": "monster_boss_ranger_4_name", "Key": "monster_boss_ranger_4_name"}, "6370": {"Id": 6370, "StringId": "monster_boss_tank_1_name", "Key": "monster_boss_tank_1_name"}, "6371": {"Id": 6371, "StringId": "monster_boss_tank_2_name", "Key": "monster_boss_tank_2_name"}, "6372": {"Id": 6372, "StringId": "monster_boss_tank_3_name", "Key": "monster_boss_tank_3_name"}, "6373": {"Id": 6373, "StringId": "monster_boss_long_ranger_1_name", "Key": "monster_boss_long_ranger_1_name"}, "6374": {"Id": 6374, "StringId": "monster_boss_long_ranger_2_name", "Key": "monster_boss_long_ranger_2_name"}, "6375": {"Id": 6375, "StringId": "monster_boss_long_ranger_3_name", "Key": "monster_boss_long_ranger_3_name"}, "6376": {"Id": 6376, "StringId": "buff_fire_up_desc", "Key": "buff_fire_up_desc"}, "6377": {"Id": 6377, "StringId": "buff_fire_down_desc", "Key": "buff_fire_down_desc"}, "6378": {"Id": 6378, "StringId": "buff_fire_immune_desc", "Key": "buff_fire_immune_desc"}, "6379": {"Id": 6379, "StringId": "buff_ice_up_desc", "Key": "buff_ice_up_desc"}, "6380": {"Id": 6380, "StringId": "buff_ice_down_desc", "Key": "buff_ice_down_desc"}, "6381": {"Id": 6381, "StringId": "buff_ice_immune_desc", "Key": "buff_ice_immune_desc"}, "6382": {"Id": 6382, "StringId": "buff_wind_up_desc", "Key": "buff_wind_up_desc"}, "6383": {"Id": 6383, "StringId": "buff_wind_down_desc", "Key": "buff_wind_down_desc"}, "6384": {"Id": 6384, "StringId": "buff_wind_immune_desc", "Key": "buff_wind_immune_desc"}, "6385": {"Id": 6385, "StringId": "buff_light_up_desc", "Key": "buff_light_up_desc"}, "6386": {"Id": 6386, "StringId": "buff_light_down_desc", "Key": "buff_light_down_desc"}, "6387": {"Id": 6387, "StringId": "buff_light_immune_desc", "Key": "buff_light_immune_desc"}, "6388": {"Id": 6388, "StringId": "buff_electrical_up_desc", "Key": "buff_electrical_up_desc"}, "6389": {"Id": 6389, "StringId": "buff_electrical_down_desc", "Key": "buff_electrical_down_desc"}, "6390": {"Id": 6390, "StringId": "buff_electrical_immune_desc", "Key": "buff_electrical_immune_desc"}, "6391": {"Id": 6391, "StringId": "buff_physical_up_desc", "Key": "buff_physical_up_desc"}, "6392": {"Id": 6392, "StringId": "buff_physical_down_desc", "Key": "buff_physical_down_desc"}, "6393": {"Id": 6393, "StringId": "buff_physical_immune_desc", "Key": "buff_physical_immune_desc"}, "6394": {"Id": 6394, "StringId": "buff_fly_desc", "Key": "buff_fly_desc"}, "6395": {"Id": 6395, "StringId": "buff_longrange_desc", "Key": "buff_longrange_desc"}, "6396": {"Id": 6396, "StringId": "buff_condition_immune_desc", "Key": "buff_condition_immune_desc"}, "6397": {"Id": 6397, "StringId": "buff_scurry_desc", "Key": "buff_scurry_desc"}, "6398": {"Id": 6398, "StringId": "buff_fire_up_dungeon_desc", "Key": "buff_fire_up_dungeon_desc"}, "6399": {"Id": 6399, "StringId": "buff_fire_down_dungeon_desc", "Key": "buff_fire_down_dungeon_desc"}, "6400": {"Id": 6400, "StringId": "buff_fire_immune_dungeon_desc", "Key": "buff_fire_immune_dungeon_desc"}, "6401": {"Id": 6401, "StringId": "buff_ice_up_dungeon_desc", "Key": "buff_ice_up_dungeon_desc"}, "6402": {"Id": 6402, "StringId": "buff_ice_down_dungeon_desc", "Key": "buff_ice_down_dungeon_desc"}, "6403": {"Id": 6403, "StringId": "buff_ice_immune_dungeon_desc", "Key": "buff_ice_immune_dungeon_desc"}, "6404": {"Id": 6404, "StringId": "buff_wind_up_dungeon_desc", "Key": "buff_wind_up_dungeon_desc"}, "6405": {"Id": 6405, "StringId": "buff_wind_down_dungeon_desc", "Key": "buff_wind_down_dungeon_desc"}, "6406": {"Id": 6406, "StringId": "buff_wind_immune_dungeon_desc", "Key": "buff_wind_immune_dungeon_desc"}, "6407": {"Id": 6407, "StringId": "buff_light_up_dungeon_desc", "Key": "buff_light_up_dungeon_desc"}, "6408": {"Id": 6408, "StringId": "buff_light_down_dungeon_desc", "Key": "buff_light_down_dungeon_desc"}, "6409": {"Id": 6409, "StringId": "buff_light_immune_dungeon_desc", "Key": "buff_light_immune_dungeon_desc"}, "6410": {"Id": 6410, "StringId": "buff_electrical_up_dungeon_desc", "Key": "buff_electrical_up_dungeon_desc"}, "6411": {"Id": 6411, "StringId": "buff_electrical_down_dungeon_desc", "Key": "buff_electrical_down_dungeon_desc"}, "6412": {"Id": 6412, "StringId": "buff_electrical_immune_dungeon_desc", "Key": "buff_electrical_immune_dungeon_desc"}, "6413": {"Id": 6413, "StringId": "buff_physical_up_dungeon_desc", "Key": "buff_physical_up_dungeon_desc"}, "6414": {"Id": 6414, "StringId": "buff_physical_down_dungeon_desc", "Key": "buff_physical_down_dungeon_desc"}, "6415": {"Id": 6415, "StringId": "buff_physical_immune_dungeon_desc", "Key": "buff_physical_immune_dungeon_desc"}, "6416": {"Id": 6416, "StringId": "buff_fly_dungeon_desc", "Key": "buff_fly_dungeon_desc"}, "6417": {"Id": 6417, "StringId": "buff_longrange_dungeon_desc", "Key": "buff_longrange_dungeon_desc"}, "6418": {"Id": 6418, "StringId": "buff_condition_immune_dungeon_desc", "Key": "buff_condition_immune_dungeon_desc"}, "6419": {"Id": 6419, "StringId": "buff_scurry_dungeon_desc", "Key": "buff_scurry_dungeon_desc"}, "6420": {"Id": 6420, "StringId": "monster_feature_1", "Key": "monster_feature_1"}, "6421": {"Id": 6421, "StringId": "monster_feature_2", "Key": "monster_feature_2"}, "6422": {"Id": 6422, "StringId": "monster_feature_3", "Key": "monster_feature_3"}, "6423": {"Id": 6423, "StringId": "monster_feature_4", "Key": "monster_feature_4"}, "6424": {"Id": 6424, "StringId": "monster_feature_5", "Key": "monster_feature_5"}, "6425": {"Id": 6425, "StringId": "monster_feature_6", "Key": "monster_feature_6"}, "6426": {"Id": 6426, "StringId": "monster_feature_7", "Key": "monster_feature_7"}, "6427": {"Id": 6427, "StringId": "monster_feature_8", "Key": "monster_feature_8"}, "6428": {"Id": 6428, "StringId": "monster_feature_9", "Key": "monster_feature_9"}, "6429": {"Id": 6429, "StringId": "monster_feature_10", "Key": "monster_feature_10"}, "6430": {"Id": 6430, "StringId": "monster_feature_11", "Key": "monster_feature_11"}, "6431": {"Id": 6431, "StringId": "monster_feature_12", "Key": "monster_feature_12"}, "6432": {"Id": 6432, "StringId": "monster_feature_13", "Key": "monster_feature_13"}, "6433": {"Id": 6433, "StringId": "monster_feature_14", "Key": "monster_feature_14"}, "6434": {"Id": 6434, "StringId": "monster_feature_15", "Key": "monster_feature_15"}, "6435": {"Id": 6435, "StringId": "monster_feature_16", "Key": "monster_feature_16"}, "6436": {"Id": 6436, "StringId": "monster_feature_17", "Key": "monster_feature_17"}, "7001": {"Id": 7001, "StringId": "hero_lottery_default_group_1_title", "Key": "hero_lottery_default_group_1_title"}, "7002": {"Id": 7002, "StringId": "hero_lottery_default_group_1_desc", "Key": "hero_lottery_default_group_1_desc"}, "7003": {"Id": 7003, "StringId": "hero_lottery_default_group_1_tips", "Key": "hero_lottery_default_group_1_tips"}, "7004": {"Id": 7004, "StringId": "hero_lottery_default_must_desc", "Key": "hero_lottery_default_must_desc"}, "7005": {"Id": 7005, "StringId": "hero_lottery_default_group_1_chance_desc", "Key": "hero_lottery_default_group_1_chance_desc"}, "7006": {"Id": 7006, "StringId": "battle_name", "Key": "battle_name"}, "7007": {"Id": 7007, "StringId": "currency_diamond_name", "Key": "currency_diamond_name"}, "7008": {"Id": 7008, "StringId": "currency_sunshine_name", "Key": "currency_sunshine_name"}, "7009": {"Id": 7009, "StringId": "main_page_avatar_name", "Key": "main_page_avatar_name"}, "7010": {"Id": 7010, "StringId": "main_page_power_name", "Key": "main_page_power_name"}, "7011": {"Id": 7011, "StringId": "hero_in_name", "Key": "hero_in_name"}, "7012": {"Id": 7012, "StringId": "hero_level_up_name", "Key": "hero_level_up_name"}, "7013": {"Id": 7013, "StringId": "hero_skill_name", "Key": "hero_skill_name"}, "7014": {"Id": 7014, "StringId": "hero_star_up_name", "Key": "hero_star_up_name"}, "7015": {"Id": 7015, "StringId": "hero_config_name", "Key": "hero_config_name"}, "7016": {"Id": 7016, "StringId": "idle_reward_name", "Key": "idle_reward_name"}, "7017": {"Id": 7017, "StringId": "hero_lottery_name", "Key": "hero_lottery_name"}, "7018": {"Id": 7018, "StringId": "ui_rename_name", "Key": "ui_rename_name"}, "7019": {"Id": 7019, "StringId": "ui_change_avatar_name", "Key": "ui_change_avatar_name"}, "7020": {"Id": 7020, "StringId": "ui_server_id_title_desc", "Key": "ui_server_id_title_desc"}, "7021": {"Id": 7021, "StringId": "ui_total_power_desc", "Key": "ui_total_power_desc"}, "7022": {"Id": 7022, "StringId": "ui_max_level_desc", "Key": "ui_max_level_desc"}, "7023": {"Id": 7023, "StringId": "ui_rename_tips_1_desc", "Key": "ui_rename_tips_1_desc"}, "7024": {"Id": 7024, "StringId": "ui_rename_tips_2_desc", "Key": "ui_rename_tips_2_desc"}, "7025": {"Id": 7025, "StringId": "ui_rename_tips_3_desc", "Key": "ui_rename_tips_3_desc"}, "7026": {"Id": 7026, "StringId": "ui_rename_tips_4_desc", "Key": "ui_rename_tips_4_desc"}, "7027": {"Id": 7027, "StringId": "ui_use_desc", "Key": "ui_use_desc"}, "7028": {"Id": 7028, "StringId": "ui_item_resource_desc", "Key": "ui_item_resource_desc"}, "7029": {"Id": 7029, "StringId": "set_name", "Key": "set_name"}, "7030": {"Id": 7030, "StringId": "game_set_name", "Key": "game_set_name"}, "7031": {"Id": 7031, "StringId": "language_set_name", "Key": "language_set_name"}, "7032": {"Id": 7032, "StringId": "go_to_next_chapter", "Key": "go_to_next_chapter"}, "7033": {"Id": 7033, "StringId": "heroLevelMax", "Key": "heroLevelMax"}, "7034": {"Id": 7034, "StringId": "heroStarMax", "Key": "heroStarMax"}, "7035": {"Id": 7035, "StringId": "heroSkillMax", "Key": "heroSkillMax"}, "7036": {"Id": 7036, "StringId": "exchange", "Key": "exchange"}, "7037": {"Id": 7037, "StringId": "success", "Key": "success"}, "7038": {"Id": 7038, "StringId": "failure", "Key": "failure"}, "7039": {"Id": 7039, "StringId": "challengeBoss", "Key": "challengeBoss"}, "7040": {"Id": 7040, "StringId": "exchangeUpClass", "Key": "exchangeUpClass"}, "7041": {"Id": 7041, "StringId": "exchangeUpStar", "Key": "exchangeUpStar"}, "7042": {"Id": 7042, "StringId": "hitSkill", "Key": "hitSkill"}, "7043": {"Id": 7043, "StringId": "negativeSkill", "Key": "negativeSkill"}, "7044": {"Id": 7044, "StringId": "giftSkill", "Key": "giftSkill"}, "7045": {"Id": 7045, "StringId": "locked", "Key": "locked"}, "7046": {"Id": 7046, "StringId": "needUpGradeTo", "Key": "needUpGradeTo"}, "7047": {"Id": 7047, "StringId": "unlockTip", "Key": "unlockTip"}, "7048": {"Id": 7048, "StringId": "upStarTip", "Key": "upStarTip"}, "7049": {"Id": 7049, "StringId": "heroDetailSkillLevel", "Key": "heroDetailSkillLevel"}, "7050": {"Id": 7050, "StringId": "heroDetailSkillCD", "Key": "heroDetailSkillCD"}, "7051": {"Id": 7051, "StringId": "level", "Key": "level"}, "7052": {"Id": 7052, "StringId": "level_up_item_insufficient", "Key": "level_up_item_insufficient"}, "7053": {"Id": 7053, "StringId": "star_up_item_insufficient", "Key": "star_up_item_insufficient"}, "7054": {"Id": 7054, "StringId": "skill_level_up_item_insufficient", "Key": "skill_level_up_item_insufficient"}, "7055": {"Id": 7055, "StringId": "star_addition_name", "Key": "star_addition_name"}, "7056": {"Id": 7056, "StringId": "skill_addition_name", "Key": "skill_addition_name"}, "7057": {"Id": 7057, "StringId": "level_addition_name", "Key": "level_addition_name"}, "7058": {"Id": 7058, "StringId": "no_hero_config_toast", "Key": "no_hero_config_toast"}, "7059": {"Id": 7059, "StringId": "on_btn_exit_title", "Key": "on_btn_exit_title"}, "7060": {"Id": 7060, "StringId": "on_btn_exit_content", "Key": "on_btn_exit_content"}, "7061": {"Id": 7061, "StringId": "on_btn_exit_yestext", "Key": "on_btn_exit_yesText"}, "7062": {"Id": 7062, "StringId": "on_btn_exit_noText", "Key": "on_btn_exit_noText"}, "7063": {"Id": 7063, "StringId": "recruitment", "Key": "recruitment"}, "7064": {"Id": 7064, "StringId": "detail", "Key": "detail"}, "7065": {"Id": 7065, "StringId": "hero_cruit_tip", "Key": "hero_cruit_tip"}, "7066": {"Id": 7066, "StringId": "recruit_times", "Key": "recruit_times"}, "7067": {"Id": 7067, "StringId": "free", "Key": "free"}, "7068": {"Id": 7068, "StringId": "next_free_time", "Key": "next_free_time"}, "7069": {"Id": 7069, "StringId": "skip_animation", "Key": "skip_animation"}, "7070": {"Id": 7070, "StringId": "npc_dave_name", "Key": "npc_dave_name"}, "7071": {"Id": 7071, "StringId": "npc_dialouge_1_content", "Key": "npc_dialouge_1_content"}, "7072": {"Id": 7072, "StringId": "npc_dialouge_2_content", "Key": "npc_dialouge_2_content"}, "7073": {"Id": 7073, "StringId": "npc_dialouge_3_content", "Key": "npc_dialouge_3_content"}, "7074": {"Id": 7074, "StringId": "npc_dialouge_4_content", "Key": "npc_dialouge_4_content"}, "7075": {"Id": 7075, "StringId": "npc_dialouge_5_content", "Key": "npc_dialouge_5_content"}, "7076": {"Id": 7076, "StringId": "item_legendary_name", "Key": "item_legendary_name"}, "7077": {"Id": 7077, "StringId": "item_epic_name", "Key": "item_epic_name"}, "7078": {"Id": 7078, "StringId": "item_rare_name", "Key": "item_rare_name"}, "7079": {"Id": 7079, "StringId": "item_common_name", "Key": "item_common_name"}, "7080": {"Id": 7080, "StringId": "reset_chapter_des", "Key": "reset_chapter_des"}, "7081": {"Id": 7081, "StringId": "reset_chapter_name", "Key": "reset_chapter_name"}, "7082": {"Id": 7082, "StringId": "hero_summon_diamond_not_enough", "Key": "hero_summon_diamond_not_enough"}, "7083": {"Id": 7083, "StringId": "hero_recruit_tip", "Key": "hero_recruit_tip"}, "7084": {"Id": 7084, "StringId": "energy_deficit_desc", "Key": "energy_deficit_desc"}, "7085": {"Id": 7085, "StringId": "npc_sunflower_name", "Key": "npc_sunflower_name"}, "7086": {"Id": 7086, "StringId": "level_max_reached", "Key": "level_max_reached"}, "7087": {"Id": 7087, "StringId": "item_owned_desc", "Key": "item_owned_desc"}, "7088": {"Id": 7088, "StringId": "parkour_slider_tips", "Key": "parkour_slider_tips"}, "7089": {"Id": 7089, "StringId": "function_idle_reward_name", "Key": "function_idle_reward_name"}, "7090": {"Id": 7090, "StringId": "function_rank_name", "Key": "function_rank_name"}, "7091": {"Id": 7091, "StringId": "function_energy_factory_name", "Key": "function_energy_factory_name"}, "7092": {"Id": 7092, "StringId": "function_profile_name", "Key": "function_profile_name"}, "7093": {"Id": 7093, "StringId": "function_hero_in_name", "Key": "function_hero_in_name"}, "7094": {"Id": 7094, "StringId": "function_monster_manual_name", "Key": "function_monster_manual_name"}, "7095": {"Id": 7095, "StringId": "function_set_name", "Key": "function_set_name"}, "7096": {"Id": 7096, "StringId": "acceleration_no_tips", "Key": "acceleration_no_tips"}, "7097": {"Id": 7097, "StringId": "ui_level_maps_desc", "Key": "ui_level_maps_desc"}, "7098": {"Id": 7098, "StringId": "ui_level_remaining_hp_desc", "Key": "ui_level_remaining_hp_desc"}, "7099": {"Id": 7099, "StringId": "ui_level_best_record_desc", "Key": "ui_level_best_record_desc"}, "7100": {"Id": 7100, "StringId": "ui_reward_preview_title_desc", "Key": "ui_reward_preview_title_desc"}, "7101": {"Id": 7101, "StringId": "ui_loading_desc", "Key": "ui_loading_desc"}, "7102": {"Id": 7102, "StringId": "ui_tap_close_desc", "Key": "ui_tap_close_desc"}, "7103": {"Id": 7103, "StringId": "ui_boss_comming_desc", "Key": "ui_boss_comming_desc"}, "7104": {"Id": 7104, "StringId": "ui_sweep_left_or_right_desc", "Key": "ui_sweep_left_or_right_desc"}, "7105": {"Id": 7105, "StringId": "ui_zombie_eat_your_desc", "Key": "ui_zombie_eat_your_desc"}, "7106": {"Id": 7106, "StringId": "ui_brain_desc", "Key": "ui_brain_desc"}, "7107": {"Id": 7107, "StringId": "ui_get_rewards_desc", "Key": "ui_get_rewards_desc"}, "7108": {"Id": 7108, "StringId": "ui_dmg_desc", "Key": "ui_dmg_desc"}, "7109": {"Id": 7109, "StringId": "ui_get_stronger_desc", "Key": "ui_get_stronger_desc"}, "7110": {"Id": 7110, "StringId": "ui_enhance_plants_desc", "Key": "ui_enhance_plants_desc"}, "7111": {"Id": 7111, "StringId": "ui_recruit_more_plants_desc", "Key": "ui_recruit_more_plants_desc"}, "7112": {"Id": 7112, "StringId": "ui_chance_lineup_desc", "Key": "ui_chance_lineup_desc"}, "7113": {"Id": 7113, "StringId": "ui_level_common_title_desc", "Key": "ui_level_common_title_desc"}, "7114": {"Id": 7114, "StringId": "ui_level_elite_title_desc", "Key": "ui_level_elite_title_desc"}, "7115": {"Id": 7115, "StringId": "ui_level_pass_through", "Key": "ui_level_pass_through"}, "7116": {"Id": 7116, "StringId": "ui_level_pass_remaining_50%_hp_desc", "Key": "ui_level_remaining_50%_hp_desc"}, "7117": {"Id": 7117, "StringId": "ui_level_pass_perfect_desc", "Key": "ui_level_pass_perfect_desc"}, "7118": {"Id": 7118, "StringId": "ui_level_parkour_start_desc", "Key": "ui_level_parkour_start_desc"}, "7119": {"Id": 7119, "StringId": "ui_level_parkour_completed_desc", "Key": "ui_level_parkour_completed_desc"}, "7120": {"Id": 7120, "StringId": "ui_challenge_desc", "Key": "ui_challenge_desc"}, "7121": {"Id": 7121, "StringId": "ui_tips_current_level_pass_perfect_desc", "Key": "ui_tips_current_level_pass_perfect_desc"}, "7122": {"Id": 7122, "StringId": "ui_tips_not_prompt_again_desc", "Key": "ui_tips_not_prompt_again_desc"}, "7123": {"Id": 7123, "StringId": "ui_go_desc", "Key": "ui_go_desc"}, "7124": {"Id": 7124, "StringId": "ui_keep_challenge_desc", "Key": "ui_keep_challenge_desc"}, "7125": {"Id": 7125, "StringId": "ui_skill_choice_desc", "Key": "ui_skill_choice_desc"}, "7126": {"Id": 7126, "StringId": "ui_refresh_desc", "Key": "ui_refresh_desc"}, "7127": {"Id": 7127, "StringId": "ui_assistant_desc", "Key": "ui_assistant_desc"}, "7128": {"Id": 7128, "StringId": "ui_intell_desc", "Key": "ui_intell_desc"}, "7129": {"Id": 7129, "StringId": "ui_lucky_wheel_desc", "Key": "ui_lucky_wheel_desc"}, "7130": {"Id": 7130, "StringId": "ui_remaining_attempt_cnt_tips_desc", "Key": "ui_remaining_attempt_cnt_tips_desc"}, "7131": {"Id": 7131, "StringId": "ui_elite_drop_desc", "Key": "ui_elite_drop_desc"}, "7132": {"Id": 7132, "StringId": "ui_level_preview_desc", "Key": "ui_level_preview_desc"}, "7133": {"Id": 7133, "StringId": "ui_monsters_in_this_level_desc", "Key": "ui_monsters_in_this_level_desc"}, "7134": {"Id": 7134, "StringId": "ui_drops_in_this_level_desc", "Key": "ui_drops_in_this_level_desc"}, "7135": {"Id": 7135, "StringId": "ui_monsters_weakness_desc", "Key": "ui_monsters_weakness_desc"}, "7136": {"Id": 7136, "StringId": "ui_monsters_advantage_desc", "Key": "ui_monsters_advantage_desc"}, "7137": {"Id": 7137, "StringId": "ui_monsters_trait_desc", "Key": "ui_monsters_trait_desc"}, "7138": {"Id": 7138, "StringId": "ui_monsters_desc", "Key": "ui_monsters_desc"}, "7139": {"Id": 7139, "StringId": "ui_skills_desc", "Key": "ui_skills_desc"}, "7140": {"Id": 7140, "StringId": "ui_next_not_perfect_level_desc", "Key": "ui_next_not_perfect_level_desc"}, "7141": {"Id": 7141, "StringId": "ui_btn_check_name", "Key": "ui_btn_check_name"}, "7142": {"Id": 7142, "StringId": "ui_btn_choice_name", "Key": "ui_btn_choice_name"}, "7143": {"Id": 7143, "StringId": "ui_hero_bond_title_name", "Key": "ui_hero_bond_title_name"}, "7144": {"Id": 7144, "StringId": "ui_quick_deploy_desc", "Key": "ui_quick_deploy_desc"}, "7145": {"Id": 7145, "StringId": "ui_squad_setting_desc", "Key": "ui_squad_setting_desc"}, "7146": {"Id": 7146, "StringId": "ui_fight_desc", "Key": "ui_fight_desc"}, "7147": {"Id": 7147, "StringId": "ui_all_desc", "Key": "ui_all_desc"}, "7148": {"Id": 7148, "StringId": "ui_current_squad_desc", "Key": "ui_current_squad_desc"}, "7149": {"Id": 7149, "StringId": "ui_skill_description_desc", "Key": "ui_skill_description_desc"}, "7150": {"Id": 7150, "StringId": "ui_hero_gene_tap_desc", "Key": "ui_hero_gene_tap_desc"}, "7151": {"Id": 7151, "StringId": "ui_level_up_desc", "Key": "ui_level_up_desc"}, "7152": {"Id": 7152, "StringId": "ui_star_up_desc", "Key": "ui_star_up_desc"}, "7153": {"Id": 7153, "StringId": "ui_hero_gene_secrets_desc", "Key": "ui_hero_gene_secrets_desc"}, "7154": {"Id": 7154, "StringId": "ui_tap_tab_to_check_desc", "Key": "ui_tap_tab_to_check_desc"}, "7155": {"Id": 7155, "StringId": "ui_preposition_desc", "Key": "ui_preposition_desc"}, "7156": {"Id": 7156, "StringId": "ui_mutually_desc", "Key": "ui_mutually_desc"}, "7157": {"Id": 7157, "StringId": "ui_insufficient_item_desc", "Key": "ui_insufficient_item_desc"}, "7158": {"Id": 7158, "StringId": "ui_get_more_diamond_desc", "Key": "ui_get_more_item_desc"}, "7159": {"Id": 7159, "StringId": "ui_get_more_summon_card_desc", "Key": "ui_get_more_item_use_diamond_desc"}, "7160": {"Id": 7160, "StringId": "ui_a_big_wave_zombies_desc", "Key": "ui_a_big_wave_zombies_desc"}, "7161": {"Id": 7161, "StringId": "ui_the_final_wave_zombies_desc", "Key": "ui_the_final_wave_zombies_desc"}, "7162": {"Id": 7162, "StringId": "ui_level_rank_desc", "Key": "ui_level_rank_desc"}, "7163": {"Id": 7163, "StringId": "ui_level_rank_rank_desc", "Key": "ui_level_rank_rank_desc"}, "7164": {"Id": 7164, "StringId": "ui_level_rank_lord_desc", "Key": "ui_level_rank_lord_desc"}, "7165": {"Id": 7165, "StringId": "ui_level_rank_level_desc", "Key": "ui_level_rank_level_desc"}, "7166": {"Id": 7166, "StringId": "ui_free_desc", "Key": "ui_free_desc"}, "7167": {"Id": 7167, "StringId": "ui_level_progress_reward_desc", "Key": "ui_level_progress_reward_desc"}, "7168": {"Id": 7168, "StringId": "ui_anyone_pass_level_desc", "Key": "ui_anyone_pass_level_desc"}, "7169": {"Id": 7169, "StringId": "ui_no_one_pass_level_desc", "Key": "ui_no_one_pass_level_desc"}, "7170": {"Id": 7170, "StringId": "ui_fast_5_lord_desc", "Key": "ui_fast_5_lord_desc"}, "7171": {"Id": 7171, "StringId": "ui_confirm_desc", "Key": "ui_confirm_desc"}, "7172": {"Id": 7172, "StringId": "ui_cancel_desc", "Key": "ui_cancel_desc"}, "7173": {"Id": 7173, "StringId": "ui_you_not_obtain_the_hero_desc", "Key": "ui_you_not_obtain_the_hero_desc"}, "7174": {"Id": 7174, "StringId": "ui_hero_gene_level_unlock_desc", "Key": "ui_hero_gene_level_unlock_desc"}, "7175": {"Id": 7175, "StringId": "ui_PhotovoltaicTable_power_collected_desc", "Key": "ui_PhotovoltaicTable_power_collected_desc"}, "7176": {"Id": 7176, "StringId": "ui_PhotovoltaicTable_power_expires_desc", "Key": "ui_PhotovoltaicTable_power_expires_desc"}, "7177": {"Id": 7177, "StringId": "ui_PhotovoltaicTable_power_limit_desc", "Key": "ui_PhotovoltaicTable_power_limit_desc"}, "7178": {"Id": 7178, "StringId": "ui_claim_desc", "Key": "ui_claim_desc"}, "7179": {"Id": 7179, "StringId": "ui_set_music_name", "Key": "ui_set_music_name"}, "7180": {"Id": 7180, "StringId": "ui_set_sfx_name", "Key": "ui_set_sfx_name"}, "7181": {"Id": 7181, "StringId": "ui_set_language_name", "Key": "ui_set_language_name"}, "7182": {"Id": 7182, "StringId": "ui_set_shake_name", "Key": "ui_set_shake_name"}, "7183": {"Id": 7183, "StringId": "ui_set_shake_desc", "Key": "ui_set_shake_desc"}, "7184": {"Id": 7184, "StringId": "ui_set_image_quality_name", "Key": "ui_set_image_quality_name"}, "7185": {"Id": 7185, "StringId": "ui_set_image_quality_normal_name", "Key": "ui_set_image_quality_normal_name"}, "7186": {"Id": 7186, "StringId": "ui_set_image_quality_medium_name", "Key": "ui_set_image_quality_medium_name"}, "7187": {"Id": 7187, "StringId": "ui_set_image_quality_high_name", "Key": "ui_set_image_quality_high_name"}, "7188": {"Id": 7188, "StringId": "ui_set_image_quality_desc", "Key": "ui_set_image_quality_desc"}, "7189": {"Id": 7189, "StringId": "ui_new_function_unlocked_desc", "Key": "ui_new_function_unlocked_desc"}, "7190": {"Id": 7190, "StringId": "ui_idle_reward_tips_desc", "Key": "ui_idle_reward_tips_desc"}, "7191": {"Id": 7191, "StringId": "ui_idle_reward_blitz_tips_desc", "Key": "ui_idle_reward_blitz_tips_desc"}, "7192": {"Id": 7192, "StringId": "ui_blitz_btn_name", "Key": "ui_blitz_btn_name"}, "7193": {"Id": 7193, "StringId": "ui_monster_manual_unlock_desc", "Key": "ui_monster_manual_unlock_desc"}, "7194": {"Id": 7194, "StringId": "ui_hero_recommed_desc", "Key": "ui_hero_recommed_desc"}, "7195": {"Id": 7195, "StringId": "ui_monster_manual_reward_desc", "Key": "ui_monster_manual_reward_desc"}, "7196": {"Id": 7196, "StringId": "ui_contains_one_from_below_desc", "Key": "ui_contains_one_from_below_desc"}, "7197": {"Id": 7197, "StringId": "ui_hero_desc", "Key": "ui_hero_desc"}, "7198": {"Id": 7198, "StringId": "ui_per_hour_desc", "Key": "ui_per_hour_desc"}, "7199": {"Id": 7199, "StringId": "ui_reward_max_desc", "Key": "ui_reward_max_desc"}, "7200": {"Id": 7200, "StringId": "ui_all_hero_atk_desc", "Key": "ui_all_hero_atk_desc"}, "7201": {"Id": 7201, "StringId": "ui_summon_plants_desc", "Key": "ui_summon_plants_desc"}, "7202": {"Id": 7202, "StringId": "ui_level_desc", "Key": "ui_level_desc"}, "7203": {"Id": 7203, "StringId": "ui_free_time_desc", "Key": "ui_free_time_desc"}, "7204": {"Id": 7204, "StringId": "ui_speed_up_desc", "Key": "ui_speed_up_desc"}, "7205": {"Id": 7205, "StringId": "ui_formation_cnt_desc", "Key": "ui_formation_cnt_desc"}, "7206": {"Id": 7206, "StringId": "ui_troops_detail_desc", "Key": "ui_troops_detail_desc"}, "7207": {"Id": 7207, "StringId": "ui_base_stats_desc", "Key": "ui_base_stats_desc"}, "7208": {"Id": 7208, "StringId": "ui_hp_desc", "Key": "ui_hp_desc"}, "7209": {"Id": 7209, "StringId": "ui_defened_desc", "Key": "ui_defened_desc"}, "7210": {"Id": 7210, "StringId": "ui_attack_desc", "Key": "ui_attack_desc"}, "7211": {"Id": 7211, "StringId": "ui_recruit_1_desc", "Key": "ui_recruit_1_desc"}, "7212": {"Id": 7212, "StringId": "ui_recruit_10_desc", "Key": "ui_recruit_10_desc"}, "7213": {"Id": 7213, "StringId": "ui_hero_upgrade_desc", "Key": "ui_hero_upgrade_desc"}, "7214": {"Id": 7214, "StringId": "ui_hero_ascend_desc", "Key": "ui_hero_ascend_desc"}, "7215": {"Id": 7215, "StringId": "ui_plants_list_desc", "Key": "ui_plants_list_desc"}, "7216": {"Id": 7216, "StringId": "ui_idle_desc", "Key": "ui_idle_desc"}, "7217": {"Id": 7217, "StringId": "ui_finish_now_desc", "Key": "ui_finish_now_desc"}, "7218": {"Id": 7218, "StringId": "ui_item_desc", "Key": "ui_item_desc"}, "7219": {"Id": 7219, "StringId": "ui_summon_hero_desc", "Key": "ui_summon_hero_desc"}, "7220": {"Id": 7220, "StringId": "ui_boss_1_name", "Key": "ui_boss_1_name"}, "7221": {"Id": 7221, "StringId": "ui_challenge_victory_desc", "Key": "ui_challenge_victory_desc"}, "7222": {"Id": 7222, "StringId": "ui_challenge_falure_desc", "Key": "ui_challenge_falure_desc"}, "7223": {"Id": 7223, "StringId": "ui_rewards_desc", "Key": "ui_rewards_desc"}, "7224": {"Id": 7224, "StringId": "ui_clearance_reward_desc", "Key": "ui_clearance_reward_desc"}, "7225": {"Id": 7225, "StringId": "ui_victory_desc", "Key": "ui_victory_desc"}, "7226": {"Id": 7226, "StringId": "ui_congratulations_you_got_desc", "Key": "ui_congratulations_you_got_desc"}, "7227": {"Id": 7227, "StringId": "ui_next_level_decs", "Key": "ui_next_level_decs"}, "7228": {"Id": 7228, "StringId": "ui_failure_desc", "Key": "ui_failure_desc"}, "7229": {"Id": 7229, "StringId": "ui_clear_level_desc", "Key": "ui_clear_level_desc"}, "7230": {"Id": 7230, "StringId": "ui_nothing_desc", "Key": "ui_nothing_desc"}, "7231": {"Id": 7231, "StringId": "ui_plants_details_desc", "Key": "ui_plants_details_desc"}, "7232": {"Id": 7232, "StringId": "ui_claim_reward_desc", "Key": "ui_claim_reward_desc"}, "7233": {"Id": 7233, "StringId": "ui_promoted_desc", "Key": "ui_promoted_desc"}, "7234": {"Id": 7234, "StringId": "ui_plants_recruitment_desc", "Key": "ui_plants_recruitment_desc"}, "7235": {"Id": 7235, "StringId": "ui_recruit_desc", "Key": "ui_recruit_desc"}, "7236": {"Id": 7236, "StringId": "ui_tap_to_continue_desc", "Key": "ui_tap_to_continue_desc"}, "7237": {"Id": 7237, "StringId": "ui_restart_desc", "Key": "ui_restart_desc"}, "7238": {"Id": 7238, "StringId": "ui_continue_desc", "Key": "ui_continue_desc"}, "7239": {"Id": 7239, "StringId": "ui_no_skill_yet_desc", "Key": "ui_no_skill_yet_desc"}, "7240": {"Id": 7240, "StringId": "ui_special_rec_desc", "Key": "ui_special_rec_desc"}, "7241": {"Id": 7241, "StringId": "ui_start_desc", "Key": "ui_start_desc"}, "7242": {"Id": 7242, "StringId": "ui_hero_power_desc", "Key": "ui_hero_power_desc"}, "7243": {"Id": 7243, "StringId": "ui_hero_level_power_desc", "Key": "ui_hero_level_power_desc"}, "7244": {"Id": 7244, "StringId": "ui_hero_star_power_desc", "Key": "ui_hero_star_power_desc"}, "7245": {"Id": 7245, "StringId": "ui_hero_skill_power_desc", "Key": "ui_hero_skill_power_desc"}, "7246": {"Id": 7246, "StringId": "ui_hero_gene_power_desc", "Key": "ui_hero_gene_power_desc"}, "7247": {"Id": 7247, "StringId": "ui_cnt_desc", "Key": "ui_cnt_desc"}, "7248": {"Id": 7248, "StringId": "ui_more_desc", "Key": "ui_more_desc"}, "7249": {"Id": 7249, "StringId": "ui_hero_deployed_cnt_desc", "Key": "ui_hero_deployed_cnt_desc"}, "7250": {"Id": 7250, "StringId": "ui_configuration_info_desc", "Key": "ui_configuration_info_desc"}, "7251": {"Id": 7251, "StringId": "ui_rename_desc", "Key": "ui_rename_desc"}, "7252": {"Id": 7252, "StringId": "ui_first_clearance_desc", "Key": "ui_first_clearance_desc"}, "7253": {"Id": 7253, "StringId": "ui_claim_all_desc", "Key": "ui_claim_all_desc"}, "7254": {"Id": 7254, "StringId": "ui_explore_desc", "Key": "ui_explore_desc"}, "7255": {"Id": 7255, "StringId": "ui_idle_rewards_desc", "Key": "ui_idle_rewards_desc"}, "7256": {"Id": 7256, "StringId": "ui_max_idle_income_8_hours_desc", "Key": "ui_max_idle_income_8_hours_desc"}, "7257": {"Id": 7257, "StringId": "ui_free_refresh_desc", "Key": "ui_free_refresh_desc"}, "7258": {"Id": 7258, "StringId": "ui_skill_preview_desc", "Key": "ui_skill_preview_desc"}, "7259": {"Id": 7259, "StringId": "ui_enter_level_desc", "Key": "ui_enter_level_desc"}, "7260": {"Id": 7260, "StringId": "ui_try_again_desc", "Key": "ui_try_again_desc"}, "7261": {"Id": 7261, "StringId": "ui_duel_desc", "Key": "ui_duel_desc"}, "7262": {"Id": 7262, "StringId": "ui_seed_desc", "Key": "ui_seed_desc"}, "7263": {"Id": 7263, "StringId": "ui_auto_star_up_desc", "Key": "ui_auto_star_up_desc"}, "7264": {"Id": 7264, "StringId": "ui_auto_reset_desc", "Key": "ui_auto_reset_desc"}, "7265": {"Id": 7265, "StringId": "ui_idle_time_desc", "Key": "ui_idle_time_desc"}, "7266": {"Id": 7266, "StringId": "ui_auto_use_desc", "Key": "ui_auto_use_desc"}, "7267": {"Id": 7267, "StringId": "ui_rate_info_desc", "Key": "ui_rate_info_desc"}, "7268": {"Id": 7268, "StringId": "ui_new_hero_desc", "Key": "ui_new_hero_desc"}, "7269": {"Id": 7269, "StringId": "ui_total_hero_atk_desc", "Key": "ui_total_hero_atk_desc"}, "7270": {"Id": 7270, "StringId": "ui_strength_method_desc", "Key": "ui_strength_method_desc"}, "7271": {"Id": 7271, "StringId": "ui_stop_desc", "Key": "ui_stop_desc"}, "7272": {"Id": 7272, "StringId": "ui_new_plants_desc", "Key": "ui_new_plants_desc"}, "7273": {"Id": 7273, "StringId": "ui_obtain_desc", "Key": "ui_obtain_desc"}, "7274": {"Id": 7274, "StringId": "ui_hero_not_obtain_desc", "Key": "ui_hero_not_obtain_desc"}, "7275": {"Id": 7275, "StringId": "ui_tap_to_start_desc", "Key": "ui_tap_to_start_desc"}, "7276": {"Id": 7276, "StringId": "ui_locked_desc", "Key": "ui_locked_desc"}, "7277": {"Id": 7277, "StringId": "ui_hero_promote_desc", "Key": "ui_hero_promote_desc"}, "7278": {"Id": 7278, "StringId": "ui_id_desc", "Key": "ui_id_desc"}, "7279": {"Id": 7279, "StringId": "ui_language_switch_desc", "Key": "ui_language_switch_desc"}, "7280": {"Id": 7280, "StringId": "ui_nickname_desc", "Key": "ui_nickname_desc"}, "7281": {"Id": 7281, "StringId": "ui_fast_1_lord_desc", "Key": "ui_fast_1_lord_desc"}, "7282": {"Id": 7282, "StringId": "ui_goto_star_up_desc", "Key": "ui_goto_star_up_desc"}, "7283": {"Id": 7283, "StringId": "ui_copy_done_desc", "Key": "ui_copy_done_desc"}, "7284": {"Id": 7284, "StringId": "ui_elite_level_unlocked_desc", "Key": "ui_elite_level_unlocked_desc"}, "7285": {"Id": 7285, "StringId": "ui_reward_can_get_desc", "Key": "ui_reward_can_get_desc"}, "7286": {"Id": 7286, "StringId": "ui_skill_level_limit_desc", "Key": "ui_skill_level_limit_desc"}, "7287": {"Id": 7287, "StringId": "ui_language_change_tips_desc", "Key": "ui_language_change_tips_desc"}, "7288": {"Id": 7288, "StringId": "ui_failure_settlement_desc", "Key": "ui_failure_settlement_desc"}, "7289": {"Id": 7289, "StringId": "ui_function_locked_desc", "Key": "ui_function_locked_desc"}, "7290": {"Id": 7290, "StringId": "ui_exchange_success_desc", "Key": "ui_exchange_success_desc"}, "7291": {"Id": 7291, "StringId": "ui_not_on_rank_desc", "Key": "ui_not_on_rank_desc"}, "7292": {"Id": 7292, "StringId": "ui_battle_accelerate_change_desc", "Key": "ui_battle_accelerate_change_desc"}, "7293": {"Id": 7293, "StringId": "ui_monsters_in_this_level_hard_desc", "Key": "ui_monsters_in_this_level_hard_desc"}, "7294": {"Id": 7294, "StringId": "ui_drops_in_this_level_hard_desc", "Key": "ui_drops_in_this_level_hard_desc"}, "7295": {"Id": 7295, "StringId": "ui_td_falure_desc", "Key": "ui_td_falure_desc"}, "7296": {"Id": 7296, "StringId": "gene_addition_name", "Key": "gene_addition_name"}, "7297": {"Id": 7297, "StringId": "ui_main_login_btn", "Key": "ui_main_login_btn"}, "7298": {"Id": 7298, "StringId": "ui_hero_summon_daily_limit_desc", "Key": "ui_hero_summon_daily_limit_desc"}, "7299": {"Id": 7299, "StringId": "ui_hero_summon_daily_limit_tips", "Key": "ui_hero_summon_daily_limit_tips"}, "7300": {"Id": 7300, "StringId": "function_guild_name", "Key": "function_guild_name"}, "7301": {"Id": 7301, "StringId": "function_preview_name", "Key": "function_preview_name"}, "7302": {"Id": 7302, "StringId": "function_lord_equip_name", "Key": "function_lord_equip_name"}, "7303": {"Id": 7303, "StringId": "function_sign_7_name", "Key": "function_sign_7_name"}, "7304": {"Id": 7304, "StringId": "function_tower_name", "Key": "function_tower_name"}, "7305": {"Id": 7305, "StringId": "function_arena_name", "Key": "function_arena_name"}, "7306": {"Id": 7306, "StringId": "function_coin_dungeon_image", "Key": "function_coin_dungeon_image"}, "7307": {"Id": 7307, "StringId": "function_gene_dungeon_image", "Key": "function_gene_dungeon_image"}, "7308": {"Id": 7308, "StringId": "function_lord_equip_dungeon_image", "Key": "function_lord_equip_dungeon_image"}, "7309": {"Id": 7309, "StringId": "npc_dialouge_6_content", "Key": "npc_dialouge_6_content"}, "7310": {"Id": 7310, "StringId": "npc_dialouge_7_content", "Key": "npc_dialouge_7_content"}, "7311": {"Id": 7311, "StringId": "npc_dialouge_8_content", "Key": "npc_dialouge_8_content"}, "7312": {"Id": 7312, "StringId": "npc_dialouge_9_content", "Key": "npc_dialouge_9_content"}, "7313": {"Id": 7313, "StringId": "npc_dialouge_10_content", "Key": "npc_dialouge_10_content"}, "7314": {"Id": 7314, "StringId": "ui_guild_boss_title", "Key": "ui_guild_boss_title"}, "7315": {"Id": 7315, "StringId": "ui_guild_member_title", "Key": "ui_guild_member_title"}, "7316": {"Id": 7316, "StringId": "ui_guild_notice_modify_tips", "Key": "ui_guild_notice_modify_tips"}, "7317": {"Id": 7317, "StringId": "ui_guild_power_title", "Key": "ui_guild_power_title"}, "7318": {"Id": 7318, "StringId": "ui_click_enter_tips", "Key": "ui_click_enter_tips"}, "7319": {"Id": 7319, "StringId": "ui_enter_character_tips", "Key": "ui_enter_character_tips"}, "7320": {"Id": 7320, "StringId": "ui_online_desc", "Key": "ui_online_desc"}, "7321": {"Id": 7321, "StringId": "ui_online_tag", "Key": "ui_online_tag"}, "7322": {"Id": 7322, "StringId": "ui_offline_min_desc", "Key": "ui_offline_min_desc"}, "7323": {"Id": 7323, "StringId": "ui_offline_hour_desc", "Key": "ui_offline_hour_desc"}, "7324": {"Id": 7324, "StringId": "ui_offline_day_desc", "Key": "ui_offline_day_desc"}, "7325": {"Id": 7325, "StringId": "ui_chat_btn_name", "Key": "ui_chat_btn_name"}, "7326": {"Id": 7326, "StringId": "ui_nigger_btn_name", "Key": "ui_nigger_btn_name"}, "7327": {"Id": 7327, "StringId": "ui_nigger_appliacation_success_tips", "Key": "ui_nigger_appliacation_success_tips"}, "7328": {"Id": 7328, "StringId": "ui_nigger_appliacation_exist_tips", "Key": "ui_nigger_appliacation_exist_tips"}, "7329": {"Id": 7329, "StringId": "ui_popup_transfer_boss_content", "Key": "ui_popup_transfer_boss_content"}, "7330": {"Id": 7330, "StringId": "ui_transfer_boss_tips", "Key": "ui_transfer_boss_tips"}, "7331": {"Id": 7331, "StringId": "ui_popup_remove_member_content", "Key": "ui_popup_remove_member_content"}, "7332": {"Id": 7332, "StringId": "ui_remove_member_tips", "Key": "ui_remove_member_tips"}, "7333": {"Id": 7333, "StringId": "ui_change_rank_tips", "Key": "ui_change_rank_tips"}, "7334": {"Id": 7334, "StringId": "ui_popup_guild_permission_title", "Key": "ui_popup_guild_permission_title"}, "7335": {"Id": 7335, "StringId": "ui_flag_title", "Key": "ui_flag_title"}, "7336": {"Id": 7336, "StringId": "ui_examine_title", "Key": "ui_examine_title"}, "7337": {"Id": 7337, "StringId": "ui_open_recruit_tips", "Key": "ui_open_recruit_tips"}, "7338": {"Id": 7338, "StringId": "ui_set_guild_application_condition_tips", "Key": "ui_set_guild_application_condition_tips"}, "7339": {"Id": 7339, "StringId": "ui_power_condition_tips", "Key": "ui_power_condition_tips"}, "7340": {"Id": 7340, "StringId": "ui_level_condition_tips", "Key": "ui_level_condition_tips"}, "7341": {"Id": 7341, "StringId": "ui_save_btn", "Key": "ui_save_btn"}, "7342": {"Id": 7342, "StringId": "ui_recruit_setting_changed_tips", "Key": "ui_recruit_setting_changed_tips"}, "7343": {"Id": 7343, "StringId": "ui_popup_diy_flag_title", "Key": "ui_popup_diy_flag_title"}, "7344": {"Id": 7344, "StringId": "ui_flag_base", "Key": "ui_flag_base"}, "7345": {"Id": 7345, "StringId": "ui_flag_emblem", "Key": "ui_flag_emblem"}, "7346": {"Id": 7346, "StringId": "ui_flag_change_tips", "Key": "ui_flag_change_tips"}, "7347": {"Id": 7347, "StringId": "ui_power_rank_desc", "Key": "ui_power_rank_desc"}, "7348": {"Id": 7348, "StringId": "ui_guild_application_member_cnt_tips", "Key": "ui_guild_application_member_cnt_tips"}, "7349": {"Id": 7349, "StringId": "ui_guild_application_placeholder_tips", "Key": "ui_guild_application_placeholder_tips"}, "7350": {"Id": 7350, "StringId": "ui_agree_btn", "Key": "ui_agree_btn"}, "7351": {"Id": 7351, "StringId": "ui_disagree_btn", "Key": "ui_disagree_btn"}, "7352": {"Id": 7352, "StringId": "ui_ta_join_other_guild_tips", "Key": "ui_ta_join_other_guild_tips"}, "7353": {"Id": 7353, "StringId": "ui_guild_member_full_tips", "Key": "ui_guild_member_full_tips"}, "7354": {"Id": 7354, "StringId": "ui_ta_join_guild_success_tips", "Key": "ui_ta_join_guild_success_tips"}, "7355": {"Id": 7355, "StringId": "ui_sell_out_tips", "Key": "ui_sell_out_tips"}, "7356": {"Id": 7356, "StringId": "ui_next_refresh_time_tips", "Key": "ui_next_refresh_time_tips"}, "7357": {"Id": 7357, "StringId": "ui_day_desc", "Key": "ui_day_desc"}, "7358": {"Id": 7358, "StringId": "ui_guild_level_unlock_tips", "Key": "ui_guild_level_unlock_tips"}, "7359": {"Id": 7359, "StringId": "ui_guild_build_tab", "Key": "ui_guild_build_tab"}, "7360": {"Id": 7360, "StringId": "ui_guild_add_tab", "Key": "ui_guild_add_tab"}, "7361": {"Id": 7361, "StringId": "ui_guild_application_tab", "Key": "ui_guild_application_tab"}, "7362": {"Id": 7362, "StringId": "ui_guild_name_title", "Key": "ui_guild_name_title"}, "7363": {"Id": 7363, "StringId": "ui_guild_add_direct_desc", "Key": "ui_guild_add_direct_desc"}, "7364": {"Id": 7364, "StringId": "ui_guild_add_need_app_desc", "Key": "ui_guild_add_need_app_desc"}, "7365": {"Id": 7365, "StringId": "ui_app_btn", "Key": "ui_app_btn"}, "7366": {"Id": 7366, "StringId": "ui_app_cancel_btn", "Key": "ui_app_cancel_btn"}, "7367": {"Id": 7367, "StringId": "ui_app_success_tips", "Key": "ui_app_success_tips"}, "7368": {"Id": 7368, "StringId": "ui_app_cancel_success_tips", "Key": "ui_app_cancel_success_tips"}, "7369": {"Id": 7369, "StringId": "ui_add_btn", "Key": "ui_add_btn"}, "7370": {"Id": 7370, "StringId": "ui_app_conditon_no_tips", "Key": "ui_app_conditon_no_tips"}, "7371": {"Id": 7371, "StringId": "ui_no_app_placeholder", "Key": "ui_no_app_placeholder"}, "7372": {"Id": 7372, "StringId": "ui_chat_world_channel", "Key": "ui_chat_world_channel"}, "7373": {"Id": 7373, "StringId": "ui_chat_guild_channel", "Key": "ui_chat_guild_channel"}, "7374": {"Id": 7374, "StringId": "ui_chat_nigger_channel", "Key": "ui_chat_nigger_channel"}, "7375": {"Id": 7375, "StringId": "ui_send_btn", "Key": "ui_send_btn"}, "7376": {"Id": 7376, "StringId": "ui_chat_guild_placefolder", "Key": "ui_chat_guild_placefolder"}, "7377": {"Id": 7377, "StringId": "ui_guild_join_btn", "Key": "ui_guild_join_btn"}, "7378": {"Id": 7378, "StringId": "ui_guild_join_tips", "Key": "ui_guild_join_tips"}, "7379": {"Id": 7379, "StringId": "ui_guild_exit_tips", "Key": "ui_guild_exit_tips"}, "7380": {"Id": 7380, "StringId": "ui_upset_btn", "Key": "ui_upset_btn"}, "7381": {"Id": 7381, "StringId": "ui_upset_cancel_btn", "Key": "ui_upset_cancel_btn"}, "7382": {"Id": 7382, "StringId": "ui_upset_success_tips", "Key": "ui_upset_success_tips"}, "7383": {"Id": 7383, "StringId": "ui_upset_cancel_success_tips", "Key": "ui_upset_cancel_success_tips"}, "7384": {"Id": 7384, "StringId": "ui_delete_success_tips", "Key": "ui_delete_success_tips"}, "7385": {"Id": 7385, "StringId": "ui_nigger_sql_tips", "Key": "ui_nigger_sql_tips"}, "7386": {"Id": 7386, "StringId": "ui_no_app_nigger_condition_tips", "Key": "ui_no_app_nigger_condition_tips"}, "7387": {"Id": 7387, "StringId": "ui_app_nigger_to_me_limit_tips", "Key": "ui_app_nigger_to_me_limit_tips"}, "7388": {"Id": 7388, "StringId": "ui_agree_avbody_nigger_app_tips", "Key": "ui_agree_avbody_nigger_app_tips"}, "7389": {"Id": 7389, "StringId": "ui_nigger_set_success_tips", "Key": "ui_nigger_set_success_tips"}, "7390": {"Id": 7390, "StringId": "ui_delete_nigger_btn", "Key": "ui_delete_nigger_btn"}, "7391": {"Id": 7391, "StringId": "ui_join_blacklist_btn", "Key": "ui_join_blacklist_btn"}, "7392": {"Id": 7392, "StringId": "ui_popup_delete_nigger_content", "Key": "ui_popup_delete_nigger_content"}, "7393": {"Id": 7393, "StringId": "ui_deleter_nigger_success_tips", "Key": "ui_deleter_nigger_success_tips"}, "7394": {"Id": 7394, "StringId": "ui_popup_nigger_blacklist_title", "Key": "ui_popup_nigger_blacklist_title"}, "7395": {"Id": 7395, "StringId": "ui_popup_nigger_blacklist_content", "Key": "ui_popup_nigger_blacklist_content"}, "7396": {"Id": 7396, "StringId": "ui_black_nigger_success_tips", "Key": "ui_black_nigger_success_tips"}, "7397": {"Id": 7397, "StringId": "ui_remove_btn", "Key": "ui_remove_btn"}, "7398": {"Id": 7398, "StringId": "ui_remove_blacklist_success_tips", "Key": "ui_remove_blacklist_success_tips"}, "7399": {"Id": 7399, "StringId": "ui_nigger_app_btn", "Key": "ui_nigger_app_btn"}, "7400": {"Id": 7400, "StringId": "ui_add_nigger_success_tips", "Key": "ui_add_nigger_success_tips"}, "7401": {"Id": 7401, "StringId": "ui_apped_btn", "Key": "ui_apped_btn"}, "7402": {"Id": 7402, "StringId": "ui_refresh_list_desc", "Key": "ui_refresh_list_desc"}, "7403": {"Id": 7403, "StringId": "ui_agree_all_btn", "Key": "ui_agree_all_btn"}, "7404": {"Id": 7404, "StringId": "ui_disagree_all_btn", "Key": "ui_disagree_all_btn"}, "7405": {"Id": 7405, "StringId": "ui_no_nigger_app_placefolder", "Key": "ui_no_nigger_app_placefolder"}, "7406": {"Id": 7406, "StringId": "ui_popup_delete_all_mail_title", "Key": "ui_popup_delete_all_mail_title"}, "7407": {"Id": 7407, "StringId": "ui_popup_delete_all_mail_content", "Key": "ui_popup_delete_all_mail_content"}, "7408": {"Id": 7408, "StringId": "ui_delete_all_mail_btn", "Key": "ui_delete_all_mail_btn"}, "7409": {"Id": 7409, "StringId": "ui_read_all_mail_btn", "Key": "ui_read_all_mail_btn"}, "7410": {"Id": 7410, "StringId": "ui_recipient_name", "Key": "ui_recipient_name"}, "7411": {"Id": 7411, "StringId": "ui_claimed_btn", "Key": "ui_claimed_btn"}, "7412": {"Id": 7412, "StringId": "ui_mail_overtime_tips", "Key": "ui_mail_overtime_tips"}, "7413": {"Id": 7413, "StringId": "ui_mail_delete_tips", "Key": "ui_mail_delete_tips"}, "7414": {"Id": 7414, "StringId": "ui_popup_delete_mail_content", "Key": "ui_popup_delete_mail_content"}, "7415": {"Id": 7415, "StringId": "ui_guild_notice_placefolder", "Key": "ui_guild_notice_placefolder"}, "7416": {"Id": 7416, "StringId": "ui_hero_level_up_limit", "Key": "ui_hero_level_up_limit"}, "7417": {"Id": 7417, "StringId": "ui_guild_quit_title", "Key": "ui_guild_quit_title"}, "7418": {"Id": 7418, "StringId": "ui_guild_quit_desc", "Key": "ui_guild_quit_desc"}, "7419": {"Id": 7419, "StringId": "ui_guild_quit_success_tips", "Key": "ui_guild_quit_success_tips"}, "7420": {"Id": 7420, "StringId": "ui_guild_disband_title", "Key": "ui_guild_disband_title"}, "7421": {"Id": 7421, "StringId": "ui_guild_no_disband_desc", "Key": "ui_guild_no_disband_desc"}, "7422": {"Id": 7422, "StringId": "ui_guild_disband_desc", "Key": "ui_guild_disband_desc"}, "7423": {"Id": 7423, "StringId": "ui_guild_disband_success_tips", "Key": "ui_guild_disband_success_tips"}, "7424": {"Id": 7424, "StringId": "ui_chapter_locked_tips", "Key": "ui_chapter_locked_tips"}, "7425": {"Id": 7425, "StringId": "ui_month_card_subscribe_instant_claim", "Key": "ui_month_card_subscribe_instant_claim"}, "7426": {"Id": 7426, "StringId": "ui_not_subscribe_desc", "Key": "ui_not_subscribe_desc"}, "7427": {"Id": 7427, "StringId": "ui_remaining_days", "Key": "ui_remaining_days"}, "7428": {"Id": 7428, "StringId": "ui_renew_btn", "Key": "ui_renew_btn"}, "7429": {"Id": 7429, "StringId": "ui_month_card_subscribe_desc", "Key": "ui_month_card_subscribe_desc"}, "7430": {"Id": 7430, "StringId": "ui_fund_free_reward_desc", "Key": "ui_fund_free_reward_desc"}, "7431": {"Id": 7431, "StringId": "ui_fund_iap_reward_desc", "Key": "ui_fund_iap_reward_desc"}, "7432": {"Id": 7432, "StringId": "ui_fund_purchase_all_reward_desc", "Key": "ui_fund_purchase_all_reward_desc"}, "7433": {"Id": 7433, "StringId": "ui_fund_purchase_current_reward_desc", "Key": "ui_fund_purchase_current_reward_desc"}, "7434": {"Id": 7434, "StringId": "ui_unlock_btn", "Key": "ui_unlock_btn"}, "7435": {"Id": 7435, "StringId": "ui_choiced_btn", "Key": "ui_choiced_btn"}, "7436": {"Id": 7436, "StringId": "function_level_unlock_desc", "Key": "function_level_unlock_desc"}, "7437": {"Id": 7437, "StringId": "ui_hot_sale_tag", "Key": "ui_hot_sale_tag"}, "7438": {"Id": 7438, "StringId": "ui_claim_again_btn_name", "Key": "ui_claim_again_btn_name"}, "7439": {"Id": 7439, "StringId": "ui_tap_blank_to_close", "Key": "ui_tap_blank_to_close"}, "7440": {"Id": 7440, "StringId": "ui_daily_sale_hero_change_tips", "Key": "ui_daily_sale_hero_change_tips"}, "7441": {"Id": 7441, "StringId": "ui_activity_outdate_title", "Key": "ui_activity_outdate_title"}, "7442": {"Id": 7442, "StringId": "ui_activity_outdate_desc", "Key": "ui_activity_outdate_desc"}, "7443": {"Id": 7443, "StringId": "ui_permanent_desc", "Key": "ui_permanent_desc"}, "7444": {"Id": 7444, "StringId": "ui_nigger_tag", "Key": "ui_nigger_tag"}, "7445": {"Id": 7445, "StringId": "ui_blacklist_tag", "Key": "ui_blacklist_tag"}, "7446": {"Id": 7446, "StringId": "ui_nigger_set_title", "Key": "ui_nigger_set_title"}, "7447": {"Id": 7447, "StringId": "ui_she_is_not_your_nigger", "Key": "ui_she_is_not_your_nigger"}, "7448": {"Id": 7448, "StringId": "ui_f_word_tips", "Key": "ui_f_word_tips"}, "7449": {"Id": 7449, "StringId": "ui_single_chat_level_limit_tips", "Key": "ui_single_chat_level_limit_tips"}, "7450": {"Id": 7450, "StringId": "ui_guild_name_reset_tips", "Key": "ui_guild_name_reset_tips"}, "7451": {"Id": 7451, "StringId": "ui_guild_short_name_reset_tips", "Key": "ui_guild_short_name_reset_tips"}, "7452": {"Id": 7452, "StringId": "ui_guild_rank_name_reset_tips", "Key": "ui_guild_rank_name_reset_tips"}, "7453": {"Id": 7453, "StringId": "ui_swap_btn", "Key": "ui_swap_btn"}, "7454": {"Id": 7454, "StringId": "function_chat_name", "Key": "function_chat_name"}, "7455": {"Id": 7455, "StringId": "ui_guest_login", "Key": "ui_guest_login"}, "7456": {"Id": 7456, "StringId": "ui_id_login", "Key": "ui_id_login"}, "7457": {"Id": 7457, "StringId": "ui_currency_insufficient_tips", "Key": "ui_currency_insufficient_tips"}, "7458": {"Id": 7458, "StringId": "ui_empty_placeholder", "Key": "ui_empty_placeholder"}, "7459": {"Id": 7459, "StringId": "function_nigger_name", "Key": "function_nigger_name"}, "7460": {"Id": 7460, "StringId": "ui_profile_guild_title", "Key": "ui_profile_guild_title"}, "7461": {"Id": 7461, "StringId": "ui_profile_empty_now", "Key": "ui_profile_empty_now"}, "7462": {"Id": 7462, "StringId": "function_mail_name", "Key": "function_mail_name"}, "7463": {"Id": 7463, "StringId": "function_summon_name", "Key": "function_summon_name"}, "7464": {"Id": 7464, "StringId": "function_gem_draw_tag", "Key": "function_gem_draw_tag"}, "7465": {"Id": 7465, "StringId": "ui_gem_draw_1_btn", "Key": "ui_gem_draw_1_btn"}, "7466": {"Id": 7466, "StringId": "ui_gem_draw_10_btn", "Key": "ui_gem_draw_10_btn"}, "7467": {"Id": 7467, "StringId": "ui_gem_draw_must_desc", "Key": "ui_gem_draw_must_desc"}, "7468": {"Id": 7468, "StringId": "ui_function_gem_name", "Key": "ui_function_gem_name"}, "7469": {"Id": 7469, "StringId": "ui_function_lab_name", "Key": "ui_function_lab_name"}, "7470": {"Id": 7470, "StringId": "ui_craft_all_btn", "Key": "ui_craft_all_btn"}, "7471": {"Id": 7471, "StringId": "ui_craft_all_tips", "Key": "ui_craft_all_tips"}, "7472": {"Id": 7472, "StringId": "ui_craft_tips", "Key": "ui_craft_tips"}, "7473": {"Id": 7473, "StringId": "ui_craft_no_tips", "Key": "ui_craft_no_tips"}, "7474": {"Id": 7474, "StringId": "ui_craft_rare_tips", "Key": "ui_craft_rare_tips"}, "7475": {"Id": 7475, "StringId": "ui_gem_detail_title", "Key": "ui_gem_detail_title"}, "7476": {"Id": 7476, "StringId": "ui_gem_install_btn", "Key": "ui_gem_install_btn"}, "7477": {"Id": 7477, "StringId": "ui_gem_reforce_btn", "Key": "ui_gem_reforce_btn"}, "7478": {"Id": 7478, "StringId": "ui_gem_repeat_replace_tips", "Key": "ui_gem_repeat_replace_tips"}, "7479": {"Id": 7479, "StringId": "ui_gem_bag_title", "Key": "ui_gem_bag_title"}, "7480": {"Id": 7480, "StringId": "ui_gem_replace_tips", "Key": "ui_gem_replace_tips"}, "7481": {"Id": 7481, "StringId": "ui_lord_equip_strengthen_btn", "Key": "ui_lord_equip_strengthen_btn"}, "7482": {"Id": 7482, "StringId": "ui_gem_review_title", "Key": "ui_gem_review_title"}, "7483": {"Id": 7483, "StringId": "ui_strengthen_btn", "Key": "ui_strengthen_btn"}, "7484": {"Id": 7484, "StringId": "ui_can_not_strengthen_tips", "Key": "ui_can_not_strengthen_tips"}, "7485": {"Id": 7485, "StringId": "ui_grade_up_btn", "Key": "ui_grade_up_btn"}, "7486": {"Id": 7486, "StringId": "ui_comming_soon_tips", "Key": "ui_comming_soon_tips"}, "7487": {"Id": 7487, "StringId": "ui_difficulty_tips", "Key": "ui_difficulty_tips"}, "7488": {"Id": 7488, "StringId": "ui_remaining_challenge_times_tips", "Key": "ui_remaining_challenge_times_tips"}, "7489": {"Id": 7489, "StringId": "ui_select_skills_please_tips", "Key": "ui_select_skills_please_tips"}, "7490": {"Id": 7490, "StringId": "ui_function_dungeon_name", "Key": "ui_function_dungeon_name"}, "7491": {"Id": 7491, "StringId": "ui_function_play_name", "Key": "ui_function_play_name"}, "7492": {"Id": 7492, "StringId": "ui_sweep_last_difficulty_tips", "Key": "ui_sweep_last_difficulty_tips"}, "7493": {"Id": 7493, "StringId": "ui_lord_equip_level_limit", "Key": "ui_lord_equip_level_limit"}, "7494": {"Id": 7494, "StringId": "ui_hero_quality_up_tips", "Key": "ui_hero_quality_up_tips"}, "7495": {"Id": 7495, "StringId": "ui_hero_quality_up_btn", "Key": "ui_hero_quality_up_btn"}, "7496": {"Id": 7496, "StringId": "ui_gem_unload_btn", "Key": "ui_gem_unload_btn"}, "7497": {"Id": 7497, "StringId": "ui_not_inlaid_yet", "Key": "ui_not_inlaid_yet"}, "7498": {"Id": 7498, "StringId": "ui_mutated_gene_effects", "Key": "ui_mutated_gene_effects"}, "7499": {"Id": 7499, "StringId": "ui_effected_hero", "Key": "ui_effected_hero"}, "7500": {"Id": 7500, "StringId": "ui_gem_quality_desc", "Key": "ui_gem_quality_desc"}, "7501": {"Id": 7501, "StringId": "ui_gem_level_desc", "Key": "ui_gem_level_desc"}, "7502": {"Id": 7502, "StringId": "ui_gem_addition_desc", "Key": "ui_gem_addition_desc"}, "7503": {"Id": 7503, "StringId": "ui_lord_equip_addition_desc", "Key": "ui_lord_equip_addition_desc"}, "7504": {"Id": 7504, "StringId": "ui_hero_star_limit_desc", "Key": "ui_hero_star_limit_desc"}, "7505": {"Id": 7505, "StringId": "ui_next_claimed_time_tips", "Key": "ui_next_claimed_time_tips"}, "7506": {"Id": 7506, "StringId": "ui_incomplete_tips", "Key": "ui_incomplete_tips"}, "7507": {"Id": 7507, "StringId": "ui_level_x", "Key": "ui_level_x"}, "7508": {"Id": 7508, "StringId": "ui_lord_equip_level_up_limit_tips", "Key": "ui_lord_equip_level_up_limit_tips"}, "7509": {"Id": 7509, "StringId": "ui_lord_equip_grade_up_btn", "Key": "ui_lord_equip_grade_up_btn"}, "7510": {"Id": 7510, "StringId": "npc_dialouge_11_content", "Key": "npc_dialouge_11_content"}, "7511": {"Id": 7511, "StringId": "npc_dialouge_12_content", "Key": "npc_dialouge_12_content"}, "7512": {"Id": 7512, "StringId": "npc_dialouge_13_content", "Key": "npc_dialouge_13_content"}, "7513": {"Id": 7513, "StringId": "npc_dialouge_14_content", "Key": "npc_dialouge_14_content"}, "7514": {"Id": 7514, "StringId": "npc_dialouge_15_content", "Key": "npc_dialouge_15_content"}, "7515": {"Id": 7515, "StringId": "npc_dialouge_16_content", "Key": "npc_dialouge_16_content"}, "7516": {"Id": 7516, "StringId": "npc_dialouge_17_content", "Key": "npc_dialouge_17_content"}, "7517": {"Id": 7517, "StringId": "npc_dialouge_18_content", "Key": "npc_dialouge_18_content"}, "7518": {"Id": 7518, "StringId": "npc_dialouge_19_content", "Key": "npc_dialouge_19_content"}, "7519": {"Id": 7519, "StringId": "npc_dialouge_20_content", "Key": "npc_dialouge_20_content"}, "7520": {"Id": 7520, "StringId": "npc_dialouge_21_content", "Key": "npc_dialouge_21_content"}, "7521": {"Id": 7521, "StringId": "npc_dialouge_22_content", "Key": "npc_dialouge_22_content"}, "7522": {"Id": 7522, "StringId": "npc_dialouge_23_content", "Key": "npc_dialouge_23_content"}, "7523": {"Id": 7523, "StringId": "npc_dialouge_24_content", "Key": "npc_dialouge_24_content"}, "7524": {"Id": 7524, "StringId": "npc_dialouge_25_content", "Key": "npc_dialouge_25_content"}, "7525": {"Id": 7525, "StringId": "npc_dialouge_26_content", "Key": "npc_dialouge_26_content"}, "7526": {"Id": 7526, "StringId": "npc_dialouge_27_content", "Key": "npc_dialouge_27_content"}, "7527": {"Id": 7527, "StringId": "npc_dialouge_28_content", "Key": "npc_dialouge_28_content"}, "7528": {"Id": 7528, "StringId": "npc_dialouge_29_content", "Key": "npc_dialouge_29_content"}, "7529": {"Id": 7529, "StringId": "npc_dialouge_30_content", "Key": "npc_dialouge_30_content"}, "7530": {"Id": 7530, "StringId": "npc_dialouge_31_content", "Key": "npc_dialouge_31_content"}, "7531": {"Id": 7531, "StringId": "ui_effected_all_hero", "Key": "ui_effected_all_hero"}, "7532": {"Id": 7532, "StringId": "npc_dialouge_32_content", "Key": "npc_dialouge_32_content"}, "7533": {"Id": 7533, "StringId": "npc_dialouge_33_content", "Key": "npc_dialouge_33_content"}, "7534": {"Id": 7534, "StringId": "ui_back_to_home_btn", "Key": "ui_back_to_home_btn"}, "7535": {"Id": 7535, "StringId": "ui_challenge_next_level_btn", "Key": "ui_challenge_next_level_btn"}, "7536": {"Id": 7536, "StringId": "function_daily_tasks", "Key": "function_daily_tasks"}, "7537": {"Id": 7537, "StringId": "npc_dialouge_34_content", "Key": "npc_dialouge_34_content"}, "7538": {"Id": 7538, "StringId": "npc_dialouge_35_content", "Key": "npc_dialouge_35_content"}, "7539": {"Id": 7539, "StringId": "npc_dialouge_36_content", "Key": "npc_dialouge_36_content"}, "7540": {"Id": 7540, "StringId": "npc_dialouge_37_content", "Key": "npc_dialouge_37_content"}, "7541": {"Id": 7541, "StringId": "npc_dialouge_38_content", "Key": "npc_dialouge_38_content"}, "7542": {"Id": 7542, "StringId": "npc_dialouge_39_content", "Key": "npc_dialouge_39_content"}, "7543": {"Id": 7543, "StringId": "function_hero_gene_name", "Key": "function_hero_gene_name"}, "7544": {"Id": 7544, "StringId": "function_event_name", "Key": "function_event_name"}, "7545": {"Id": 7545, "StringId": "ui_notenough_desc", "Key": "ui_notenough_desc"}, "7546": {"Id": 7546, "StringId": "ui_reward_blitz_title", "Key": "ui_reward_blitz_title"}, "7547": {"Id": 7547, "StringId": "ui_reward_overview_title", "Key": "ui_reward_overview_title"}, "7548": {"Id": 7548, "StringId": "ui_reward_basic_des", "Key": "ui_reward_basic_des"}, "7549": {"Id": 7549, "StringId": "ui_reward_firstdone_des", "Key": "ui_reward_firstdone_des"}, "7550": {"Id": 7550, "StringId": "npc_dialouge_40_content", "Key": "npc_dialouge_40_content"}, "7551": {"Id": 7551, "StringId": "npc_dialouge_41_content", "Key": "npc_dialouge_41_content"}, "7552": {"Id": 7552, "StringId": "npc_dialouge_42_content", "Key": "npc_dialouge_42_content"}, "7553": {"Id": 7553, "StringId": "npc_dialouge_43_content", "Key": "npc_dialouge_43_content"}, "7554": {"Id": 7554, "StringId": "npc_dialouge_44_content", "Key": "npc_dialouge_44_content"}, "7555": {"Id": 7555, "StringId": "npc_dialouge_45_content", "Key": "npc_dialouge_45_content"}, "7556": {"Id": 7556, "StringId": "npc_dialouge_46_content", "Key": "npc_dialouge_46_content"}, "7557": {"Id": 7557, "StringId": "npc_dialouge_47_content", "Key": "npc_dialouge_47_content"}, "7558": {"Id": 7558, "StringId": "npc_dialouge_48_content", "Key": "npc_dialouge_48_content"}, "7559": {"Id": 7559, "StringId": "npc_dialouge_49_content", "Key": "npc_dialouge_49_content"}, "7560": {"Id": 7560, "StringId": "npc_dialouge_50_content", "Key": "npc_dialouge_50_content"}, "7561": {"Id": 7561, "StringId": "npc_dialouge_51_content", "Key": "npc_dialouge_51_content"}, "7562": {"Id": 7562, "StringId": "npc_dialouge_52_content", "Key": "npc_dialouge_52_content"}, "7563": {"Id": 7563, "StringId": "npc_dialouge_53_content", "Key": "npc_dialouge_53_content"}, "7564": {"Id": 7564, "StringId": "function_sign_7_day7_desc", "Key": "function_sign_7_day7_desc"}, "7565": {"Id": 7565, "StringId": "function_sign_7_day14_desc", "Key": "function_sign_7_day14_desc"}, "7566": {"Id": 7566, "StringId": "function_task_7_name", "Key": "function_task_7_name"}, "7567": {"Id": 7567, "StringId": "function_task_7_choose_title", "Key": "function_task_7_choose_title"}, "7568": {"Id": 7568, "StringId": "function_task_7_choose_desc", "Key": "function_task_7_choose_desc"}, "7569": {"Id": 7569, "StringId": "function_task_7_lock", "Key": "function_task_7_lock"}, "7570": {"Id": 7570, "StringId": "function_task_7_confirm1", "Key": "function_task_7_confirm1"}, "7571": {"Id": 7571, "StringId": "function_task_7_confirm2", "Key": "function_task_7_confirm2"}, "7572": {"Id": 7572, "StringId": "ui_lord_gem_can_not_reforge", "Key": "ui_lord_gem_can_not_reforge"}, "7573": {"Id": 7573, "StringId": "ui_the_strongest_output", "Key": "ui_the_strongest_output"}, "7574": {"Id": 7574, "StringId": "ui_battle_reward", "Key": "ui_battle_reward"}, "7575": {"Id": 7575, "StringId": "error_disconnect", "Key": "error_disconnect"}, "7576": {"Id": 7576, "StringId": "ui_daily_claimed_reward", "Key": "ui_daily_claimed_reward"}, "7577": {"Id": 7577, "StringId": "function_echo_name", "Key": "function_echo_name"}, "7578": {"Id": 7578, "StringId": "function_echo_change_name", "Key": "function_echo_change_name"}, "7579": {"Id": 7579, "StringId": "function_echo_level", "Key": "function_echo_level"}, "7580": {"Id": 7580, "StringId": "function_echo_gotolevelup", "Key": "function_echo_gotolevelup"}, "7581": {"Id": 7581, "StringId": "function_echo_autolevelup", "Key": "function_echo_autolevelup"}, "7582": {"Id": 7582, "StringId": "function_echo_change_des", "Key": "function_echo_change_des"}, "7583": {"Id": 7583, "StringId": "function_echo_autoreset", "Key": "function_echo_autoreset"}, "7584": {"Id": 7584, "StringId": "function_echo_levelup_ues", "Key": "function_echo_levelup_ues"}, "7585": {"Id": 7585, "StringId": "ui_goto_echo", "Key": "ui_goto_echo"}, "7586": {"Id": 7586, "StringId": "ui_echo_levelmax", "Key": "ui_echo_levelmax"}, "7587": {"Id": 7587, "StringId": "ui_cannot_login_tips", "Key": "ui_cannot_login_tips"}, "7588": {"Id": 7588, "StringId": "ui_function_achievement", "Key": "ui_function_achievement"}, "7589": {"Id": 7589, "StringId": "chooseserver_name", "Key": "chooseserver_name"}, "7590": {"Id": 7590, "StringId": "lineup_name", "Key": "lineup_name"}, "7591": {"Id": 7591, "StringId": "task_active_week", "Key": "task_active_week"}, "7592": {"Id": 7592, "StringId": "task_active_day", "Key": "task_active_day"}, "7593": {"Id": 7593, "StringId": "ui_recruit_name", "Key": "ui_recruit_name"}, "7594": {"Id": 7594, "StringId": "ui_button_fight", "Key": "ui_button_fight"}, "7595": {"Id": 7595, "StringId": "ui_comingsoon", "Key": "ui_comingsoon"}, "7596": {"Id": 7596, "StringId": "ui_basic_option_name", "Key": "ui_basic_option_name"}, "7597": {"Id": 7597, "StringId": "ui_advance_option_name", "Key": "ui_advance_option_name"}, "7598": {"Id": 7598, "StringId": "ui_idle_reward_blitz_unlimit_tips_desc", "Key": "ui_idle_reward_blitz_unlimit_tips_desc"}, "7599": {"Id": 7599, "StringId": "ui_quick_assemble", "Key": "ui_quick_assemble"}, "7600": {"Id": 7600, "StringId": "ui_elemental_ice_name", "Key": "ui_elemental_ice_name"}, "7601": {"Id": 7601, "StringId": "ui_elemental_fire_name", "Key": "ui_elemental_fire_name"}, "7602": {"Id": 7602, "StringId": "ui_elemental_electricity_name", "Key": "ui_elemental_electricity_name"}, "7603": {"Id": 7603, "StringId": "ui_elemental_wind_name", "Key": "ui_elemental_wind_name"}, "7604": {"Id": 7604, "StringId": "ui_elemental_light_name", "Key": "ui_elemental_light_name"}, "7605": {"Id": 7605, "StringId": "ui_elemental_physics_name", "Key": "ui_elemental_physics_name"}, "7606": {"Id": 7606, "StringId": "ui_elemental_ice_des", "Key": "ui_elemental_ice_des"}, "7607": {"Id": 7607, "StringId": "ui_elemental_fire_des", "Key": "ui_elemental_fire_des"}, "7608": {"Id": 7608, "StringId": "ui_elemental_electricity_des", "Key": "ui_elemental_electricity_des"}, "7609": {"Id": 7609, "StringId": "ui_elemental_wind_des", "Key": "ui_elemental_wind_des"}, "7610": {"Id": 7610, "StringId": "ui_elemental_light_des", "Key": "ui_elemental_light_des"}, "7611": {"Id": 7611, "StringId": "ui_elemental_physics_des", "Key": "ui_elemental_physics_des"}, "7612": {"Id": 7612, "StringId": "ui_giftskill_title", "Key": "ui_giftskill_title"}, "7613": {"Id": 7613, "StringId": "ui_bag_name", "Key": "ui_bag_name"}, "7614": {"Id": 7614, "StringId": "ui_bag_plant_name", "Key": "ui_bag_plant_name"}, "7615": {"Id": 7615, "StringId": "ui_bag_material_name", "Key": "ui_bag_material_name"}, "7616": {"Id": 7616, "StringId": "ui_bag_consumable_name", "Key": "ui_bag_consumable_name"}, "7617": {"Id": 7617, "StringId": "ui_bag_item_title", "Key": "ui_bag_item_title"}, "7618": {"Id": 7618, "StringId": "UI_ChapterTask_name", "Key": "UI_ChapterTask_name"}, "7619": {"Id": 7619, "StringId": "ui_rank_guild_power_title", "Key": "ui_rank_guild_power_title"}, "7620": {"Id": 7620, "StringId": "ui_rank_guild_name", "Key": "ui_rank_guild_name"}, "7621": {"Id": 7621, "StringId": "ui_rank_guild_power", "Key": "ui_rank_guild_power"}, "7622": {"Id": 7622, "StringId": "ui_rank_player_power_title", "Key": "ui_rank_player_power_title"}, "7623": {"Id": 7623, "StringId": "ui_rank_player_power", "Key": "ui_rank_player_power"}, "7624": {"Id": 7624, "StringId": "ui_choose_skin_title", "Key": "ui_choose_skin_title"}, "7625": {"Id": 7625, "StringId": "ui_head_name", "Key": "ui_head_name"}, "7626": {"Id": 7626, "StringId": "ui_headframe_name", "Key": "ui_headframe_name"}, "7627": {"Id": 7627, "StringId": "ui_label_name", "Key": "ui_label_name"}, "7628": {"Id": 7628, "StringId": "ui_button_unlockby_1", "Key": "ui_button_unlockby_1"}, "7629": {"Id": 7629, "StringId": "ui_button_unlockby_2", "Key": "ui_button_unlockby_2"}, "7630": {"Id": 7630, "StringId": "ui_chatbubble_name", "Key": "ui_chatbubble_name"}, "7631": {"Id": 7631, "StringId": "function_dungeon_name", "Key": "function_dungeon_name"}, "7632": {"Id": 7632, "StringId": "function_elite_name", "Key": "function_elite_name"}, "7633": {"Id": 7633, "StringId": "ui_quickvictory_desc", "Key": "ui_quickvictory_desc"}, "7634": {"Id": 7634, "StringId": "ui_level_star_1", "Key": "ui_level_star_1"}, "7635": {"Id": 7635, "StringId": "ui_level_star_2", "Key": "ui_level_star_2"}, "7636": {"Id": 7636, "StringId": "ui_level_star_3", "Key": "ui_level_star_3"}, "7637": {"Id": 7637, "StringId": "ui_level_locked", "Key": "ui_level_locked"}, "7638": {"Id": 7638, "StringId": "ui_bag_randompool_title", "Key": "ui_bag_randompool_title"}, "7639": {"Id": 7639, "StringId": "ui_hello", "Key": "ui_hello"}, "8001": {"Id": 8001, "StringId": "lord_equip_1_desc", "Key": "lord_equip_1_desc"}, "8002": {"Id": 8002, "StringId": "lord_equip_2_desc", "Key": "lord_equip_2_desc"}, "8003": {"Id": 8003, "StringId": "lord_equip_3_desc", "Key": "lord_equip_3_desc"}, "8004": {"Id": 8004, "StringId": "lord_equip_4_desc", "Key": "lord_equip_4_desc"}, "8005": {"Id": 8005, "StringId": "lord_equip_5_desc", "Key": "lord_equip_5_desc"}, "8006": {"Id": 8006, "StringId": "lord_equip_6_desc", "Key": "lord_equip_6_desc"}, "8007": {"Id": 8007, "StringId": "gem_affix_exclusive_desc", "Key": "gem_affix_exclusive_desc"}, "8008": {"Id": 8008, "StringId": "gem_affix_rare_desc", "Key": "gem_affix_rare_desc"}, "8009": {"Id": 8009, "StringId": "gem_affix_common_desc", "Key": "gem_affix_common_desc"}, "8010": {"Id": 8010, "StringId": "gem_quality_1_desc", "Key": "gem_quality_1_desc"}, "8011": {"Id": 8011, "StringId": "gem_quality_2_desc", "Key": "gem_quality_2_desc"}, "8012": {"Id": 8012, "StringId": "gem_quality_3_desc", "Key": "gem_quality_3_desc"}, "8013": {"Id": 8013, "StringId": "gem_quality_4_desc", "Key": "gem_quality_4_desc"}, "8014": {"Id": 8014, "StringId": "gem_quality_5_desc", "Key": "gem_quality_5_desc"}, "8015": {"Id": 8015, "StringId": "gem_quality_6_desc", "Key": "gem_quality_6_desc"}, "8016": {"Id": 8016, "StringId": "gem_quality_7_desc", "Key": "gem_quality_7_desc"}, "8017": {"Id": 8017, "StringId": "gem_1_desc", "Key": "gem_1_desc"}, "8018": {"Id": 8018, "StringId": "gem_2_desc", "Key": "gem_2_desc"}, "8019": {"Id": 8019, "StringId": "gem_3_desc", "Key": "gem_3_desc"}, "8020": {"Id": 8020, "StringId": "gem_4_desc", "Key": "gem_4_desc"}, "8021": {"Id": 8021, "StringId": "gem_5_desc", "Key": "gem_5_desc"}, "8022": {"Id": 8022, "StringId": "gem_6_desc", "Key": "gem_6_desc"}, "8023": {"Id": 8023, "StringId": "gem_7_desc", "Key": "gem_7_desc"}, "8024": {"Id": 8024, "StringId": "gem_8_desc", "Key": "gem_8_desc"}, "8025": {"Id": 8025, "StringId": "gem_9_desc", "Key": "gem_9_desc"}, "8026": {"Id": 8026, "StringId": "gem_10_desc", "Key": "gem_10_desc"}, "8027": {"Id": 8027, "StringId": "gem_11_desc", "Key": "gem_11_desc"}, "8028": {"Id": 8028, "StringId": "gem_12_desc", "Key": "gem_12_desc"}, "8029": {"Id": 8029, "StringId": "gem_13_desc", "Key": "gem_13_desc"}, "8030": {"Id": 8030, "StringId": "gem_14_desc", "Key": "gem_14_desc"}, "8031": {"Id": 8031, "StringId": "gem_15_desc", "Key": "gem_15_desc"}, "8032": {"Id": 8032, "StringId": "gem_16_desc", "Key": "gem_16_desc"}, "8033": {"Id": 8033, "StringId": "gem_17_desc", "Key": "gem_17_desc"}, "8034": {"Id": 8034, "StringId": "gem_18_desc", "Key": "gem_18_desc"}, "8035": {"Id": 8035, "StringId": "gem_19_desc", "Key": "gem_19_desc"}, "8036": {"Id": 8036, "StringId": "gem_20_desc", "Key": "gem_20_desc"}, "8037": {"Id": 8037, "StringId": "gem_21_desc", "Key": "gem_21_desc"}, "8038": {"Id": 8038, "StringId": "gem_22_desc", "Key": "gem_22_desc"}, "8039": {"Id": 8039, "StringId": "gem_23_desc", "Key": "gem_23_desc"}, "8040": {"Id": 8040, "StringId": "gem_24_desc", "Key": "gem_24_desc"}, "8041": {"Id": 8041, "StringId": "gem_25_desc", "Key": "gem_25_desc"}, "8042": {"Id": 8042, "StringId": "gem_26_desc", "Key": "gem_26_desc"}, "8043": {"Id": 8043, "StringId": "gem_27_desc", "Key": "gem_27_desc"}, "8044": {"Id": 8044, "StringId": "gem_28_desc", "Key": "gem_28_desc"}, "8045": {"Id": 8045, "StringId": "gem_29_desc", "Key": "gem_29_desc"}, "8046": {"Id": 8046, "StringId": "gem_30_desc", "Key": "gem_30_desc"}, "8047": {"Id": 8047, "StringId": "gem_31_desc", "Key": "gem_31_desc"}, "8048": {"Id": 8048, "StringId": "gem_32_desc", "Key": "gem_32_desc"}, "8049": {"Id": 8049, "StringId": "gem_33_desc", "Key": "gem_33_desc"}, "8050": {"Id": 8050, "StringId": "gem_34_desc", "Key": "gem_34_desc"}, "8051": {"Id": 8051, "StringId": "gem_35_desc", "Key": "gem_35_desc"}, "8052": {"Id": 8052, "StringId": "gem_36_desc", "Key": "gem_36_desc"}, "8053": {"Id": 8053, "StringId": "gem_37_desc", "Key": "gem_37_desc"}, "8054": {"Id": 8054, "StringId": "gem_38_desc", "Key": "gem_38_desc"}, "8055": {"Id": 8055, "StringId": "gem_39_desc", "Key": "gem_39_desc"}, "8056": {"Id": 8056, "StringId": "gem_40_desc", "Key": "gem_40_desc"}, "8057": {"Id": 8057, "StringId": "gem_41_desc", "Key": "gem_41_desc"}, "8058": {"Id": 8058, "StringId": "gem_42_desc", "Key": "gem_42_desc"}, "8059": {"Id": 8059, "StringId": "gem_43_desc", "Key": "gem_43_desc"}, "8060": {"Id": 8060, "StringId": "gem_44_desc", "Key": "gem_44_desc"}, "8061": {"Id": 8061, "StringId": "gem_45_desc", "Key": "gem_45_desc"}, "8062": {"Id": 8062, "StringId": "gem_46_desc", "Key": "gem_46_desc"}, "8063": {"Id": 8063, "StringId": "gem_47_desc", "Key": "gem_47_desc"}, "8064": {"Id": 8064, "StringId": "gem_48_desc", "Key": "gem_48_desc"}, "8065": {"Id": 8065, "StringId": "gem_49_desc", "Key": "gem_49_desc"}, "8066": {"Id": 8066, "StringId": "gem_50_desc", "Key": "gem_50_desc"}, "8067": {"Id": 8067, "StringId": "gem_51_desc", "Key": "gem_51_desc"}, "8068": {"Id": 8068, "StringId": "gem_52_desc", "Key": "gem_52_desc"}, "8069": {"Id": 8069, "StringId": "gem_53_desc", "Key": "gem_53_desc"}, "8070": {"Id": 8070, "StringId": "gem_54_desc", "Key": "gem_54_desc"}, "8071": {"Id": 8071, "StringId": "gem_55_desc", "Key": "gem_55_desc"}, "8072": {"Id": 8072, "StringId": "gem_56_desc", "Key": "gem_56_desc"}, "8073": {"Id": 8073, "StringId": "gem_57_desc", "Key": "gem_57_desc"}, "8074": {"Id": 8074, "StringId": "gem_58_desc", "Key": "gem_58_desc"}, "8075": {"Id": 8075, "StringId": "gem_59_desc", "Key": "gem_59_desc"}, "8076": {"Id": 8076, "StringId": "gem_60_desc", "Key": "gem_60_desc"}, "8077": {"Id": 8077, "StringId": "gem_61_desc", "Key": "gem_61_desc"}, "8078": {"Id": 8078, "StringId": "gem_62_desc", "Key": "gem_62_desc"}, "8079": {"Id": 8079, "StringId": "gem_63_desc", "Key": "gem_63_desc"}, "8080": {"Id": 8080, "StringId": "gem_random_group_common_title", "Key": "gem_random_group_common_title"}, "8081": {"Id": 8081, "StringId": "gem_random_group_senior_title", "Key": "gem_random_group_senior_title"}, "8082": {"Id": 8082, "StringId": "gem_random_group_common_must_desc", "Key": "gem_random_group_common_must_desc"}, "8083": {"Id": 8083, "StringId": "gem_random_group_senior_must_desc", "Key": "gem_random_group_senior_must_desc"}, "8084": {"Id": 8084, "StringId": "lord_equip_grade_1_name", "Key": "lord_equip_grade_1_name"}, "8085": {"Id": 8085, "StringId": "lord_equip_grade_2_name", "Key": "lord_equip_grade_2_name"}, "8086": {"Id": 8086, "StringId": "lord_equip_grade_3_name", "Key": "lord_equip_grade_3_name"}, "8087": {"Id": 8087, "StringId": "lord_equip_grade_4_name", "Key": "lord_equip_grade_4_name"}, "8088": {"Id": 8088, "StringId": "lord_equip_grade_5_name", "Key": "lord_equip_grade_5_name"}, "8089": {"Id": 8089, "StringId": "lord_equip_grade_6_name", "Key": "lord_equip_grade_6_name"}, "8090": {"Id": 8090, "StringId": "lord_equip_grade_7_name", "Key": "lord_equip_grade_7_name"}, "8091": {"Id": 8091, "StringId": "ui_gem_phase_desc", "Key": "ui_gem_phase_desc"}, "9001": {"Id": 9001, "StringId": "task_level_pass_desc", "Key": "task_level_pass_desc"}, "9002": {"Id": 9002, "StringId": "task_level_challenge_desc", "Key": "task_level_challenge_desc"}, "9003": {"Id": 9003, "StringId": "task_hero_level_up_desc", "Key": "task_hero_level_up_desc"}, "9004": {"Id": 9004, "StringId": "task_hero_level_up_to_desc", "Key": "task_hero_level_up_to_desc"}, "9005": {"Id": 9005, "StringId": "task_daily_login_desc", "Key": "task_daily_login_desc"}, "9006": {"Id": 9006, "StringId": "task_use_energy_desc", "Key": "task_use_energy_desc"}, "9007": {"Id": 9007, "StringId": "task_kill_monster_desc", "Key": "task_kill_monster_desc"}, "9008": {"Id": 9008, "StringId": "task_claim_idle_reward_desc", "Key": "task_claim_idle_reward_desc"}, "9009": {"Id": 9009, "StringId": "task_hero_summon_desc", "Key": "task_hero_summon_desc"}, "9010": {"Id": 9010, "StringId": "task_chat_guild_desc", "Key": "task_chat_guild_desc"}, "9011": {"Id": 9011, "StringId": "task_idle_sweep_desc", "Key": "task_idle_sweep_desc"}, "9012": {"Id": 9012, "StringId": "task_use_diamond_desc", "Key": "task_use_diamond_desc"}, "9013": {"Id": 9013, "StringId": "task_hard_level_challenge_desc", "Key": "task_hard_level_challenge_desc"}, "9014": {"Id": 9014, "StringId": "task_random_level_pass_desc", "Key": "task_random_level_pass_desc"}, "9015": {"Id": 9015, "StringId": "task_hero_gene_up_desc", "Key": "task_hero_gene_up_desc"}, "9016": {"Id": 9016, "StringId": "task_hero_config_desc", "Key": "task_hero_config_desc"}, "9017": {"Id": 9017, "StringId": "task_level_pass_reward_desc", "Key": "task_level_pass_reward_desc"}, "9018": {"Id": 9018, "StringId": "task_lord_equip_level_up_desc", "Key": "task_lord_equip_level_up_desc"}, "9019": {"Id": 9019, "StringId": "task_lord_equip_level_up_to_desc", "Key": "task_lord_equip_level_up_to_desc"}, "9020": {"Id": 9020, "StringId": "task_gem_craft_desc", "Key": "task_gem_craft_desc"}, "9021": {"Id": 9021, "StringId": "task_gem_craft_to_desc", "Key": "task_gem_craft_to_desc"}, "9022": {"Id": 9022, "StringId": "task_gem_summon_desc", "Key": "task_gem_summon_desc"}, "9023": {"Id": 9023, "StringId": "task_shopping_desc", "Key": "task_shopping_desc"}, "9024": {"Id": 9024, "StringId": "task_dungeon_challenge_desc", "Key": "task_dungeon_challenge_desc"}, "9025": {"Id": 9025, "StringId": "task_dungeon_sweep_desc", "Key": "task_dungeon_sweep_desc"}, "9026": {"Id": 9026, "StringId": "task_arena_challenge_desc", "Key": "task_arena_challenge_desc"}, "9027": {"Id": 9027, "StringId": "seven_task_all_login_desc", "Key": "seven_task_all_login_desc"}, "9028": {"Id": 9028, "StringId": "seven_task_pass_level_desc", "Key": "seven_task_pass_level_desc"}, "9029": {"Id": 9029, "StringId": "seven_task_active_ hero_desc", "Key": "seven_task_active_ hero_desc"}, "9030": {"Id": 9030, "StringId": "seven_task_cost_desc", "Key": "seven_task_cost_desc"}, "9031": {"Id": 9031, "StringId": "seven_task_upgrade_ hero_desc", "Key": "seven_task_upgrade_ hero_desc"}, "9032": {"Id": 9032, "StringId": "seven_task_total_kill_ monster_desc", "Key": "seven_task_total_kill_ monster_desc"}, "9033": {"Id": 9033, "StringId": "seven_task_total_ hero_summon_desc", "Key": "seven_task_total_ hero_summon_desc"}, "9034": {"Id": 9034, "StringId": "seven_task_total_ gem_summon_desc", "Key": "seven_task_total_ gem_summon_desc"}, "9035": {"Id": 9035, "StringId": "task_ gem_summon_desc", "Key": "task_ gem_summon_desc"}, "9036": {"Id": 9036, "StringId": "task_ world_speak_desc", "Key": "task_ world_speak_desc"}, "9037": {"Id": 9037, "StringId": "task_unlock_plant_desc", "Key": "task_unlock_plant_desc"}, "9038": {"Id": 9038, "StringId": "task_lab_quality_1_desc", "Key": "task_lab_quality_1_desc"}, "9039": {"Id": 9039, "StringId": "task_lab_quality_2_desc", "Key": "task_lab_quality_2_desc"}, "9040": {"Id": 9040, "StringId": "task_lab_quality_3_desc", "Key": "task_lab_quality_3_desc"}, "9041": {"Id": 9041, "StringId": "task_lab_quality_4_desc", "Key": "task_lab_quality_4_desc"}, "9042": {"Id": 9042, "StringId": "task_mianstar_desc", "Key": "task_mianstar_desc"}, "9043": {"Id": 9043, "StringId": "task_star_up_desc", "Key": "task_star_up_desc"}, "9044": {"Id": 9044, "StringId": "task_dailybox_desc", "Key": "task_dailybox_desc"}, "9045": {"Id": 9045, "StringId": "task_energy_factory_desc", "Key": "task_energy_factory_desc"}, "9046": {"Id": 9046, "StringId": "task_friend_desc", "Key": "task_friend_desc"}, "9047": {"Id": 9047, "StringId": "task_talent_level_desc", "Key": "task_talent_level_desc"}, "9048": {"Id": 9048, "StringId": "task_lordequip_quality_1_desc", "Key": "task_lordequip_quality_1_desc"}, "9049": {"Id": 9049, "StringId": "task_lordequip_quality_2_desc", "Key": "task_lordequip_quality_2_desc"}, "9050": {"Id": 9050, "StringId": "task_lordequip_quality_3_desc", "Key": "task_lordequip_quality_3_desc"}, "9051": {"Id": 9051, "StringId": "task_lordequip_quality_4_desc", "Key": "task_lordequip_quality_4_desc"}, "9052": {"Id": 9052, "StringId": "task_lordequip_quality_5_desc", "Key": "task_lordequip_quality_5_desc"}, "9053": {"Id": 9053, "StringId": "task_lordequip_quality_6_desc", "Key": "task_lordequip_quality_6_desc"}, "9054": {"Id": 9054, "StringId": "task_lordequip_quality_7_desc", "Key": "task_lordequip_quality_7_desc"}, "9055": {"Id": 9055, "StringId": "task_weeklybox_desc", "Key": "task_weeklybox_desc"}, "9056": {"Id": 9056, "StringId": "task_dungeon_coin_level_desc", "Key": "task_dungeon_coin_level_desc"}, "9057": {"Id": 9057, "StringId": "task_dungeon_gene_level_desc", "Key": "task_dungeon_gene_level_desc"}, "9058": {"Id": 9058, "StringId": "task_dungeon_lordequip_level_desc", "Key": "task_dungeon_lordequip_level_desc"}, "9059": {"Id": 9059, "StringId": "task_dungeon_sunshine_level_desc", "Key": "task_dungeon_sunshine_level_desc"}, "9060": {"Id": 9060, "StringId": "task_lab_level_desc", "Key": "task_lab_level_desc"}, "9061": {"Id": 9061, "StringId": "task_dungeon_level_desc", "Key": "task_dungeon_level_desc"}, "9062": {"Id": 9062, "StringId": "task_hero_quality_1_desc", "Key": "task_hero_quality_1_desc"}, "9063": {"Id": 9063, "StringId": "task_hero_quality_2_desc", "Key": "task_hero_quality_2_desc"}, "9064": {"Id": 9064, "StringId": "task_hero_quality_3_desc", "Key": "task_hero_quality_3_desc"}, "10001": {"Id": 10001, "StringId": "guild_rank_1_name", "Key": "guild_rank_1_name"}, "10002": {"Id": 10002, "StringId": "guild_rank_2_name", "Key": "guild_rank_2_name"}, "10003": {"Id": 10003, "StringId": "guild_rank_3_name", "Key": "guild_rank_3_name"}, "10004": {"Id": 10004, "StringId": "guild_rank_4_name", "Key": "guild_rank_4_name"}, "10005": {"Id": 10005, "StringId": "guild_rank_5_name", "Key": "guild_rank_5_name"}, "10006": {"Id": 10006, "StringId": "guild_rank_1_title", "Key": "guild_rank_1_title"}, "10007": {"Id": 10007, "StringId": "guild_rank_2_title", "Key": "guild_rank_2_title"}, "10008": {"Id": 10008, "StringId": "guild_rank_3_title", "Key": "guild_rank_3_title"}, "10009": {"Id": 10009, "StringId": "guild_rank_4_title", "Key": "guild_rank_4_title"}, "10010": {"Id": 10010, "StringId": "guild_rank_5_title", "Key": "guild_rank_5_title"}, "10011": {"Id": 10011, "StringId": "ChangeGuildFlag_name", "Key": "ChangeGuildFlag_name"}, "10012": {"Id": 10012, "StringId": "ChangeGuildShortName_name", "Key": "ChangeGuildShortName_name"}, "10013": {"Id": 10013, "StringId": "ChangeGuildName_name", "Key": "ChangeGuildName_name"}, "10014": {"Id": 10014, "StringId": "EditNotice_name", "Key": "EditNotice_name"}, "10015": {"Id": 10015, "StringId": "ChangeRecruitSetting_name", "Key": "ChangeRecruitSetting_name"}, "10016": {"Id": 10016, "StringId": "ManageJoinApplication_name", "Key": "ManageJoinApplication_name"}, "10017": {"Id": 10017, "StringId": "DisbandGuild_name", "Key": "DisbandGuild_name"}, "10018": {"Id": 10018, "StringId": "TransferPresident_name", "Key": "TransferPresident_name"}, "10019": {"Id": 10019, "StringId": "RemoveMember_name", "Key": "RemoveMember_name"}, "10020": {"Id": 10020, "StringId": "ChangeMemberRank_name", "Key": "ChangeMemberRank_name"}, "10021": {"Id": 10021, "StringId": "ViewMemberInfo_name", "Key": "ViewMemberInfo_name"}, "10022": {"Id": 10022, "StringId": "ChangeRankTitle_name", "Key": "ChangeRankTitle_name"}, "10023": {"Id": 10023, "StringId": "ChangeLanguage_name", "Key": "ChangeLanguage_name"}, "10024": {"Id": 10024, "StringId": "ExitGuild_name", "Key": "ExitGuild_name"}, "10025": {"Id": 10025, "StringId": "function_guild_notice_name", "Key": "function_guild_notice_name"}, "10026": {"Id": 10026, "StringId": "function_guild_shop_name", "Key": "function_guild_shop_name"}, "10027": {"Id": 10027, "StringId": "function_guild_task_name", "Key": "function_guild_task_name"}, "10028": {"Id": 10028, "StringId": "function_guild_rank_name", "Key": "function_guild_rank_name"}, "10029": {"Id": 10029, "StringId": "function_guild_set_name", "Key": "function_guild_set_name"}, "10030": {"Id": 10030, "StringId": "function_guild_member_name", "Key": "function_guild_member_name"}, "11001": {"Id": 11001, "StringId": "mall_diamond_shop_name", "Key": "mall_diamond_shop_name"}, "11002": {"Id": 11002, "StringId": "iap_package_diamond_first_double_desc", "Key": "iap_package_diamond_first_double_desc"}, "11003": {"Id": 11003, "StringId": "guild_shop_name", "Key": "guild_shop_name"}, "11004": {"Id": 11004, "StringId": "arena_shop_name", "Key": "arena_shop_name"}, "11005": {"Id": 11005, "StringId": "turn_table_1_title", "Key": "turn_table_1_title"}, "11006": {"Id": 11006, "StringId": "turn_pack_1_title", "Key": "turn_pack_1_title"}, "11007": {"Id": 11007, "StringId": "turn_pack_2_title", "Key": "turn_pack_2_title"}, "11008": {"Id": 11008, "StringId": "turn_pack_3_title", "Key": "turn_pack_3_title"}, "11009": {"Id": 11009, "StringId": "turn_pack_4_title", "Key": "turn_pack_4_title"}, "11010": {"Id": 11010, "StringId": "turn_pack_5_title", "Key": "turn_pack_5_title"}, "11011": {"Id": 11011, "StringId": "turn_pack_6_title", "Key": "turn_pack_6_title"}, "11012": {"Id": 11012, "StringId": "iap_1st_charge_title", "Key": "iap_1st_charge_title"}, "11013": {"Id": 11013, "StringId": "iap_1st_charge_1_desc", "Key": "iap_1st_charge_1_desc"}, "11014": {"Id": 11014, "StringId": "iap_1st_charge_2_desc", "Key": "iap_1st_charge_2_desc"}, "11015": {"Id": 11015, "StringId": "iap_1st_charge_3_desc", "Key": "iap_1st_charge_3_desc"}, "11016": {"Id": 11016, "StringId": "iap_1st_charge_tag_1", "Key": "iap_1st_charge_tag_1"}, "11017": {"Id": 11017, "StringId": "iap_1st_charge_tag_2", "Key": "iap_1st_charge_tag_2"}, "11018": {"Id": 11018, "StringId": "iap_1st_charge_tag_3", "Key": "iap_1st_charge_tag_3"}, "11019": {"Id": 11019, "StringId": "ui_day_x", "Key": "ui_day_x"}, "11020": {"Id": 11020, "StringId": "month_card_1_title", "Key": "month_card_1_title"}, "11021": {"Id": 11021, "StringId": "month_card_2_title", "Key": "month_card_2_title"}, "11030": {"Id": 11030, "StringId": "level_fund_title", "Key": "level_fund_title"}, "11031": {"Id": 11031, "StringId": "level_fund_desc", "Key": "level_fund_desc"}, "11032": {"Id": 11032, "StringId": "sign_1_title", "Key": "sign_1_title"}, "11033": {"Id": 11033, "StringId": "sign_1_desc", "Key": "sign_1_desc"}, "11034": {"Id": 11034, "StringId": "sign_day_desc", "Key": "sign_day_desc"}, "11035": {"Id": 11035, "StringId": "regular_bp_1_name", "Key": "regular_bp_1_name"}, "11036": {"Id": 11036, "StringId": "regular_bp_1_desc", "Key": "regular_bp_1_desc"}, "11037": {"Id": 11037, "StringId": "ad_free_lt_title", "Key": "ad_free_ad_lt_title"}, "11038": {"Id": 11038, "StringId": "ad_free_lt_desc", "Key": "ad_free_ad_lt_desc"}, "11039": {"Id": 11039, "StringId": "battle_2X_lt_title", "Key": "battle_2X_lt_title"}, "11040": {"Id": 11040, "StringId": "battle_2X_lt_desc", "Key": "battle_2X_lt_desc"}, "11041": {"Id": 11041, "StringId": "daily_sale_title", "Key": "daily_sale_title"}, "11042": {"Id": 11042, "StringId": "daily_sale_desc", "Key": "daily_sale_desc"}, "11043": {"Id": 11043, "StringId": "daily_sale_change_hero_btn", "Key": "daily_sale_change_hero_btn"}, "11044": {"Id": 11044, "StringId": "daily_sale_purchase_all_desc", "Key": "daily_sale_purchase_all_desc"}, "11045": {"Id": 11045, "StringId": "daily_sale_purchase_limit_desc", "Key": "daily_sale_purchase_limit_desc"}, "11046": {"Id": 11046, "StringId": "daily_sale_change_hero_tips", "Key": "daily_sale_change_hero_tips"}, "11055": {"Id": 11055, "StringId": "trigger_pack_level_pass_title", "Key": "trigger_pack_level_pass_title"}, "11056": {"Id": 11056, "StringId": "trigger_pack_level_pass_desc", "Key": "trigger_pack_level_pass_desc"}, "11057": {"Id": 11057, "StringId": "trigger_pack_hero_summon_title", "Key": "trigger_pack_hero_summon_title"}, "11058": {"Id": 11058, "StringId": "trigger_pack_hero_summon_desc", "Key": "trigger_pack_hero_summon_desc"}, "11059": {"Id": 11059, "StringId": "trigger_pack_gem_draw_title", "Key": "trigger_pack_gem_draw_title"}, "11060": {"Id": 11060, "StringId": "trigger_pack_gem_draw_desc", "Key": "trigger_pack_gem_draw_desc"}, "11061": {"Id": 11061, "StringId": "turn_table_score_reward_title", "Key": "turn_table_score_reward_title"}, "11062": {"Id": 11062, "StringId": "turn_table_draw_1_btn_name", "Key": "turn_table_draw_1_btn_name"}, "11063": {"Id": 11063, "StringId": "turn_table_draw_10_btn_name", "Key": "turn_table_draw_10_btn_name"}, "11064": {"Id": 11064, "StringId": "month_card_daily_reward_tips", "Key": "month_card_daily_reward_tips"}, "11065": {"Id": 11065, "StringId": "deal_month_card_tag", "Key": "deal_month_card_tag"}, "11066": {"Id": 11066, "StringId": "mall_regular_pack_tag", "Key": "mall_regular_pack_tag"}, "11067": {"Id": 11067, "StringId": "mail_name", "Key": "mail_name"}, "11068": {"Id": 11068, "StringId": "shop_name", "Key": "shop_name"}, "11069": {"Id": 11069, "StringId": "ui_legendary_plants", "Key": "ui_legendary_plants"}, "11070": {"Id": 11070, "StringId": "ui_daily_sign_in_gift", "Key": "ui_daily_sign_in_gift"}, "11071": {"Id": 11071, "StringId": "ui_inaugural_festivities_title", "Key": "ui_inaugural_festivities_title"}, "11072": {"Id": 11072, "StringId": "ui_event_end_tips", "Key": "ui_event_end_tips"}, "11073": {"Id": 11073, "StringId": "ui_inaugural_festivities_desc", "Key": "ui_inaugural_festivities_desc"}, "11074": {"Id": 11074, "StringId": "ui_can_not_purchase_all_tips", "Key": "ui_can_not_purchase_all_tips"}, "11075": {"Id": 11075, "StringId": "ui_hero_star_full_warning", "Key": "ui_hero_star_full_warning"}, "11076": {"Id": 11076, "StringId": "ui_after_buy_desc", "Key": "ui_after_buy_desc"}, "11077": {"Id": 11077, "StringId": "ui_gift_time_out_tips", "Key": "ui_gift_time_out_tips"}, "11078": {"Id": 11078, "StringId": "ui_unlock_iap_reward", "Key": "ui_unlock_iap_reward"}, "11079": {"Id": 11079, "StringId": "deal_name", "Key": "deal_name"}, "11080": {"Id": 11080, "StringId": "ui_bp_buy", "Key": "ui_bp_buy"}, "11081": {"Id": 11081, "StringId": "level_fund_number", "Key": "level_fund_number"}, "11082": {"Id": 11082, "StringId": "life_card_1_title", "Key": "life_card_1_title"}, "11083": {"Id": 11083, "StringId": "mall_name", "Key": "mall_name"}, "12001": {"Id": 12001, "StringId": "help_guild_main_title", "Key": "help_guild_main_title"}, "12002": {"Id": 12002, "StringId": "help_guild_main_tag_1_name", "Key": "help_guild_main_tag_1_name"}, "12003": {"Id": 12003, "StringId": "help_guild_main_tag_2_name", "Key": "help_guild_main_tag_2_name"}, "12004": {"Id": 12004, "StringId": "help_guild_main_tag_3_name", "Key": "help_guild_main_tag_3_name"}, "12005": {"Id": 12005, "StringId": "help_guild_main_tag_4_name", "Key": "help_guild_main_tag_4_name"}, "12006": {"Id": 12006, "StringId": "help_guild_main_tag_1_content_1", "Key": "help_guild_main_tag_1_content_1"}, "12007": {"Id": 12007, "StringId": "help_guild_main_tag_1_content_2", "Key": "help_guild_main_tag_1_content_2"}, "12008": {"Id": 12008, "StringId": "help_guild_main_tag_1_content_3", "Key": "help_guild_main_tag_1_content_3"}, "12009": {"Id": 12009, "StringId": "help_guild_main_tag_1_content_4", "Key": "help_guild_main_tag_1_content_4"}, "12010": {"Id": 12010, "StringId": "help_guild_main_tag_2_content_1", "Key": "help_guild_main_tag_2_content_1"}, "12011": {"Id": 12011, "StringId": "help_guild_main_tag_2_content_2", "Key": "help_guild_main_tag_2_content_2"}, "12012": {"Id": 12012, "StringId": "help_guild_main_tag_2_content_3", "Key": "help_guild_main_tag_2_content_3"}, "12013": {"Id": 12013, "StringId": "help_guild_main_tag_2_content_4", "Key": "help_guild_main_tag_2_content_4"}, "12014": {"Id": 12014, "StringId": "help_guild_main_tag_3_content_1", "Key": "help_guild_main_tag_3_content_1"}, "12015": {"Id": 12015, "StringId": "help_guild_main_tag_3_content_2", "Key": "help_guild_main_tag_3_content_2"}, "12016": {"Id": 12016, "StringId": "help_guild_main_tag_3_content_3", "Key": "help_guild_main_tag_3_content_3"}, "12017": {"Id": 12017, "StringId": "help_guild_main_tag_4_content_1", "Key": "help_guild_main_tag_4_content_1"}, "12018": {"Id": 12018, "StringId": "help_guild_main_tag_4_content_2", "Key": "help_guild_main_tag_4_content_2"}, "12019": {"Id": 12019, "StringId": "help_guild_main_tag_4_content_3", "Key": "help_guild_main_tag_4_content_3"}, "12020": {"Id": 12020, "StringId": "help_arena_title", "Key": "help_arena_title"}, "12021": {"Id": 12021, "StringId": "help_arena_tag_1_name", "Key": "help_arena_tag_1_name"}, "12022": {"Id": 12022, "StringId": "help_arena_tag_2_name", "Key": "help_arena_tag_2_name"}, "12023": {"Id": 12023, "StringId": "help_arena_tag_3_name", "Key": "help_arena_tag_3_name"}, "12024": {"Id": 12024, "StringId": "help_arena_tag_4_name", "Key": "help_arena_tag_4_name"}, "12025": {"Id": 12025, "StringId": "help_arena_tag_5_name", "Key": "help_arena_tag_5_name"}, "12026": {"Id": 12026, "StringId": "help_arena_tag_6_name", "Key": "help_arena_tag_6_name"}, "12027": {"Id": 12027, "StringId": "help_arena_tag_1_content_1", "Key": "help_arena_tag_1_content_1"}, "12028": {"Id": 12028, "StringId": "help_arena_tag_2_content_1", "Key": "help_arena_tag_2_content_1"}, "12029": {"Id": 12029, "StringId": "help_arena_tag_2_content_2", "Key": "help_arena_tag_2_content_2"}, "12030": {"Id": 12030, "StringId": "help_arena_tag_3_content_1", "Key": "help_arena_tag_3_content_1"}, "12031": {"Id": 12031, "StringId": "help_arena_tag_3_content_2", "Key": "help_arena_tag_3_content_2"}, "12032": {"Id": 12032, "StringId": "help_arena_tag_3_content_3", "Key": "help_arena_tag_3_content_3"}, "12033": {"Id": 12033, "StringId": "help_arena_tag_4_content_1", "Key": "help_arena_tag_4_content_1"}, "12034": {"Id": 12034, "StringId": "help_arena_tag_4_content_2", "Key": "help_arena_tag_4_content_2"}, "12035": {"Id": 12035, "StringId": "help_arena_tag_4_content_3", "Key": "help_arena_tag_4_content_3"}, "12036": {"Id": 12036, "StringId": "help_arena_tag_5_content_1", "Key": "help_arena_tag_5_content_1"}, "12037": {"Id": 12037, "StringId": "help_arena_tag_5_content_2", "Key": "help_arena_tag_5_content_2"}, "12038": {"Id": 12038, "StringId": "help_arena_tag_5_content_3", "Key": "help_arena_tag_5_content_3"}, "12039": {"Id": 12039, "StringId": "help_arena_tag_6_content_1", "Key": "help_arena_tag_6_content_1"}, "12040": {"Id": 12040, "StringId": "help_arena_tag_6_content_2", "Key": "help_arena_tag_6_content_2"}, "12041": {"Id": 12041, "StringId": "help_arena_tag_6_content_3", "Key": "help_arena_tag_6_content_3"}, "12042": {"Id": 12042, "StringId": "help_tower_title", "Key": "help_tower_title"}, "12043": {"Id": 12043, "StringId": "help_tower_content_1", "Key": "help_tower_content_1"}, "12044": {"Id": 12044, "StringId": "help_tower_content_2", "Key": "help_tower_content_2"}, "12045": {"Id": 12045, "StringId": "help_tower_content_3", "Key": "help_tower_content_3"}, "12046": {"Id": 12046, "StringId": "help_tower_content_4", "Key": "help_tower_content_4"}, "12047": {"Id": 12047, "StringId": "help_daily_sale", "Key": "help_daily_sale"}, "12048": {"Id": 12048, "StringId": "help_daily_sale_tag_1_name", "Key": "help_daily_sale_tag_1_name"}, "12049": {"Id": 12049, "StringId": "help_daily_sale_tag_2_name", "Key": "help_daily_sale_tag_2_name"}, "12050": {"Id": 12050, "StringId": "help_daily_sale_tag_1_content_1", "Key": "help_daily_sale_tag_1_content_1"}, "12051": {"Id": 12051, "StringId": "help_daily_sale_tag_1_content_2", "Key": "help_daily_sale_tag_1_content_2"}, "12052": {"Id": 12052, "StringId": "help_daily_sale_tag_1_content_3", "Key": "help_daily_sale_tag_1_content_3"}, "12053": {"Id": 12053, "StringId": "help_daily_sale_tag_2_content_1", "Key": "help_daily_sale_tag_2_content_1"}, "12054": {"Id": 12054, "StringId": "help_daily_sale_tag_2_content_2", "Key": "help_daily_sale_tag_2_content_2"}, "12055": {"Id": 12055, "StringId": "help_regular_bp_1_title", "Key": "help_regular_bp_1_title"}, "12056": {"Id": 12056, "StringId": "help_regular_bp_1_content_1", "Key": "help_regular_bp_1_content_1"}, "12057": {"Id": 12057, "StringId": "help_regular_bp_1_content_2", "Key": "help_regular_bp_1_content_2"}, "12058": {"Id": 12058, "StringId": "help_regular_bp_1_content_3", "Key": "help_regular_bp_1_content_3"}, "12059": {"Id": 12059, "StringId": "help_regular_bp_1_content_4", "Key": "help_regular_bp_1_content_4"}, "12060": {"Id": 12060, "StringId": "help_ad_free_title", "Key": "help_ad_free_title"}, "12061": {"Id": 12061, "StringId": "help_ad_free_content_1", "Key": "help_ad_free_content_1"}, "12062": {"Id": 12062, "StringId": "help_ad_free_content_2", "Key": "help_ad_free_content_2"}, "12063": {"Id": 12063, "StringId": "help_fund_1_title", "Key": "help_level_fund_title"}, "12064": {"Id": 12064, "StringId": "help_fund_1_content_1", "Key": "help_level_fund_content_1"}, "12065": {"Id": 12065, "StringId": "help_fund_1_content_2", "Key": "help_level_fund_content_2"}, "12066": {"Id": 12066, "StringId": "help_fund_1_content_3", "Key": "help_level_fund_content_3"}, "12067": {"Id": 12067, "StringId": "help_turn_table_1_title", "Key": "help_turn_table_1_title"}, "12068": {"Id": 12068, "StringId": "help_turn_table_1_content_1", "Key": "help_turn_table_1_content_1"}, "12069": {"Id": 12069, "StringId": "help_turn_table_1_content_2", "Key": "help_turn_table_1_content_2"}, "12070": {"Id": 12070, "StringId": "help_turn_table_1_content_3", "Key": "help_turn_table_1_content_3"}, "12071": {"Id": 12071, "StringId": "help_turn_table_1_content_4", "Key": "help_turn_table_1_content_4"}, "12072": {"Id": 12072, "StringId": "help_turn_table_1_content_5", "Key": "help_turn_table_1_content_5"}, "13001": {"Id": 13001, "StringId": "mail_month_card_1_reissue_name", "Key": "mail_month_card_1_reissue_name"}, "13002": {"Id": 13002, "StringId": "mail_month_card_2_reissue_name", "Key": "mail_month_card_2_reissue_name"}, "13003": {"Id": 13003, "StringId": "mail_regular_bp_reissue_name", "Key": "mail_regular_bp_reissue_name"}, "13004": {"Id": 13004, "StringId": "mail_turn_table_reissue_name", "Key": "mail_turn_table_reissue_name"}, "13005": {"Id": 13005, "StringId": "mail_sign_name", "Key": "mail_sign_name"}, "13006": {"Id": 13006, "StringId": "mail_month_card_1_reissue_content", "Key": "mail_month_card_1_reissue_content"}, "13007": {"Id": 13007, "StringId": "mail_month_card_2_reissue_content", "Key": "mail_month_card_2_reissue_content"}, "13008": {"Id": 13008, "StringId": "mail_regular_bp_reissue_content", "Key": "mail_regular_bp_reissue_content"}, "13009": {"Id": 13009, "StringId": "mail_turn_table_reissue_content", "Key": "mail_turn_table_reissue_content"}, "13010": {"Id": 13010, "StringId": "mail_sign_content", "Key": "mail_sign_content"}, "13011": {"Id": 13011, "StringId": "mail_energy_reissure_name", "Key": "mail_energy_reissure_name"}, "13012": {"Id": 13012, "StringId": "mail_energy_reissue_content", "Key": "mail_energy_reissue_content"}, "13013": {"Id": 13013, "StringId": "mail_welcome_to_china_ka<PERSON><PERSON><PERSON>_name", "Key": "mail_welcome_to_china_ka<PERSON><PERSON><PERSON>_name"}, "13014": {"Id": 13014, "StringId": "mail_welcome_to_china_kaoshantun_content", "Key": "mail_welcome_to_china_kaoshantun_content"}, "13015": {"Id": 13015, "StringId": "mail_sevendaytask_reissue_name", "Key": "mail_sevendaytask_reissue_name"}, "13016": {"Id": 13016, "StringId": "mail_sevendaytask_reissue_content", "Key": "mail_sevendaytask_reissue_content"}, "13017": {"Id": 13017, "StringId": "mail_disconnect_reissue_name", "Key": "mail_disconnect_reissue_name"}, "13018": {"Id": 13018, "StringId": "mail_disconnect_reissue_content", "Key": "mail_disconnect_reissue_content"}, "13019": {"Id": 13019, "StringId": "mail_sweep_reissue_name", "Key": "mail_sweep_reissue_name"}, "13020": {"Id": 13020, "StringId": "mail_sweep_reissue_content", "Key": "mail_sweep_reissue_content"}, "95001": {"Id": 95001, "StringId": "function_level_fund", "Key": "function_level_fund"}, "95002": {"Id": 95002, "StringId": "function_month_card", "Key": "function_month_card"}, "95003": {"Id": 95003, "StringId": "benefit_sunshine_dungeon_daily_challenge_limit_up_name", "Key": "benefit_sunshine_dungeon_daily_challenge_limit_up_name"}, "95004": {"Id": 95004, "StringId": "benefit_gene_dungeon_daily_challenge_limit_up_name", "Key": "benefit_gene_dungeon_daily_challenge_limit_up_name"}, "95005": {"Id": 95005, "StringId": "benefit_equip_dungeon_daily_challenge_limit_up_name", "Key": "benefit_equip_dungeon_daily_challenge_limit_up_name"}, "95006": {"Id": 95006, "StringId": "benefit_coin_dungeon_daily_challenge_limit_up_name", "Key": "benefit_coin_dungeon_daily_challenge_limit_up_name"}, "95007": {"Id": 95007, "StringId": "benefit_afk_reward_up_name", "Key": "benefit_afk_reward_up_name"}, "95008": {"Id": 95008, "StringId": "benefit_afk_sweep_unlimit_name", "Key": "benefit_afk_sweep_unlimit_name"}, "95009": {"Id": 95009, "StringId": "benefit_energy_mail_reissue_name", "Key": "benefit_energy_mail_reissue_name"}, "95010": {"Id": 95010, "StringId": "benefit_rouge_refresh_free_name", "Key": "benefit_rouge_refresh_free_name"}, "95011": {"Id": 95011, "StringId": "benefit_afk_sweep_bet_name", "Key": "benefit_afk_sweep_bet_name"}, "95012": {"Id": 95012, "StringId": "benefit_battle_2X_lt_name", "Key": "benefit_battle_2X_lt_name"}, "95013": {"Id": 95013, "StringId": "regular_pack_hero_draw_title", "Key": "regular_pack_hero_draw_title"}, "95014": {"Id": 95014, "StringId": "regular_pack_gem_draw_title", "Key": "regular_pack_gem_draw_title"}, "95015": {"Id": 95015, "StringId": "regular_pack_hero_3_0_title", "Key": "regular_pack_hero_3_0_title"}, "95016": {"Id": 95016, "StringId": "regular_pack_coin_title", "Key": "regular_pack_coin_title"}, "95017": {"Id": 95017, "StringId": "regular_pack_sunflower_title", "Key": "regular_pack_sunflower_title"}, "95018": {"Id": 95018, "StringId": "regular_pack_lord_equip_up_title", "Key": "regular_pack_lord_equip_up_title"}, "95019": {"Id": 95019, "StringId": "regular_pack_hero_skill_up_title", "Key": "regular_pack_hero_skill_up_title"}, "95020": {"Id": 95020, "StringId": "month_card_1_desc_1", "Key": "month_card_1_desc_1"}, "95021": {"Id": 95021, "StringId": "month_card_1_desc_2", "Key": "month_card_1_desc_2"}, "95022": {"Id": 95022, "StringId": "month_card_1_desc_3", "Key": "month_card_1_desc_3"}, "95023": {"Id": 95023, "StringId": "month_card_1_desc_4", "Key": "month_card_1_desc_4"}, "95024": {"Id": 95024, "StringId": "month_card_1_desc_5", "Key": "month_card_1_desc_5"}, "95025": {"Id": 95025, "StringId": "month_card_2_desc_1", "Key": "month_card_2_desc_1"}, "95026": {"Id": 95026, "StringId": "month_card_2_desc_2", "Key": "month_card_2_desc_2"}, "95027": {"Id": 95027, "StringId": "month_card_2_desc_3", "Key": "month_card_2_desc_3"}, "95028": {"Id": 95028, "StringId": "month_card_2_desc_4", "Key": "month_card_2_desc_4"}, "95029": {"Id": 95029, "StringId": "month_card_2_desc_5", "Key": "month_card_2_desc_5"}, "95030": {"Id": 95030, "StringId": "month_card_2_desc_6", "Key": "month_card_2_desc_6"}, "95031": {"Id": 95031, "StringId": "month_card_2_desc_7", "Key": "month_card_2_desc_7"}, "95032": {"Id": 95032, "StringId": "level_fund_tag", "Key": "level_fund_tag"}, "95033": {"Id": 95033, "StringId": "dungeon_coin_fund_tag", "Key": "dungeon_coin_fund_tag"}, "95034": {"Id": 95034, "StringId": "help_dungeon_coin_fund_title", "Key": "help_dungeon_coin_fund_title"}, "95035": {"Id": 95035, "StringId": "help_dungeon_coin_fund_content_1", "Key": "help_dungeon_coin_fund_content_1"}, "95036": {"Id": 95036, "StringId": "help_dungeon_coin_fund_content_2", "Key": "help_dungeon_coin_fund_content_2"}, "95037": {"Id": 95037, "StringId": "help_dungeon_coin_fund_content_3", "Key": "help_dungeon_coin_fund_content_3"}, "95038": {"Id": 95038, "StringId": "dungeon_gene_fund_tag", "Key": "dungeon_gene_fund_tag"}, "95039": {"Id": 95039, "StringId": "help_dungeon_gene_fund_title", "Key": "help_dungeon_gene_fund_title"}, "95040": {"Id": 95040, "StringId": "help_dungeon_gene_fund_content_1", "Key": "help_dungeon_gene_fund_content_1"}, "95041": {"Id": 95041, "StringId": "help_dungeon_gene_fund_content_2", "Key": "help_dungeon_gene_fund_content_2"}, "95042": {"Id": 95042, "StringId": "help_dungeon_gene_fund_content_3", "Key": "help_dungeon_gene_fund_content_3"}, "95043": {"Id": 95043, "StringId": "dungeon_lord_equip_fund_tag", "Key": "dungeon_lord_equip_fund_tag"}, "95044": {"Id": 95044, "StringId": "help_dungeon_lord_equip_fund_title", "Key": "help_dungeon_lord_equip_fund_title"}, "95045": {"Id": 95045, "StringId": "help_dungeon_lord_equip_fund_content_1", "Key": "help_dungeon_lord_equip_fund_content_1"}, "95046": {"Id": 95046, "StringId": "help_dungeon_lord_equip_fund_content_2", "Key": "help_dungeon_lord_equip_fund_content_2"}, "95047": {"Id": 95047, "StringId": "help_dungeon_lord_equip_fund_content_3", "Key": "help_dungeon_lord_equip_fund_content_3"}, "95048": {"Id": 95048, "StringId": "dungeon_sunshine_fund_tag", "Key": "dungeon_sunshine_fund_tag"}, "95049": {"Id": 95049, "StringId": "help_dungeon_sunshine_fund_title", "Key": "help_dungeon_sunshine_fund_title"}, "95050": {"Id": 95050, "StringId": "help_dungeon_sunshine_fund_content_1", "Key": "help_dungeon_sunshine_fund_content_1"}, "95051": {"Id": 95051, "StringId": "help_dungeon_sunshine_fund_content_2", "Key": "help_dungeon_sunshine_fund_content_2"}, "95052": {"Id": 95052, "StringId": "help_dungeon_sunshine_fund_content_3", "Key": "help_dungeon_sunshine_fund_content_3"}}