package model

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"context"
	"github.com/sasha-s/go-deadlock"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/locker"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	_ "gitlab-ee.funplus.io/backend-platform/zplus-go/timestamp"
	"sync"
	_ "time"
)

func init() {
	orm.RegisterModel[*TaskCounter](nil)
}

type TaskCounter struct {
	mu     deadlock.RWMutex
	locker sync.RWMutex
	minirpc.TaskCounterRecord
}

func (h *TaskCounter) Locker() locker.RWLocker {
	return nil
}

func (h *TaskCounter) LockPriority() int {
	return int(h.Uid())
}

func GetTaskCounter(ctx context.Context, uid int64, typeId int32) (*TaskCounter, error) {
	d, err := orm.Get[*TaskCounter](ctx, uid, typeId)
	if err != nil {
		return nil, kdmerr.SysDBError.CastErrorf("db error")
	}
	if d == nil {
		d, err = orm.Create[*TaskCounter](ctx, &minirpc.TaskCounter{
			Uid:          uid,
			CounterId:    typeId,
			CounterValue: map[string]int32{},
		})
	}
	return d, err
}

func GetTaskCounterValue(ctx context.Context, uid int64, typeId int32, key string) int32 {
	taskCounter, err := GetTaskCounter(ctx, uid, typeId)
	if err != nil {
		return 0
	}
	if key == "" {
		return taskCounter.CounterValue()["total"]
	}
	return taskCounter.CounterValue()[key]
}

func SetTaskCounter(ctx context.Context, uid int64, typeId int32, key string, value int32) {
	taskCounter, err := GetTaskCounter(ctx, uid, typeId)
	if err != nil {
		return
	}
	countValue := taskCounter.CounterValue()
	if key != "" {
		countValue[key] = value
	}
	if key != "total" {
		countValue["total"] += value
	}
	taskCounter.SetCounterValue(ctx, countValue)
}

func (h *TaskCounter) CanSyncChange(orm.FieldSet) bool {
	return false
}
