// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type DungeonCoinLevelTableCfg struct {
	Id          int32                `json:"Id"`          // Id
	Dungeon     int32                `json:"Dungeon"`     // 副本类型
	DungeonRef  *DungeonTypeTableCfg `json:"-"`           // 副本类型
	Level       int32                `json:"Level"`       // 等级
	ChooseNum   int32                `json:"ChooseNum"`   // 开场选卡次数
	IsMax       bool                 `json:"IsMax"`       // 是否最大关卡
	FirstReward *RewardKVS           `json:"FirstReward"` // 首通奖励
	Reward      []*RewardKVS         `json:"Reward"`      // 奖励
}

func NewDungeonCoinLevelTableCfg() *DungeonCoinLevelTableCfg {
	return &DungeonCoinLevelTableCfg{
		Id:          0,
		Dungeon:     0,
		DungeonRef:  nil,
		Level:       0,
		ChooseNum:   0,
		IsMax:       false,
		FirstReward: NewRewardKVS(),
		Reward:      []*RewardKVS{},
	}
}

type DungeonCoinLevelTable struct {
	records  map[int32]*DungeonCoinLevelTableCfg
	localIds map[int32]struct{}
}

func NewDungeonCoinLevelTable() *DungeonCoinLevelTable {
	return &DungeonCoinLevelTable{
		records:  map[int32]*DungeonCoinLevelTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *DungeonCoinLevelTable) Get(key int32) *DungeonCoinLevelTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *DungeonCoinLevelTable) GetAll() map[int32]*DungeonCoinLevelTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *DungeonCoinLevelTable) put(key int32, value *DungeonCoinLevelTableCfg, local bool) *DungeonCoinLevelTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *DungeonCoinLevelTable) Range(f func(v *DungeonCoinLevelTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *DungeonCoinLevelTable) Filter(filterFuncs ...func(v *DungeonCoinLevelTableCfg) bool) map[int32]*DungeonCoinLevelTableCfg {
	filtered := map[int32]*DungeonCoinLevelTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *DungeonCoinLevelTable) FilterSlice(filterFuncs ...func(v *DungeonCoinLevelTableCfg) bool) []*DungeonCoinLevelTableCfg {
	filtered := []*DungeonCoinLevelTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *DungeonCoinLevelTable) FilterKeys(filterFuncs ...func(v *DungeonCoinLevelTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *DungeonCoinLevelTable) satisfied(v *DungeonCoinLevelTableCfg, filterFuncs ...func(v *DungeonCoinLevelTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *DungeonCoinLevelTable) setupIndexes() error {
	return nil
}

func (t *DungeonCoinLevelTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *DungeonCoinLevelTableCfg) bindRefs(c *Configs) {
	r.DungeonRef = c.DungeonTypeTable.Get(r.Dungeon)
	r.FirstReward.bindRefs(c)
	for _, e := range r.Reward {
		e.bindRefs(c)
	}
}
