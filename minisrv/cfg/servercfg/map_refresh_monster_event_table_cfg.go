// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type MapRefreshMonsterEventTableCfg struct {
	Id                    int32              `json:"Id"`                    // Id
	Chapters              int32              `json:"Chapters"`              // 章节
	Levels                int32              `json:"Levels"`                // 关卡
	Wave                  int32              `json:"Wave"`                  // 波次
	SpeedRatio            float32            `json:"SpeedRatio"`            // 速度放大系数
	HpRatio               float32            `json:"HpRatio"`               // 血量放大系数
	AtkRatio              float32            `json:"AtkRatio"`              // 攻击放大系数
	DefRatio              float32            `json:"DefRatio"`              // 防御放大系数
	Reversionary          int32              `json:"Reversionary"`          // 承接波次
	RefreshType           MonsterRefreshType `json:"RefreshType"`           // 刷怪类型
	RefreshParamDelayTime []float32          `json:"RefreshParamDelayTime"` // 规则数值(时间区间)秒
	RefreshParamDeathCnt  int32              `json:"RefreshParamDeathCnt"`  // 规则数值(数量)
	MonsterId             []int32            `json:"MonsterId"`             // 刷新怪物
	MonsterIdRef          []*MonsterTableCfg `json:"-"`                     // 刷新怪物
	MonsterCnt            []int32            `json:"MonsterCnt"`            // 怪物数量区间
	Weights               []int32            `json:"Weights"`               // 刷新权重(优先刷新的怪)
	Intervals             []float32          `json:"Intervals"`             // 怪物刷新间隔(秒)
}

func NewMapRefreshMonsterEventTableCfg() *MapRefreshMonsterEventTableCfg {
	return &MapRefreshMonsterEventTableCfg{
		Id:                    0,
		Chapters:              0,
		Levels:                0,
		Wave:                  0,
		SpeedRatio:            0.0,
		HpRatio:               0.0,
		AtkRatio:              0.0,
		DefRatio:              0.0,
		Reversionary:          0,
		RefreshType:           MonsterRefreshType(enumDefaultValue),
		RefreshParamDelayTime: []float32{},
		RefreshParamDeathCnt:  0,
		MonsterId:             []int32{},
		MonsterIdRef:          []*MonsterTableCfg{},
		MonsterCnt:            []int32{},
		Weights:               []int32{},
		Intervals:             []float32{},
	}
}

type MapRefreshMonsterEventTable struct {
	records  map[int32]*MapRefreshMonsterEventTableCfg
	localIds map[int32]struct{}
}

func NewMapRefreshMonsterEventTable() *MapRefreshMonsterEventTable {
	return &MapRefreshMonsterEventTable{
		records:  map[int32]*MapRefreshMonsterEventTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *MapRefreshMonsterEventTable) Get(key int32) *MapRefreshMonsterEventTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *MapRefreshMonsterEventTable) GetAll() map[int32]*MapRefreshMonsterEventTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *MapRefreshMonsterEventTable) put(key int32, value *MapRefreshMonsterEventTableCfg, local bool) *MapRefreshMonsterEventTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *MapRefreshMonsterEventTable) Range(f func(v *MapRefreshMonsterEventTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *MapRefreshMonsterEventTable) Filter(filterFuncs ...func(v *MapRefreshMonsterEventTableCfg) bool) map[int32]*MapRefreshMonsterEventTableCfg {
	filtered := map[int32]*MapRefreshMonsterEventTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *MapRefreshMonsterEventTable) FilterSlice(filterFuncs ...func(v *MapRefreshMonsterEventTableCfg) bool) []*MapRefreshMonsterEventTableCfg {
	filtered := []*MapRefreshMonsterEventTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *MapRefreshMonsterEventTable) FilterKeys(filterFuncs ...func(v *MapRefreshMonsterEventTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *MapRefreshMonsterEventTable) satisfied(v *MapRefreshMonsterEventTableCfg, filterFuncs ...func(v *MapRefreshMonsterEventTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *MapRefreshMonsterEventTable) setupIndexes() error {
	return nil
}

func (t *MapRefreshMonsterEventTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *MapRefreshMonsterEventTableCfg) bindRefs(c *Configs) {
	for _, e := range r.MonsterId {
		cfgoRefRecord := c.MonsterTable.Get(e)
		r.MonsterIdRef = append(r.MonsterIdRef, cfgoRefRecord)
	}
}
