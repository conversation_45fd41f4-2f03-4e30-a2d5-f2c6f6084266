package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/activity/day7quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 领取 7 日任务活动奖励
func (_ KnightsHandlers) CollectDay7RewardHandler(ctx *wctx.Context, req *wrpc.CollectDay7RewardRequest) (*wrpc.CollectDay7RewardReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		ret := []*wrpc.Rewards{}
		for _, questId := range req.QuestId {
			reward, e := day7quest.CollectDay7Reward(ctx, req.UID, questId)
			if e != nil {
				err = e
				continue
			}
			ret = consumable.CombineRewards(ret, reward)
		}
		rawResp = &wrpc.CollectDay7RewardReply{Result: ret}
	})
	return rawResp.(*wrpc.CollectDay7RewardReply), err
}
