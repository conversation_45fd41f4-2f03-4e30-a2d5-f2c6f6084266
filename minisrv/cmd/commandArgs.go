package cmd

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/system"
	"flag"
	"fmt"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/globals"
	"os"
	"path/filepath"
	"time"
)

var DefaultCmdArgs *CmdArgs

type CmdArgs struct {
	Kingdom          int
	LogProd          bool
	LogTag           string
	LogStatsInterval time.Duration
	ProjectPath      string
	GameConfDir      string
	AddrGrpc         string
	AddrPProf        string
	DebugMsg         bool
	ProfileTest      bool
	Deadlock         bool
	BattleDebug      bool
	GuardEnabled     bool
	CpuProf          string
	HotfixPatchDir   string
	AddrHttpcmd      string
	AddrClientHttp   string
}

func ParseArgs() {
	wd, err := filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
		panic(err)
	}
	DefaultCmdArgs = &CmdArgs{}
	flag.IntVar(&DefaultCmdArgs.Kingdom, "kingdom", 0, "kingdom of the kdmsrv server")
	flag.BoolVar(&DefaultCmdArgs.LogProd, "logProd", false, "apply production log settings")
	flag.StringVar(&DefaultCmdArgs.LogTag, "logTag", "", "the tag written along with each log message")
	flag.DurationVar(&DefaultCmdArgs.LogStatsInterval, "logStatsInterval", time.Second*15, "the interval between stats logs")
	flag.StringVar(&DefaultCmdArgs.AddrGrpc, "addrGrpc", ":10010", "address of the grpc service")
	flag.StringVar(&DefaultCmdArgs.AddrPProf, "addrPProf", "", "address of the pprof service")
	flag.BoolVar(&DefaultCmdArgs.DebugMsg, "debugMsg", false, "show debug messages")
	flag.BoolVar(&DefaultCmdArgs.ProfileTest, "test", false, "some conditions are ignored in gvg test")
	flag.BoolVar(&DefaultCmdArgs.Deadlock, "deadlock", false, "open deadlock check")
	flag.BoolVar(&DefaultCmdArgs.BattleDebug, "battleDebug", false, "open battleDebug msg")
	flag.BoolVar(&DefaultCmdArgs.GuardEnabled, "guardEnabled", false, "enable guard info")
	flag.StringVar(&DefaultCmdArgs.ProjectPath, "projectPath", wd, "project path")
	defaultGameConfDir := filepath.Join(wd, "..", "config")
	flag.StringVar(&DefaultCmdArgs.GameConfDir, "gameConfDir", defaultGameConfDir, "game conf dir")
	defaultHotFixPatchDir := filepath.Join(wd, "..", "minisrv/patch")
	flag.StringVar(&DefaultCmdArgs.HotfixPatchDir, "hotfixPatchDir", defaultHotFixPatchDir, "hot fix patch dir")
	flag.StringVar(&DefaultCmdArgs.AddrHttpcmd, "addrHttpcmd", ":10012", "address of the httpcmd service")
	flag.CommandLine.Parse(os.Args[2:])
	if DefaultCmdArgs.Kingdom <= 0 {
		panic(fmt.Errorf("args not with -kingdom"))
	}
	setGlobalArgs()
}

func setGlobalArgs() {
	system.ProjectPath = DefaultCmdArgs.ProjectPath
	system.HotFixPatchDir = DefaultCmdArgs.HotfixPatchDir
	system.ProfileTest = DefaultCmdArgs.ProfileTest
	system.SrvConfDir = filepath.Join(DefaultCmdArgs.ProjectPath, "srvconf", "data")
	system.ConfigRoot = filepath.Join(DefaultCmdArgs.ProjectPath, "..", "config")
	system.KingdomId = int32(DefaultCmdArgs.Kingdom)
	system.BattleDebug = DefaultCmdArgs.BattleDebug
	globals.SetAppName(fmt.Sprintf("kingdom_%d", DefaultCmdArgs.Kingdom))
}
