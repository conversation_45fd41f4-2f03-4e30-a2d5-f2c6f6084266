package constDef

const (
	BENEFIT_HP_UP_PERCENT  int32 = 1
	BENEFIT_ATK_UP_PERCENT int32 = 2
	BENEFIT_DEF_UP_PERCENT int32 = 3

	// 特殊增益效果
	BENEFIT_AD_FREE_LT                             int32 = 69  // 终身免广告
	BENEFIT_BATTLE_2X_LT                           int32 = 70  // 战斗2倍速
	BENEFIT_AFK_SWEEP_UNLIMIT                      int32 = 71  // 挂机奖励无限扫荡
	BENEFIT_ENERGY_LIMIT_UP                        int32 = 72  // 体力上限增加值
	BENEFIT_AFK_TIME_UP                            int32 = 73  // 挂机奖励保存时间增加
	BENEFIT_AFK_SWEEP_BET                          int32 = 74  // 挂机奖励多倍扫荡
	BENEFIT_AFK_REWARD_UP                          int32 = 75  // 挂机奖励增加
	BENEFIT_COIN_DUNGEON_DAILY_CHALLENGE_LIMIT_UP  int32 = 76  // 金币副本每日挑战次数上限增加
	BENEFIT_GENE_DUNGEON_DAILY_CHALLENGE_LIMIT_UP  int32 = 77  // 基因副本每日挑战次数上限增加
	BENEFIT_EQUIP_DUNGEON_DAILY_CHALLENGE_LIMIT_UP int32 = 78  // 装备副本每日挑战次数上限增加
	BENEFIT_ENERGY_MAIL_REISSUE                    int32 = 106 // 体力邮件补发
	BENEFIT_ROUGE_REFRESH_FREE                     int32 = 107 // 肉鸽技能刷新不消耗钻石
	BENEFIT_ROUGE_REFRESH_CNT_UP                   int32 = 108 // 肉鸽技能刷新次数增加
	//benefit_sunshine_dungeon_daily_challenge_limit_up
	BENEFIT_SUNSHINE_DUNGEON_DAILY_CHALLENGE_LIMIT_UP int32 = 127 // 阳光副本每日挑战次数上限增加
)
