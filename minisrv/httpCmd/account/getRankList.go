package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
)

func init() {
	http_post.AddPostHandler("getRankList", getRankList)
}
func getRankList(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		ret := rank.GetStageLevelRankInfoList(ctx, 0, 99)
		for _, v := range ret {
			fmt.Fprintf(w, " rank : %d, level : %d, finish time : %d,uid : %d，name : %s \n", v.<PERSON>, v.<PERSON>, v.FinishTime, v.Uid, v.Name)
		}
	})

}
