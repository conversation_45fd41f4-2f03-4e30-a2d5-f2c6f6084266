package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
	"strconv"
)

func init() {
	http_post.AddPostHandler("delHero", delHero)
}
func delHero(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid")     // 获取查询参数q的值
		heroStr := values.Get("heroId") // 获取查询参数q的值
		if uidStr != "" {
			uid, _ := strconv.ParseInt(uidStr, 10, 64)
			heroId, _ := strconv.ParseInt(heroStr, 10, 64)
			heroModel, _ := model.GetHero(ctx, uid, int32(heroId))
			if heroModel == nil {
				fmt.Fprintf(w, "英雄不存在")
			} else {
				heroModel.Delete(ctx)
				heroSkillS := model.GetHeroSkills(ctx, uid, int32(heroId))
				for _, v := range heroSkillS {
					v.Delete(ctx)
				}
				fmt.Fprintf(w, "英雄已删除")
			}
			return
		}
	})

}
