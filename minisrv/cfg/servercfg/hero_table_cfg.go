// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type HeroTableCfg struct {
	Id                 int32                   `json:"Id"`                // Id
	ItemId             int32                   `json:"ItemId"`            // 对应道具表ID
	ItemIdRef          *ItemTableCfg           `json:"-"`                 // 对应道具表ID
	ItemGeneId         int32                   `json:"ItemGeneId"`        // 对应基因道具ID
	ItemGeneIdRef      *ItemTableCfg           `json:"-"`                 // 对应基因道具ID
	StarUpCostItem     int32                   `json:"StarUpCostItem"`    // 升星消耗道具
	StarUpCostItemRef  *ItemTableCfg           `json:"-"`                 // 升星消耗道具
	GradeUpCostItem    int32                   `json:"GradeUpCostItem"`   // 升品消耗道具
	GradeUpCostItemRef *ItemTableCfg           `json:"-"`                 // 升品消耗道具
	StarPlanID         HeroStarUpPlan          `json:"StarPlanID"`        // 升星方案
	LevelPlanID        HeroLevelUpPlan         `json:"LevelPlanID"`       // 升级方案
	HeroCareer         HeroCareer              `json:"HeroCareer"`        // 英雄职业
	HeroElement        int32                   `json:"HeroElement"`       // 英雄元素
	HeroElementRef     *HeroElementTableCfg    `json:"-"`                 // 英雄元素
	HeroQuality        HeroQuality             `json:"HeroQuality"`       // 英雄品质
	HeroType           HeroType                `json:"HeroType"`          // 英雄类型
	HasAtkFirstCareer  bool                    `json:"HasAtkFirstCareer"` // 是否存在优先攻击职业
	AtkFirstCareer     HeroCareer              `json:"AtkFirstCareer"`    // 优先攻击职业
	SkillRectangle     HeroSkillRangePolygon   `json:"SkillRectangle"`    // 施法形状，没扇形
	CollisionRadius    float32                 `json:"CollisionRadius"`   // 碰撞半径
	HitSkill           int32                   `json:"HitSkill"`          // 主动
	HitSkillRef        *HeroSkillGroupTableCfg `json:"-"`                 // 主动
	NegativeSkill      int32                   `json:"NegativeSkill"`     // 被动
	NegativeSkillRef   *HeroSkillGroupTableCfg `json:"-"`                 // 被动
	GiftSkill          int32                   `json:"GiftSkill"`         // 天赋
	GiftSkillRef       *HeroSkillGroupTableCfg `json:"-"`                 // 天赋
	GiftSkillBenefits  []*BenefitsKVS          `json:"GiftSkillBenefits"` // 天赋技能属性
}

func NewHeroTableCfg() *HeroTableCfg {
	return &HeroTableCfg{
		Id:                 0,
		ItemId:             0,
		ItemIdRef:          nil,
		ItemGeneId:         0,
		ItemGeneIdRef:      nil,
		StarUpCostItem:     0,
		StarUpCostItemRef:  nil,
		GradeUpCostItem:    0,
		GradeUpCostItemRef: nil,
		StarPlanID:         HeroStarUpPlan(enumDefaultValue),
		LevelPlanID:        HeroLevelUpPlan(enumDefaultValue),
		HeroCareer:         HeroCareer(enumDefaultValue),
		HeroElement:        0,
		HeroElementRef:     nil,
		HeroQuality:        HeroQuality(enumDefaultValue),
		HeroType:           HeroType(enumDefaultValue),
		HasAtkFirstCareer:  false,
		AtkFirstCareer:     HeroCareer(enumDefaultValue),
		SkillRectangle:     HeroSkillRangePolygon(enumDefaultValue),
		CollisionRadius:    0.0,
		HitSkill:           0,
		HitSkillRef:        nil,
		NegativeSkill:      0,
		NegativeSkillRef:   nil,
		GiftSkill:          0,
		GiftSkillRef:       nil,
		GiftSkillBenefits:  []*BenefitsKVS{},
	}
}

type HeroTable struct {
	records  map[int32]*HeroTableCfg
	localIds map[int32]struct{}
}

func NewHeroTable() *HeroTable {
	return &HeroTable{
		records:  map[int32]*HeroTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *HeroTable) Get(key int32) *HeroTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *HeroTable) GetAll() map[int32]*HeroTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *HeroTable) put(key int32, value *HeroTableCfg, local bool) *HeroTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *HeroTable) Range(f func(v *HeroTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *HeroTable) Filter(filterFuncs ...func(v *HeroTableCfg) bool) map[int32]*HeroTableCfg {
	filtered := map[int32]*HeroTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *HeroTable) FilterSlice(filterFuncs ...func(v *HeroTableCfg) bool) []*HeroTableCfg {
	filtered := []*HeroTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *HeroTable) FilterKeys(filterFuncs ...func(v *HeroTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *HeroTable) satisfied(v *HeroTableCfg, filterFuncs ...func(v *HeroTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *HeroTable) setupIndexes() error {
	return nil
}

func (t *HeroTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *HeroTableCfg) bindRefs(c *Configs) {
	r.ItemIdRef = c.ItemTable.Get(r.ItemId)
	r.ItemGeneIdRef = c.ItemTable.Get(r.ItemGeneId)
	r.StarUpCostItemRef = c.ItemTable.Get(r.StarUpCostItem)
	r.GradeUpCostItemRef = c.ItemTable.Get(r.GradeUpCostItem)
	r.HeroElementRef = c.HeroElementTable.Get(r.HeroElement)
	r.HitSkillRef = c.HeroSkillGroupTable.Get(r.HitSkill)
	r.NegativeSkillRef = c.HeroSkillGroupTable.Get(r.NegativeSkill)
	r.GiftSkillRef = c.HeroSkillGroupTable.Get(r.GiftSkill)
	for _, e := range r.GiftSkillBenefits {
		e.bindRefs(c)
	}
}
