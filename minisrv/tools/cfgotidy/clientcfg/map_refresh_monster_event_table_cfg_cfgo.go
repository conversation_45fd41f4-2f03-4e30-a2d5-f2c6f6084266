// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"text/template"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t MapRefreshMonsterEventTable) saveJson(dir string) error {
	return t.SaveJsonWithSuffix(dir, "", t.Filter<PERSON>(func(v *MapRefreshMonsterEventTableCfg) bool {
		return true
	}))
}

func (t MapRefreshMonsterEventTable) SaveJsonWithSuffix(dir string, suffix string, cfgs []*MapRefreshMonsterEventTableCfg) error {
	jsonPath := filepath.Join(dir, "MapRefreshMonsterEventTable"+suffix+".json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[MapRefreshMonsterEventTable]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	// sort
	s := cfgoMapRefreshMonsterEventTableSlice{}
	for _, cfgoCfg := range cfgs {
		if _, ok := t.localIds[cfgoCfg.Id]; !ok {
			continue
		}
		s = append(s, cfgoCfg)
	}
	sort.Sort(s)

	w := bufio.NewWriter(f)
	w.WriteString("{\n")
	w.WriteString("\t\"Cells\": ")
	bytes, err := json.MarshalIndent(s, "\t", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	w.WriteString("\n}\n")

	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

type cfgoMapRefreshMonsterEventTableSlice []*MapRefreshMonsterEventTableCfg

func (x cfgoMapRefreshMonsterEventTableSlice) Len() int           { return len(x) }
func (x cfgoMapRefreshMonsterEventTableSlice) Less(i, j int) bool { return x[i].Id < x[j].Id }
func (x cfgoMapRefreshMonsterEventTableSlice) Swap(i, j int)      { x[i], x[j] = x[j], x[i] }

func (t *MapRefreshMonsterEventTable) LoadJsonByPath(jsonPath string, configs *Configs) error {
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[MapRefreshMonsterEventTable]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*MapRefreshMonsterEventTableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		if err = t.Put(k, v); err != nil {
			return fmt.Errorf("[MapRefreshMonsterEventTable]put record failed, path=%s, err=[%w]", jsonPath, err)
		}
	}
	if err = t.setupIndexes(); err != nil {
		return fmt.Errorf("[MapRefreshMonsterEventTable]setup indexes failed, path=%s, err=[%w]", jsonPath, err)
	}
	return nil
}

func (t MapRefreshMonsterEventTable) saveMeta(dir string) error {
	return t.SaveMetaWithSuffix(dir, "")
}

func (t MapRefreshMonsterEventTable) SaveMetaWithSuffix(dir string, suffix string) error {
	fbPlus := false
	td := &TableData{}
	if err := json.Unmarshal(string2Bytes(MapRefreshMonsterEventTableJsonContent), td); err != nil {
		return fmt.Errorf("[client]unmarshal json failed, err=[%w]", err)
	}
	if len(suffix) > 0 {
		td.FileName = td.FileName + suffix
		td.TableName = td.TableName + suffix
	}
	path := filepath.Join(dir, td.FileName+".fbs")
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	defer f.Close()

	tplStr := FlatBufferTableTpl
	if fbPlus {
		tplStr = FlatBufferPlusTableTpl
	}
	tpl, err := template.New("table_schema").Parse(tplStr)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}

	if err = tpl.Execute(f, td); err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	return nil
}

var MapRefreshMonsterEventTableJsonContent string = `{
		"FileName": "MapRefreshMonsterEventTable",
		"ConstTable": false,
		"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
		"TableName": "MapRefreshMonsterEventTable",
		"Key": {
			"FieldName": "Id",
			"FieldType": "int32"
		},
		"Fields": [
			{
				"FieldName": "Chapters",
				"FieldType": "int32"
			},
			{
				"FieldName": "Levels",
				"FieldType": "int32"
			},
			{
				"FieldName": "Wave",
				"FieldType": "int32"
			},
			{
				"FieldName": "SpeedRatio",
				"FieldType": "float"
			},
			{
				"FieldName": "HpRatio",
				"FieldType": "float"
			},
			{
				"FieldName": "AtkRatio",
				"FieldType": "float"
			},
			{
				"FieldName": "DefRatio",
				"FieldType": "float"
			},
			{
				"FieldName": "Reversionary",
				"FieldType": "int32"
			},
			{
				"FieldName": "RefreshType",
				"FieldType": "MonsterRefreshType = InitialRefresh"
			},
			{
				"FieldName": "RefreshParamDelayTime",
				"FieldType": "[float]"
			},
			{
				"FieldName": "RefreshParamDeathCnt",
				"FieldType": "int32"
			},
			{
				"FieldName": "MonsterId",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "MonsterCnt",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "Weights",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "Intervals",
				"FieldType": "[float]"
			},
			{
				"FieldName": "Habit1",
				"FieldType": "[float]"
			},
			{
				"FieldName": "Habit2",
				"FieldType": "[float]"
			},
			{
				"FieldName": "Habit3",
				"FieldType": "[float]"
			},
			{
				"FieldName": "Habit4",
				"FieldType": "[float]"
			},
			{
				"FieldName": "Habit5",
				"FieldType": "[float]"
			}
		]
	}`
