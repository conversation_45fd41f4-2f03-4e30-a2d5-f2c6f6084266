// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

type GoToStr struct {
	Type        GoToType             `json:"Type"`     // 跳转类型
	Hud         HudType              `json:"Hud"`      // Hud类型
	Level       int32                `json:"Level"`    // 跳转关卡
	LevelRef    *MainLevelTableCfg   `json:"-"`        // 跳转关卡
	Path        string               `json:"Path"`     // 路径
	Size        float32              `json:"Size"`     // 比例
	Dialogue    int32                `json:"Dialogue"` // 对话
	DialogueRef *NpcDialogueTableCfg `json:"-"`        // 对话
}

func NewGoToStr() *GoToStr {
	return &GoToStr{
		Type:        GoToType(enumDefaultValue),
		Hud:         HudType(enumDefaultValue),
		Level:       0,
		LevelRef:    nil,
		Path:        "",
		Size:        0.0,
		Dialogue:    0,
		DialogueRef: nil,
	}
}

func NewMockGoToStr() *GoToStr {
	return &GoToStr{
		Type:        GoToType(enumDefaultValue),
		Hud:         HudType(enumDefaultValue),
		Level:       0,
		LevelRef:    nil,
		Path:        "",
		Size:        0.0,
		Dialogue:    0,
		DialogueRef: nil,
	}
}

func (s *GoToStr) bindRefs(c *Configs) {
	s.LevelRef = c.MainLevelTable.Get(s.Level)
	s.DialogueRef = c.NpcDialogueTable.Get(s.Dialogue)
}
