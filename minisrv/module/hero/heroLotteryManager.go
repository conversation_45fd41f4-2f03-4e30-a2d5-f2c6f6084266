package hero

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"sort"
	"strconv"
	"time"
)

// 获取对应的 random group 表
func GetHeroLotteryRandomTableByGroup(ctx context.Context, uid int64) *servercfg.HeroLotteryRandomGroupTableCfg {
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	if stageModel == nil {
		return nil
	}
	unlockStageId := stageModel.UnlockStageId()
	lines := cfg_mgr.Cfg.HeroLotteryRandomGroupTable.FilterSlice(func(v *servercfg.HeroLotteryRandomGroupTableCfg) bool {
		return true
	})
	finishStatus := stageModel.StageFinishStatus()
	sort.Slice(lines, func(i, j int) bool {
		return lines[i].Id < lines[j].Id
	})
	curStatus := finishStatus[unlockStageId-1]
	if curStatus == nil {
		return lines[0]
	}
	passRewardLines := cfg_mgr.Cfg.MainLevelPassRewardTable.FilterSlice(func(v *servercfg.MainLevelPassRewardTableCfg) bool {
		return curStatus.StageId == v.MainLevel && curStatus.PerfectStatus == v.Star
	})
	if passRewardLines == nil {
		return lines[0]
	}
	for _, v := range lines {
		if v.ChapterUnlockMin != 0 && v.ChapterUnlockMin > passRewardLines[0].Id {
			continue
		}
		if v.ChapterUnlockMax != 0 && v.ChapterUnlockMax < passRewardLines[0].Id {
			continue
		}
		return v
	}
	return nil
}

// HeroLottery 英雄招募
func HeroLottery(ctx context.Context, uid int64, configId int32, isFree bool, cost *map[int32]int64, biRewards *map[int32]int64) (*wrpc.Rewards, *constDef.HeroLotteryStats, error) {
	lotteryModel, err := model.GetLotteryModel(ctx, uid)
	retReward := wrpc.Rewards{ItemId: 0, ItemValue: 0, Type: 0}
	if err != nil {
		return &retReward, nil, err
	}
	groupLine := cfg_mgr.Cfg.HeroLotteryGroupTable.Get(configId)
	if isFree {
		if lotteryModel.TodayFreeLotteryTimes() >= groupLine.DailyFreeTimesLimit {
			return &retReward, nil, kdmerr.SysInvalidArguments.CastErrorf("free times more than")
		}
		if time.Now().Unix()-lotteryModel.LastFreeLotteryTime() < int64(groupLine.FreeCD) {
			return &retReward, nil, kdmerr.SysInvalidArguments.CastErrorf("not in free time ")
		}
		lotteryModel.SetTodayFreeLotteryTimes(ctx, lotteryModel.TodayFreeLotteryTimes()+1)
		lotteryModel.SetLastFreeLotteryTime(ctx, time.Now().Unix())
	} else {
		sequence := util.GenerateSequenceString()
		e := consumable.DeductItem(ctx, uid, groupLine.SingleDrawCostType, int64(groupLine.SingleDrawCostValue), bi.ItemFlowReason_HERO_LOTTERY, strconv.Itoa(int(configId)), sequence)
		*cost = util.CombineRewards(*cost, map[int32]int64{groupLine.SingleDrawCostType: int64(groupLine.SingleDrawCostValue)})
		if e != nil {
			//消耗钻石
			e = consumable.DeductItem(ctx, uid, constDef.ITEM_DIAMOND, int64(groupLine.SingleDrawCostDiamdondCnt), bi.ItemFlowReason_HERO_LOTTERY, strconv.Itoa(int(configId)), sequence)
			if e != nil {
				return &retReward, nil, e
			}
		}
	}
	lotteryModel.SetToMustTimes(ctx, lotteryModel.ToMustTimes()+1)

	//检测必中
	lotteryMustTables := cfg_mgr.Cfg.HeroLotteryMustTable.FilterSlice(func(v *servercfg.HeroLotteryMustTableCfg) bool {
		return v.HeroLotteryGroup == groupLine.Id
	})
	if lotteryMustTables == nil {
		return &retReward, nil, kdmerr.SysInvalidArguments.CastErrorf("conf is error")
	}

	idWeight := map[int32]int32{}
	isMust := false
	curIsInTutorial := lotteryModel.NotInTutorial()
	if !lotteryModel.NotInTutorial() && lotteryModel.ToMustTimes() <= 11 {
		idWeight[lotteryMustTables[0].Must11[lotteryModel.ToMustTimes()-1]] += 100
		isMust = lotteryMustTables[0].Must11Ref[lotteryModel.ToMustTimes()-1].IsMust
		if lotteryModel.ToMustTimes() == 11 {
			lotteryModel.SetNotInTutorial(ctx, true)
		}
	} else {
		if lotteryMustTables[0].MustCnt <= lotteryModel.ToMustTimes() {
			isMust = true
		}

		//获取 random group line
		randomGroupLine := GetHeroLotteryRandomTableByGroup(ctx, uid)
		if randomGroupLine == nil {
			return &retReward, nil, kdmerr.SysInvalidArguments.CastErrorf("conf is error")
		}

		heroLotteryRandomLines := cfg_mgr.Cfg.HeroLotteryRandomTable.Filter(func(v *servercfg.HeroLotteryRandomTableCfg) bool {
			return v.HeroLotteryGroup == groupLine.Id && v.RandomGroup == randomGroupLine.Id
		})
		if isMust {
			heroLotteryRandomLines = cfg_mgr.Cfg.HeroLotteryRandomTable.Filter(func(v *servercfg.HeroLotteryRandomTableCfg) bool {
				return v.HeroLotteryGroup == groupLine.Id && v.IsMust == isMust && v.RandomGroup == randomGroupLine.Id
			})
		}

		mustCount := float32(0)
		for _, v := range heroLotteryRandomLines {
			idWeight[v.Id] += v.Weight

			if v.IsMust {
				mustCount++
			}

		}
		addWeight := float32(0)
		if !isMust && lotteryModel.ToMustTimes() > lotteryMustTables[0].IncreaseCnt {
			addWeight = float32(lotteryMustTables[0].Increment[lotteryModel.ToMustTimes()-lotteryMustTables[0].IncreaseCnt-1]) / mustCount
		}
		for _, v := range heroLotteryRandomLines {
			if v.IsMust {
				idWeight[v.Id] += int32(addWeight)
			}

		}
	}

	if idWeight == nil {
		return &retReward, nil, kdmerr.SysInvalidArguments.CastErrorf("conf is error")
	}
	randomLineId := util.RandomByWeight(idWeight)
	randomLine := cfg_mgr.Cfg.HeroLotteryRandomTable.Get(randomLineId)

	*biRewards = util.CombineRewards(*biRewards, map[int32]int64{randomLine.ItemType: int64(randomLine.ItemValue)})
	heroLines := cfg_mgr.Cfg.HeroTable.FilterSlice(func(v *servercfg.HeroTableCfg) bool {
		return v.ItemId == randomLine.ItemType
	})

	//如果是必中
	if randomLine.IsMust && curIsInTutorial {
		heroIdWeight := map[int32]int32{}
		for k, rl := range lotteryMustTables[0].MustSSR.HeroRef {
			tempHeroLine := cfg_mgr.Cfg.HeroTable.FilterSlice(func(v *servercfg.HeroTableCfg) bool {
				return v.ItemId == rl.ItemType
			})
			if tempHeroLine == nil || len(tempHeroLine) == 0 {
				continue
			}
			h, _ := model.GetHero(ctx, uid, tempHeroLine[0].Id)
			if h != nil {
				continue
			}
			heroIdWeight[tempHeroLine[0].Id] += lotteryMustTables[0].MustSSR.Weight[k]
		}
		heroId := util.RandomByWeight(heroIdWeight)
		if heroId != 0 {
			heroLines = cfg_mgr.Cfg.HeroTable.FilterSlice(func(v *servercfg.HeroTableCfg) bool {
				return v.Id == heroId
			})
		}
	}

	sequence := util.GenerateSequenceString()
	var stats *constDef.HeroLotteryStats
	if len(heroLines) == 0 {

		model.AddItem(ctx, uid, randomLine.ItemType, int64(randomLine.ItemValue), bi.ItemFlowReason_HERO_LOTTERY, strconv.Itoa(int(configId)), sequence)
		retReward = wrpc.Rewards{ItemId: randomLine.ItemType, ItemValue: int64(randomLine.ItemValue), Type: int32(minirpc.RewardTypeItem)}
	} else {
		h, _ := model.GetHero(ctx, uid, heroLines[0].Id)
		if h == nil {
			AddHero(ctx, uid, heroLines[0].Id, bi.ItemFlowReason_HERO_LOTTERY, configId)
			retReward = wrpc.Rewards{ItemId: heroLines[0].Id, ItemValue: 1, Type: int32(minirpc.RewardTypeHero)}
		} else {
			heroFragmentLine := cfg_mgr.Cfg.HeroFragmentTable.FilterSlice(func(v *servercfg.HeroFragmentTableCfg) bool {
				return v.Hero == heroLines[0].Id
			})
			model.AddItem(ctx, uid, heroFragmentLine[0].HeroFragment, int64(heroFragmentLine[0].DivisionCnt), bi.ItemFlowReason_HERO_LOTTERY, strconv.Itoa(int(configId)), sequence)
			retReward = wrpc.Rewards{ItemId: heroFragmentLine[0].HeroFragment, ItemValue: int64(heroFragmentLine[0].DivisionCnt), Type: int32(minirpc.RewardTypeItem)}
		}
		if heroLines[0].HeroQuality == servercfg.HeroQuality_HeroLegendary {
			stats = &constDef.HeroLotteryStats{
				RealTime: int(lotteryModel.ToMustTimes()),
				HeroID:   int(heroLines[0].Id),
			}
			if lotteryModel.NotInTutorial() {
				lotteryModel.SetToMustTimes(ctx, 0)
			}

		}
		if isMust {
			if lotteryModel.NotInTutorial() {
				lotteryModel.SetToMustTimes(ctx, 0)
			}
		}
	}

	//lotteryModel.UpdateSumAmount(ctx, lotteryModel.SumAmount()+1)
	accAmount := lotteryModel.AccAmount()
	if accAmount == nil {
		accAmount = make(map[int32]int32)
	}
	accAmount[configId] = accAmount[configId] + 1
	lotteryModel.SetAccAmount(ctx, accAmount)
	model.SetTaskCounter(ctx, uid, int32(servercfg.TaskType_TotalHeroSummon), "", 1)
	quest.Notify(ctx, uid, servercfg.TaskType_HeroSummon, map[string]string{}, 1)
	quest.Notify(ctx, uid, servercfg.TaskType_TotalHeroSummon, map[string]string{}, 0)
	return &retReward, stats, nil
}
func CollectHeroLotteryAccReward(ctx context.Context, uid int64, configId int32) (map[int32]int64, error) {
	//lotteryModel, err := model.GetLotteryModel(ctx, uid)
	//if err != nil {
	//	return nil, err
	//}
	//line := cfg_mgr.Cfg.HeroLotteryGroupTable.Get(configId)
	//accCnts := line.AccumulatedLotteryCnt
	////needCnt := accCnts[pos]
	//curCnt := lotteryModel.AccAmount()[configId]
	////if curCnt < needCnt {
	////	return nil, kdmerr.SysInvalidArguments.CastErrorf("acc cnt less than need")
	////}
	//accRewards := lotteryModel.AccReward()
	//if accRewards == nil {
	//	accRewards = make(map[int32]int64)
	//}
	//var rewards map[int32]int64
	//for i := accRewards[configId]; i <= int64(len(accCnts)); i++ {
	//	if accCnts[i] > curCnt {
	//		accRewards[configId] = i
	//		break
	//	}
	//	rewardId := line.AccumulatedLotteryRewardType[i]
	//	rewardValue := line.AccumulatedLotteryRewardValue[i]
	//	rewards = util.CombineRewards(rewards, map[int32]int64{rewardId: int64(rewardValue)})
	//	model.AddItem(ctx, uid, rewardId, int64(rewardValue))
	//}
	//lotteryModel.SetAccReward(ctx, accRewards)
	//return rewards, nil
	return nil, nil
}

func CollectHeroLotteryLevelReward(ctx context.Context, uid int64, levelId int32) (map[int32]int64, error) {
	//lotteryModel, err := model.GetLotteryModel(ctx, uid)
	//if err != nil {
	//	return nil, err
	//}
	//if levelId > lotteryModel.LevelId() {
	//	return nil, kdmerr.SysInvalidArguments.CastErrorf("level need more")
	//}
	//levelRewards := lotteryModel.LevelRewards()
	//if levelRewards[levelId] != 0 {
	//	return nil, kdmerr.SysInvalidArguments.CastErrorf("cur level has be collect")
	//}
	//if levelRewards == nil {
	//	levelRewards = map[int32]int64{}
	//}
	//levelRewards[levelId] = 1
	//lotteryModel.SetLevelRewards(ctx, levelRewards)
	//collectLevelLine := cfg_mgr.Cfg.HeroLotteryLevelTable.Get(levelId)
	//var rewards map[int32]int64
	//rewards = util.CombineRewards(rewards, map[int32]int64{collectLevelLine.RewardType: int64(collectLevelLine.RewardValue)})
	//return rewards, nil
	return nil, nil
}
