// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"text/template"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t ItemTable) saveJson(dir string) error {
	return t.SaveJsonWithSuffix(dir, "", t.Filter<PERSON>(func(v *ItemTableCfg) bool {
		return true
	}))
}

func (t ItemTable) SaveJsonWithSuffix(dir string, suffix string, cfgs []*ItemTableCfg) error {
	jsonPath := filepath.Join(dir, "ItemTable"+suffix+".json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[ItemTable]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	// sort
	s := cfgoItemTableSlice{}
	for _, cfgoCfg := range cfgs {
		if _, ok := t.localIds[cfgoCfg.Id]; !ok {
			continue
		}
		s = append(s, cfgoCfg)
	}
	sort.Sort(s)

	w := bufio.NewWriter(f)
	w.WriteString("{\n")
	w.WriteString("\t\"Cells\": ")
	bytes, err := json.MarshalIndent(s, "\t", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	w.WriteString("\n}\n")

	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

type cfgoItemTableSlice []*ItemTableCfg

func (x cfgoItemTableSlice) Len() int           { return len(x) }
func (x cfgoItemTableSlice) Less(i, j int) bool { return x[i].Id < x[j].Id }
func (x cfgoItemTableSlice) Swap(i, j int)      { x[i], x[j] = x[j], x[i] }

func (t *ItemTable) LoadJsonByPath(jsonPath string, configs *Configs) error {
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[ItemTable]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*ItemTableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[ItemTable]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		if err = t.Put(k, v); err != nil {
			return fmt.Errorf("[ItemTable]put record failed, path=%s, err=[%w]", jsonPath, err)
		}
	}
	if err = t.setupIndexes(); err != nil {
		return fmt.Errorf("[ItemTable]setup indexes failed, path=%s, err=[%w]", jsonPath, err)
	}
	return nil
}

func (t ItemTable) saveMeta(dir string) error {
	return t.SaveMetaWithSuffix(dir, "")
}

func (t ItemTable) SaveMetaWithSuffix(dir string, suffix string) error {
	fbPlus := false
	td := &TableData{}
	if err := json.Unmarshal(string2Bytes(ItemTableJsonContent), td); err != nil {
		return fmt.Errorf("[client]unmarshal json failed, err=[%w]", err)
	}
	if len(suffix) > 0 {
		td.FileName = td.FileName + suffix
		td.TableName = td.TableName + suffix
	}
	path := filepath.Join(dir, td.FileName+".fbs")
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	defer f.Close()

	tplStr := FlatBufferTableTpl
	if fbPlus {
		tplStr = FlatBufferPlusTableTpl
	}
	tpl, err := template.New("table_schema").Parse(tplStr)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}

	if err = tpl.Execute(f, td); err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	return nil
}

var ItemTableJsonContent string = `{
		"FileName": "ItemTable",
		"ConstTable": false,
		"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
		"TableName": "ItemTable",
		"Key": {
			"FieldName": "Id",
			"FieldType": "int32"
		},
		"Fields": [
			{
				"FieldName": "StringId",
				"FieldType": "string"
			},
			{
				"FieldName": "Priority",
				"FieldType": "int32"
			},
			{
				"FieldName": "Type",
				"FieldType": "ItemType = Chest"
			},
			{
				"FieldName": "Value",
				"FieldType": "int32"
			},
			{
				"FieldName": "Benefits",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "BenefitsValue",
				"FieldType": "[float]"
			},
			{
				"FieldName": "Image",
				"FieldType": "string"
			},
			{
				"FieldName": "Name",
				"FieldType": "string"
			},
			{
				"FieldName": "Desc",
				"FieldType": "string"
			},
			{
				"FieldName": "Param",
				"FieldType": "[string]"
			},
			{
				"FieldName": "Quality",
				"FieldType": "int32"
			},
			{
				"FieldName": "AutoUse",
				"FieldType": "bool"
			},
			{
				"FieldName": "RepeatAutoTransform",
				"FieldType": "bool"
			},
			{
				"FieldName": "ChestDropGroup",
				"FieldType": "int32"
			},
			{
				"FieldName": "ChestSelect",
				"FieldType": "int32"
			},
			{
				"FieldName": "MaxUseCnt",
				"FieldType": "int32"
			},
			{
				"FieldName": "IsDiamond",
				"FieldType": "bool"
			},
			{
				"FieldName": "DiamondExchange",
				"FieldType": "bool"
			},
			{
				"FieldName": "DiamondCnt",
				"FieldType": "int32"
			},
			{
				"FieldName": "ShowSource",
				"FieldType": "bool"
			},
			{
				"FieldName": "ItemSource",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "InBag",
				"FieldType": "bool"
			},
			{
				"FieldName": "BagType",
				"FieldType": "BagType = Plants"
			},
			{
				"FieldName": "BagStack",
				"FieldType": "int32"
			}
		]
	}`
