// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"text/template"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t AchievementTable) saveJson(dir string) error {
	return t.SaveJsonWithSuffix(dir, "", t.Filter<PERSON>lice(func(v *AchievementTableCfg) bool {
		return true
	}))
}

func (t AchievementTable) SaveJsonWithSuffix(dir string, suffix string, cfgs []*AchievementTableCfg) error {
	jsonPath := filepath.Join(dir, "AchievementTable"+suffix+".json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[AchievementTable]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	// sort
	s := cfgoAchievementTableSlice{}
	for _, cfgoCfg := range cfgs {
		if _, ok := t.localIds[cfgoCfg.Id]; !ok {
			continue
		}
		s = append(s, cfgoCfg)
	}
	sort.Sort(s)

	w := bufio.NewWriter(f)
	w.WriteString("{\n")
	w.WriteString("\t\"Cells\": ")
	bytes, err := json.MarshalIndent(s, "\t", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	w.WriteString("\n}\n")

	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

type cfgoAchievementTableSlice []*AchievementTableCfg

func (x cfgoAchievementTableSlice) Len() int           { return len(x) }
func (x cfgoAchievementTableSlice) Less(i, j int) bool { return x[i].Id < x[j].Id }
func (x cfgoAchievementTableSlice) Swap(i, j int)      { x[i], x[j] = x[j], x[i] }

func (t *AchievementTable) LoadJsonByPath(jsonPath string, configs *Configs) error {
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[AchievementTable]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*AchievementTableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[AchievementTable]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		if err = t.Put(k, v); err != nil {
			return fmt.Errorf("[AchievementTable]put record failed, path=%s, err=[%w]", jsonPath, err)
		}
	}
	if err = t.setupIndexes(); err != nil {
		return fmt.Errorf("[AchievementTable]setup indexes failed, path=%s, err=[%w]", jsonPath, err)
	}
	return nil
}

func (t AchievementTable) saveMeta(dir string) error {
	return t.SaveMetaWithSuffix(dir, "")
}

func (t AchievementTable) SaveMetaWithSuffix(dir string, suffix string) error {
	fbPlus := false
	td := &TableData{}
	if err := json.Unmarshal(string2Bytes(AchievementTableJsonContent), td); err != nil {
		return fmt.Errorf("[client]unmarshal json failed, err=[%w]", err)
	}
	if len(suffix) > 0 {
		td.FileName = td.FileName + suffix
		td.TableName = td.TableName + suffix
	}
	path := filepath.Join(dir, td.FileName+".fbs")
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	defer f.Close()

	tplStr := FlatBufferTableTpl
	if fbPlus {
		tplStr = FlatBufferPlusTableTpl
	}
	tpl, err := template.New("table_schema").Parse(tplStr)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}

	if err = tpl.Execute(f, td); err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	return nil
}

var AchievementTableJsonContent string = `{
		"FileName": "AchievementTable",
		"ConstTable": false,
		"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
		"TableName": "AchievementTable",
		"Key": {
			"FieldName": "Id",
			"FieldType": "int32"
		},
		"Fields": [
			{
				"FieldName": "ReqFunction",
				"FieldType": "int32"
			},
			{
				"FieldName": "Order",
				"FieldType": "int32"
			},
			{
				"FieldName": "Group",
				"FieldType": "int32"
			},
			{
				"FieldName": "Sub",
				"FieldType": "int32"
			},
			{
				"FieldName": "TaskType",
				"FieldType": "TaskType = Login"
			},
			{
				"FieldName": "TaskCounterType",
				"FieldType": "TaskCounterType = Reset"
			},
			{
				"FieldName": "Formula",
				"FieldType": "string"
			},
			{
				"FieldName": "Value",
				"FieldType": "int32"
			},
			{
				"FieldName": "GoTo",
				"FieldType": "int32"
			},
			{
				"FieldName": "Desc",
				"FieldType": "string"
			},
			{
				"FieldName": "Param",
				"FieldType": "[string]"
			},
			{
				"FieldName": "Reward",
				"FieldType": "[RewardKVS]"
			}
		]
	}`
