// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type HeroStarTableCfg struct {
	Id                  int32                `json:"Id"`                  // Id
	PlanID              HeroStarUpPlan       `json:"PlanID"`              // 升星方案
	HeroStarLevel       int32                `json:"HeroStarLevel"`       // 英雄星级
	HeroSkillLevelLimit int32                `json:"HeroSkillLevelLimit"` // 对应的主动技能等级上限值
	IsMax               bool                 `json:"IsMax"`               // 是否最大星级
	StarUpCostValue     int32                `json:"StarUpCostValue"`     // 升星消耗数量
	StarUpDifference    int32                `json:"StarUpDifference"`    // 升星差值
	StarUpCommonItem    string               `json:"StarUpCommonItem"`    // 升星通用材料
	StarUpCommonItemNum int32                `json:"StarUpCommonItemNum"` // 升星通用材料数量
	IsGradeUp           bool                 `json:"IsGradeUp"`           // 需要升品
	GradeUpCostValue    int32                `json:"GradeUpCostValue"`    // 升品消耗数量
	HeroQuality         int32                `json:"HeroQuality"`         // 英雄品质
	HeroQualityRef      *HeroQualityTableCfg `json:"-"`                   // 英雄品质
	Power               int32                `json:"Power"`               // 戰力
	Attr                *AttrStr             `json:"Attr"`                // 属性
}

func NewHeroStarTableCfg() *HeroStarTableCfg {
	return &HeroStarTableCfg{
		Id:                  0,
		PlanID:              HeroStarUpPlan(enumDefaultValue),
		HeroStarLevel:       0,
		HeroSkillLevelLimit: 0,
		IsMax:               false,
		StarUpCostValue:     0,
		StarUpDifference:    0,
		StarUpCommonItem:    "",
		StarUpCommonItemNum: 0,
		IsGradeUp:           false,
		GradeUpCostValue:    0,
		HeroQuality:         0,
		HeroQualityRef:      nil,
		Power:               0,
		Attr:                NewAttrStr(),
	}
}

func NewMockHeroStarTableCfg() *HeroStarTableCfg {
	return &HeroStarTableCfg{
		Id:                  0,
		PlanID:              HeroStarUpPlan(enumDefaultValue),
		HeroStarLevel:       0,
		HeroSkillLevelLimit: 0,
		IsMax:               false,
		StarUpCostValue:     0,
		StarUpDifference:    0,
		StarUpCommonItem:    "",
		StarUpCommonItemNum: 0,
		IsGradeUp:           false,
		GradeUpCostValue:    0,
		HeroQuality:         0,
		HeroQualityRef:      nil,
		Power:               0,
		Attr:                NewMockAttrStr(),
	}
}

type HeroStarTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*HeroStarTableCfg
	localIds         map[int32]struct{}
}

func NewHeroStarTable(configs *Configs) *HeroStarTable {
	return &HeroStarTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*HeroStarTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *HeroStarTable) Get(key int32) *HeroStarTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *HeroStarTable) GetAll() map[int32]*HeroStarTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *HeroStarTable) put(key int32, value *HeroStarTableCfg, local bool) *HeroStarTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *HeroStarTable) putFromInheritedTable(key int32, value *HeroStarTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[HeroStarTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *HeroStarTable) Put(key int32, value *HeroStarTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[HeroStarTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *HeroStarTable) PutAll(m map[int32]*HeroStarTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *HeroStarTable) Range(f func(v *HeroStarTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *HeroStarTable) Filter(filterFuncs ...func(v *HeroStarTableCfg) bool) map[int32]*HeroStarTableCfg {
	filtered := map[int32]*HeroStarTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *HeroStarTable) FilterSlice(filterFuncs ...func(v *HeroStarTableCfg) bool) []*HeroStarTableCfg {
	filtered := []*HeroStarTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *HeroStarTable) FilterKeys(filterFuncs ...func(v *HeroStarTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *HeroStarTable) satisfied(v *HeroStarTableCfg, filterFuncs ...func(v *HeroStarTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *HeroStarTable) setupIndexes() error {
	return nil
}

func (t *HeroStarTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *HeroStarTableCfg) bindRefs(c *Configs) {
	r.HeroQualityRef = c.HeroQualityTable.Get(r.HeroQuality)
	r.Attr.bindRefs(c)
}

func (t *HeroStarTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[HeroStarTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewHeroStarTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// PlanID
		{
			if record[t.getIndexInCsv("PlanID")] == "" {
				recordCfg.PlanID = HeroStarUpPlan(enumDefaultValue)
			} else {
				cfgoEnum, err := parseHeroStarUpPlan(record[t.getIndexInCsv("PlanID")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=PlanID, type=enum@HeroStarUpPlan, value=%s, err:[%s]\n", record[t.getIndexInCsv("PlanID")], err)
					} else {
						return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=PlanID, type=enum@HeroStarUpPlan, value=%s, err:[%s]", record[t.getIndexInCsv("PlanID")], err)
					}
				}
				recordCfg.PlanID = cfgoEnum
			}
		}
		// HeroStarLevel
		{
			if record[t.getIndexInCsv("HeroStarLevel")] == "" {
				recordCfg.HeroStarLevel = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("HeroStarLevel")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=HeroStarLevel, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("HeroStarLevel")], err)
					} else {
						return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=HeroStarLevel, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("HeroStarLevel")], err)
					}
				}
				recordCfg.HeroStarLevel = int32(cfgoInt)
			}
		}
		// HeroSkillLevelLimit
		{
			if record[t.getIndexInCsv("HeroSkillLevelLimit")] == "" {
				recordCfg.HeroSkillLevelLimit = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("HeroSkillLevelLimit")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=HeroSkillLevelLimit, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("HeroSkillLevelLimit")], err)
					} else {
						return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=HeroSkillLevelLimit, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("HeroSkillLevelLimit")], err)
					}
				}
				recordCfg.HeroSkillLevelLimit = int32(cfgoInt)
			}
		}
		// IsMax
		{
			if record[t.getIndexInCsv("IsMax")] == "" {
				recordCfg.IsMax = false
			} else {
				var err error
				recordCfg.IsMax, err = strconv.ParseBool(record[t.getIndexInCsv("IsMax")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=IsMax, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("IsMax")], err)
					} else {
						return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=IsMax, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("IsMax")], err)
					}
				}
			}
		}
		// StarUpCostValue
		{
			if record[t.getIndexInCsv("StarUpCostValue")] == "" {
				recordCfg.StarUpCostValue = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("StarUpCostValue")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=StarUpCostValue, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("StarUpCostValue")], err)
					} else {
						return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=StarUpCostValue, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("StarUpCostValue")], err)
					}
				}
				recordCfg.StarUpCostValue = int32(cfgoInt)
			}
		}
		// StarUpDifference
		{
			if record[t.getIndexInCsv("StarUpDifference")] == "" {
				recordCfg.StarUpDifference = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("StarUpDifference")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=StarUpDifference, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("StarUpDifference")], err)
					} else {
						return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=StarUpDifference, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("StarUpDifference")], err)
					}
				}
				recordCfg.StarUpDifference = int32(cfgoInt)
			}
		}
		// StarUpCommonItem
		{
			recordCfg.StarUpCommonItem = strings.TrimSpace(record[t.getIndexInCsv("StarUpCommonItem")])
		}
		// StarUpCommonItemNum
		{
			if record[t.getIndexInCsv("StarUpCommonItemNum")] == "" {
				recordCfg.StarUpCommonItemNum = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("StarUpCommonItemNum")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=StarUpCommonItemNum, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("StarUpCommonItemNum")], err)
					} else {
						return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=StarUpCommonItemNum, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("StarUpCommonItemNum")], err)
					}
				}
				recordCfg.StarUpCommonItemNum = int32(cfgoInt)
			}
		}
		// IsGradeUp
		{
			if record[t.getIndexInCsv("IsGradeUp")] == "" {
				recordCfg.IsGradeUp = false
			} else {
				var err error
				recordCfg.IsGradeUp, err = strconv.ParseBool(record[t.getIndexInCsv("IsGradeUp")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=IsGradeUp, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("IsGradeUp")], err)
					} else {
						return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=IsGradeUp, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("IsGradeUp")], err)
					}
				}
			}
		}
		// GradeUpCostValue
		{
			if record[t.getIndexInCsv("GradeUpCostValue")] == "" {
				recordCfg.GradeUpCostValue = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("GradeUpCostValue")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=GradeUpCostValue, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("GradeUpCostValue")], err)
					} else {
						return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=GradeUpCostValue, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("GradeUpCostValue")], err)
					}
				}
				recordCfg.GradeUpCostValue = int32(cfgoInt)
			}
		}
		// HeroQuality
		if record[t.getIndexInCsv("HeroQuality")] == "" {
			recordCfg.HeroQuality = 0
		} else {
			var err error
			recordCfg.HeroQuality, err = configs.HeroQualityTable.getIdByRef(record[t.getIndexInCsv("HeroQuality")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=HeroQuality, type=ref@HeroQualityTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("HeroQuality")], err)
				} else {
					return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=HeroQuality, type=ref@HeroQualityTable, value=%s, err:[%s]", record[t.getIndexInCsv("HeroQuality")], err)
				}
			}
		}
		// Power
		{
			if record[t.getIndexInCsv("Power")] == "" {
				recordCfg.Power = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Power")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=Power, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Power")], err)
					} else {
						return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=Power, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Power")], err)
					}
				}
				recordCfg.Power = int32(cfgoInt)
			}
		}
		// Attr
		{
			// Atk
			{
				if record[t.getIndexInCsv("AttrAtk")] == "" {
					recordCfg.Attr.Atk = 0
				} else {
					cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("AttrAtk")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=Atk, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrAtk")], err)
						} else {
							return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=Atk, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("AttrAtk")], err)
						}
					}
					recordCfg.Attr.Atk = int32(cfgoInt)
				}
			}
			// Def
			{
				if record[t.getIndexInCsv("AttrDef")] == "" {
					recordCfg.Attr.Def = 0
				} else {
					cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("AttrDef")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=Def, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrDef")], err)
						} else {
							return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=Def, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("AttrDef")], err)
						}
					}
					recordCfg.Attr.Def = int32(cfgoInt)
				}
			}
			// Hp
			{
				if record[t.getIndexInCsv("AttrHp")] == "" {
					recordCfg.Attr.Hp = 0
				} else {
					cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("AttrHp")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=Hp, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrHp")], err)
						} else {
							return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=Hp, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("AttrHp")], err)
						}
					}
					recordCfg.Attr.Hp = int32(cfgoInt)
				}
			}
			// CritChance
			{
				if record[t.getIndexInCsv("AttrCritChance")] == "" {
					recordCfg.Attr.CritChance = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrCritChance")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=CritChance, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrCritChance")], err)
						} else {
							return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=CritChance, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrCritChance")], err)
						}
					}
					recordCfg.Attr.CritChance = float32(cfgoFloat)
				}
			}
			// CritDmgUpPer
			{
				if record[t.getIndexInCsv("AttrCritDmgUpPer")] == "" {
					recordCfg.Attr.CritDmgUpPer = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrCritDmgUpPer")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=CritDmgUpPer, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrCritDmgUpPer")], err)
						} else {
							return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=CritDmgUpPer, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrCritDmgUpPer")], err)
						}
					}
					recordCfg.Attr.CritDmgUpPer = float32(cfgoFloat)
				}
			}
			// DmgUpPer
			{
				if record[t.getIndexInCsv("AttrDmgUpPer")] == "" {
					recordCfg.Attr.DmgUpPer = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrDmgUpPer")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=DmgUpPer, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrDmgUpPer")], err)
						} else {
							return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=DmgUpPer, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrDmgUpPer")], err)
						}
					}
					recordCfg.Attr.DmgUpPer = float32(cfgoFloat)
				}
			}
			// CritResistChance
			{
				if record[t.getIndexInCsv("AttrCritResistChance")] == "" {
					recordCfg.Attr.CritResistChance = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrCritResistChance")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=CritResistChance, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrCritResistChance")], err)
						} else {
							return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=CritResistChance, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrCritResistChance")], err)
						}
					}
					recordCfg.Attr.CritResistChance = float32(cfgoFloat)
				}
			}
			// BeCritDmgDownPer
			{
				if record[t.getIndexInCsv("AttrBeCritDmgDownPer")] == "" {
					recordCfg.Attr.BeCritDmgDownPer = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrBeCritDmgDownPer")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=BeCritDmgDownPer, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBeCritDmgDownPer")], err)
						} else {
							return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=BeCritDmgDownPer, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBeCritDmgDownPer")], err)
						}
					}
					recordCfg.Attr.BeCritDmgDownPer = float32(cfgoFloat)
				}
			}
			// BeDmgDownPer
			{
				if record[t.getIndexInCsv("AttrBeDmgDownPer")] == "" {
					recordCfg.Attr.BeDmgDownPer = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrBeDmgDownPer")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=BeDmgDownPer, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBeDmgDownPer")], err)
						} else {
							return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=BeDmgDownPer, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBeDmgDownPer")], err)
						}
					}
					recordCfg.Attr.BeDmgDownPer = float32(cfgoFloat)
				}
			}
			// CdRate
			{
				if record[t.getIndexInCsv("AttrCdRate")] == "" {
					recordCfg.Attr.CdRate = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrCdRate")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, varName=CdRate, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrCdRate")], err)
						} else {
							return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, varName=CdRate, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrCdRate")], err)
						}
					}
					recordCfg.Attr.CdRate = float32(cfgoFloat)
				}
			}
			// Benefits
			{
				cfgoMeetNilForBenefitsOfAttrOfRecordCfg := false
				// element 0 of Benefits
				if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
					cfgoMeetNilForBenefitsOfAttrOfRecordCfg = true
					var cfgoElemOfBenefitsOfAttrOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
					{
						if record[t.getIndexInCsv("AttrBenefits1BenefitsID")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							var err error
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("AttrBenefits1BenefitsID")])
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroStarTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits1BenefitsID")], err)
								} else {
									return fmt.Errorf("[HeroStarTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits1BenefitsID")], err)
								}
							}
						}
					}
					{
						if record[t.getIndexInCsv("AttrBenefits1BenefitsValue")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrBenefits1BenefitsValue")], 32)
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroStarTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits1BenefitsValue")], err)
								} else {
									return fmt.Errorf("[HeroStarTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits1BenefitsValue")], err)
								}
							}
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue = float32(cfgoFloat)
						}
					}

					if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
						recordCfg.Attr.Benefits = append(recordCfg.Attr.Benefits, cfgoElemOfBenefitsOfAttrOfRecordCfg)
					}
				}
				// element 1 of Benefits
				if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
					cfgoMeetNilForBenefitsOfAttrOfRecordCfg = true
					var cfgoElemOfBenefitsOfAttrOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
					{
						if record[t.getIndexInCsv("AttrBenefits2BenefitsID")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							var err error
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("AttrBenefits2BenefitsID")])
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroStarTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits2BenefitsID")], err)
								} else {
									return fmt.Errorf("[HeroStarTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits2BenefitsID")], err)
								}
							}
						}
					}
					{
						if record[t.getIndexInCsv("AttrBenefits2BenefitsValue")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrBenefits2BenefitsValue")], 32)
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroStarTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits2BenefitsValue")], err)
								} else {
									return fmt.Errorf("[HeroStarTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits2BenefitsValue")], err)
								}
							}
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue = float32(cfgoFloat)
						}
					}

					if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
						recordCfg.Attr.Benefits = append(recordCfg.Attr.Benefits, cfgoElemOfBenefitsOfAttrOfRecordCfg)
					}
				}
				// element 2 of Benefits
				if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
					cfgoMeetNilForBenefitsOfAttrOfRecordCfg = true
					var cfgoElemOfBenefitsOfAttrOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
					{
						if record[t.getIndexInCsv("AttrBenefits3BenefitsID")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							var err error
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("AttrBenefits3BenefitsID")])
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroStarTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits3BenefitsID")], err)
								} else {
									return fmt.Errorf("[HeroStarTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits3BenefitsID")], err)
								}
							}
						}
					}
					{
						if record[t.getIndexInCsv("AttrBenefits3BenefitsValue")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrBenefits3BenefitsValue")], 32)
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroStarTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits3BenefitsValue")], err)
								} else {
									return fmt.Errorf("[HeroStarTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits3BenefitsValue")], err)
								}
							}
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue = float32(cfgoFloat)
						}
					}

					if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
						recordCfg.Attr.Benefits = append(recordCfg.Attr.Benefits, cfgoElemOfBenefitsOfAttrOfRecordCfg)
					}
				}
				// element 3 of Benefits
				if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
					cfgoMeetNilForBenefitsOfAttrOfRecordCfg = true
					var cfgoElemOfBenefitsOfAttrOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
					{
						if record[t.getIndexInCsv("AttrBenefits4BenefitsID")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							var err error
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("AttrBenefits4BenefitsID")])
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroStarTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits4BenefitsID")], err)
								} else {
									return fmt.Errorf("[HeroStarTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits4BenefitsID")], err)
								}
							}
						}
					}
					{
						if record[t.getIndexInCsv("AttrBenefits4BenefitsValue")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrBenefits4BenefitsValue")], 32)
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroStarTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits4BenefitsValue")], err)
								} else {
									return fmt.Errorf("[HeroStarTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits4BenefitsValue")], err)
								}
							}
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue = float32(cfgoFloat)
						}
					}

					if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
						recordCfg.Attr.Benefits = append(recordCfg.Attr.Benefits, cfgoElemOfBenefitsOfAttrOfRecordCfg)
					}
				}

			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [HeroStarTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[HeroStarTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *HeroStarTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "HeroStarTable.csv") && (!strings.HasPrefix(fileName, "HeroStarTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for HeroStarTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[HeroStarTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[HeroStarTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[HeroStarTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[HeroStarTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[HeroStarTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[HeroStarTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[HeroStarTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[HeroStarTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [HeroStarTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *HeroStarTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[HeroStarTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [HeroStarTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *HeroStarTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[HeroStarTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *HeroStarTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[HeroStarTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[HeroStarTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
