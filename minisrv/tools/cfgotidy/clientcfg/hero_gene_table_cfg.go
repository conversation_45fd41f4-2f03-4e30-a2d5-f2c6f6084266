// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type HeroGeneTableCfg struct {
	Id                       int32                      `json:"Id"`                       // Id
	HeroID                   int32                      `json:"HeroID"`                   // 英雄ID
	HeroIDRef                *HeroTableCfg              `json:"-"`                        // 英雄ID
	HeroGeneLevel            int32                      `json:"HeroGeneLevel"`            // 英雄基因等级
	IsMax                    bool                       `json:"IsMax"`                    // 是否最大基因等级
	LevelUp                  []*CostKVS                 `json:"LevelUp"`                  // 基因升级
	Power                    int32                      `json:"Power"`                    // 戰力
	Attr                     *AttrStr                   `json:"Attr"`                     // 属性
	AllHeroCritDmgUp         float32                    `json:"AllHeroCritDmgUp"`         // 全体英雄暴击伤害提升
	IsUnlockExtraSkillEffect bool                       `json:"IsUnlockExtraSkillEffect"` // 是否解锁额外技能效果
	RefHeroStarLevel         int32                      `json:"RefHeroStarLevel"`         // 解锁额外技能所需英雄星级
	SkillTitle               string                     `json:"SkillTitle"`               // 技能标题
	SkillDesc                string                     `json:"SkillDesc"`                // 技能描述
	SkillDescParam           []string                   `json:"SkillDescParam"`           // 技能描述参数
	DetailSkillDesc          string                     `json:"DetailSkillDesc"`          // 详情里的技能描述
	DetailSkillDescParam     []string                   `json:"DetailSkillDescParam"`     // 详情里的技能描述参数
	Icon                     string                     `json:"Icon"`                     // 图标
	IconLocked               string                     `json:"IconLocked"`               // 未解锁图标
	IsSkillPreview           bool                       `json:"IsSkillPreview"`           // 是否可预览
	SkillPreview             string                     `json:"SkillPreview"`             // 技能预览资源
	IsPopUp                  bool                       `json:"IsPopUp"`                  // 是否弹出窗口
	PopupName                string                     `json:"PopupName"`                // 弹出窗口的名字
	PopupDesc                string                     `json:"PopupDesc"`                // 弹出窗口的描述
	PopupDescParam           []string                   `json:"PopupDescParam"`           // 窗口描述参数
	PVEPassiveSkillEffect    []int32                    `json:"PVEPassiveSkillEffect"`    // 仅pve生效被动技能段组
	PVEPassiveSkillEffectRef []*HeroSkillEffectTableCfg `json:"-"`                        // 仅pve生效被动技能段组
}

func NewHeroGeneTableCfg() *HeroGeneTableCfg {
	return &HeroGeneTableCfg{
		Id:                       0,
		HeroID:                   0,
		HeroIDRef:                nil,
		HeroGeneLevel:            0,
		IsMax:                    false,
		LevelUp:                  []*CostKVS{},
		Power:                    0,
		Attr:                     NewAttrStr(),
		AllHeroCritDmgUp:         0.0,
		IsUnlockExtraSkillEffect: false,
		RefHeroStarLevel:         0,
		SkillTitle:               "",
		SkillDesc:                "",
		SkillDescParam:           []string{},
		DetailSkillDesc:          "",
		DetailSkillDescParam:     []string{},
		Icon:                     "",
		IconLocked:               "",
		IsSkillPreview:           false,
		SkillPreview:             "",
		IsPopUp:                  false,
		PopupName:                "",
		PopupDesc:                "",
		PopupDescParam:           []string{},
		PVEPassiveSkillEffect:    []int32{},
		PVEPassiveSkillEffectRef: []*HeroSkillEffectTableCfg{},
	}
}

func NewMockHeroGeneTableCfg() *HeroGeneTableCfg {
	return &HeroGeneTableCfg{
		Id:                       0,
		HeroID:                   0,
		HeroIDRef:                nil,
		HeroGeneLevel:            0,
		IsMax:                    false,
		LevelUp:                  []*CostKVS{NewMockCostKVS()},
		Power:                    0,
		Attr:                     NewMockAttrStr(),
		AllHeroCritDmgUp:         0.0,
		IsUnlockExtraSkillEffect: false,
		RefHeroStarLevel:         0,
		SkillTitle:               "",
		SkillDesc:                "",
		SkillDescParam:           []string{""},
		DetailSkillDesc:          "",
		DetailSkillDescParam:     []string{""},
		Icon:                     "",
		IconLocked:               "",
		IsSkillPreview:           false,
		SkillPreview:             "",
		IsPopUp:                  false,
		PopupName:                "",
		PopupDesc:                "",
		PopupDescParam:           []string{""},
		PVEPassiveSkillEffect:    []int32{0},
		PVEPassiveSkillEffectRef: []*HeroSkillEffectTableCfg{},
	}
}

type HeroGeneTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*HeroGeneTableCfg
	localIds         map[int32]struct{}
}

func NewHeroGeneTable(configs *Configs) *HeroGeneTable {
	return &HeroGeneTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*HeroGeneTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *HeroGeneTable) Get(key int32) *HeroGeneTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *HeroGeneTable) GetAll() map[int32]*HeroGeneTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *HeroGeneTable) put(key int32, value *HeroGeneTableCfg, local bool) *HeroGeneTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *HeroGeneTable) putFromInheritedTable(key int32, value *HeroGeneTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[HeroGeneTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *HeroGeneTable) Put(key int32, value *HeroGeneTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[HeroGeneTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *HeroGeneTable) PutAll(m map[int32]*HeroGeneTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *HeroGeneTable) Range(f func(v *HeroGeneTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *HeroGeneTable) Filter(filterFuncs ...func(v *HeroGeneTableCfg) bool) map[int32]*HeroGeneTableCfg {
	filtered := map[int32]*HeroGeneTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *HeroGeneTable) FilterSlice(filterFuncs ...func(v *HeroGeneTableCfg) bool) []*HeroGeneTableCfg {
	filtered := []*HeroGeneTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *HeroGeneTable) FilterKeys(filterFuncs ...func(v *HeroGeneTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *HeroGeneTable) satisfied(v *HeroGeneTableCfg, filterFuncs ...func(v *HeroGeneTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *HeroGeneTable) setupIndexes() error {
	return nil
}

func (t *HeroGeneTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *HeroGeneTableCfg) bindRefs(c *Configs) {
	r.HeroIDRef = c.HeroTable.Get(r.HeroID)
	for _, e := range r.LevelUp {
		e.bindRefs(c)
	}
	r.Attr.bindRefs(c)
	for _, e := range r.PVEPassiveSkillEffect {
		cfgoRefRecord := c.HeroSkillEffectTable.Get(e)
		r.PVEPassiveSkillEffectRef = append(r.PVEPassiveSkillEffectRef, cfgoRefRecord)
	}
}

func (t *HeroGeneTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[HeroGeneTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewHeroGeneTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// HeroID
		if record[t.getIndexInCsv("HeroID")] == "" {
			recordCfg.HeroID = 0
		} else {
			var err error
			recordCfg.HeroID, err = configs.HeroTable.getIdByRef(record[t.getIndexInCsv("HeroID")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=HeroID, type=ref@HeroTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("HeroID")], err)
				} else {
					return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=HeroID, type=ref@HeroTable, value=%s, err:[%s]", record[t.getIndexInCsv("HeroID")], err)
				}
			}
		}
		// HeroGeneLevel
		{
			if record[t.getIndexInCsv("HeroGeneLevel")] == "" {
				recordCfg.HeroGeneLevel = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("HeroGeneLevel")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=HeroGeneLevel, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("HeroGeneLevel")], err)
					} else {
						return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=HeroGeneLevel, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("HeroGeneLevel")], err)
					}
				}
				recordCfg.HeroGeneLevel = int32(cfgoInt)
			}
		}
		// IsMax
		{
			if record[t.getIndexInCsv("IsMax")] == "" {
				recordCfg.IsMax = false
			} else {
				var err error
				recordCfg.IsMax, err = strconv.ParseBool(record[t.getIndexInCsv("IsMax")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=IsMax, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("IsMax")], err)
					} else {
						return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=IsMax, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("IsMax")], err)
					}
				}
			}
		}
		// LevelUp
		{
			cfgoMeetNilForLevelUpOfRecordCfg := false
			// element 0 of LevelUp
			if !cfgoMeetNilForLevelUpOfRecordCfg {
				cfgoMeetNilForLevelUpOfRecordCfg = true
				var cfgoElemOfLevelUpOfRecordCfg *CostKVS = NewCostKVS()
				{
					if record[t.getIndexInCsv("LevelUp1CostType")] != "" {
						cfgoMeetNilForLevelUpOfRecordCfg = false
						var err error
						cfgoElemOfLevelUpOfRecordCfg.CostType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("LevelUp1CostType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfLevelUpOfRecordCfg.CostType, value=%s, err:[%s]\n", record[t.getIndexInCsv("LevelUp1CostType")], err)
							} else {
								return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfLevelUpOfRecordCfg.CostType, value=%s, err:[%s]", record[t.getIndexInCsv("LevelUp1CostType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("LevelUp1CostValue")] != "" {
						cfgoMeetNilForLevelUpOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("LevelUp1CostValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfLevelUpOfRecordCfg.CostValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("LevelUp1CostValue")], err)
							} else {
								return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfLevelUpOfRecordCfg.CostValue, value=%s, err:[%s]", record[t.getIndexInCsv("LevelUp1CostValue")], err)
							}
						}
						cfgoElemOfLevelUpOfRecordCfg.CostValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForLevelUpOfRecordCfg {
					recordCfg.LevelUp = append(recordCfg.LevelUp, cfgoElemOfLevelUpOfRecordCfg)
				}
			}
			// element 1 of LevelUp
			if !cfgoMeetNilForLevelUpOfRecordCfg {
				cfgoMeetNilForLevelUpOfRecordCfg = true
				var cfgoElemOfLevelUpOfRecordCfg *CostKVS = NewCostKVS()
				{
					if record[t.getIndexInCsv("LevelUp2CostType")] != "" {
						cfgoMeetNilForLevelUpOfRecordCfg = false
						var err error
						cfgoElemOfLevelUpOfRecordCfg.CostType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("LevelUp2CostType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfLevelUpOfRecordCfg.CostType, value=%s, err:[%s]\n", record[t.getIndexInCsv("LevelUp2CostType")], err)
							} else {
								return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfLevelUpOfRecordCfg.CostType, value=%s, err:[%s]", record[t.getIndexInCsv("LevelUp2CostType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("LevelUp2CostValue")] != "" {
						cfgoMeetNilForLevelUpOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("LevelUp2CostValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfLevelUpOfRecordCfg.CostValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("LevelUp2CostValue")], err)
							} else {
								return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfLevelUpOfRecordCfg.CostValue, value=%s, err:[%s]", record[t.getIndexInCsv("LevelUp2CostValue")], err)
							}
						}
						cfgoElemOfLevelUpOfRecordCfg.CostValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForLevelUpOfRecordCfg {
					recordCfg.LevelUp = append(recordCfg.LevelUp, cfgoElemOfLevelUpOfRecordCfg)
				}
			}

		}
		// Power
		{
			if record[t.getIndexInCsv("Power")] == "" {
				recordCfg.Power = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Power")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=Power, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Power")], err)
					} else {
						return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=Power, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Power")], err)
					}
				}
				recordCfg.Power = int32(cfgoInt)
			}
		}
		// Attr
		{
			// Atk
			{
				if record[t.getIndexInCsv("AttrAtk")] == "" {
					recordCfg.Attr.Atk = 0
				} else {
					cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("AttrAtk")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=Atk, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrAtk")], err)
						} else {
							return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=Atk, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("AttrAtk")], err)
						}
					}
					recordCfg.Attr.Atk = int32(cfgoInt)
				}
			}
			// Def
			{
				if record[t.getIndexInCsv("AttrDef")] == "" {
					recordCfg.Attr.Def = 0
				} else {
					cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("AttrDef")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=Def, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrDef")], err)
						} else {
							return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=Def, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("AttrDef")], err)
						}
					}
					recordCfg.Attr.Def = int32(cfgoInt)
				}
			}
			// Hp
			{
				if record[t.getIndexInCsv("AttrHp")] == "" {
					recordCfg.Attr.Hp = 0
				} else {
					cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("AttrHp")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=Hp, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrHp")], err)
						} else {
							return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=Hp, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("AttrHp")], err)
						}
					}
					recordCfg.Attr.Hp = int32(cfgoInt)
				}
			}
			// CritChance
			{
				if record[t.getIndexInCsv("AttrCritChance")] == "" {
					recordCfg.Attr.CritChance = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrCritChance")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=CritChance, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrCritChance")], err)
						} else {
							return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=CritChance, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrCritChance")], err)
						}
					}
					recordCfg.Attr.CritChance = float32(cfgoFloat)
				}
			}
			// CritDmgUpPer
			{
				if record[t.getIndexInCsv("AttrCritDmgUpPer")] == "" {
					recordCfg.Attr.CritDmgUpPer = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrCritDmgUpPer")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=CritDmgUpPer, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrCritDmgUpPer")], err)
						} else {
							return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=CritDmgUpPer, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrCritDmgUpPer")], err)
						}
					}
					recordCfg.Attr.CritDmgUpPer = float32(cfgoFloat)
				}
			}
			// DmgUpPer
			{
				if record[t.getIndexInCsv("AttrDmgUpPer")] == "" {
					recordCfg.Attr.DmgUpPer = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrDmgUpPer")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=DmgUpPer, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrDmgUpPer")], err)
						} else {
							return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=DmgUpPer, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrDmgUpPer")], err)
						}
					}
					recordCfg.Attr.DmgUpPer = float32(cfgoFloat)
				}
			}
			// CritResistChance
			{
				if record[t.getIndexInCsv("AttrCritResistChance")] == "" {
					recordCfg.Attr.CritResistChance = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrCritResistChance")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=CritResistChance, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrCritResistChance")], err)
						} else {
							return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=CritResistChance, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrCritResistChance")], err)
						}
					}
					recordCfg.Attr.CritResistChance = float32(cfgoFloat)
				}
			}
			// BeCritDmgDownPer
			{
				if record[t.getIndexInCsv("AttrBeCritDmgDownPer")] == "" {
					recordCfg.Attr.BeCritDmgDownPer = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrBeCritDmgDownPer")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=BeCritDmgDownPer, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBeCritDmgDownPer")], err)
						} else {
							return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=BeCritDmgDownPer, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBeCritDmgDownPer")], err)
						}
					}
					recordCfg.Attr.BeCritDmgDownPer = float32(cfgoFloat)
				}
			}
			// BeDmgDownPer
			{
				if record[t.getIndexInCsv("AttrBeDmgDownPer")] == "" {
					recordCfg.Attr.BeDmgDownPer = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrBeDmgDownPer")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=BeDmgDownPer, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBeDmgDownPer")], err)
						} else {
							return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=BeDmgDownPer, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBeDmgDownPer")], err)
						}
					}
					recordCfg.Attr.BeDmgDownPer = float32(cfgoFloat)
				}
			}
			// CdRate
			{
				if record[t.getIndexInCsv("AttrCdRate")] == "" {
					recordCfg.Attr.CdRate = 0
				} else {
					cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrCdRate")], 32)
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=CdRate, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrCdRate")], err)
						} else {
							return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=CdRate, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AttrCdRate")], err)
						}
					}
					recordCfg.Attr.CdRate = float32(cfgoFloat)
				}
			}
			// Benefits
			{
				cfgoMeetNilForBenefitsOfAttrOfRecordCfg := false
				// element 0 of Benefits
				if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
					cfgoMeetNilForBenefitsOfAttrOfRecordCfg = true
					var cfgoElemOfBenefitsOfAttrOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
					{
						if record[t.getIndexInCsv("AttrBenefits1BenefitsID")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							var err error
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("AttrBenefits1BenefitsID")])
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits1BenefitsID")], err)
								} else {
									return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits1BenefitsID")], err)
								}
							}
						}
					}
					{
						if record[t.getIndexInCsv("AttrBenefits1BenefitsValue")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrBenefits1BenefitsValue")], 32)
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits1BenefitsValue")], err)
								} else {
									return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits1BenefitsValue")], err)
								}
							}
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue = float32(cfgoFloat)
						}
					}

					if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
						recordCfg.Attr.Benefits = append(recordCfg.Attr.Benefits, cfgoElemOfBenefitsOfAttrOfRecordCfg)
					}
				}
				// element 1 of Benefits
				if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
					cfgoMeetNilForBenefitsOfAttrOfRecordCfg = true
					var cfgoElemOfBenefitsOfAttrOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
					{
						if record[t.getIndexInCsv("AttrBenefits2BenefitsID")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							var err error
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("AttrBenefits2BenefitsID")])
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits2BenefitsID")], err)
								} else {
									return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits2BenefitsID")], err)
								}
							}
						}
					}
					{
						if record[t.getIndexInCsv("AttrBenefits2BenefitsValue")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrBenefits2BenefitsValue")], 32)
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits2BenefitsValue")], err)
								} else {
									return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits2BenefitsValue")], err)
								}
							}
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue = float32(cfgoFloat)
						}
					}

					if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
						recordCfg.Attr.Benefits = append(recordCfg.Attr.Benefits, cfgoElemOfBenefitsOfAttrOfRecordCfg)
					}
				}
				// element 2 of Benefits
				if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
					cfgoMeetNilForBenefitsOfAttrOfRecordCfg = true
					var cfgoElemOfBenefitsOfAttrOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
					{
						if record[t.getIndexInCsv("AttrBenefits3BenefitsID")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							var err error
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("AttrBenefits3BenefitsID")])
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits3BenefitsID")], err)
								} else {
									return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits3BenefitsID")], err)
								}
							}
						}
					}
					{
						if record[t.getIndexInCsv("AttrBenefits3BenefitsValue")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrBenefits3BenefitsValue")], 32)
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits3BenefitsValue")], err)
								} else {
									return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits3BenefitsValue")], err)
								}
							}
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue = float32(cfgoFloat)
						}
					}

					if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
						recordCfg.Attr.Benefits = append(recordCfg.Attr.Benefits, cfgoElemOfBenefitsOfAttrOfRecordCfg)
					}
				}
				// element 3 of Benefits
				if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
					cfgoMeetNilForBenefitsOfAttrOfRecordCfg = true
					var cfgoElemOfBenefitsOfAttrOfRecordCfg *BenefitsKVS = NewBenefitsKVS()
					{
						if record[t.getIndexInCsv("AttrBenefits4BenefitsID")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							var err error
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, err = configs.BenefitsTable.getIdByRef(record[t.getIndexInCsv("AttrBenefits4BenefitsID")])
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits4BenefitsID")], err)
								} else {
									return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse ref@BenefitsTable in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsID, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits4BenefitsID")], err)
								}
							}
						}
					}
					{
						if record[t.getIndexInCsv("AttrBenefits4BenefitsValue")] != "" {
							cfgoMeetNilForBenefitsOfAttrOfRecordCfg = false
							cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AttrBenefits4BenefitsValue")], 32)
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("AttrBenefits4BenefitsValue")], err)
								} else {
									return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue, value=%s, err:[%s]", record[t.getIndexInCsv("AttrBenefits4BenefitsValue")], err)
								}
							}
							cfgoElemOfBenefitsOfAttrOfRecordCfg.BenefitsValue = float32(cfgoFloat)
						}
					}

					if !cfgoMeetNilForBenefitsOfAttrOfRecordCfg {
						recordCfg.Attr.Benefits = append(recordCfg.Attr.Benefits, cfgoElemOfBenefitsOfAttrOfRecordCfg)
					}
				}

			}
		}
		// AllHeroCritDmgUp
		{
			if record[t.getIndexInCsv("AllHeroCritDmgUp")] == "" {
				recordCfg.AllHeroCritDmgUp = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AllHeroCritDmgUp")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=AllHeroCritDmgUp, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AllHeroCritDmgUp")], err)
					} else {
						return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=AllHeroCritDmgUp, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AllHeroCritDmgUp")], err)
					}
				}
				recordCfg.AllHeroCritDmgUp = float32(cfgoFloat)
			}
		}
		// IsUnlockExtraSkillEffect
		{
			if record[t.getIndexInCsv("IsUnlockExtraSkillEffect")] == "" {
				recordCfg.IsUnlockExtraSkillEffect = false
			} else {
				var err error
				recordCfg.IsUnlockExtraSkillEffect, err = strconv.ParseBool(record[t.getIndexInCsv("IsUnlockExtraSkillEffect")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=IsUnlockExtraSkillEffect, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("IsUnlockExtraSkillEffect")], err)
					} else {
						return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=IsUnlockExtraSkillEffect, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("IsUnlockExtraSkillEffect")], err)
					}
				}
			}
		}
		// RefHeroStarLevel
		{
			if record[t.getIndexInCsv("RefHeroStarLevel")] == "" {
				recordCfg.RefHeroStarLevel = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("RefHeroStarLevel")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=RefHeroStarLevel, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("RefHeroStarLevel")], err)
					} else {
						return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=RefHeroStarLevel, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("RefHeroStarLevel")], err)
					}
				}
				recordCfg.RefHeroStarLevel = int32(cfgoInt)
			}
		}
		// SkillTitle
		{
			recordCfg.SkillTitle = strings.TrimSpace(record[t.getIndexInCsv("SkillTitle")])
		}
		// SkillDesc
		{
			recordCfg.SkillDesc = strings.TrimSpace(record[t.getIndexInCsv("SkillDesc")])
		}
		// SkillDescParam
		{
			if record[t.getIndexInCsv("SkillDescParam")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("SkillDescParam")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfSkillDescParam string = ""
					cfgoElemOfSkillDescParam = strings.TrimSpace(cfgoSplitStr)

					recordCfg.SkillDescParam = append(recordCfg.SkillDescParam, cfgoElemOfSkillDescParam)
				}
			}
		}
		// DetailSkillDesc
		{
			recordCfg.DetailSkillDesc = strings.TrimSpace(record[t.getIndexInCsv("DetailSkillDesc")])
		}
		// DetailSkillDescParam
		{
			if record[t.getIndexInCsv("DetailSkillDescParam")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("DetailSkillDescParam")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfDetailSkillDescParam string = ""
					cfgoElemOfDetailSkillDescParam = strings.TrimSpace(cfgoSplitStr)

					recordCfg.DetailSkillDescParam = append(recordCfg.DetailSkillDescParam, cfgoElemOfDetailSkillDescParam)
				}
			}
		}
		// Icon
		{
			recordCfg.Icon = strings.TrimSpace(record[t.getIndexInCsv("Icon")])
		}
		// IconLocked
		{
			recordCfg.IconLocked = strings.TrimSpace(record[t.getIndexInCsv("IconLocked")])
		}
		// IsSkillPreview
		{
			if record[t.getIndexInCsv("IsSkillPreview")] == "" {
				recordCfg.IsSkillPreview = false
			} else {
				var err error
				recordCfg.IsSkillPreview, err = strconv.ParseBool(record[t.getIndexInCsv("IsSkillPreview")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=IsSkillPreview, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("IsSkillPreview")], err)
					} else {
						return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=IsSkillPreview, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("IsSkillPreview")], err)
					}
				}
			}
		}
		// SkillPreview
		{
			recordCfg.SkillPreview = strings.TrimSpace(record[t.getIndexInCsv("SkillPreview")])
		}
		// IsPopUp
		{
			if record[t.getIndexInCsv("IsPopUp")] == "" {
				recordCfg.IsPopUp = false
			} else {
				var err error
				recordCfg.IsPopUp, err = strconv.ParseBool(record[t.getIndexInCsv("IsPopUp")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, varName=IsPopUp, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("IsPopUp")], err)
					} else {
						return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, varName=IsPopUp, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("IsPopUp")], err)
					}
				}
			}
		}
		// PopupName
		{
			recordCfg.PopupName = strings.TrimSpace(record[t.getIndexInCsv("PopupName")])
		}
		// PopupDesc
		{
			recordCfg.PopupDesc = strings.TrimSpace(record[t.getIndexInCsv("PopupDesc")])
		}
		// PopupDescParam
		{
			if record[t.getIndexInCsv("PopupDescParam")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("PopupDescParam")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfPopupDescParam string = ""
					cfgoElemOfPopupDescParam = strings.TrimSpace(cfgoSplitStr)

					recordCfg.PopupDescParam = append(recordCfg.PopupDescParam, cfgoElemOfPopupDescParam)
				}
			}
		}
		// PVEPassiveSkillEffect
		{
			if record[t.getIndexInCsv("PVEPassiveSkillEffect")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("PVEPassiveSkillEffect")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfPVEPassiveSkillEffect int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfPVEPassiveSkillEffect = 0
					} else {
						var err error
						cfgoElemOfPVEPassiveSkillEffect, err = configs.HeroSkillEffectTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroGeneTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in vector, varName=PVEPassiveSkillEffect, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[HeroGeneTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in vector, varName=PVEPassiveSkillEffect, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.PVEPassiveSkillEffect = append(recordCfg.PVEPassiveSkillEffect, cfgoElemOfPVEPassiveSkillEffect)
				}
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [HeroGeneTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[HeroGeneTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *HeroGeneTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "HeroGeneTable.csv") && (!strings.HasPrefix(fileName, "HeroGeneTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for HeroGeneTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[HeroGeneTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[HeroGeneTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[HeroGeneTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[HeroGeneTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[HeroGeneTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[HeroGeneTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[HeroGeneTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[HeroGeneTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [HeroGeneTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *HeroGeneTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[HeroGeneTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [HeroGeneTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *HeroGeneTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[HeroGeneTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *HeroGeneTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[HeroGeneTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[HeroGeneTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
