package main_line_stage

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"context"
	"fmt"
	"math"
	"math/rand"
	"net/http"
	"sort"
	"time"
)

const ignoreSelect = 0
const selected = 1
const unSelect = 2

func RefreshRougeByKillMonster(ctx context.Context, uid int64, w http.ResponseWriter) []int32 {
	rewardWeightLine := cfg_mgr.Cfg.MainLevelRogueRewardWeightTable.Get(1)
	rougeIdWeights := map[int32]int32{}
	for i, _ := range rewardWeightLine.KillRewardRougeTabCnt {
		rougeIdWeights[rewardWeightLine.KillRewardRougeTabCnt[i]] = rewardWeightLine.KillRewardRougeTabCntWeight[i]
	}
	needNum := util.RandomByWeight(rougeIdWeights)
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	if stageModel.EliteRefreshCardIds() != nil {
		needNum = int32(len(stageModel.EliteRefreshCardIds()))
	}

	//先随机已上阵未选中的
	rougeIds := RefreshRougeGroupByCondition(ctx, uid, needNum, true, false, w)
	if int(needNum) <= len(rougeIds) {
		return rougeIds
	}
	return rougeIds
}

// 检测是否满足前置卡组
func checkPreRougeTabGroup(ctx context.Context, uid int64, curLine *servercfg.RougeTabTableCfg, cardIdCounts *map[int32]int32) bool {
	//检测基因等级
	if curLine.ReqHeroGeneLevel != 0 {
		genLevelLine := cfg_mgr.Cfg.HeroGeneTable.Get(curLine.ReqHeroGeneLevel)
		if genLevelLine == nil {
			return false
		}
		heroModel, _ := model.GetHero(ctx, uid, genLevelLine.HeroID)
		if heroModel == nil {
			return false
		}
		if heroModel.GeneConfigId() < curLine.ReqHeroGeneLevel {
			return false
		}
	}
	//检测前置卡组
	if curLine.ReqRougeTab != nil {
		for i, _ := range curLine.ReqRougeTab {
			if len(curLine.ReqRougeTabCnt) < i+1 {
				continue
			}
			if (*cardIdCounts)[curLine.ReqRougeTab[i]] < curLine.ReqRougeTabCnt[i] {
				return false
			}
		}
	}
	return true
}

// 刷新上阵卡
func refreshConfigCard(ctx context.Context, uid int64, heroIds []int32, cardIds []int32, cardIdCounts map[int32]int32, w http.ResponseWriter) int32 {
	//当前上阵英雄
	userModel, _ := model.GetUserModel(ctx, uid)
	battlePosInfo := userModel.BattlePosInfo()
	if battlePosInfo == nil {
		return 0
	}
	rougeTabGroupRandomTableLine := cfg_mgr.Cfg.RougeTabGroupRandomTable.FilterSlice(func(line *servercfg.RougeTabGroupRandomTableCfg) bool {
		return line.HeroDeployedCnt == int32(len(heroIds))
	})
	if len(rougeTabGroupRandomTableLine) == 0 {
		return 0
	}
	chance := rougeTabGroupRandomTableLine[0].Chance
	if rand.Float32() > chance {
		//直接刷普通卡
		return 0
	}
	//刷新上阵卡
	rougeTabLines := cfg_mgr.Cfg.RougeTabTable.FilterSlice(func(line *servercfg.RougeTabTableCfg) bool {
		return line.RougeTabType == servercfg.RougeTabType_ConfigTab
	})
	if rougeTabLines == nil {
		return 0
	}
	rougeIdWeights := map[int32]int32{}
	for _, line := range rougeTabLines {
		//已经上阵的英雄不能刷新上阵卡
		if cardIdCounts[line.Id] >= line.Limit {
			continue
		}
		rougeTabGroupLine := cfg_mgr.Cfg.RougeTabGroupTable.Get(line.RougeTabGroup)
		flag := true
		for _, v := range cardIds {
			if v == line.Id {
				flag = false
				break
			}
		}
		if flag {
			for _, v := range heroIds {
				if v == rougeTabGroupLine.Hero {
					flag = false
					break
				}
			}
		}

		if flag {
			//没有的英雄也不能刷新
			heroModel, _ := model.GetHero(ctx, uid, rougeTabGroupLine.Hero)
			if heroModel != nil {
				rougeIdWeights[line.Id] = line.Weight
			}
		}
	}
	if rougeIdWeights != nil {
		if w != nil {
			fmt.Fprintf(w, "当前上阵卡池")
			//fmt.Fprintf(w, "%v\n", rougeIdWeights)
			for k1, v1 := range rougeIdWeights {
				curRougeLine := cfg_mgr.Cfg.RougeTabTable.Get(k1)
				fmt.Fprintf(w, "%d:%d:%d  ", k1, curRougeLine.Limit, v1)
			}
			fmt.Fprintf(w, "\n")
		}
		rougeId := util.RandomByWeight(rougeIdWeights)
		return rougeId
	}
	return 0
}

// 计算肉鸽技能的权重
func calcRougeGroupWeight(ctx context.Context, uid int64, heroids []int32) map[int32]int64 {

	initWeightValue := cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_ROUGE_INITIAL_WEIGHT)      //初始权重
	rougeSkillCoeff := cfg_mgr.GetGameConfigFloat64Value(constDef.GAME_CONFIG_ROUGE_SKILL_COEFF)       //技能等级限制系数
	rougeContCoeff := cfg_mgr.GetGameConfigFloat64Value(constDef.GAME_CONFIG_ROUGE_CONT_COEFF)         //连续选择限制系数
	rougeMinWeight := cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_ROUGE_MIN_WEIGHT)           //最低权重
	rougeRecoveryCoeff := cfg_mgr.GetGameConfigFloat64Value(constDef.GAME_CONFIG_ROUGE_RECOVERY_COEFF) // 恢复权重系数
	//计算英雄和 rougeGroupLine 的对应表
	heroIdToGroupLine := map[int32]*servercfg.RougeTabGroupTableCfg{}
	groupLines := cfg_mgr.Cfg.RougeTabGroupTable.GetAll()
	for _, groupLine := range groupLines {
		heroIdToGroupLine[groupLine.Hero] = groupLine
	}

	stageModel, _ := model.GetMainLineStage(ctx, uid)
	selectCards := stageModel.CurSelectCardIds()
	//计算 英雄权重
	rougeGroupWeight := map[int32]int64{}
	//英雄等级
	rougegroupLevel := map[int32]int32{}

	//连续选中次数
	selectTimes := map[int32]int32{}
	for _, v := range selectCards {
		line := cfg_mgr.Cfg.RougeTabTable.Get(v)
		if line.RougeTabType == servercfg.RougeTabType_ConfigTab {
			continue
		}
		rougeTabGroupLine := cfg_mgr.Cfg.RougeTabGroupTable.Get(line.RougeTabGroup)
		if rougeTabGroupLine == nil {
			continue
		}
		//更新等级
		rougegroupLevel[rougeTabGroupLine.Id]++
		//计算基础权重=初始权重*技能等级限制系数^(技能等级-1)
		curWeight := rougeGroupWeight[rougeTabGroupLine.Id]
		if curWeight == 0 {
			curWeight = initWeightValue
		}
		curWeight = int64(float64(curWeight) * math.Pow(rougeSkillCoeff, float64(rougegroupLevel[rougeTabGroupLine.Id])))

		//根据连续次数(更新后的)计算新的权重=基础权重*连续选择限制系数^连续选择次数
		if selectTimes[rougeTabGroupLine.Id] > 0 {
			selectTimes[rougeTabGroupLine.Id]++
			curWeight = int64(float64(curWeight) * math.Pow(rougeContCoeff, float64(selectTimes[rougeTabGroupLine.Id])))
		} else {
			selectTimes = map[int32]int32{}
		}
		//.确保新权重大于最低权重，否则设为最低权重
		if curWeight < rougeMinWeight {
			curWeight = rougeMinWeight
		}
		rougeGroupWeight[rougeTabGroupLine.Id] = curWeight
		//对于未被选中的英雄:
		for _, heroId := range heroids {
			unSelectGroupLine := heroIdToGroupLine[heroId]
			if unSelectGroupLine == nil {
				continue
			}
			if unSelectGroupLine.Id == rougeTabGroupLine.Id {
				continue
			}
			//	1.新的权重=当前权重*(1-恢复系数)+初始权重*恢复系数
			curUnselectWeight := rougeGroupWeight[unSelectGroupLine.Id]
			if curUnselectWeight == 0 {
				curUnselectWeight = initWeightValue
			}
			curUnselectWeight = int64(float64(curUnselectWeight)*(1-rougeRecoveryCoeff) + float64(initWeightValue)*rougeRecoveryCoeff)

			//.确保新权重大于最低权重，否则设为最低权重
			if curUnselectWeight < rougeMinWeight {
				curUnselectWeight = rougeMinWeight
			}
			rougeGroupWeight[unSelectGroupLine.Id] = curUnselectWeight
		}

	}
	return rougeGroupWeight
}

func RefreshRougeGroupByCondition(ctx context.Context, uid int64, count int32, isBattle bool, isFillInCards bool, w http.ResponseWriter) []int32 {
	userModel, _ := model.GetUserModel(ctx, uid)
	if userModel == nil {
		return nil
	}
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	heroIds := []int32{}
	if isBattle {
		selectIds := stageModel.CurSelectCardIds()
		for _, v := range selectIds {
			line := cfg_mgr.Cfg.RougeTabTable.Get(v)
			if line != nil && line.RougeTabType == servercfg.RougeTabType_ConfigTab {
				rougeTabGroupLine := cfg_mgr.Cfg.RougeTabGroupTable.Get(line.RougeTabGroup)
				if rougeTabGroupLine != nil {
					heroIds = append(heroIds, rougeTabGroupLine.Hero)

				}
			}
		}
	} else {
		heroModels, _ := model.GetAllHeros(ctx, uid)
		if heroModels != nil {
			for _, v := range heroModels {
				if v.IsBattle() {
					continue
				}
				heroIds = append(heroIds, v.ConfigId())
			}
		}
	}

	rougeIdWeights := map[int32]int32{}
	rougeIdToSkillId := map[int32]int32{}
	cardIdCounts := map[int32]int32{}
	banCardIds := map[int32]int32{}
	unLimitIds := map[int32]bool{}
	rougeGroupToWeight := calcRougeGroupWeight(ctx, uid, heroIds)
	for _, v := range heroIds {
		flag := true
		for _, id := range stageModel.KilledCardIds() {
			if id == v {
				flag = false
				break
			}
		}
		if !flag {
			continue
		}
		skillModels := model.GetHeroSkills(ctx, uid, v)
		if skillModels == nil {
			continue
		}
		for _, skillModel := range skillModels {
			skillLine := cfg_mgr.Cfg.HeroSkillGroupTable.Get(skillModel.GroupId())
			if skillLine.HeroSkillType == servercfg.HeroSkillType_HitSkill {
				rougeTabLines := cfg_mgr.Cfg.RougeTabGroupTable.FilterSlice(func(line *servercfg.RougeTabGroupTableCfg) bool {
					return line.HeroSkillGroup == skillModel.GroupId()
				})
				if rougeTabLines == nil {
					continue
				}
				if rougeGroupToWeight[rougeTabLines[0].Id] > 0 {
					rougeIdWeights[rougeTabLines[0].Id] = int32(rougeGroupToWeight[rougeTabLines[0].Id])
				} else {
					initWeightValue := cfg_mgr.GetGameConfigInt64Value(constDef.GAME_CONFIG_ROUGE_INITIAL_WEIGHT) //初始权重
					rougeIdWeights[rougeTabLines[0].Id] = int32(initWeightValue)
				}

				rougeIdToSkillId[rougeTabLines[0].Id] = skillModel.ConfigId()
			}
		}
		//添加填进去的卡
		heroModel, _ := model.GetHero(ctx, uid, v)
		if heroModel != nil {
			geneLine := cfg_mgr.Cfg.HeroGeneTable.Get(heroModel.GeneConfigId())
			if geneLine != nil && geneLine.PVEPassiveSkillEffect != nil {
				for _, j := range geneLine.PVEPassiveSkillEffect {
					heroSkillEffectLine := cfg_mgr.Cfg.HeroSkillEffectTable.Get(j)
					if heroSkillEffectLine != nil {
						if heroSkillEffectLine.SkillEffectType == servercfg.HeroSkillEffectType_GetTab { //直接放入
							if heroSkillEffectLine.RougeTab != 0 {
								cardIdCounts[heroSkillEffectLine.RougeTab]++
							}
						} else if heroSkillEffectLine.SkillEffectType == servercfg.HeroSkillEffectType_ReplacedTab {
							if heroSkillEffectLine.RougeTabReplace != 0 {
								banCardIds[heroSkillEffectLine.RougeTabReplace] = heroSkillEffectLine.RougeTabReplaced
							}
						} else if heroSkillEffectLine.SkillEffectType == servercfg.HeroSkillEffectType_UnlockTab { //取消限制
							if heroSkillEffectLine.RougeTabUnlock != nil {
								for _, e := range heroSkillEffectLine.RougeTabUnlock {
									unLimitIds[e] = true
								}

							}

						}
					}
				}
			}
		}

	}
	if w != nil {
		fmt.Fprintf(w, "当前肉鸽组权重")
		fmt.Fprintf(w, "%v\n", rougeIdWeights)
	}
	rougeIds := []int32{}

	if stageModel.CurSelectCardIds() != nil {
		for _, v := range stageModel.CurSelectCardIds() {
			cardIdCounts[v]++
		}
	}
	if stageModel.EliteRefreshCardIds() != nil {
		for _, v := range stageModel.EliteRefreshCardIds() {
			cardIdCounts[v]++
			line := cfg_mgr.Cfg.RougeTabTable.Get(v)
			if line != nil && line.RougeTabType == servercfg.RougeTabType_ConfigTab {
				rougeTabGroupLine := cfg_mgr.Cfg.RougeTabGroupTable.Get(line.RougeTabGroup)
				if rougeTabGroupLine != nil {
					heroIds = append(heroIds, rougeTabGroupLine.Hero)

				}
			}
		}
	}
	for banCardIdPre, banCardIdReplace := range banCardIds {
		if cardIdCounts[banCardIdReplace] > 0 {
			cardIdCounts[banCardIdPre] += cardIdCounts[banCardIdReplace]
		}
	}
	rougeLevel := GetRougeLevel(ctx, uid, 0)
	if w != nil {
		fmt.Fprintf(w, "当前所有技能")
		fmt.Fprintf(w, "%v\n", cardIdCounts)
		fmt.Fprintf(w, "当前替换技能")
		fmt.Fprintf(w, "%v\n", banCardIds)
		fmt.Fprintf(w, "当前非限制技能")
		fmt.Fprintf(w, "%v\n", unLimitIds)
	}
	for i := 0; i < 10; i++ {
		rougeId := int32(0)
		if isFillInCards {
			rougeId = refreshConfigCard(ctx, uid, heroIds, rougeIds, cardIdCounts, w)
		} else {
			if !isBattle {
				rougeId = refreshConfigCard(ctx, uid, []int32{}, rougeIds, cardIdCounts, w)
			}
		}

		if rougeId != 0 {
			rougeIds = append(rougeIds, rougeId)
		} else {
			rougeGroupId := util.RandomByWeight(rougeIdWeights)
			if rougeGroupId != 0 {
				rougeId = refreshRougeTabIds(ctx, uid, rougeGroupId, rougeIds, &cardIdCounts, banCardIds, unLimitIds, rougeLevel, w)
				if rougeId != 0 {
					rougeIds = append(rougeIds, rougeId)
				} else {
					rougeIdWeights[rougeGroupId] = 0
				}
			}
		}

		if len(rougeIds) >= int(count) {
			break
		}

	}
	lenRougeIds := len(rougeIds)
	if lenRougeIds < int(count) {
		//添加通用技能
		for i := 0; i < int(count)-lenRougeIds; i++ {
			rougeIds = append(rougeIds, refreshSurplusCard(ctx, uid, rougeIds, &cardIdCounts))
		}
	}
	if isFillInCards {
		stageModel.SetRefreshCardIds(ctx, rougeIds)
	} else {
		stageModel.SetEliteRefreshCardIds(ctx, rougeIds)
	}

	return rougeIds
}

// 手动刷新肉鸽卡
func RefreshRougeGroup(ctx context.Context, uid int64, dungeonType int32, w http.ResponseWriter) []int32 {
	ret := RefreshNewBieRougeGroup(ctx, uid)
	if ret != nil {
		limitNum := int32(3)
		if len(ret) < int(limitNum) {
			addRet := RefreshRougeGroupByCondition(ctx, uid, limitNum, true, true, w)
			for _, v := range addRet {
				flag := true
				for _, r := range ret {
					if r == v {
						flag = false
						break
					}
				}
				if flag {
					ret = append(ret, v)
				}
				if len(ret) >= int(limitNum) {
					break
				}
			}
		}
		return ret
	}
	if dungeonType == int32(servercfg.DungeonType_GeneDungeon) {
		return RefreshRougeGroupByCondition(ctx, uid, 4, true, true, w)
	}
	return RefreshRougeGroupByCondition(ctx, uid, 3, true, true, w)
}

// 新手引导数据
func RefreshNewBieRougeGroup(ctx context.Context, uid int64) []int32 {
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	//if stageModel.UnlockStageId() != 1 {
	//	return nil
	//}
	rougeLevel := GetRougeLevel(ctx, uid, 0)
	newBieLines := cfg_mgr.Cfg.RougeTabNewbieTable.FilterSlice(func(v *servercfg.RougeTabNewbieTableCfg) bool {
		return v.Chapter == stageModel.StageId() && rougeLevel-1 == v.Count
	})
	if newBieLines == nil || len(newBieLines) == 0 {
		return nil
	}
	stageModel.SetRefreshCardIds(ctx, newBieLines[0].RougeTabGroup)
	return newBieLines[0].RougeTabGroup
}

type LevelCost struct {
	LevelUpCost int32
	Level       int32
}

// 获取肉鸽等级
func GetRougeLevel(ctx context.Context, uid int64, dungeonType int32) int32 {
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	monsterNum := stageModel.CurKillMonster()
	stageId := stageModel.StageId()
	var levelCostMap []*LevelCost
	if dungeonType == 0 {
		rougeTabLines := cfg_mgr.Cfg.MainChapterLevelTable.FilterSlice(func(line *servercfg.MainChapterLevelTableCfg) bool {
			return line.Chapter == stageId
		})
		sort.Slice(rougeTabLines, func(i, j int) bool {
			return rougeTabLines[i].Level < rougeTabLines[j].Level
		})
		for _, v := range rougeTabLines {
			levelCostMap = append(levelCostMap, &LevelCost{
				Level:       v.Level,
				LevelUpCost: v.LevelUpCost.CostValue,
			})
		}

	} else {

		rougeTabLines := cfg_mgr.Cfg.DungeonChapterLevelTable.FilterSlice(func(line *servercfg.DungeonChapterLevelTableCfg) bool {
			return line.Dungeon == dungeonType
		})
		sort.Slice(rougeTabLines, func(i, j int) bool {
			return rougeTabLines[i].Level < rougeTabLines[j].Level
		})
		for _, v := range rougeTabLines {
			levelCostMap = append(levelCostMap, &LevelCost{
				Level:       v.Level,
				LevelUpCost: v.LevelUpCost.CostValue,
			})
		}
	}
	maxLevel := int32(0)
	for _, v := range levelCostMap {
		if monsterNum < v.LevelUpCost {
			return v.Level
		}
		monsterNum -= v.LevelUpCost
		maxLevel = util.MaxInt32(maxLevel, v.Level)
	}
	return maxLevel

}

// 判断是否合法
func searchInIds(ctx context.Context, uid int64, curLine *servercfg.RougeTabTableCfg, ids []int32, cardIdCounts *map[int32]int32) bool {
	//已刷新出来的不能再刷新
	for _, v := range ids {
		if curLine.Id == v {
			return true
		}

	}
	//互斥组不能同时出现
	for k, _ := range *cardIdCounts {
		if curLine.MutexGroup != 0 {
			line := cfg_mgr.Cfg.RougeTabTable.Get(k)
			if line != nil && line.MutexGroup == curLine.MutexGroup {
				return true
			}
		}
	}
	//超过个数的不刷新
	if (*cardIdCounts)[curLine.Id] >= curLine.Limit {
		return true
	}
	//已经上阵的英雄不能刷新上阵卡
	if !checkRougeTabType(ctx, uid, curLine) {
		return true
	}
	return false
}

// 已经上阵的英雄不能刷新上阵卡
func checkRougeTabType(ctx context.Context, uid int64, curLine *servercfg.RougeTabTableCfg) bool {
	//非上阵卡都可以刷新
	if curLine.RougeTabType != servercfg.RougeTabType_ConfigTab {
		return true
	}
	//如果是已经抽出来的上阵卡，不能出现第二次
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	if stageModel.CurSelectCardIds() != nil {
		for _, v := range stageModel.CurSelectCardIds() {
			if v == curLine.Id {
				return false
			}
		}
	}
	if stageModel.EliteRefreshCardIds() != nil {
		for _, v := range stageModel.EliteRefreshCardIds() {
			if v == curLine.Id {
				return false
			}
		}
	}
	//如果是自己没有的英雄，不能刷新
	rougeTabGroupLine := cfg_mgr.Cfg.RougeTabGroupTable.Get(curLine.RougeTabGroup)
	heroModel, _ := model.GetHero(ctx, uid, rougeTabGroupLine.Hero)
	if heroModel == nil {
		return false
	}
	//如果是已经上阵的英雄，不能刷新上阵卡
	if heroModel.IsBattle() {
		return false
	}
	return true
}

func getUnlockHeroPosNum(ctx context.Context, uid int64) int32 {
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	lines := cfg_mgr.Cfg.HeroConfigTable.Filter(func(line *servercfg.HeroConfigTableCfg) bool {
		return line.Unlock <= stageModel.UnlockStageId()
	})
	return int32(len(lines))
}

// 获取肉鸽卡中上阵卡数量
func getRougeHeroConfigNum(ctx context.Context, uid int64) int32 {
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	num := int32(0)
	cardIds := stageModel.CurSelectCardIds()
	if cardIds != nil {
		for _, v := range cardIds {
			line := cfg_mgr.Cfg.RougeTabTable.Get(v)
			if line != nil && line.RougeTabType == servercfg.RougeTabType_ConfigTab {
				num++
			}
		}
	}
	//选择中的
	eliteCards := stageModel.EliteRefreshCardIds()
	if eliteCards != nil {
		for _, v := range eliteCards {
			line := cfg_mgr.Cfg.RougeTabTable.Get(v)
			if line != nil && line.RougeTabType == servercfg.RougeTabType_ConfigTab {
				num++
			}
		}
	}
	return num
}

func refreshSurplusCard(ctx context.Context, uid int64, curIds []int32, cardIdCounts *map[int32]int32) int32 {
	rougeTabLines := cfg_mgr.Cfg.RougeTabTable.FilterSlice(func(line *servercfg.RougeTabTableCfg) bool {
		return line.IsSurplus == true
	})
	rougeIdWeights := map[int32]int32{}
	for _, line := range rougeTabLines {
		if searchInIds(ctx, uid, line, curIds, cardIdCounts) {
			continue
		}
		rougeIdWeights[line.Id] = line.Weight
	}
	rougeId := util.RandomByWeight(rougeIdWeights)
	return rougeId

}

func refreshRougeTabIds(ctx context.Context, uid int64, groupId int32, curIds []int32, cardIdCounts *map[int32]int32, banCardIds map[int32]int32, unLimitIds map[int32]bool, rougeLevel int32, w http.ResponseWriter) int32 {
	rougeTabLines := cfg_mgr.Cfg.RougeTabTable.FilterSlice(func(line *servercfg.RougeTabTableCfg) bool {
		return line.RougeTabGroup == groupId && line.IsSurplus == false
	})
	rougeIdWeights := map[int32]int32{}
	for _, line := range rougeTabLines {
		//如果英雄上阵位置满了就不能继续刷新上阵卡
		if line.RougeTabType == servercfg.RougeTabType_ConfigTab && getRougeHeroConfigNum(ctx, uid) >= getUnlockHeroPosNum(ctx, uid)-1 {
			continue
		}
		if searchInIds(ctx, uid, line, curIds, cardIdCounts) {
			continue
		}
		//检测前置
		if !unLimitIds[line.Id] && !checkPreRougeTabGroup(ctx, uid, line, cardIdCounts) {
			continue
		}
		if banCardIds[line.Id] != 0 {
			continue
		}
		rougeWeightCoefLine := cfg_mgr.Cfg.RougeWeightCoef.Get(line.WeightCoef)
		coef := float32(1)
		if rougeWeightCoefLine != nil {
			if rougeLevel-2 < 0 {
				rougeLevel = 1
			} else {
				coef = rougeWeightCoefLine.UpgradeTime1Coef[rougeLevel-2].UpgradeTimeCoef
			}

		}
		rougeIdWeights[line.Id] = int32(float32(line.Weight) * coef)
	}
	if w != nil {
		fmt.Fprintf(w, "当前卡池")
		//fmt.Fprintf(w, "%v\n", rougeIdWeights)
		for k1, v1 := range rougeIdWeights {
			curRougeLine := cfg_mgr.Cfg.RougeTabTable.Get(k1)
			fmt.Fprintf(w, "%d:%d:%d  ", k1, curRougeLine.Limit-(*cardIdCounts)[k1], v1)
		}

		fmt.Fprintf(w, "\n")
	}
	rougeId := util.RandomByWeight(rougeIdWeights)
	if rougeId != 0 {
		curIds = append(curIds, rougeId)
	}
	return rougeId
}

func SelectRougeSkillHandler(ctx context.Context, uid int64, skillId int32) error {
	stageModel, err := model.GetMainLineStage(ctx, uid)
	if err != nil {
		return err
	}
	// 原有逻辑
	curSelectIds := stageModel.CurSelectCardIds()
	if curSelectIds == nil {
		curSelectIds = []int32{}
	}
	curSelectIds = append(curSelectIds, skillId)
	stageModel.SetCurSelectCardIds(ctx, curSelectIds)
	stageModel.SetRefreshCardIds(ctx, nil)
	stageLine := cfg_mgr.Cfg.MainLevelTable.Get(stageModel.StageId())

	// 获取当前关卡信息
	questType := "tower" // 默认为塔防关卡
	if stageModel.IsElite() {
		questType = "tower_elite"
	}
	if stageLine.LevelType == servercfg.LevelType_ParkOur {
		questType = "parkour"
	}

	// 获取技能/卡牌信息
	rougeTabLine := cfg_mgr.Cfg.RougeTabTable.Get(skillId)
	groupLine := cfg_mgr.Cfg.RougeTabGroupTable.Get(rougeTabLine.RougeTabGroup)
	heroId := int32(0)
	if groupLine != nil {
		heroId = groupLine.Hero
	}

	// 计算当前轮次
	round := stageModel.CurNormalCardsRefreshTimes()
	stageModel.SetCurNormalCardsRefreshTimes(ctx, round+1)

	// 构建打点数据
	logger.LogCustom(ctx, uid, "quest", logger.NewDetail().
		Put("action", "click_select").
		Put("version", "2.0.22").
		Put("quest_type", questType).
		Put("group_id", stageLine.Chapter).
		Put("quest_id", 1).
		Put("round", round).
		Put("select_id", skillId).
		Put("select_name", rougeTabLine.StringId).
		Put("cor_hero_id", heroId).
		Put("time", time.Now().Unix()-stageModel.CurStageStartTime()).
		Put("type", "1"))

	return nil
}

// 精英肉鸽卡随机
func RefreshEliteRougeGroup(ctx context.Context, uid int64) []int32 {
	//已上阵英雄且未选择过
	userModel, _ := model.GetUserModel(ctx, uid)
	if userModel == nil {
		return nil
	}
	return nil
}

// 选取精英怪肉鸽卡牌
func SelectEliteRougeSkill(ctx context.Context, uid int64) error {
	stageModel, err := model.GetMainLineStage(ctx, uid)
	if err != nil {
		return err
	}
	rougeIds := stageModel.EliteRefreshCardIds()
	curSelectIds := stageModel.CurSelectCardIds()
	if curSelectIds == nil {
		curSelectIds = []int32{}
	}
	// 原有逻辑
	for _, v := range rougeIds {
		curSelectIds = append(curSelectIds, v)

	}
	stageModel.SetCurSelectCardIds(ctx, curSelectIds)
	stageModel.SetEliteRefreshCardIds(ctx, nil)

	// 获取当前关卡信息
	stageLine := cfg_mgr.Cfg.MainLevelTable.Get(stageModel.StageId())
	questType := "tower_elite"
	if stageLine.LevelType == servercfg.LevelType_ParkOur {
		questType = "parkour"
	}

	// 获取当前轮次并递增
	round := stageModel.CurEliteCardsRefreshTimes()
	stageModel.SetCurEliteCardsRefreshTimes(ctx, round+1)

	// 构建打点数据
	logger.LogCustom(ctx, uid, "quest", logger.NewDetail().
		Put("action", "skill_choice").
		Put("version", "2.0.22").
		Put("quest_type", questType).
		Put("group_id", stageLine.Chapter).
		Put("quest_id", 1).
		Put("round", round).
		Put("num", len(rougeIds)).
		Put("time", time.Now().Unix()-stageModel.CurStageStartTime()).
		Put("skill_list", rougeIds))

	return nil
}
