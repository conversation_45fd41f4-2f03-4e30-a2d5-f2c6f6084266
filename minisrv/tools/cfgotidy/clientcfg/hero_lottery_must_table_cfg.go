// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type HeroLotteryMustTableCfg struct {
	Id                  int32                        `json:"Id"`               // Id
	HeroLotteryGroup    int32                        `json:"HeroLotteryGroup"` // 所属召唤池
	HeroLotteryGroupRef *HeroLotteryGroupTableCfg    `json:"-"`                // 所属召唤池
	MustCnt             int32                        `json:"MustCnt"`          // 必中次数
	IncreaseCnt         int32                        `json:"IncreaseCnt"`      // 橙卡在第几次开始概率递增
	Increment           []int32                      `json:"Increment"`        // 橙卡权重递增幅度
	IsRefresh           bool                         `json:"IsRefresh"`        // 中了之后是否刷新
	Desc                string                       `json:"Desc"`             // 必中描述
	Must11              []int32                      `json:"Must11"`           // 前11抽安排
	Must11Ref           []*HeroLotteryRandomTableCfg `json:"-"`                // 前11抽安排
	MustSSR             *HeroLotterySSR              `json:"MustSSR"`          // 橙卡保底安排
	FirstHero           []int32                      `json:"FirstHero"`        // 首抽保底英雄池
	FirstHeroRef        []*HeroTableCfg              `json:"-"`                // 首抽保底英雄池
	FirstHeroWeight     []int32                      `json:"FirstHeroWeight"`  // 首抽保底英雄权重
	TenthHero           []int32                      `json:"TenthHero"`        // 第十抽保底英雄池
	TenthHeroRef        []*HeroTableCfg              `json:"-"`                // 第十抽保底英雄池
	TenthHeroWeight     []int32                      `json:"TenthHeroWeight"`  // 第十抽保底英雄权重
}

func NewHeroLotteryMustTableCfg() *HeroLotteryMustTableCfg {
	return &HeroLotteryMustTableCfg{
		Id:                  0,
		HeroLotteryGroup:    0,
		HeroLotteryGroupRef: nil,
		MustCnt:             0,
		IncreaseCnt:         0,
		Increment:           []int32{},
		IsRefresh:           false,
		Desc:                "",
		Must11:              []int32{},
		Must11Ref:           []*HeroLotteryRandomTableCfg{},
		MustSSR:             NewHeroLotterySSR(),
		FirstHero:           []int32{},
		FirstHeroRef:        []*HeroTableCfg{},
		FirstHeroWeight:     []int32{},
		TenthHero:           []int32{},
		TenthHeroRef:        []*HeroTableCfg{},
		TenthHeroWeight:     []int32{},
	}
}

func NewMockHeroLotteryMustTableCfg() *HeroLotteryMustTableCfg {
	return &HeroLotteryMustTableCfg{
		Id:                  0,
		HeroLotteryGroup:    0,
		HeroLotteryGroupRef: nil,
		MustCnt:             0,
		IncreaseCnt:         0,
		Increment:           []int32{0},
		IsRefresh:           false,
		Desc:                "",
		Must11:              []int32{0},
		Must11Ref:           []*HeroLotteryRandomTableCfg{},
		MustSSR:             NewMockHeroLotterySSR(),
		FirstHero:           []int32{0},
		FirstHeroRef:        []*HeroTableCfg{},
		FirstHeroWeight:     []int32{0},
		TenthHero:           []int32{0},
		TenthHeroRef:        []*HeroTableCfg{},
		TenthHeroWeight:     []int32{0},
	}
}

type HeroLotteryMustTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*HeroLotteryMustTableCfg
	localIds         map[int32]struct{}
}

func NewHeroLotteryMustTable(configs *Configs) *HeroLotteryMustTable {
	return &HeroLotteryMustTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*HeroLotteryMustTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *HeroLotteryMustTable) Get(key int32) *HeroLotteryMustTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *HeroLotteryMustTable) GetAll() map[int32]*HeroLotteryMustTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *HeroLotteryMustTable) put(key int32, value *HeroLotteryMustTableCfg, local bool) *HeroLotteryMustTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *HeroLotteryMustTable) putFromInheritedTable(key int32, value *HeroLotteryMustTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[HeroLotteryMustTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *HeroLotteryMustTable) Put(key int32, value *HeroLotteryMustTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[HeroLotteryMustTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *HeroLotteryMustTable) PutAll(m map[int32]*HeroLotteryMustTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *HeroLotteryMustTable) Range(f func(v *HeroLotteryMustTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *HeroLotteryMustTable) Filter(filterFuncs ...func(v *HeroLotteryMustTableCfg) bool) map[int32]*HeroLotteryMustTableCfg {
	filtered := map[int32]*HeroLotteryMustTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *HeroLotteryMustTable) FilterSlice(filterFuncs ...func(v *HeroLotteryMustTableCfg) bool) []*HeroLotteryMustTableCfg {
	filtered := []*HeroLotteryMustTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *HeroLotteryMustTable) FilterKeys(filterFuncs ...func(v *HeroLotteryMustTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *HeroLotteryMustTable) satisfied(v *HeroLotteryMustTableCfg, filterFuncs ...func(v *HeroLotteryMustTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *HeroLotteryMustTable) setupIndexes() error {
	return nil
}

func (t *HeroLotteryMustTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *HeroLotteryMustTableCfg) bindRefs(c *Configs) {
	r.HeroLotteryGroupRef = c.HeroLotteryGroupTable.Get(r.HeroLotteryGroup)
	for _, e := range r.Must11 {
		cfgoRefRecord := c.HeroLotteryRandomTable.Get(e)
		r.Must11Ref = append(r.Must11Ref, cfgoRefRecord)
	}
	r.MustSSR.bindRefs(c)
	for _, e := range r.FirstHero {
		cfgoRefRecord := c.HeroTable.Get(e)
		r.FirstHeroRef = append(r.FirstHeroRef, cfgoRefRecord)
	}
	for _, e := range r.TenthHero {
		cfgoRefRecord := c.HeroTable.Get(e)
		r.TenthHeroRef = append(r.TenthHeroRef, cfgoRefRecord)
	}
}

func (t *HeroLotteryMustTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[HeroLotteryMustTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewHeroLotteryMustTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[HeroLotteryMustTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// HeroLotteryGroup
		if record[t.getIndexInCsv("HeroLotteryGroup")] == "" {
			recordCfg.HeroLotteryGroup = 0
		} else {
			var err error
			recordCfg.HeroLotteryGroup, err = configs.HeroLotteryGroupTable.getIdByRef(record[t.getIndexInCsv("HeroLotteryGroup")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal csv record failed, varName=HeroLotteryGroup, type=ref@HeroLotteryGroupTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("HeroLotteryGroup")], err)
				} else {
					return fmt.Errorf("[HeroLotteryMustTable]unmarshal csv record failed, varName=HeroLotteryGroup, type=ref@HeroLotteryGroupTable, value=%s, err:[%s]", record[t.getIndexInCsv("HeroLotteryGroup")], err)
				}
			}
		}
		// MustCnt
		{
			if record[t.getIndexInCsv("MustCnt")] == "" {
				recordCfg.MustCnt = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("MustCnt")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal csv record failed, varName=MustCnt, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("MustCnt")], err)
					} else {
						return fmt.Errorf("[HeroLotteryMustTable]unmarshal csv record failed, varName=MustCnt, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("MustCnt")], err)
					}
				}
				recordCfg.MustCnt = int32(cfgoInt)
			}
		}
		// IncreaseCnt
		{
			if record[t.getIndexInCsv("IncreaseCnt")] == "" {
				recordCfg.IncreaseCnt = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("IncreaseCnt")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal csv record failed, varName=IncreaseCnt, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("IncreaseCnt")], err)
					} else {
						return fmt.Errorf("[HeroLotteryMustTable]unmarshal csv record failed, varName=IncreaseCnt, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("IncreaseCnt")], err)
					}
				}
				recordCfg.IncreaseCnt = int32(cfgoInt)
			}
		}
		// Increment
		{
			if record[t.getIndexInCsv("Increment")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Increment")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfIncrement int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfIncrement = 0
					} else {
						cfgoInt, err := strconv.Atoi(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse int in vector, varName=Increment, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse int in vector, varName=Increment, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfIncrement = int32(cfgoInt)
					}

					recordCfg.Increment = append(recordCfg.Increment, cfgoElemOfIncrement)
				}
			}
		}
		// IsRefresh
		{
			if record[t.getIndexInCsv("IsRefresh")] == "" {
				recordCfg.IsRefresh = false
			} else {
				var err error
				recordCfg.IsRefresh, err = strconv.ParseBool(record[t.getIndexInCsv("IsRefresh")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal csv record failed, varName=IsRefresh, type=bool, value=%s, err:[%s]\n", record[t.getIndexInCsv("IsRefresh")], err)
					} else {
						return fmt.Errorf("[HeroLotteryMustTable]unmarshal csv record failed, varName=IsRefresh, type=bool, value=%s, err:[%s]", record[t.getIndexInCsv("IsRefresh")], err)
					}
				}
			}
		}
		// Desc
		{
			recordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc")])
		}
		// Must11
		{
			cfgoMeetNilForMust11OfRecordCfg := false
			// element 0 of Must11
			if !cfgoMeetNilForMust11OfRecordCfg {
				cfgoMeetNilForMust11OfRecordCfg = true
				var cfgoElemOfMust11OfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Must111")] != "" {
					cfgoMeetNilForMust11OfRecordCfg = false
					var err error
					cfgoElemOfMust11OfRecordCfg, err = configs.HeroLotteryRandomTable.getIdByRef(record[t.getIndexInCsv("Must111")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Must111")], err)
						} else {
							return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Must111")], err)
						}
					}
				}

				if !cfgoMeetNilForMust11OfRecordCfg {
					recordCfg.Must11 = append(recordCfg.Must11, cfgoElemOfMust11OfRecordCfg)
				}
			}
			// element 1 of Must11
			if !cfgoMeetNilForMust11OfRecordCfg {
				cfgoMeetNilForMust11OfRecordCfg = true
				var cfgoElemOfMust11OfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Must112")] != "" {
					cfgoMeetNilForMust11OfRecordCfg = false
					var err error
					cfgoElemOfMust11OfRecordCfg, err = configs.HeroLotteryRandomTable.getIdByRef(record[t.getIndexInCsv("Must112")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Must112")], err)
						} else {
							return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Must112")], err)
						}
					}
				}

				if !cfgoMeetNilForMust11OfRecordCfg {
					recordCfg.Must11 = append(recordCfg.Must11, cfgoElemOfMust11OfRecordCfg)
				}
			}
			// element 2 of Must11
			if !cfgoMeetNilForMust11OfRecordCfg {
				cfgoMeetNilForMust11OfRecordCfg = true
				var cfgoElemOfMust11OfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Must113")] != "" {
					cfgoMeetNilForMust11OfRecordCfg = false
					var err error
					cfgoElemOfMust11OfRecordCfg, err = configs.HeroLotteryRandomTable.getIdByRef(record[t.getIndexInCsv("Must113")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Must113")], err)
						} else {
							return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Must113")], err)
						}
					}
				}

				if !cfgoMeetNilForMust11OfRecordCfg {
					recordCfg.Must11 = append(recordCfg.Must11, cfgoElemOfMust11OfRecordCfg)
				}
			}
			// element 3 of Must11
			if !cfgoMeetNilForMust11OfRecordCfg {
				cfgoMeetNilForMust11OfRecordCfg = true
				var cfgoElemOfMust11OfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Must114")] != "" {
					cfgoMeetNilForMust11OfRecordCfg = false
					var err error
					cfgoElemOfMust11OfRecordCfg, err = configs.HeroLotteryRandomTable.getIdByRef(record[t.getIndexInCsv("Must114")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Must114")], err)
						} else {
							return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Must114")], err)
						}
					}
				}

				if !cfgoMeetNilForMust11OfRecordCfg {
					recordCfg.Must11 = append(recordCfg.Must11, cfgoElemOfMust11OfRecordCfg)
				}
			}
			// element 4 of Must11
			if !cfgoMeetNilForMust11OfRecordCfg {
				cfgoMeetNilForMust11OfRecordCfg = true
				var cfgoElemOfMust11OfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Must115")] != "" {
					cfgoMeetNilForMust11OfRecordCfg = false
					var err error
					cfgoElemOfMust11OfRecordCfg, err = configs.HeroLotteryRandomTable.getIdByRef(record[t.getIndexInCsv("Must115")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Must115")], err)
						} else {
							return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Must115")], err)
						}
					}
				}

				if !cfgoMeetNilForMust11OfRecordCfg {
					recordCfg.Must11 = append(recordCfg.Must11, cfgoElemOfMust11OfRecordCfg)
				}
			}
			// element 5 of Must11
			if !cfgoMeetNilForMust11OfRecordCfg {
				cfgoMeetNilForMust11OfRecordCfg = true
				var cfgoElemOfMust11OfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Must116")] != "" {
					cfgoMeetNilForMust11OfRecordCfg = false
					var err error
					cfgoElemOfMust11OfRecordCfg, err = configs.HeroLotteryRandomTable.getIdByRef(record[t.getIndexInCsv("Must116")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Must116")], err)
						} else {
							return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Must116")], err)
						}
					}
				}

				if !cfgoMeetNilForMust11OfRecordCfg {
					recordCfg.Must11 = append(recordCfg.Must11, cfgoElemOfMust11OfRecordCfg)
				}
			}
			// element 6 of Must11
			if !cfgoMeetNilForMust11OfRecordCfg {
				cfgoMeetNilForMust11OfRecordCfg = true
				var cfgoElemOfMust11OfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Must117")] != "" {
					cfgoMeetNilForMust11OfRecordCfg = false
					var err error
					cfgoElemOfMust11OfRecordCfg, err = configs.HeroLotteryRandomTable.getIdByRef(record[t.getIndexInCsv("Must117")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Must117")], err)
						} else {
							return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Must117")], err)
						}
					}
				}

				if !cfgoMeetNilForMust11OfRecordCfg {
					recordCfg.Must11 = append(recordCfg.Must11, cfgoElemOfMust11OfRecordCfg)
				}
			}
			// element 7 of Must11
			if !cfgoMeetNilForMust11OfRecordCfg {
				cfgoMeetNilForMust11OfRecordCfg = true
				var cfgoElemOfMust11OfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Must118")] != "" {
					cfgoMeetNilForMust11OfRecordCfg = false
					var err error
					cfgoElemOfMust11OfRecordCfg, err = configs.HeroLotteryRandomTable.getIdByRef(record[t.getIndexInCsv("Must118")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Must118")], err)
						} else {
							return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Must118")], err)
						}
					}
				}

				if !cfgoMeetNilForMust11OfRecordCfg {
					recordCfg.Must11 = append(recordCfg.Must11, cfgoElemOfMust11OfRecordCfg)
				}
			}
			// element 8 of Must11
			if !cfgoMeetNilForMust11OfRecordCfg {
				cfgoMeetNilForMust11OfRecordCfg = true
				var cfgoElemOfMust11OfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Must119")] != "" {
					cfgoMeetNilForMust11OfRecordCfg = false
					var err error
					cfgoElemOfMust11OfRecordCfg, err = configs.HeroLotteryRandomTable.getIdByRef(record[t.getIndexInCsv("Must119")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Must119")], err)
						} else {
							return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Must119")], err)
						}
					}
				}

				if !cfgoMeetNilForMust11OfRecordCfg {
					recordCfg.Must11 = append(recordCfg.Must11, cfgoElemOfMust11OfRecordCfg)
				}
			}
			// element 9 of Must11
			if !cfgoMeetNilForMust11OfRecordCfg {
				cfgoMeetNilForMust11OfRecordCfg = true
				var cfgoElemOfMust11OfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Must1110")] != "" {
					cfgoMeetNilForMust11OfRecordCfg = false
					var err error
					cfgoElemOfMust11OfRecordCfg, err = configs.HeroLotteryRandomTable.getIdByRef(record[t.getIndexInCsv("Must1110")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Must1110")], err)
						} else {
							return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Must1110")], err)
						}
					}
				}

				if !cfgoMeetNilForMust11OfRecordCfg {
					recordCfg.Must11 = append(recordCfg.Must11, cfgoElemOfMust11OfRecordCfg)
				}
			}
			// element 10 of Must11
			if !cfgoMeetNilForMust11OfRecordCfg {
				cfgoMeetNilForMust11OfRecordCfg = true
				var cfgoElemOfMust11OfRecordCfg int32 = 0
				if record[t.getIndexInCsv("Must1111")] != "" {
					cfgoMeetNilForMust11OfRecordCfg = false
					var err error
					cfgoElemOfMust11OfRecordCfg, err = configs.HeroLotteryRandomTable.getIdByRef(record[t.getIndexInCsv("Must1111")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("Must1111")], err)
						} else {
							return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in collection, elemVarName=cfgoElemOfMust11OfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("Must1111")], err)
						}
					}
				}

				if !cfgoMeetNilForMust11OfRecordCfg {
					recordCfg.Must11 = append(recordCfg.Must11, cfgoElemOfMust11OfRecordCfg)
				}
			}

		}
		// MustSSR
		{
			// Hero
			{
				if record[t.getIndexInCsv("MustSSRHero")] != "" {
					cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("MustSSRHero")], func(r rune) bool {
						if r == ';' {
							return true
						}
						if r == '；' {
							return true
						}
						return false
					})
					for _, cfgoSplitStr := range cfgoSplitStrs {
						var cfgoElemOfHero int32 = 0
						if cfgoSplitStr == "" {
							cfgoElemOfHero = 0
						} else {
							var err error
							cfgoElemOfHero, err = configs.HeroLotteryRandomTable.getIdByRef(cfgoSplitStr)
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in vector, varName=Hero, value=%s, err:[%s]\n", cfgoSplitStr, err)
								} else {
									return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroLotteryRandomTable in vector, varName=Hero, value=%s, err:[%s]", cfgoSplitStr, err)
								}
							}
						}

						recordCfg.MustSSR.Hero = append(recordCfg.MustSSR.Hero, cfgoElemOfHero)
					}
				}
			}
			// Weight
			{
				if record[t.getIndexInCsv("MustSSRWeight")] != "" {
					cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("MustSSRWeight")], func(r rune) bool {
						if r == ';' {
							return true
						}
						if r == '；' {
							return true
						}
						return false
					})
					for _, cfgoSplitStr := range cfgoSplitStrs {
						var cfgoElemOfWeight int32 = 0
						if cfgoSplitStr == "" {
							cfgoElemOfWeight = 0
						} else {
							cfgoInt, err := strconv.Atoi(cfgoSplitStr)
							if err != nil {
								if debugMode {
									fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse int in vector, varName=Weight, value=%s, err:[%s]\n", cfgoSplitStr, err)
								} else {
									return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse int in vector, varName=Weight, value=%s, err:[%s]", cfgoSplitStr, err)
								}
							}
							cfgoElemOfWeight = int32(cfgoInt)
						}

						recordCfg.MustSSR.Weight = append(recordCfg.MustSSR.Weight, cfgoElemOfWeight)
					}
				}
			}
		}
		// FirstHero
		{
			if record[t.getIndexInCsv("FirstHero")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("FirstHero")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfFirstHero int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfFirstHero = 0
					} else {
						var err error
						cfgoElemOfFirstHero, err = configs.HeroTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroTable in vector, varName=FirstHero, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroTable in vector, varName=FirstHero, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.FirstHero = append(recordCfg.FirstHero, cfgoElemOfFirstHero)
				}
			}
		}
		// FirstHeroWeight
		{
			if record[t.getIndexInCsv("FirstHeroWeight")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("FirstHeroWeight")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfFirstHeroWeight int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfFirstHeroWeight = 0
					} else {
						cfgoInt, err := strconv.Atoi(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse int in vector, varName=FirstHeroWeight, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse int in vector, varName=FirstHeroWeight, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfFirstHeroWeight = int32(cfgoInt)
					}

					recordCfg.FirstHeroWeight = append(recordCfg.FirstHeroWeight, cfgoElemOfFirstHeroWeight)
				}
			}
		}
		// TenthHero
		{
			if record[t.getIndexInCsv("TenthHero")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("TenthHero")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfTenthHero int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfTenthHero = 0
					} else {
						var err error
						cfgoElemOfTenthHero, err = configs.HeroTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroTable in vector, varName=TenthHero, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse ref@HeroTable in vector, varName=TenthHero, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.TenthHero = append(recordCfg.TenthHero, cfgoElemOfTenthHero)
				}
			}
		}
		// TenthHeroWeight
		{
			if record[t.getIndexInCsv("TenthHeroWeight")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("TenthHeroWeight")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfTenthHeroWeight int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfTenthHeroWeight = 0
					} else {
						cfgoInt, err := strconv.Atoi(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal record failed, cannot parse int in vector, varName=TenthHeroWeight, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[HeroLotteryMustTable]unmarshal record failed, cannot parse int in vector, varName=TenthHeroWeight, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfTenthHeroWeight = int32(cfgoInt)
					}

					recordCfg.TenthHeroWeight = append(recordCfg.TenthHeroWeight, cfgoElemOfTenthHeroWeight)
				}
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [HeroLotteryMustTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[HeroLotteryMustTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *HeroLotteryMustTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "HeroLotteryMustTable.csv") && (!strings.HasPrefix(fileName, "HeroLotteryMustTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for HeroLotteryMustTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[HeroLotteryMustTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[HeroLotteryMustTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[HeroLotteryMustTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[HeroLotteryMustTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[HeroLotteryMustTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[HeroLotteryMustTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[HeroLotteryMustTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[HeroLotteryMustTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [HeroLotteryMustTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *HeroLotteryMustTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[HeroLotteryMustTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [HeroLotteryMustTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *HeroLotteryMustTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[HeroLotteryMustTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *HeroLotteryMustTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[HeroLotteryMustTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[HeroLotteryMustTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
