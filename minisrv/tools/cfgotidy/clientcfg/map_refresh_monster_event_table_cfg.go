// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type MapRefreshMonsterEventTableCfg struct {
	Id                    int32              `json:"Id"`                    // Id
	Chapters              int32              `json:"Chapters"`              // 章节
	Levels                int32              `json:"Levels"`                // 关卡
	Wave                  int32              `json:"Wave"`                  // 波次
	SpeedRatio            float32            `json:"SpeedRatio"`            // 速度放大系数
	HpRatio               float32            `json:"HpRatio"`               // 血量放大系数
	AtkRatio              float32            `json:"AtkRatio"`              // 攻击放大系数
	DefRatio              float32            `json:"DefRatio"`              // 防御放大系数
	Reversionary          int32              `json:"Reversionary"`          // 承接波次
	RefreshType           MonsterRefreshType `json:"RefreshType"`           // 刷怪类型
	RefreshParamDelayTime []float32          `json:"RefreshParamDelayTime"` // 规则数值(时间区间)秒
	RefreshParamDeathCnt  int32              `json:"RefreshParamDeathCnt"`  // 规则数值(数量)
	MonsterId             []int32            `json:"MonsterId"`             // 刷新怪物
	MonsterIdRef          []*MonsterTableCfg `json:"-"`                     // 刷新怪物
	MonsterCnt            []int32            `json:"MonsterCnt"`            // 怪物数量区间
	Weights               []int32            `json:"Weights"`               // 刷新权重(优先刷新的怪)
	Intervals             []float32          `json:"Intervals"`             // 怪物刷新间隔(秒)
	Habit1                []float32          `json:"Habit1"`                // 怪物1体型随机区间
	Habit2                []float32          `json:"Habit2"`                // 怪物2体型随机区间
	Habit3                []float32          `json:"Habit3"`                // 怪物3体型随机区间
	Habit4                []float32          `json:"Habit4"`                // 怪物4体型随机区间
	Habit5                []float32          `json:"Habit5"`                // 怪物5体型随机区间
}

func NewMapRefreshMonsterEventTableCfg() *MapRefreshMonsterEventTableCfg {
	return &MapRefreshMonsterEventTableCfg{
		Id:                    0,
		Chapters:              0,
		Levels:                0,
		Wave:                  0,
		SpeedRatio:            0.0,
		HpRatio:               0.0,
		AtkRatio:              0.0,
		DefRatio:              0.0,
		Reversionary:          0,
		RefreshType:           MonsterRefreshType(enumDefaultValue),
		RefreshParamDelayTime: []float32{},
		RefreshParamDeathCnt:  0,
		MonsterId:             []int32{},
		MonsterIdRef:          []*MonsterTableCfg{},
		MonsterCnt:            []int32{},
		Weights:               []int32{},
		Intervals:             []float32{},
		Habit1:                []float32{},
		Habit2:                []float32{},
		Habit3:                []float32{},
		Habit4:                []float32{},
		Habit5:                []float32{},
	}
}

func NewMockMapRefreshMonsterEventTableCfg() *MapRefreshMonsterEventTableCfg {
	return &MapRefreshMonsterEventTableCfg{
		Id:                    0,
		Chapters:              0,
		Levels:                0,
		Wave:                  0,
		SpeedRatio:            0.0,
		HpRatio:               0.0,
		AtkRatio:              0.0,
		DefRatio:              0.0,
		Reversionary:          0,
		RefreshType:           MonsterRefreshType(enumDefaultValue),
		RefreshParamDelayTime: []float32{0.0},
		RefreshParamDeathCnt:  0,
		MonsterId:             []int32{0},
		MonsterIdRef:          []*MonsterTableCfg{},
		MonsterCnt:            []int32{0},
		Weights:               []int32{0},
		Intervals:             []float32{0.0},
		Habit1:                []float32{0.0},
		Habit2:                []float32{0.0},
		Habit3:                []float32{0.0},
		Habit4:                []float32{0.0},
		Habit5:                []float32{0.0},
	}
}

type MapRefreshMonsterEventTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*MapRefreshMonsterEventTableCfg
	localIds         map[int32]struct{}
}

func NewMapRefreshMonsterEventTable(configs *Configs) *MapRefreshMonsterEventTable {
	return &MapRefreshMonsterEventTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*MapRefreshMonsterEventTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *MapRefreshMonsterEventTable) Get(key int32) *MapRefreshMonsterEventTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *MapRefreshMonsterEventTable) GetAll() map[int32]*MapRefreshMonsterEventTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *MapRefreshMonsterEventTable) put(key int32, value *MapRefreshMonsterEventTableCfg, local bool) *MapRefreshMonsterEventTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *MapRefreshMonsterEventTable) putFromInheritedTable(key int32, value *MapRefreshMonsterEventTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[MapRefreshMonsterEventTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *MapRefreshMonsterEventTable) Put(key int32, value *MapRefreshMonsterEventTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[MapRefreshMonsterEventTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *MapRefreshMonsterEventTable) PutAll(m map[int32]*MapRefreshMonsterEventTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *MapRefreshMonsterEventTable) Range(f func(v *MapRefreshMonsterEventTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *MapRefreshMonsterEventTable) Filter(filterFuncs ...func(v *MapRefreshMonsterEventTableCfg) bool) map[int32]*MapRefreshMonsterEventTableCfg {
	filtered := map[int32]*MapRefreshMonsterEventTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *MapRefreshMonsterEventTable) FilterSlice(filterFuncs ...func(v *MapRefreshMonsterEventTableCfg) bool) []*MapRefreshMonsterEventTableCfg {
	filtered := []*MapRefreshMonsterEventTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *MapRefreshMonsterEventTable) FilterKeys(filterFuncs ...func(v *MapRefreshMonsterEventTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *MapRefreshMonsterEventTable) satisfied(v *MapRefreshMonsterEventTableCfg, filterFuncs ...func(v *MapRefreshMonsterEventTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *MapRefreshMonsterEventTable) setupIndexes() error {
	return nil
}

func (t *MapRefreshMonsterEventTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *MapRefreshMonsterEventTableCfg) bindRefs(c *Configs) {
	for _, e := range r.MonsterId {
		cfgoRefRecord := c.MonsterTable.Get(e)
		r.MonsterIdRef = append(r.MonsterIdRef, cfgoRefRecord)
	}
}

func (t *MapRefreshMonsterEventTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewMapRefreshMonsterEventTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// Chapters
		{
			if record[t.getIndexInCsv("Chapters")] == "" {
				recordCfg.Chapters = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Chapters")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal csv record failed, varName=Chapters, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Chapters")], err)
					} else {
						return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv record failed, varName=Chapters, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Chapters")], err)
					}
				}
				recordCfg.Chapters = int32(cfgoInt)
			}
		}
		// Levels
		{
			if record[t.getIndexInCsv("Levels")] == "" {
				recordCfg.Levels = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Levels")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal csv record failed, varName=Levels, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Levels")], err)
					} else {
						return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv record failed, varName=Levels, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Levels")], err)
					}
				}
				recordCfg.Levels = int32(cfgoInt)
			}
		}
		// Wave
		{
			if record[t.getIndexInCsv("Wave")] == "" {
				recordCfg.Wave = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Wave")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal csv record failed, varName=Wave, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Wave")], err)
					} else {
						return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv record failed, varName=Wave, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Wave")], err)
					}
				}
				recordCfg.Wave = int32(cfgoInt)
			}
		}
		// SpeedRatio
		{
			if record[t.getIndexInCsv("SpeedRatio")] == "" {
				recordCfg.SpeedRatio = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("SpeedRatio")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal csv record failed, varName=SpeedRatio, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("SpeedRatio")], err)
					} else {
						return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv record failed, varName=SpeedRatio, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("SpeedRatio")], err)
					}
				}
				recordCfg.SpeedRatio = float32(cfgoFloat)
			}
		}
		// HpRatio
		{
			if record[t.getIndexInCsv("HpRatio")] == "" {
				recordCfg.HpRatio = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("HpRatio")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal csv record failed, varName=HpRatio, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("HpRatio")], err)
					} else {
						return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv record failed, varName=HpRatio, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("HpRatio")], err)
					}
				}
				recordCfg.HpRatio = float32(cfgoFloat)
			}
		}
		// AtkRatio
		{
			if record[t.getIndexInCsv("AtkRatio")] == "" {
				recordCfg.AtkRatio = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("AtkRatio")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal csv record failed, varName=AtkRatio, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("AtkRatio")], err)
					} else {
						return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv record failed, varName=AtkRatio, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("AtkRatio")], err)
					}
				}
				recordCfg.AtkRatio = float32(cfgoFloat)
			}
		}
		// DefRatio
		{
			if record[t.getIndexInCsv("DefRatio")] == "" {
				recordCfg.DefRatio = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("DefRatio")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal csv record failed, varName=DefRatio, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("DefRatio")], err)
					} else {
						return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv record failed, varName=DefRatio, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("DefRatio")], err)
					}
				}
				recordCfg.DefRatio = float32(cfgoFloat)
			}
		}
		// Reversionary
		{
			if record[t.getIndexInCsv("Reversionary")] == "" {
				recordCfg.Reversionary = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Reversionary")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal csv record failed, varName=Reversionary, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reversionary")], err)
					} else {
						return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv record failed, varName=Reversionary, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Reversionary")], err)
					}
				}
				recordCfg.Reversionary = int32(cfgoInt)
			}
		}
		// RefreshType
		{
			if record[t.getIndexInCsv("RefreshType")] == "" {
				recordCfg.RefreshType = MonsterRefreshType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseMonsterRefreshType(record[t.getIndexInCsv("RefreshType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal csv record failed, varName=RefreshType, type=enum@MonsterRefreshType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RefreshType")], err)
					} else {
						return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv record failed, varName=RefreshType, type=enum@MonsterRefreshType, value=%s, err:[%s]", record[t.getIndexInCsv("RefreshType")], err)
					}
				}
				recordCfg.RefreshType = cfgoEnum
			}
		}
		// RefreshParamDelayTime
		{
			if record[t.getIndexInCsv("RefreshParamDelayTime")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("RefreshParamDelayTime")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfRefreshParamDelayTime float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfRefreshParamDelayTime = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=RefreshParamDelayTime, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=RefreshParamDelayTime, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfRefreshParamDelayTime = float32(cfgoFloat)
					}

					recordCfg.RefreshParamDelayTime = append(recordCfg.RefreshParamDelayTime, cfgoElemOfRefreshParamDelayTime)
				}
			}
		}
		// RefreshParamDeathCnt
		{
			if record[t.getIndexInCsv("RefreshParamDeathCnt")] == "" {
				recordCfg.RefreshParamDeathCnt = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("RefreshParamDeathCnt")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal csv record failed, varName=RefreshParamDeathCnt, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("RefreshParamDeathCnt")], err)
					} else {
						return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv record failed, varName=RefreshParamDeathCnt, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("RefreshParamDeathCnt")], err)
					}
				}
				recordCfg.RefreshParamDeathCnt = int32(cfgoInt)
			}
		}
		// MonsterId
		{
			if record[t.getIndexInCsv("MonsterId")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("MonsterId")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfMonsterId int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfMonsterId = 0
					} else {
						var err error
						cfgoElemOfMonsterId, err = configs.MonsterTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal record failed, cannot parse ref@MonsterTable in vector, varName=MonsterId, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal record failed, cannot parse ref@MonsterTable in vector, varName=MonsterId, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.MonsterId = append(recordCfg.MonsterId, cfgoElemOfMonsterId)
				}
			}
		}
		// MonsterCnt
		{
			if record[t.getIndexInCsv("MonsterCnt")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("MonsterCnt")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfMonsterCnt int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfMonsterCnt = 0
					} else {
						cfgoInt, err := strconv.Atoi(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal record failed, cannot parse int in vector, varName=MonsterCnt, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal record failed, cannot parse int in vector, varName=MonsterCnt, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfMonsterCnt = int32(cfgoInt)
					}

					recordCfg.MonsterCnt = append(recordCfg.MonsterCnt, cfgoElemOfMonsterCnt)
				}
			}
		}
		// Weights
		{
			if record[t.getIndexInCsv("Weights")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Weights")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfWeights int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfWeights = 0
					} else {
						cfgoInt, err := strconv.Atoi(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal record failed, cannot parse int in vector, varName=Weights, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal record failed, cannot parse int in vector, varName=Weights, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfWeights = int32(cfgoInt)
					}

					recordCfg.Weights = append(recordCfg.Weights, cfgoElemOfWeights)
				}
			}
		}
		// Intervals
		{
			if record[t.getIndexInCsv("Intervals")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Intervals")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfIntervals float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfIntervals = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=Intervals, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=Intervals, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfIntervals = float32(cfgoFloat)
					}

					recordCfg.Intervals = append(recordCfg.Intervals, cfgoElemOfIntervals)
				}
			}
		}
		// Habit1
		{
			if record[t.getIndexInCsv("Habit1")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Habit1")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfHabit1 float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfHabit1 = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=Habit1, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=Habit1, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfHabit1 = float32(cfgoFloat)
					}

					recordCfg.Habit1 = append(recordCfg.Habit1, cfgoElemOfHabit1)
				}
			}
		}
		// Habit2
		{
			if record[t.getIndexInCsv("Habit2")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Habit2")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfHabit2 float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfHabit2 = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=Habit2, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=Habit2, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfHabit2 = float32(cfgoFloat)
					}

					recordCfg.Habit2 = append(recordCfg.Habit2, cfgoElemOfHabit2)
				}
			}
		}
		// Habit3
		{
			if record[t.getIndexInCsv("Habit3")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Habit3")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfHabit3 float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfHabit3 = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=Habit3, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=Habit3, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfHabit3 = float32(cfgoFloat)
					}

					recordCfg.Habit3 = append(recordCfg.Habit3, cfgoElemOfHabit3)
				}
			}
		}
		// Habit4
		{
			if record[t.getIndexInCsv("Habit4")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Habit4")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfHabit4 float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfHabit4 = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=Habit4, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=Habit4, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfHabit4 = float32(cfgoFloat)
					}

					recordCfg.Habit4 = append(recordCfg.Habit4, cfgoElemOfHabit4)
				}
			}
		}
		// Habit5
		{
			if record[t.getIndexInCsv("Habit5")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Habit5")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfHabit5 float32 = 0.0
					if cfgoSplitStr == "" {
						cfgoElemOfHabit5 = 0
					} else {
						cfgoFloat, err := strconv.ParseFloat(cfgoSplitStr, 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=Habit5, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal record failed, cannot parse float in vector, varName=Habit5, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfHabit5 = float32(cfgoFloat)
					}

					recordCfg.Habit5 = append(recordCfg.Habit5, cfgoElemOfHabit5)
				}
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [MapRefreshMonsterEventTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *MapRefreshMonsterEventTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "MapRefreshMonsterEventTable.csv") && (!strings.HasPrefix(fileName, "MapRefreshMonsterEventTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for MapRefreshMonsterEventTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[MapRefreshMonsterEventTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[MapRefreshMonsterEventTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[MapRefreshMonsterEventTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[MapRefreshMonsterEventTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[MapRefreshMonsterEventTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[MapRefreshMonsterEventTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[MapRefreshMonsterEventTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[MapRefreshMonsterEventTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [MapRefreshMonsterEventTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *MapRefreshMonsterEventTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[MapRefreshMonsterEventTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [MapRefreshMonsterEventTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *MapRefreshMonsterEventTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[MapRefreshMonsterEventTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *MapRefreshMonsterEventTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[MapRefreshMonsterEventTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[MapRefreshMonsterEventTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
