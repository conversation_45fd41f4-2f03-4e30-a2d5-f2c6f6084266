// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type LordGemTableCfg struct {
	Id                 int32                    `json:"Id"`              // Id
	Item               int32                    `json:"Item"`            // 对应道具
	ItemRef            *ItemTableCfg            `json:"-"`               // 对应道具
	Hero               []int32                  `json:"Hero"`            // 对应英雄
	HeroRef            []*HeroTableCfg          `json:"-"`               // 对应英雄
	Image              string                   `json:"Image"`           // 小图
	GemAffixId         int32                    `json:"GemAffixId"`      // 词条id（用于替换）
	LordEquipType      int32                    `json:"LordEquipType"`   // 宝石位置类型
	LordEquipTypeRef   *LordEquipTypeTableCfg   `json:"-"`               // 宝石位置类型
	GemQualityType     int32                    `json:"GemQualityType"`  // 宝石品质
	GemQualityTypeRef  *GemQualityTypeTableCfg  `json:"-"`               // 宝石品质
	GemAffixQuality    int32                    `json:"GemAffixQuality"` // 宝石词条品质
	GemAffixQualityRef *GemAffixQualityTableCfg `json:"-"`               // 宝石词条品质
	Modifier           int32                    `json:"Modifier"`        // 修改器
	ModifierRef        *ModifierTableCfg        `json:"-"`               // 修改器
	Weight             int32                    `json:"Weight"`          // 合成/洗练权重
}

func NewLordGemTableCfg() *LordGemTableCfg {
	return &LordGemTableCfg{
		Id:                 0,
		Item:               0,
		ItemRef:            nil,
		Hero:               []int32{},
		HeroRef:            []*HeroTableCfg{},
		Image:              "",
		GemAffixId:         0,
		LordEquipType:      0,
		LordEquipTypeRef:   nil,
		GemQualityType:     0,
		GemQualityTypeRef:  nil,
		GemAffixQuality:    0,
		GemAffixQualityRef: nil,
		Modifier:           0,
		ModifierRef:        nil,
		Weight:             0,
	}
}

func NewMockLordGemTableCfg() *LordGemTableCfg {
	return &LordGemTableCfg{
		Id:                 0,
		Item:               0,
		ItemRef:            nil,
		Hero:               []int32{0},
		HeroRef:            []*HeroTableCfg{},
		Image:              "",
		GemAffixId:         0,
		LordEquipType:      0,
		LordEquipTypeRef:   nil,
		GemQualityType:     0,
		GemQualityTypeRef:  nil,
		GemAffixQuality:    0,
		GemAffixQualityRef: nil,
		Modifier:           0,
		ModifierRef:        nil,
		Weight:             0,
	}
}

type LordGemTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*LordGemTableCfg
	localIds         map[int32]struct{}
}

func NewLordGemTable(configs *Configs) *LordGemTable {
	return &LordGemTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*LordGemTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *LordGemTable) Get(key int32) *LordGemTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *LordGemTable) GetAll() map[int32]*LordGemTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *LordGemTable) put(key int32, value *LordGemTableCfg, local bool) *LordGemTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *LordGemTable) putFromInheritedTable(key int32, value *LordGemTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[LordGemTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *LordGemTable) Put(key int32, value *LordGemTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[LordGemTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *LordGemTable) PutAll(m map[int32]*LordGemTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *LordGemTable) Range(f func(v *LordGemTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *LordGemTable) Filter(filterFuncs ...func(v *LordGemTableCfg) bool) map[int32]*LordGemTableCfg {
	filtered := map[int32]*LordGemTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *LordGemTable) FilterSlice(filterFuncs ...func(v *LordGemTableCfg) bool) []*LordGemTableCfg {
	filtered := []*LordGemTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *LordGemTable) FilterKeys(filterFuncs ...func(v *LordGemTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *LordGemTable) satisfied(v *LordGemTableCfg, filterFuncs ...func(v *LordGemTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *LordGemTable) setupIndexes() error {
	return nil
}

func (t *LordGemTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *LordGemTableCfg) bindRefs(c *Configs) {
	r.ItemRef = c.ItemTable.Get(r.Item)
	for _, e := range r.Hero {
		cfgoRefRecord := c.HeroTable.Get(e)
		r.HeroRef = append(r.HeroRef, cfgoRefRecord)
	}
	r.LordEquipTypeRef = c.LordEquipTypeTable.Get(r.LordEquipType)
	r.GemQualityTypeRef = c.GemQualityTypeTable.Get(r.GemQualityType)
	r.GemAffixQualityRef = c.GemAffixQualityTable.Get(r.GemAffixQuality)
	r.ModifierRef = c.ModifierTable.Get(r.Modifier)
}

func (t *LordGemTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[LordGemTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewLordGemTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LordGemTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[LordGemTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// Item
		if record[t.getIndexInCsv("Item")] == "" {
			recordCfg.Item = 0
		} else {
			var err error
			recordCfg.Item, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Item")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [LordGemTable]unmarshal csv record failed, varName=Item, type=ref@ItemTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Item")], err)
				} else {
					return fmt.Errorf("[LordGemTable]unmarshal csv record failed, varName=Item, type=ref@ItemTable, value=%s, err:[%s]", record[t.getIndexInCsv("Item")], err)
				}
			}
		}
		// Hero
		{
			if record[t.getIndexInCsv("Hero")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("Hero")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfHero int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfHero = 0
					} else {
						var err error
						cfgoElemOfHero, err = configs.HeroTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [LordGemTable]unmarshal record failed, cannot parse ref@HeroTable in vector, varName=Hero, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[LordGemTable]unmarshal record failed, cannot parse ref@HeroTable in vector, varName=Hero, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.Hero = append(recordCfg.Hero, cfgoElemOfHero)
				}
			}
		}
		// Image
		{
			recordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Image")])
		}
		// GemAffixId
		{
			if record[t.getIndexInCsv("GemAffixId")] == "" {
				recordCfg.GemAffixId = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("GemAffixId")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LordGemTable]unmarshal csv record failed, varName=GemAffixId, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("GemAffixId")], err)
					} else {
						return fmt.Errorf("[LordGemTable]unmarshal csv record failed, varName=GemAffixId, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("GemAffixId")], err)
					}
				}
				recordCfg.GemAffixId = int32(cfgoInt)
			}
		}
		// LordEquipType
		if record[t.getIndexInCsv("LordEquipType")] == "" {
			recordCfg.LordEquipType = 0
		} else {
			var err error
			recordCfg.LordEquipType, err = configs.LordEquipTypeTable.getIdByRef(record[t.getIndexInCsv("LordEquipType")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [LordGemTable]unmarshal csv record failed, varName=LordEquipType, type=ref@LordEquipTypeTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("LordEquipType")], err)
				} else {
					return fmt.Errorf("[LordGemTable]unmarshal csv record failed, varName=LordEquipType, type=ref@LordEquipTypeTable, value=%s, err:[%s]", record[t.getIndexInCsv("LordEquipType")], err)
				}
			}
		}
		// GemQualityType
		if record[t.getIndexInCsv("GemQualityType")] == "" {
			recordCfg.GemQualityType = 0
		} else {
			var err error
			recordCfg.GemQualityType, err = configs.GemQualityTypeTable.getIdByRef(record[t.getIndexInCsv("GemQualityType")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [LordGemTable]unmarshal csv record failed, varName=GemQualityType, type=ref@GemQualityTypeTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("GemQualityType")], err)
				} else {
					return fmt.Errorf("[LordGemTable]unmarshal csv record failed, varName=GemQualityType, type=ref@GemQualityTypeTable, value=%s, err:[%s]", record[t.getIndexInCsv("GemQualityType")], err)
				}
			}
		}
		// GemAffixQuality
		if record[t.getIndexInCsv("GemAffixQuality")] == "" {
			recordCfg.GemAffixQuality = 0
		} else {
			var err error
			recordCfg.GemAffixQuality, err = configs.GemAffixQualityTable.getIdByRef(record[t.getIndexInCsv("GemAffixQuality")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [LordGemTable]unmarshal csv record failed, varName=GemAffixQuality, type=ref@GemAffixQualityTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("GemAffixQuality")], err)
				} else {
					return fmt.Errorf("[LordGemTable]unmarshal csv record failed, varName=GemAffixQuality, type=ref@GemAffixQualityTable, value=%s, err:[%s]", record[t.getIndexInCsv("GemAffixQuality")], err)
				}
			}
		}
		// Modifier
		if record[t.getIndexInCsv("Modifier")] == "" {
			recordCfg.Modifier = 0
		} else {
			var err error
			recordCfg.Modifier, err = configs.ModifierTable.getIdByRef(record[t.getIndexInCsv("Modifier")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [LordGemTable]unmarshal csv record failed, varName=Modifier, type=ref@ModifierTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Modifier")], err)
				} else {
					return fmt.Errorf("[LordGemTable]unmarshal csv record failed, varName=Modifier, type=ref@ModifierTable, value=%s, err:[%s]", record[t.getIndexInCsv("Modifier")], err)
				}
			}
		}
		// Weight
		{
			if record[t.getIndexInCsv("Weight")] == "" {
				recordCfg.Weight = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Weight")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LordGemTable]unmarshal csv record failed, varName=Weight, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Weight")], err)
					} else {
						return fmt.Errorf("[LordGemTable]unmarshal csv record failed, varName=Weight, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Weight")], err)
					}
				}
				recordCfg.Weight = int32(cfgoInt)
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [LordGemTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[LordGemTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *LordGemTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "LordGemTable.csv") && (!strings.HasPrefix(fileName, "LordGemTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for LordGemTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[LordGemTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[LordGemTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[LordGemTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[LordGemTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[LordGemTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[LordGemTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[LordGemTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[LordGemTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [LordGemTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *LordGemTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[LordGemTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [LordGemTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *LordGemTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[LordGemTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *LordGemTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[LordGemTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[LordGemTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
