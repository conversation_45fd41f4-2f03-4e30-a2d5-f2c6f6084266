// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

type RampageParamStr struct {
	Range   float32                  `json:"Range"`  // 移动距离,米
	HpPer   float32                  `json:"HpPer"`  // 低于血量百分比
	Chance  float32                  `json:"Chance"` // 加速概率
	Buff    []int32                  `json:"Buff"`   // Buff
	BuffRef []*HeroSkillBuffTableCfg `json:"-"`      // Buff
	Time    []float32                `json:"Time"`   // Buff持续时间
}

func NewRampageParamStr() *RampageParamStr {
	return &RampageParamStr{
		Range:   0.0,
		HpPer:   0.0,
		Chance:  0.0,
		Buff:    []int32{},
		BuffRef: []*HeroSkillBuffTableCfg{},
		Time:    []float32{},
	}
}

func NewMockRampageParamStr() *RampageParamStr {
	return &RampageParamStr{
		Range:   0.0,
		HpPer:   0.0,
		Chance:  0.0,
		Buff:    []int32{0},
		BuffRef: []*HeroSkillBuffTableCfg{},
		Time:    []float32{0.0},
	}
}

func (s *RampageParamStr) bindRefs(c *Configs) {
	for _, e := range s.Buff {
		cfgoRefRecord := c.HeroSkillBuffTable.Get(e)
		s.BuffRef = append(s.BuffRef, cfgoRefRecord)
	}
}
