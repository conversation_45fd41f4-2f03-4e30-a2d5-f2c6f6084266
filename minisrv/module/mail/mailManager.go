package mail

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"strconv"
	"time"
)

func GetMailList(ctx context.Context, uid int64) []*minirpc.UserMail {
	mailModels, _ := orm.GetAll[*model.UserMailModel](ctx, uid)
	if mailModels == nil {
		return nil
	}
	mailList := []*minirpc.UserMail{}
	now := time.Now().Unix()
	for _, mail := range mailModels {
		if mail.ExpireTime() < now {
			mail.Delete(ctx)
			continue
		}
		mailList = append(mailList, mail.Snapshoot().(*minirpc.UserMail))
	}
	return mailList
}

// 领取邮件奖励
func CollectMailReward(ctx context.Context, uid int64, mailId int64) ([]*wrpc.Rewards, error) {
	mail, err := orm.Get[*model.UserMailModel](ctx, uid, mailId)
	if err != nil {
		return nil, err
	}
	if mail == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("mail is empty")
	}
	if mail.Status() == int32(minirpc.MailStatusRewarded) {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("mail is rewarded")
	}
	mail.SetStatus(ctx, int32(minirpc.MailStatusRewarded))
	retRewards := []*wrpc.Rewards{}
	for k, v := range mail.Attachment() {
		model.AddItem(ctx, uid, k, v, bi.ItemFlowReason_Mail_Reward, strconv.Itoa(int(mailId)), util.GenerateSequenceString())
		retRewards = append(retRewards, &wrpc.Rewards{ItemId: k, ItemValue: v})
	}
	return retRewards, nil
}

// 邮件已读
func ReadMail(ctx context.Context, uid int64, mailId int64) error {
	mail, err := orm.Get[*model.UserMailModel](ctx, uid, mailId)
	if err != nil {
		return err
	}
	if mail == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("mail is empty")
	}
	if mail.Status() == int32(minirpc.MailStatusRead) {
		return kdmerr.SysInvalidArguments.CastErrorf("mail is read")
	}
	mail.SetStatus(ctx, int32(minirpc.MailStatusRead))
	return nil
}

// 删除邮件
func DeleteMail(ctx context.Context, uid int64, mailId int64) error {
	mail, err := orm.Get[*model.UserMailModel](ctx, uid, mailId)
	if err != nil {
		return err
	}
	if mail == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("mail is empty")
	}
	mail.Delete(ctx)
	return nil
}

// 一键删除已读
func DeleteAllReadMail(ctx context.Context, uid int64) error {
	mails, err := orm.GetAll[*model.UserMailModel](ctx, uid)
	if err != nil {
		return err
	}
	for _, mail := range mails {
		if mail.Status() == int32(minirpc.MailStatusRead) || mail.Status() == int32(minirpc.MailStatusRewarded) {
			mail.Delete(ctx)
		}
	}
	return nil
}

// 一键已读领取所有
func ReadAndCollectAllMail(ctx context.Context, uid int64) ([]*wrpc.Rewards, error) {
	mails, err := orm.GetAll[*model.UserMailModel](ctx, uid)
	if err != nil {
		return nil, err
	}
	retRewards := []*wrpc.Rewards{}
	for _, mail := range mails {
		if mail.Status() == int32(minirpc.MailStatusUnRead) {
			mail.SetStatus(ctx, int32(minirpc.MailStatusRead))
		}
		if mail.Status() == int32(minirpc.MailStatusRead) {
			rewards, _ := CollectMailReward(ctx, uid, mail.MailId())
			retRewards = consumable.CombineRewards(retRewards, rewards)
		}
	}
	return retRewards, nil
}
