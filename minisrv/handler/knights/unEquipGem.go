package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/lord"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 卸下宝石
func (_ KnightsHandlers) UnEquipGemHandler(ctx *wctx.Context, req *wrpc.UnEquipGemRequest) (*wrpc.UnEquipGemReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		err = lord.UnEquipGem(ctx, req.UID, req.EquipId, req.Pos, true, false, nil)

		rawResp = &wrpc.UnEquipGemReply{
			Result: err == nil,
		}
	})
	return rawResp.(*wrpc.UnEquipGemReply), err
}
