// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type ChapterTaskTableCfg struct {
	Id              int32                    `json:"Id"`              // Id
	StringId        string                   `json:"StringId"`        // StringId
	ReqFunction     int32                    `json:"ReqFunction"`     // 前置功能
	ReqFunctionRef  *FunctionTableCfg        `json:"-"`               // 前置功能
	Chapter         int32                    `json:"Chapter"`         // 所属章节
	ChapterRef      *ChapterTaskMainTableCfg `json:"-"`               // 所属章节
	TaskType        TaskType                 `json:"TaskType"`        // 任务类型
	TaskCounterType TaskCounterType          `json:"TaskCounterType"` // 任务计数类型
	Formula         string                   `json:"Formula"`         // 达成条件
	Value           int32                    `json:"Value"`           // 值
	GoTo            int32                    `json:"GoTo"`            // 跳转
	GoToRef         *GoToTableCfg            `json:"-"`               // 跳转
	Desc            string                   `json:"Desc"`            // 任务描述
	Param           string                   `json:"Param"`           // 描述参数
	Reward          []*RewardKVS             `json:"Reward"`          // 奖励
}

func NewChapterTaskTableCfg() *ChapterTaskTableCfg {
	return &ChapterTaskTableCfg{
		Id:              0,
		StringId:        "",
		ReqFunction:     0,
		ReqFunctionRef:  nil,
		Chapter:         0,
		ChapterRef:      nil,
		TaskType:        TaskType(enumDefaultValue),
		TaskCounterType: TaskCounterType(enumDefaultValue),
		Formula:         "",
		Value:           0,
		GoTo:            0,
		GoToRef:         nil,
		Desc:            "",
		Param:           "",
		Reward:          []*RewardKVS{},
	}
}

func NewMockChapterTaskTableCfg() *ChapterTaskTableCfg {
	return &ChapterTaskTableCfg{
		Id:              0,
		StringId:        "",
		ReqFunction:     0,
		ReqFunctionRef:  nil,
		Chapter:         0,
		ChapterRef:      nil,
		TaskType:        TaskType(enumDefaultValue),
		TaskCounterType: TaskCounterType(enumDefaultValue),
		Formula:         "",
		Value:           0,
		GoTo:            0,
		GoToRef:         nil,
		Desc:            "",
		Param:           "",
		Reward:          []*RewardKVS{NewMockRewardKVS()},
	}
}

type ChapterTaskTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*ChapterTaskTableCfg
	localIds         map[int32]struct{}
}

func NewChapterTaskTable(configs *Configs) *ChapterTaskTable {
	return &ChapterTaskTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*ChapterTaskTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *ChapterTaskTable) Get(key int32) *ChapterTaskTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *ChapterTaskTable) GetAll() map[int32]*ChapterTaskTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *ChapterTaskTable) put(key int32, value *ChapterTaskTableCfg, local bool) *ChapterTaskTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *ChapterTaskTable) putFromInheritedTable(key int32, value *ChapterTaskTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[ChapterTaskTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *ChapterTaskTable) Put(key int32, value *ChapterTaskTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[ChapterTaskTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *ChapterTaskTable) PutAll(m map[int32]*ChapterTaskTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *ChapterTaskTable) Range(f func(v *ChapterTaskTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *ChapterTaskTable) Filter(filterFuncs ...func(v *ChapterTaskTableCfg) bool) map[int32]*ChapterTaskTableCfg {
	filtered := map[int32]*ChapterTaskTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *ChapterTaskTable) FilterSlice(filterFuncs ...func(v *ChapterTaskTableCfg) bool) []*ChapterTaskTableCfg {
	filtered := []*ChapterTaskTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *ChapterTaskTable) FilterKeys(filterFuncs ...func(v *ChapterTaskTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *ChapterTaskTable) satisfied(v *ChapterTaskTableCfg, filterFuncs ...func(v *ChapterTaskTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *ChapterTaskTable) setupIndexes() error {
	return nil
}

func (t *ChapterTaskTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *ChapterTaskTableCfg) bindRefs(c *Configs) {
	r.ReqFunctionRef = c.FunctionTable.Get(r.ReqFunction)
	r.ChapterRef = c.ChapterTaskMainTable.Get(r.Chapter)
	r.GoToRef = c.GoToTable.Get(r.GoTo)
	for _, e := range r.Reward {
		e.bindRefs(c)
	}
}

func (t *ChapterTaskTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[ChapterTaskTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewChapterTaskTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ChapterTaskTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[ChapterTaskTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// ReqFunction
		if record[t.getIndexInCsv("ReqFunction")] == "" {
			recordCfg.ReqFunction = 0
		} else {
			var err error
			recordCfg.ReqFunction, err = configs.FunctionTable.getIdByRef(record[t.getIndexInCsv("ReqFunction")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [ChapterTaskTable]unmarshal csv record failed, varName=ReqFunction, type=ref@FunctionTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("ReqFunction")], err)
				} else {
					return fmt.Errorf("[ChapterTaskTable]unmarshal csv record failed, varName=ReqFunction, type=ref@FunctionTable, value=%s, err:[%s]", record[t.getIndexInCsv("ReqFunction")], err)
				}
			}
		}
		// Chapter
		if record[t.getIndexInCsv("Chapter")] == "" {
			recordCfg.Chapter = 0
		} else {
			var err error
			recordCfg.Chapter, err = configs.ChapterTaskMainTable.getIdByRef(record[t.getIndexInCsv("Chapter")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [ChapterTaskTable]unmarshal csv record failed, varName=Chapter, type=ref@ChapterTaskMainTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Chapter")], err)
				} else {
					return fmt.Errorf("[ChapterTaskTable]unmarshal csv record failed, varName=Chapter, type=ref@ChapterTaskMainTable, value=%s, err:[%s]", record[t.getIndexInCsv("Chapter")], err)
				}
			}
		}
		// TaskType
		{
			if record[t.getIndexInCsv("TaskType")] == "" {
				recordCfg.TaskType = TaskType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseTaskType(record[t.getIndexInCsv("TaskType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ChapterTaskTable]unmarshal csv record failed, varName=TaskType, type=enum@TaskType, value=%s, err:[%s]\n", record[t.getIndexInCsv("TaskType")], err)
					} else {
						return fmt.Errorf("[ChapterTaskTable]unmarshal csv record failed, varName=TaskType, type=enum@TaskType, value=%s, err:[%s]", record[t.getIndexInCsv("TaskType")], err)
					}
				}
				recordCfg.TaskType = cfgoEnum
			}
		}
		// TaskCounterType
		{
			if record[t.getIndexInCsv("TaskCounterType")] == "" {
				recordCfg.TaskCounterType = TaskCounterType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseTaskCounterType(record[t.getIndexInCsv("TaskCounterType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ChapterTaskTable]unmarshal csv record failed, varName=TaskCounterType, type=enum@TaskCounterType, value=%s, err:[%s]\n", record[t.getIndexInCsv("TaskCounterType")], err)
					} else {
						return fmt.Errorf("[ChapterTaskTable]unmarshal csv record failed, varName=TaskCounterType, type=enum@TaskCounterType, value=%s, err:[%s]", record[t.getIndexInCsv("TaskCounterType")], err)
					}
				}
				recordCfg.TaskCounterType = cfgoEnum
			}
		}
		// Formula
		{
			recordCfg.Formula = strings.TrimSpace(record[t.getIndexInCsv("Formula")])
		}
		// Value
		{
			if record[t.getIndexInCsv("Value")] == "" {
				recordCfg.Value = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Value")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [ChapterTaskTable]unmarshal csv record failed, varName=Value, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Value")], err)
					} else {
						return fmt.Errorf("[ChapterTaskTable]unmarshal csv record failed, varName=Value, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Value")], err)
					}
				}
				recordCfg.Value = int32(cfgoInt)
			}
		}
		// GoTo
		if record[t.getIndexInCsv("GoTo")] == "" {
			recordCfg.GoTo = 0
		} else {
			var err error
			recordCfg.GoTo, err = configs.GoToTable.getIdByRef(record[t.getIndexInCsv("GoTo")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [ChapterTaskTable]unmarshal csv record failed, varName=GoTo, type=ref@GoToTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoTo")], err)
				} else {
					return fmt.Errorf("[ChapterTaskTable]unmarshal csv record failed, varName=GoTo, type=ref@GoToTable, value=%s, err:[%s]", record[t.getIndexInCsv("GoTo")], err)
				}
			}
		}
		// Desc
		{
			recordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc")])
		}
		// Param
		{
			recordCfg.Param = strings.TrimSpace(record[t.getIndexInCsv("Param")])
		}
		// Reward
		{
			cfgoMeetNilForRewardOfRecordCfg := false
			// element 0 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("Reward1RewardType")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						var err error
						cfgoElemOfRewardOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward1RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [ChapterTaskTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward1RewardType")], err)
							} else {
								return fmt.Errorf("[ChapterTaskTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("Reward1RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("Reward1RewardValue")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Reward1RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [ChapterTaskTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward1RewardValue")], err)
							} else {
								return fmt.Errorf("[ChapterTaskTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("Reward1RewardValue")], err)
							}
						}
						cfgoElemOfRewardOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}
			// element 1 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("Reward2RewardType")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						var err error
						cfgoElemOfRewardOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward2RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [ChapterTaskTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward2RewardType")], err)
							} else {
								return fmt.Errorf("[ChapterTaskTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("Reward2RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("Reward2RewardValue")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Reward2RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [ChapterTaskTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward2RewardValue")], err)
							} else {
								return fmt.Errorf("[ChapterTaskTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("Reward2RewardValue")], err)
							}
						}
						cfgoElemOfRewardOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}
			// element 2 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("Reward3RewardType")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						var err error
						cfgoElemOfRewardOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward3RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [ChapterTaskTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward3RewardType")], err)
							} else {
								return fmt.Errorf("[ChapterTaskTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("Reward3RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("Reward3RewardValue")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Reward3RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [ChapterTaskTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward3RewardValue")], err)
							} else {
								return fmt.Errorf("[ChapterTaskTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("Reward3RewardValue")], err)
							}
						}
						cfgoElemOfRewardOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}

		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [ChapterTaskTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[ChapterTaskTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *ChapterTaskTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "ChapterTaskTable.csv") && (!strings.HasPrefix(fileName, "ChapterTaskTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for ChapterTaskTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[ChapterTaskTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[ChapterTaskTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[ChapterTaskTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[ChapterTaskTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[ChapterTaskTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[ChapterTaskTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[ChapterTaskTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[ChapterTaskTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [ChapterTaskTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *ChapterTaskTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[ChapterTaskTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [ChapterTaskTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *ChapterTaskTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[ChapterTaskTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *ChapterTaskTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[ChapterTaskTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[ChapterTaskTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
