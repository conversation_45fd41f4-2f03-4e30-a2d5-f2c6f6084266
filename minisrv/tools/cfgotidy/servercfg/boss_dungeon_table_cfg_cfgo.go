// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t *BossDungeonTable) saveJson(dir string) error {
	jsonPath := filepath.Join(dir, "BossDungeonTable.json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[BossDungeonTable]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	// sort
	s := cfgoBossDungeonTableSlice{}
	for _, cfgoCfg := range t.records {
		if _, ok := t.localIds[cfgoCfg.Id]; !ok {
			continue
		}
		s = append(s, cfgoCfg)
	}
	sort.Sort(s)
	w := bufio.NewWriter(f)
	w.WriteString("{\n")
	for i, cfgoCfg := range s {
		w.WriteString("\t\"")
		w.WriteString(strconv.Itoa(int(cfgoCfg.Id)))
		w.WriteString("\": ")
		bytes, err := json.MarshalIndent(cfgoCfg, "\t", "\t")
		if err != nil {
			return err
		}
		w.Write(bytes)
		if i != len(s)-1 {
			w.WriteString(",\n")
		} else {
			w.WriteString("\n")
		}
	}
	w.WriteString("}\n")
	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

type cfgoBossDungeonTableSlice []*BossDungeonTableCfg

func (x cfgoBossDungeonTableSlice) Len() int           { return len(x) }
func (x cfgoBossDungeonTableSlice) Less(i, j int) bool { return x[i].Id < x[j].Id }
func (x cfgoBossDungeonTableSlice) Swap(i, j int)      { x[i], x[j] = x[j], x[i] }

func (t *BossDungeonTable) loadJson(dir string, configs *Configs) error {
	jsonPath := filepath.Join(dir, "BossDungeonTable.json")
	return t.LoadJsonByPath(jsonPath, configs)
}

func (t *BossDungeonTable) LoadJsonByPath(jsonPath string, configs *Configs) error {
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[BossDungeonTable]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*BossDungeonTableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[BossDungeonTable]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		if err = t.Put(k, v); err != nil {
			return fmt.Errorf("[BossDungeonTable]put record failed, path=%s, err=[%w]", jsonPath, err)
		}
	}
	if err = t.setupIndexes(); err != nil {
		return fmt.Errorf("[BossDungeonTable]setup indexes failed, path=%s, err=[%w]", jsonPath, err)
	}
	return nil
}
