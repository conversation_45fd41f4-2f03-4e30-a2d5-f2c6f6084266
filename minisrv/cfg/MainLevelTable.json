{"1": {"Id": 1, "StringId": "chapter_1_1", "IsMaxLevel": false, "ChapterLevel": 1, "Chapter": 1, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 1, "EliteChallengeReward": 1001, "OneStarReward": 1, "TwoStarReward": 1001, "ThreeStarReward": 2001, "ExtraReward": [{"RewardType": 15, "RewardValue": 1}]}, "2": {"Id": 2, "StringId": "chapter_2_1", "IsMaxLevel": false, "ChapterLevel": 1, "Chapter": 2, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 2, "EliteChallengeReward": 1002, "OneStarReward": 2, "TwoStarReward": 1002, "ThreeStarReward": 2002, "ExtraReward": [{"RewardType": 2, "RewardValue": 2000}]}, "3": {"Id": 3, "StringId": "chapter_3_1", "IsMaxLevel": false, "ChapterLevel": 1, "Chapter": 3, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 3, "EliteChallengeReward": 1003, "OneStarReward": 3, "TwoStarReward": 1003, "ThreeStarReward": 2003, "ExtraReward": [{"RewardType": 4, "RewardValue": 10}]}, "4": {"Id": 4, "StringId": "chapter_4_1", "IsMaxLevel": false, "ChapterLevel": 2, "Chapter": 4, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 4, "EliteChallengeReward": 1004, "OneStarReward": 4, "TwoStarReward": 1004, "ThreeStarReward": 2004, "ExtraReward": []}, "5": {"Id": 5, "StringId": "chapter_5_1", "IsMaxLevel": false, "ChapterLevel": 2, "Chapter": 5, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 5, "EliteChallengeReward": 1005, "OneStarReward": 5, "TwoStarReward": 1005, "ThreeStarReward": 2005, "ExtraReward": []}, "6": {"Id": 6, "StringId": "chapter_6_1", "IsMaxLevel": false, "ChapterLevel": 2, "Chapter": 6, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 6, "EliteChallengeReward": 1006, "OneStarReward": 6, "TwoStarReward": 1006, "ThreeStarReward": 2006, "ExtraReward": []}, "7": {"Id": 7, "StringId": "chapter_7_1", "IsMaxLevel": false, "ChapterLevel": 2, "Chapter": 7, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 7, "EliteChallengeReward": 1007, "OneStarReward": 7, "TwoStarReward": 1007, "ThreeStarReward": 2007, "ExtraReward": []}, "8": {"Id": 8, "StringId": "chapter_8_1", "IsMaxLevel": false, "ChapterLevel": 2, "Chapter": 8, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 8, "EliteChallengeReward": 1008, "OneStarReward": 8, "TwoStarReward": 1008, "ThreeStarReward": 2008, "ExtraReward": []}, "9": {"Id": 9, "StringId": "chapter_9_1", "IsMaxLevel": false, "ChapterLevel": 2, "Chapter": 9, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 9, "EliteChallengeReward": 1009, "OneStarReward": 9, "TwoStarReward": 1009, "ThreeStarReward": 2009, "ExtraReward": []}, "10": {"Id": 10, "StringId": "chapter_10_1", "IsMaxLevel": false, "ChapterLevel": 2, "Chapter": 10, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 10, "EliteChallengeReward": 1010, "OneStarReward": 10, "TwoStarReward": 1010, "ThreeStarReward": 2010, "ExtraReward": []}, "11": {"Id": 11, "StringId": "chapter_11_1", "IsMaxLevel": false, "ChapterLevel": 2, "Chapter": 11, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 11, "EliteChallengeReward": 1011, "OneStarReward": 11, "TwoStarReward": 1011, "ThreeStarReward": 2011, "ExtraReward": []}, "12": {"Id": 12, "StringId": "chapter_12_1", "IsMaxLevel": false, "ChapterLevel": 2, "Chapter": 12, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 12, "EliteChallengeReward": 1012, "OneStarReward": 12, "TwoStarReward": 1012, "ThreeStarReward": 2012, "ExtraReward": []}, "13": {"Id": 13, "StringId": "chapter_13_1", "IsMaxLevel": false, "ChapterLevel": 2, "Chapter": 13, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 13, "EliteChallengeReward": 1013, "OneStarReward": 13, "TwoStarReward": 1013, "ThreeStarReward": 2013, "ExtraReward": []}, "14": {"Id": 14, "StringId": "chapter_14_1", "IsMaxLevel": false, "ChapterLevel": 3, "Chapter": 14, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 14, "EliteChallengeReward": 1014, "OneStarReward": 14, "TwoStarReward": 1014, "ThreeStarReward": 2014, "ExtraReward": []}, "15": {"Id": 15, "StringId": "chapter_15_1", "IsMaxLevel": false, "ChapterLevel": 3, "Chapter": 15, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 15, "EliteChallengeReward": 1015, "OneStarReward": 15, "TwoStarReward": 1015, "ThreeStarReward": 2015, "ExtraReward": []}, "16": {"Id": 16, "StringId": "chapter_16_1", "IsMaxLevel": false, "ChapterLevel": 3, "Chapter": 16, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 16, "EliteChallengeReward": 1016, "OneStarReward": 16, "TwoStarReward": 1016, "ThreeStarReward": 2016, "ExtraReward": []}, "17": {"Id": 17, "StringId": "chapter_17_1", "IsMaxLevel": false, "ChapterLevel": 3, "Chapter": 17, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 17, "EliteChallengeReward": 1017, "OneStarReward": 17, "TwoStarReward": 1017, "ThreeStarReward": 2017, "ExtraReward": []}, "18": {"Id": 18, "StringId": "chapter_18_1", "IsMaxLevel": false, "ChapterLevel": 3, "Chapter": 18, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 18, "EliteChallengeReward": 1018, "OneStarReward": 18, "TwoStarReward": 1018, "ThreeStarReward": 2018, "ExtraReward": []}, "19": {"Id": 19, "StringId": "chapter_19_1", "IsMaxLevel": false, "ChapterLevel": 3, "Chapter": 19, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 19, "EliteChallengeReward": 1019, "OneStarReward": 19, "TwoStarReward": 1019, "ThreeStarReward": 2019, "ExtraReward": []}, "20": {"Id": 20, "StringId": "chapter_20_1", "IsMaxLevel": false, "ChapterLevel": 3, "Chapter": 20, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 20, "EliteChallengeReward": 1020, "OneStarReward": 20, "TwoStarReward": 1020, "ThreeStarReward": 2020, "ExtraReward": []}, "21": {"Id": 21, "StringId": "chapter_21_1", "IsMaxLevel": false, "ChapterLevel": 3, "Chapter": 21, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 21, "EliteChallengeReward": 1021, "OneStarReward": 21, "TwoStarReward": 1021, "ThreeStarReward": 2021, "ExtraReward": []}, "22": {"Id": 22, "StringId": "chapter_22_1", "IsMaxLevel": false, "ChapterLevel": 3, "Chapter": 22, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 22, "EliteChallengeReward": 1022, "OneStarReward": 22, "TwoStarReward": 1022, "ThreeStarReward": 2022, "ExtraReward": []}, "23": {"Id": 23, "StringId": "chapter_23_1", "IsMaxLevel": false, "ChapterLevel": 3, "Chapter": 23, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 23, "EliteChallengeReward": 1023, "OneStarReward": 23, "TwoStarReward": 1023, "ThreeStarReward": 2023, "ExtraReward": []}, "24": {"Id": 24, "StringId": "chapter_24_1", "IsMaxLevel": false, "ChapterLevel": 4, "Chapter": 24, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 24, "EliteChallengeReward": 1024, "OneStarReward": 24, "TwoStarReward": 1024, "ThreeStarReward": 2024, "ExtraReward": []}, "25": {"Id": 25, "StringId": "chapter_25_1", "IsMaxLevel": false, "ChapterLevel": 4, "Chapter": 25, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 25, "EliteChallengeReward": 1025, "OneStarReward": 25, "TwoStarReward": 1025, "ThreeStarReward": 2025, "ExtraReward": []}, "26": {"Id": 26, "StringId": "chapter_26_1", "IsMaxLevel": false, "ChapterLevel": 4, "Chapter": 26, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 26, "EliteChallengeReward": 1026, "OneStarReward": 26, "TwoStarReward": 1026, "ThreeStarReward": 2026, "ExtraReward": []}, "27": {"Id": 27, "StringId": "chapter_27_1", "IsMaxLevel": false, "ChapterLevel": 4, "Chapter": 27, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 27, "EliteChallengeReward": 1027, "OneStarReward": 27, "TwoStarReward": 1027, "ThreeStarReward": 2027, "ExtraReward": []}, "28": {"Id": 28, "StringId": "chapter_28_1", "IsMaxLevel": false, "ChapterLevel": 4, "Chapter": 28, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 28, "EliteChallengeReward": 1028, "OneStarReward": 28, "TwoStarReward": 1028, "ThreeStarReward": 2028, "ExtraReward": []}, "29": {"Id": 29, "StringId": "chapter_29_1", "IsMaxLevel": false, "ChapterLevel": 4, "Chapter": 29, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 29, "EliteChallengeReward": 1029, "OneStarReward": 29, "TwoStarReward": 1029, "ThreeStarReward": 2029, "ExtraReward": []}, "30": {"Id": 30, "StringId": "chapter_30_1", "IsMaxLevel": false, "ChapterLevel": 4, "Chapter": 30, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 30, "EliteChallengeReward": 1030, "OneStarReward": 30, "TwoStarReward": 1030, "ThreeStarReward": 2030, "ExtraReward": []}, "31": {"Id": 31, "StringId": "chapter_31_1", "IsMaxLevel": false, "ChapterLevel": 4, "Chapter": 31, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 31, "EliteChallengeReward": 1031, "OneStarReward": 31, "TwoStarReward": 1031, "ThreeStarReward": 2031, "ExtraReward": []}, "32": {"Id": 32, "StringId": "chapter_32_1", "IsMaxLevel": false, "ChapterLevel": 4, "Chapter": 32, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 32, "EliteChallengeReward": 1032, "OneStarReward": 32, "TwoStarReward": 1032, "ThreeStarReward": 2032, "ExtraReward": []}, "33": {"Id": 33, "StringId": "chapter_33_1", "IsMaxLevel": false, "ChapterLevel": 4, "Chapter": 33, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 33, "EliteChallengeReward": 1033, "OneStarReward": 33, "TwoStarReward": 1033, "ThreeStarReward": 2033, "ExtraReward": []}, "34": {"Id": 34, "StringId": "chapter_34_1", "IsMaxLevel": false, "ChapterLevel": 5, "Chapter": 34, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 34, "EliteChallengeReward": 1034, "OneStarReward": 34, "TwoStarReward": 1034, "ThreeStarReward": 2034, "ExtraReward": []}, "35": {"Id": 35, "StringId": "chapter_35_1", "IsMaxLevel": false, "ChapterLevel": 5, "Chapter": 35, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 35, "EliteChallengeReward": 1035, "OneStarReward": 35, "TwoStarReward": 1035, "ThreeStarReward": 2035, "ExtraReward": []}, "36": {"Id": 36, "StringId": "chapter_36_1", "IsMaxLevel": false, "ChapterLevel": 5, "Chapter": 36, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 36, "EliteChallengeReward": 1036, "OneStarReward": 36, "TwoStarReward": 1036, "ThreeStarReward": 2036, "ExtraReward": []}, "37": {"Id": 37, "StringId": "chapter_37_1", "IsMaxLevel": false, "ChapterLevel": 5, "Chapter": 37, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 37, "EliteChallengeReward": 1037, "OneStarReward": 37, "TwoStarReward": 1037, "ThreeStarReward": 2037, "ExtraReward": []}, "38": {"Id": 38, "StringId": "chapter_38_1", "IsMaxLevel": false, "ChapterLevel": 5, "Chapter": 38, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 38, "EliteChallengeReward": 1038, "OneStarReward": 38, "TwoStarReward": 1038, "ThreeStarReward": 2038, "ExtraReward": []}, "39": {"Id": 39, "StringId": "chapter_39_1", "IsMaxLevel": false, "ChapterLevel": 5, "Chapter": 39, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 39, "EliteChallengeReward": 1039, "OneStarReward": 39, "TwoStarReward": 1039, "ThreeStarReward": 2039, "ExtraReward": []}, "40": {"Id": 40, "StringId": "chapter_40_1", "IsMaxLevel": false, "ChapterLevel": 5, "Chapter": 40, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 40, "EliteChallengeReward": 1040, "OneStarReward": 40, "TwoStarReward": 1040, "ThreeStarReward": 2040, "ExtraReward": []}, "41": {"Id": 41, "StringId": "chapter_41_1", "IsMaxLevel": false, "ChapterLevel": 5, "Chapter": 41, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 41, "EliteChallengeReward": 1041, "OneStarReward": 41, "TwoStarReward": 1041, "ThreeStarReward": 2041, "ExtraReward": []}, "42": {"Id": 42, "StringId": "chapter_42_1", "IsMaxLevel": false, "ChapterLevel": 5, "Chapter": 42, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 42, "EliteChallengeReward": 1042, "OneStarReward": 42, "TwoStarReward": 1042, "ThreeStarReward": 2042, "ExtraReward": []}, "43": {"Id": 43, "StringId": "chapter_43_1", "IsMaxLevel": false, "ChapterLevel": 5, "Chapter": 43, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 43, "EliteChallengeReward": 1043, "OneStarReward": 43, "TwoStarReward": 1043, "ThreeStarReward": 2043, "ExtraReward": []}, "44": {"Id": 44, "StringId": "chapter_44_1", "IsMaxLevel": false, "ChapterLevel": 6, "Chapter": 44, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 44, "EliteChallengeReward": 1044, "OneStarReward": 44, "TwoStarReward": 1044, "ThreeStarReward": 2044, "ExtraReward": []}, "45": {"Id": 45, "StringId": "chapter_45_1", "IsMaxLevel": false, "ChapterLevel": 6, "Chapter": 45, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 45, "EliteChallengeReward": 1045, "OneStarReward": 45, "TwoStarReward": 1045, "ThreeStarReward": 2045, "ExtraReward": []}, "46": {"Id": 46, "StringId": "chapter_46_1", "IsMaxLevel": false, "ChapterLevel": 6, "Chapter": 46, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 46, "EliteChallengeReward": 1046, "OneStarReward": 46, "TwoStarReward": 1046, "ThreeStarReward": 2046, "ExtraReward": []}, "47": {"Id": 47, "StringId": "chapter_47_1", "IsMaxLevel": false, "ChapterLevel": 6, "Chapter": 47, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 47, "EliteChallengeReward": 1047, "OneStarReward": 47, "TwoStarReward": 1047, "ThreeStarReward": 2047, "ExtraReward": []}, "48": {"Id": 48, "StringId": "chapter_48_1", "IsMaxLevel": false, "ChapterLevel": 6, "Chapter": 48, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 48, "EliteChallengeReward": 1048, "OneStarReward": 48, "TwoStarReward": 1048, "ThreeStarReward": 2048, "ExtraReward": []}, "49": {"Id": 49, "StringId": "chapter_49_1", "IsMaxLevel": false, "ChapterLevel": 6, "Chapter": 49, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 49, "EliteChallengeReward": 1049, "OneStarReward": 49, "TwoStarReward": 1049, "ThreeStarReward": 2049, "ExtraReward": []}, "50": {"Id": 50, "StringId": "chapter_50_1", "IsMaxLevel": false, "ChapterLevel": 6, "Chapter": 50, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 50, "EliteChallengeReward": 1050, "OneStarReward": 50, "TwoStarReward": 1050, "ThreeStarReward": 2050, "ExtraReward": []}, "51": {"Id": 51, "StringId": "chapter_51_1", "IsMaxLevel": false, "ChapterLevel": 6, "Chapter": 51, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 51, "EliteChallengeReward": 1051, "OneStarReward": 51, "TwoStarReward": 1051, "ThreeStarReward": 2051, "ExtraReward": []}, "52": {"Id": 52, "StringId": "chapter_52_1", "IsMaxLevel": false, "ChapterLevel": 6, "Chapter": 52, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 52, "EliteChallengeReward": 1052, "OneStarReward": 52, "TwoStarReward": 1052, "ThreeStarReward": 2052, "ExtraReward": []}, "53": {"Id": 53, "StringId": "chapter_53_1", "IsMaxLevel": false, "ChapterLevel": 6, "Chapter": 53, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 53, "EliteChallengeReward": 1053, "OneStarReward": 53, "TwoStarReward": 1053, "ThreeStarReward": 2053, "ExtraReward": []}, "54": {"Id": 54, "StringId": "chapter_54_1", "IsMaxLevel": false, "ChapterLevel": 7, "Chapter": 54, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 54, "EliteChallengeReward": 1054, "OneStarReward": 54, "TwoStarReward": 1054, "ThreeStarReward": 2054, "ExtraReward": []}, "55": {"Id": 55, "StringId": "chapter_55_1", "IsMaxLevel": false, "ChapterLevel": 7, "Chapter": 55, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 55, "EliteChallengeReward": 1055, "OneStarReward": 55, "TwoStarReward": 1055, "ThreeStarReward": 2055, "ExtraReward": []}, "56": {"Id": 56, "StringId": "chapter_56_1", "IsMaxLevel": false, "ChapterLevel": 7, "Chapter": 56, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 56, "EliteChallengeReward": 1056, "OneStarReward": 56, "TwoStarReward": 1056, "ThreeStarReward": 2056, "ExtraReward": []}, "57": {"Id": 57, "StringId": "chapter_57_1", "IsMaxLevel": false, "ChapterLevel": 7, "Chapter": 57, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 57, "EliteChallengeReward": 1057, "OneStarReward": 57, "TwoStarReward": 1057, "ThreeStarReward": 2057, "ExtraReward": []}, "58": {"Id": 58, "StringId": "chapter_58_1", "IsMaxLevel": false, "ChapterLevel": 7, "Chapter": 58, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 58, "EliteChallengeReward": 1058, "OneStarReward": 58, "TwoStarReward": 1058, "ThreeStarReward": 2058, "ExtraReward": []}, "59": {"Id": 59, "StringId": "chapter_59_1", "IsMaxLevel": false, "ChapterLevel": 7, "Chapter": 59, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 59, "EliteChallengeReward": 1059, "OneStarReward": 59, "TwoStarReward": 1059, "ThreeStarReward": 2059, "ExtraReward": []}, "60": {"Id": 60, "StringId": "chapter_60_1", "IsMaxLevel": false, "ChapterLevel": 7, "Chapter": 60, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 60, "EliteChallengeReward": 1060, "OneStarReward": 60, "TwoStarReward": 1060, "ThreeStarReward": 2060, "ExtraReward": []}, "61": {"Id": 61, "StringId": "chapter_61_1", "IsMaxLevel": false, "ChapterLevel": 7, "Chapter": 61, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 61, "EliteChallengeReward": 1061, "OneStarReward": 61, "TwoStarReward": 1061, "ThreeStarReward": 2061, "ExtraReward": []}, "62": {"Id": 62, "StringId": "chapter_62_1", "IsMaxLevel": false, "ChapterLevel": 7, "Chapter": 62, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 62, "EliteChallengeReward": 1062, "OneStarReward": 62, "TwoStarReward": 1062, "ThreeStarReward": 2062, "ExtraReward": []}, "63": {"Id": 63, "StringId": "chapter_63_1", "IsMaxLevel": false, "ChapterLevel": 7, "Chapter": 63, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 63, "EliteChallengeReward": 1063, "OneStarReward": 63, "TwoStarReward": 1063, "ThreeStarReward": 2063, "ExtraReward": []}, "64": {"Id": 64, "StringId": "chapter_64_1", "IsMaxLevel": false, "ChapterLevel": 8, "Chapter": 64, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 64, "EliteChallengeReward": 1064, "OneStarReward": 64, "TwoStarReward": 1064, "ThreeStarReward": 2064, "ExtraReward": []}, "65": {"Id": 65, "StringId": "chapter_65_1", "IsMaxLevel": false, "ChapterLevel": 8, "Chapter": 65, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 65, "EliteChallengeReward": 1065, "OneStarReward": 65, "TwoStarReward": 1065, "ThreeStarReward": 2065, "ExtraReward": []}, "66": {"Id": 66, "StringId": "chapter_66_1", "IsMaxLevel": false, "ChapterLevel": 8, "Chapter": 66, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 66, "EliteChallengeReward": 1066, "OneStarReward": 66, "TwoStarReward": 1066, "ThreeStarReward": 2066, "ExtraReward": []}, "67": {"Id": 67, "StringId": "chapter_67_1", "IsMaxLevel": false, "ChapterLevel": 8, "Chapter": 67, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 67, "EliteChallengeReward": 1067, "OneStarReward": 67, "TwoStarReward": 1067, "ThreeStarReward": 2067, "ExtraReward": []}, "68": {"Id": 68, "StringId": "chapter_68_1", "IsMaxLevel": false, "ChapterLevel": 8, "Chapter": 68, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 68, "EliteChallengeReward": 1068, "OneStarReward": 68, "TwoStarReward": 1068, "ThreeStarReward": 2068, "ExtraReward": []}, "69": {"Id": 69, "StringId": "chapter_69_1", "IsMaxLevel": false, "ChapterLevel": 8, "Chapter": 69, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 69, "EliteChallengeReward": 1069, "OneStarReward": 69, "TwoStarReward": 1069, "ThreeStarReward": 2069, "ExtraReward": []}, "70": {"Id": 70, "StringId": "chapter_70_1", "IsMaxLevel": false, "ChapterLevel": 8, "Chapter": 70, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 70, "EliteChallengeReward": 1070, "OneStarReward": 70, "TwoStarReward": 1070, "ThreeStarReward": 2070, "ExtraReward": []}, "71": {"Id": 71, "StringId": "chapter_71_1", "IsMaxLevel": false, "ChapterLevel": 8, "Chapter": 71, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 71, "EliteChallengeReward": 1071, "OneStarReward": 71, "TwoStarReward": 1071, "ThreeStarReward": 2071, "ExtraReward": []}, "72": {"Id": 72, "StringId": "chapter_72_1", "IsMaxLevel": false, "ChapterLevel": 8, "Chapter": 72, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 72, "EliteChallengeReward": 1072, "OneStarReward": 72, "TwoStarReward": 1072, "ThreeStarReward": 2072, "ExtraReward": []}, "73": {"Id": 73, "StringId": "chapter_73_1", "IsMaxLevel": false, "ChapterLevel": 8, "Chapter": 73, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 73, "EliteChallengeReward": 1073, "OneStarReward": 73, "TwoStarReward": 1073, "ThreeStarReward": 2073, "ExtraReward": []}, "74": {"Id": 74, "StringId": "chapter_74_1", "IsMaxLevel": false, "ChapterLevel": 9, "Chapter": 74, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 74, "EliteChallengeReward": 1074, "OneStarReward": 74, "TwoStarReward": 1074, "ThreeStarReward": 2074, "ExtraReward": []}, "75": {"Id": 75, "StringId": "chapter_75_1", "IsMaxLevel": false, "ChapterLevel": 9, "Chapter": 75, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 75, "EliteChallengeReward": 1075, "OneStarReward": 75, "TwoStarReward": 1075, "ThreeStarReward": 2075, "ExtraReward": []}, "76": {"Id": 76, "StringId": "chapter_76_1", "IsMaxLevel": false, "ChapterLevel": 9, "Chapter": 76, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 76, "EliteChallengeReward": 1076, "OneStarReward": 76, "TwoStarReward": 1076, "ThreeStarReward": 2076, "ExtraReward": []}, "77": {"Id": 77, "StringId": "chapter_77_1", "IsMaxLevel": false, "ChapterLevel": 9, "Chapter": 77, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 77, "EliteChallengeReward": 1077, "OneStarReward": 77, "TwoStarReward": 1077, "ThreeStarReward": 2077, "ExtraReward": []}, "78": {"Id": 78, "StringId": "chapter_78_1", "IsMaxLevel": false, "ChapterLevel": 9, "Chapter": 78, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 78, "EliteChallengeReward": 1078, "OneStarReward": 78, "TwoStarReward": 1078, "ThreeStarReward": 2078, "ExtraReward": []}, "79": {"Id": 79, "StringId": "chapter_79_1", "IsMaxLevel": false, "ChapterLevel": 9, "Chapter": 79, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 79, "EliteChallengeReward": 1079, "OneStarReward": 79, "TwoStarReward": 1079, "ThreeStarReward": 2079, "ExtraReward": []}, "80": {"Id": 80, "StringId": "chapter_80_1", "IsMaxLevel": false, "ChapterLevel": 9, "Chapter": 80, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 80, "EliteChallengeReward": 1080, "OneStarReward": 80, "TwoStarReward": 1080, "ThreeStarReward": 2080, "ExtraReward": []}, "81": {"Id": 81, "StringId": "chapter_81_1", "IsMaxLevel": false, "ChapterLevel": 9, "Chapter": 81, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 81, "EliteChallengeReward": 1081, "OneStarReward": 81, "TwoStarReward": 1081, "ThreeStarReward": 2081, "ExtraReward": []}, "82": {"Id": 82, "StringId": "chapter_82_1", "IsMaxLevel": false, "ChapterLevel": 9, "Chapter": 82, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 82, "EliteChallengeReward": 1082, "OneStarReward": 82, "TwoStarReward": 1082, "ThreeStarReward": 2082, "ExtraReward": []}, "83": {"Id": 83, "StringId": "chapter_83_1", "IsMaxLevel": false, "ChapterLevel": 9, "Chapter": 83, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 83, "EliteChallengeReward": 1083, "OneStarReward": 83, "TwoStarReward": 1083, "ThreeStarReward": 2083, "ExtraReward": []}, "84": {"Id": 84, "StringId": "chapter_84_1", "IsMaxLevel": false, "ChapterLevel": 10, "Chapter": 84, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 84, "EliteChallengeReward": 1084, "OneStarReward": 84, "TwoStarReward": 1084, "ThreeStarReward": 2084, "ExtraReward": []}, "85": {"Id": 85, "StringId": "chapter_85_1", "IsMaxLevel": false, "ChapterLevel": 10, "Chapter": 85, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 85, "EliteChallengeReward": 1085, "OneStarReward": 85, "TwoStarReward": 1085, "ThreeStarReward": 2085, "ExtraReward": []}, "86": {"Id": 86, "StringId": "chapter_86_1", "IsMaxLevel": false, "ChapterLevel": 10, "Chapter": 86, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 86, "EliteChallengeReward": 1086, "OneStarReward": 86, "TwoStarReward": 1086, "ThreeStarReward": 2086, "ExtraReward": []}, "87": {"Id": 87, "StringId": "chapter_87_1", "IsMaxLevel": false, "ChapterLevel": 10, "Chapter": 87, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 87, "EliteChallengeReward": 1087, "OneStarReward": 87, "TwoStarReward": 1087, "ThreeStarReward": 2087, "ExtraReward": []}, "88": {"Id": 88, "StringId": "chapter_88_1", "IsMaxLevel": false, "ChapterLevel": 10, "Chapter": 88, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 88, "EliteChallengeReward": 1088, "OneStarReward": 88, "TwoStarReward": 1088, "ThreeStarReward": 2088, "ExtraReward": []}, "89": {"Id": 89, "StringId": "chapter_89_1", "IsMaxLevel": false, "ChapterLevel": 10, "Chapter": 89, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 89, "EliteChallengeReward": 1089, "OneStarReward": 89, "TwoStarReward": 1089, "ThreeStarReward": 2089, "ExtraReward": []}, "90": {"Id": 90, "StringId": "chapter_90_1", "IsMaxLevel": false, "ChapterLevel": 10, "Chapter": 90, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 90, "EliteChallengeReward": 1090, "OneStarReward": 90, "TwoStarReward": 1090, "ThreeStarReward": 2090, "ExtraReward": []}, "91": {"Id": 91, "StringId": "chapter_91_1", "IsMaxLevel": false, "ChapterLevel": 10, "Chapter": 91, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 91, "EliteChallengeReward": 1091, "OneStarReward": 91, "TwoStarReward": 1091, "ThreeStarReward": 2091, "ExtraReward": []}, "92": {"Id": 92, "StringId": "chapter_92_1", "IsMaxLevel": false, "ChapterLevel": 10, "Chapter": 92, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 92, "EliteChallengeReward": 1092, "OneStarReward": 92, "TwoStarReward": 1092, "ThreeStarReward": 2092, "ExtraReward": []}, "93": {"Id": 93, "StringId": "chapter_93_1", "IsMaxLevel": false, "ChapterLevel": 10, "Chapter": 93, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 93, "EliteChallengeReward": 1093, "OneStarReward": 93, "TwoStarReward": 1093, "ThreeStarReward": 2093, "ExtraReward": []}, "94": {"Id": 94, "StringId": "chapter_94_1", "IsMaxLevel": false, "ChapterLevel": 11, "Chapter": 94, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 94, "EliteChallengeReward": 1094, "OneStarReward": 94, "TwoStarReward": 1094, "ThreeStarReward": 2094, "ExtraReward": []}, "95": {"Id": 95, "StringId": "chapter_95_1", "IsMaxLevel": false, "ChapterLevel": 11, "Chapter": 95, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 95, "EliteChallengeReward": 1095, "OneStarReward": 95, "TwoStarReward": 1095, "ThreeStarReward": 2095, "ExtraReward": []}, "96": {"Id": 96, "StringId": "chapter_96_1", "IsMaxLevel": false, "ChapterLevel": 11, "Chapter": 96, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 96, "EliteChallengeReward": 1096, "OneStarReward": 96, "TwoStarReward": 1096, "ThreeStarReward": 2096, "ExtraReward": []}, "97": {"Id": 97, "StringId": "chapter_97_1", "IsMaxLevel": false, "ChapterLevel": 11, "Chapter": 97, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 97, "EliteChallengeReward": 1097, "OneStarReward": 97, "TwoStarReward": 1097, "ThreeStarReward": 2097, "ExtraReward": []}, "98": {"Id": 98, "StringId": "chapter_98_1", "IsMaxLevel": false, "ChapterLevel": 11, "Chapter": 98, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 98, "EliteChallengeReward": 1098, "OneStarReward": 98, "TwoStarReward": 1098, "ThreeStarReward": 2098, "ExtraReward": []}, "99": {"Id": 99, "StringId": "chapter_99_1", "IsMaxLevel": false, "ChapterLevel": 11, "Chapter": 99, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 99, "EliteChallengeReward": 1099, "OneStarReward": 99, "TwoStarReward": 1099, "ThreeStarReward": 2099, "ExtraReward": []}, "100": {"Id": 100, "StringId": "chapter_100_1", "IsMaxLevel": false, "ChapterLevel": 11, "Chapter": 100, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 100, "EliteChallengeReward": 1100, "OneStarReward": 100, "TwoStarReward": 1100, "ThreeStarReward": 2100, "ExtraReward": []}, "101": {"Id": 101, "StringId": "chapter_101_1", "IsMaxLevel": false, "ChapterLevel": 11, "Chapter": 101, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 101, "EliteChallengeReward": 1101, "OneStarReward": 101, "TwoStarReward": 1101, "ThreeStarReward": 2101, "ExtraReward": []}, "102": {"Id": 102, "StringId": "chapter_102_1", "IsMaxLevel": false, "ChapterLevel": 11, "Chapter": 102, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 102, "EliteChallengeReward": 1102, "OneStarReward": 102, "TwoStarReward": 1102, "ThreeStarReward": 2102, "ExtraReward": []}, "103": {"Id": 103, "StringId": "chapter_103_1", "IsMaxLevel": false, "ChapterLevel": 11, "Chapter": 103, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 103, "EliteChallengeReward": 1103, "OneStarReward": 103, "TwoStarReward": 1103, "ThreeStarReward": 2103, "ExtraReward": []}, "104": {"Id": 104, "StringId": "chapter_104_1", "IsMaxLevel": false, "ChapterLevel": 12, "Chapter": 104, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 104, "EliteChallengeReward": 1104, "OneStarReward": 104, "TwoStarReward": 1104, "ThreeStarReward": 2104, "ExtraReward": []}, "105": {"Id": 105, "StringId": "chapter_105_1", "IsMaxLevel": false, "ChapterLevel": 12, "Chapter": 105, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 105, "EliteChallengeReward": 1105, "OneStarReward": 105, "TwoStarReward": 1105, "ThreeStarReward": 2105, "ExtraReward": []}, "106": {"Id": 106, "StringId": "chapter_106_1", "IsMaxLevel": false, "ChapterLevel": 12, "Chapter": 106, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 106, "EliteChallengeReward": 1106, "OneStarReward": 106, "TwoStarReward": 1106, "ThreeStarReward": 2106, "ExtraReward": []}, "107": {"Id": 107, "StringId": "chapter_107_1", "IsMaxLevel": false, "ChapterLevel": 12, "Chapter": 107, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 107, "EliteChallengeReward": 1107, "OneStarReward": 107, "TwoStarReward": 1107, "ThreeStarReward": 2107, "ExtraReward": []}, "108": {"Id": 108, "StringId": "chapter_108_1", "IsMaxLevel": false, "ChapterLevel": 12, "Chapter": 108, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 108, "EliteChallengeReward": 1108, "OneStarReward": 108, "TwoStarReward": 1108, "ThreeStarReward": 2108, "ExtraReward": []}, "109": {"Id": 109, "StringId": "chapter_109_1", "IsMaxLevel": false, "ChapterLevel": 12, "Chapter": 109, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 109, "EliteChallengeReward": 1109, "OneStarReward": 109, "TwoStarReward": 1109, "ThreeStarReward": 2109, "ExtraReward": []}, "110": {"Id": 110, "StringId": "chapter_110_1", "IsMaxLevel": false, "ChapterLevel": 12, "Chapter": 110, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 110, "EliteChallengeReward": 1110, "OneStarReward": 110, "TwoStarReward": 1110, "ThreeStarReward": 2110, "ExtraReward": []}, "111": {"Id": 111, "StringId": "chapter_111_1", "IsMaxLevel": false, "ChapterLevel": 12, "Chapter": 111, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 111, "EliteChallengeReward": 1111, "OneStarReward": 111, "TwoStarReward": 1111, "ThreeStarReward": 2111, "ExtraReward": []}, "112": {"Id": 112, "StringId": "chapter_112_1", "IsMaxLevel": false, "ChapterLevel": 12, "Chapter": 112, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 112, "EliteChallengeReward": 1112, "OneStarReward": 112, "TwoStarReward": 1112, "ThreeStarReward": 2112, "ExtraReward": []}, "113": {"Id": 113, "StringId": "chapter_113_1", "IsMaxLevel": false, "ChapterLevel": 12, "Chapter": 113, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 113, "EliteChallengeReward": 1113, "OneStarReward": 113, "TwoStarReward": 1113, "ThreeStarReward": 2113, "ExtraReward": []}, "114": {"Id": 114, "StringId": "chapter_114_1", "IsMaxLevel": false, "ChapterLevel": 13, "Chapter": 114, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 114, "EliteChallengeReward": 1114, "OneStarReward": 114, "TwoStarReward": 1114, "ThreeStarReward": 2114, "ExtraReward": []}, "115": {"Id": 115, "StringId": "chapter_115_1", "IsMaxLevel": false, "ChapterLevel": 13, "Chapter": 115, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 115, "EliteChallengeReward": 1115, "OneStarReward": 115, "TwoStarReward": 1115, "ThreeStarReward": 2115, "ExtraReward": []}, "116": {"Id": 116, "StringId": "chapter_116_1", "IsMaxLevel": false, "ChapterLevel": 13, "Chapter": 116, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 116, "EliteChallengeReward": 1116, "OneStarReward": 116, "TwoStarReward": 1116, "ThreeStarReward": 2116, "ExtraReward": []}, "117": {"Id": 117, "StringId": "chapter_117_1", "IsMaxLevel": false, "ChapterLevel": 13, "Chapter": 117, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 117, "EliteChallengeReward": 1117, "OneStarReward": 117, "TwoStarReward": 1117, "ThreeStarReward": 2117, "ExtraReward": []}, "118": {"Id": 118, "StringId": "chapter_118_1", "IsMaxLevel": false, "ChapterLevel": 13, "Chapter": 118, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 118, "EliteChallengeReward": 1118, "OneStarReward": 118, "TwoStarReward": 1118, "ThreeStarReward": 2118, "ExtraReward": []}, "119": {"Id": 119, "StringId": "chapter_119_1", "IsMaxLevel": false, "ChapterLevel": 13, "Chapter": 119, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 119, "EliteChallengeReward": 1119, "OneStarReward": 119, "TwoStarReward": 1119, "ThreeStarReward": 2119, "ExtraReward": []}, "120": {"Id": 120, "StringId": "chapter_120_1", "IsMaxLevel": false, "ChapterLevel": 13, "Chapter": 120, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 120, "EliteChallengeReward": 1120, "OneStarReward": 120, "TwoStarReward": 1120, "ThreeStarReward": 2120, "ExtraReward": []}, "121": {"Id": 121, "StringId": "chapter_121_1", "IsMaxLevel": false, "ChapterLevel": 13, "Chapter": 121, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 121, "EliteChallengeReward": 1121, "OneStarReward": 121, "TwoStarReward": 1121, "ThreeStarReward": 2121, "ExtraReward": []}, "122": {"Id": 122, "StringId": "chapter_122_1", "IsMaxLevel": false, "ChapterLevel": 13, "Chapter": 122, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 122, "EliteChallengeReward": 1122, "OneStarReward": 122, "TwoStarReward": 1122, "ThreeStarReward": 2122, "ExtraReward": []}, "123": {"Id": 123, "StringId": "chapter_123_1", "IsMaxLevel": false, "ChapterLevel": 13, "Chapter": 123, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 123, "EliteChallengeReward": 1123, "OneStarReward": 123, "TwoStarReward": 1123, "ThreeStarReward": 2123, "ExtraReward": []}, "124": {"Id": 124, "StringId": "chapter_124_1", "IsMaxLevel": false, "ChapterLevel": 14, "Chapter": 124, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 124, "EliteChallengeReward": 1124, "OneStarReward": 124, "TwoStarReward": 1124, "ThreeStarReward": 2124, "ExtraReward": []}, "125": {"Id": 125, "StringId": "chapter_125_1", "IsMaxLevel": false, "ChapterLevel": 14, "Chapter": 125, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 125, "EliteChallengeReward": 1125, "OneStarReward": 125, "TwoStarReward": 1125, "ThreeStarReward": 2125, "ExtraReward": []}, "126": {"Id": 126, "StringId": "chapter_126_1", "IsMaxLevel": false, "ChapterLevel": 14, "Chapter": 126, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 126, "EliteChallengeReward": 1126, "OneStarReward": 126, "TwoStarReward": 1126, "ThreeStarReward": 2126, "ExtraReward": []}, "127": {"Id": 127, "StringId": "chapter_127_1", "IsMaxLevel": false, "ChapterLevel": 14, "Chapter": 127, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 127, "EliteChallengeReward": 1127, "OneStarReward": 127, "TwoStarReward": 1127, "ThreeStarReward": 2127, "ExtraReward": []}, "128": {"Id": 128, "StringId": "chapter_128_1", "IsMaxLevel": false, "ChapterLevel": 14, "Chapter": 128, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 128, "EliteChallengeReward": 1128, "OneStarReward": 128, "TwoStarReward": 1128, "ThreeStarReward": 2128, "ExtraReward": []}, "129": {"Id": 129, "StringId": "chapter_129_1", "IsMaxLevel": false, "ChapterLevel": 14, "Chapter": 129, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 129, "EliteChallengeReward": 1129, "OneStarReward": 129, "TwoStarReward": 1129, "ThreeStarReward": 2129, "ExtraReward": []}, "130": {"Id": 130, "StringId": "chapter_130_1", "IsMaxLevel": false, "ChapterLevel": 14, "Chapter": 130, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 130, "EliteChallengeReward": 1130, "OneStarReward": 130, "TwoStarReward": 1130, "ThreeStarReward": 2130, "ExtraReward": []}, "131": {"Id": 131, "StringId": "chapter_131_1", "IsMaxLevel": false, "ChapterLevel": 14, "Chapter": 131, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 131, "EliteChallengeReward": 1131, "OneStarReward": 131, "TwoStarReward": 1131, "ThreeStarReward": 2131, "ExtraReward": []}, "132": {"Id": 132, "StringId": "chapter_132_1", "IsMaxLevel": false, "ChapterLevel": 14, "Chapter": 132, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 132, "EliteChallengeReward": 1132, "OneStarReward": 132, "TwoStarReward": 1132, "ThreeStarReward": 2132, "ExtraReward": []}, "133": {"Id": 133, "StringId": "chapter_133_1", "IsMaxLevel": false, "ChapterLevel": 14, "Chapter": 133, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 133, "EliteChallengeReward": 1133, "OneStarReward": 133, "TwoStarReward": 1133, "ThreeStarReward": 2133, "ExtraReward": []}, "134": {"Id": 134, "StringId": "chapter_134_1", "IsMaxLevel": false, "ChapterLevel": 15, "Chapter": 134, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 134, "EliteChallengeReward": 1134, "OneStarReward": 134, "TwoStarReward": 1134, "ThreeStarReward": 2134, "ExtraReward": []}, "135": {"Id": 135, "StringId": "chapter_135_1", "IsMaxLevel": false, "ChapterLevel": 15, "Chapter": 135, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 135, "EliteChallengeReward": 1135, "OneStarReward": 135, "TwoStarReward": 1135, "ThreeStarReward": 2135, "ExtraReward": []}, "136": {"Id": 136, "StringId": "chapter_136_1", "IsMaxLevel": false, "ChapterLevel": 15, "Chapter": 136, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 136, "EliteChallengeReward": 1136, "OneStarReward": 136, "TwoStarReward": 1136, "ThreeStarReward": 2136, "ExtraReward": []}, "137": {"Id": 137, "StringId": "chapter_137_1", "IsMaxLevel": false, "ChapterLevel": 15, "Chapter": 137, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 137, "EliteChallengeReward": 1137, "OneStarReward": 137, "TwoStarReward": 1137, "ThreeStarReward": 2137, "ExtraReward": []}, "138": {"Id": 138, "StringId": "chapter_138_1", "IsMaxLevel": false, "ChapterLevel": 15, "Chapter": 138, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 138, "EliteChallengeReward": 1138, "OneStarReward": 138, "TwoStarReward": 1138, "ThreeStarReward": 2138, "ExtraReward": []}, "139": {"Id": 139, "StringId": "chapter_139_1", "IsMaxLevel": false, "ChapterLevel": 15, "Chapter": 139, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 139, "EliteChallengeReward": 1139, "OneStarReward": 139, "TwoStarReward": 1139, "ThreeStarReward": 2139, "ExtraReward": []}, "140": {"Id": 140, "StringId": "chapter_140_1", "IsMaxLevel": false, "ChapterLevel": 15, "Chapter": 140, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 140, "EliteChallengeReward": 1140, "OneStarReward": 140, "TwoStarReward": 1140, "ThreeStarReward": 2140, "ExtraReward": []}, "141": {"Id": 141, "StringId": "chapter_141_1", "IsMaxLevel": false, "ChapterLevel": 15, "Chapter": 141, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 141, "EliteChallengeReward": 1141, "OneStarReward": 141, "TwoStarReward": 1141, "ThreeStarReward": 2141, "ExtraReward": []}, "142": {"Id": 142, "StringId": "chapter_142_1", "IsMaxLevel": false, "ChapterLevel": 15, "Chapter": 142, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 142, "EliteChallengeReward": 1142, "OneStarReward": 142, "TwoStarReward": 1142, "ThreeStarReward": 2142, "ExtraReward": []}, "143": {"Id": 143, "StringId": "chapter_143_1", "IsMaxLevel": false, "ChapterLevel": 15, "Chapter": 143, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 143, "EliteChallengeReward": 1143, "OneStarReward": 143, "TwoStarReward": 1143, "ThreeStarReward": 2143, "ExtraReward": []}, "144": {"Id": 144, "StringId": "chapter_144_1", "IsMaxLevel": false, "ChapterLevel": 16, "Chapter": 144, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 144, "EliteChallengeReward": 1144, "OneStarReward": 144, "TwoStarReward": 1144, "ThreeStarReward": 2144, "ExtraReward": []}, "145": {"Id": 145, "StringId": "chapter_145_1", "IsMaxLevel": false, "ChapterLevel": 16, "Chapter": 145, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 145, "EliteChallengeReward": 1145, "OneStarReward": 145, "TwoStarReward": 1145, "ThreeStarReward": 2145, "ExtraReward": []}, "146": {"Id": 146, "StringId": "chapter_146_1", "IsMaxLevel": false, "ChapterLevel": 16, "Chapter": 146, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 146, "EliteChallengeReward": 1146, "OneStarReward": 146, "TwoStarReward": 1146, "ThreeStarReward": 2146, "ExtraReward": []}, "147": {"Id": 147, "StringId": "chapter_147_1", "IsMaxLevel": false, "ChapterLevel": 16, "Chapter": 147, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 147, "EliteChallengeReward": 1147, "OneStarReward": 147, "TwoStarReward": 1147, "ThreeStarReward": 2147, "ExtraReward": []}, "148": {"Id": 148, "StringId": "chapter_148_1", "IsMaxLevel": false, "ChapterLevel": 16, "Chapter": 148, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 148, "EliteChallengeReward": 1148, "OneStarReward": 148, "TwoStarReward": 1148, "ThreeStarReward": 2148, "ExtraReward": []}, "149": {"Id": 149, "StringId": "chapter_149_1", "IsMaxLevel": false, "ChapterLevel": 16, "Chapter": 149, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 149, "EliteChallengeReward": 1149, "OneStarReward": 149, "TwoStarReward": 1149, "ThreeStarReward": 2149, "ExtraReward": []}, "150": {"Id": 150, "StringId": "chapter_150_1", "IsMaxLevel": false, "ChapterLevel": 16, "Chapter": 150, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 150, "EliteChallengeReward": 1150, "OneStarReward": 150, "TwoStarReward": 1150, "ThreeStarReward": 2150, "ExtraReward": []}, "151": {"Id": 151, "StringId": "chapter_151_1", "IsMaxLevel": false, "ChapterLevel": 16, "Chapter": 151, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 151, "EliteChallengeReward": 1151, "OneStarReward": 151, "TwoStarReward": 1151, "ThreeStarReward": 2151, "ExtraReward": []}, "152": {"Id": 152, "StringId": "chapter_152_1", "IsMaxLevel": false, "ChapterLevel": 16, "Chapter": 152, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 152, "EliteChallengeReward": 1152, "OneStarReward": 152, "TwoStarReward": 1152, "ThreeStarReward": 2152, "ExtraReward": []}, "153": {"Id": 153, "StringId": "chapter_153_1", "IsMaxLevel": false, "ChapterLevel": 16, "Chapter": 153, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 153, "EliteChallengeReward": 1153, "OneStarReward": 153, "TwoStarReward": 1153, "ThreeStarReward": 2153, "ExtraReward": []}, "154": {"Id": 154, "StringId": "chapter_154_1", "IsMaxLevel": false, "ChapterLevel": 17, "Chapter": 154, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 154, "EliteChallengeReward": 1154, "OneStarReward": 154, "TwoStarReward": 1154, "ThreeStarReward": 2154, "ExtraReward": []}, "155": {"Id": 155, "StringId": "chapter_155_1", "IsMaxLevel": false, "ChapterLevel": 17, "Chapter": 155, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 155, "EliteChallengeReward": 1155, "OneStarReward": 155, "TwoStarReward": 1155, "ThreeStarReward": 2155, "ExtraReward": []}, "156": {"Id": 156, "StringId": "chapter_156_1", "IsMaxLevel": false, "ChapterLevel": 17, "Chapter": 156, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 156, "EliteChallengeReward": 1156, "OneStarReward": 156, "TwoStarReward": 1156, "ThreeStarReward": 2156, "ExtraReward": []}, "157": {"Id": 157, "StringId": "chapter_157_1", "IsMaxLevel": false, "ChapterLevel": 17, "Chapter": 157, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 157, "EliteChallengeReward": 1157, "OneStarReward": 157, "TwoStarReward": 1157, "ThreeStarReward": 2157, "ExtraReward": []}, "158": {"Id": 158, "StringId": "chapter_158_1", "IsMaxLevel": false, "ChapterLevel": 17, "Chapter": 158, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 158, "EliteChallengeReward": 1158, "OneStarReward": 158, "TwoStarReward": 1158, "ThreeStarReward": 2158, "ExtraReward": []}, "159": {"Id": 159, "StringId": "chapter_159_1", "IsMaxLevel": false, "ChapterLevel": 17, "Chapter": 159, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 159, "EliteChallengeReward": 1159, "OneStarReward": 159, "TwoStarReward": 1159, "ThreeStarReward": 2159, "ExtraReward": []}, "160": {"Id": 160, "StringId": "chapter_160_1", "IsMaxLevel": false, "ChapterLevel": 17, "Chapter": 160, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 160, "EliteChallengeReward": 1160, "OneStarReward": 160, "TwoStarReward": 1160, "ThreeStarReward": 2160, "ExtraReward": []}, "161": {"Id": 161, "StringId": "chapter_161_1", "IsMaxLevel": false, "ChapterLevel": 17, "Chapter": 161, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 161, "EliteChallengeReward": 1161, "OneStarReward": 161, "TwoStarReward": 1161, "ThreeStarReward": 2161, "ExtraReward": []}, "162": {"Id": 162, "StringId": "chapter_162_1", "IsMaxLevel": false, "ChapterLevel": 17, "Chapter": 162, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 162, "EliteChallengeReward": 1162, "OneStarReward": 162, "TwoStarReward": 1162, "ThreeStarReward": 2162, "ExtraReward": []}, "163": {"Id": 163, "StringId": "chapter_163_1", "IsMaxLevel": false, "ChapterLevel": 17, "Chapter": 163, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 163, "EliteChallengeReward": 1163, "OneStarReward": 163, "TwoStarReward": 1163, "ThreeStarReward": 2163, "ExtraReward": []}, "164": {"Id": 164, "StringId": "chapter_164_1", "IsMaxLevel": false, "ChapterLevel": 18, "Chapter": 164, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 164, "EliteChallengeReward": 1164, "OneStarReward": 164, "TwoStarReward": 1164, "ThreeStarReward": 2164, "ExtraReward": []}, "165": {"Id": 165, "StringId": "chapter_165_1", "IsMaxLevel": false, "ChapterLevel": 18, "Chapter": 165, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 165, "EliteChallengeReward": 1165, "OneStarReward": 165, "TwoStarReward": 1165, "ThreeStarReward": 2165, "ExtraReward": []}, "166": {"Id": 166, "StringId": "chapter_166_1", "IsMaxLevel": false, "ChapterLevel": 18, "Chapter": 166, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 166, "EliteChallengeReward": 1166, "OneStarReward": 166, "TwoStarReward": 1166, "ThreeStarReward": 2166, "ExtraReward": []}, "167": {"Id": 167, "StringId": "chapter_167_1", "IsMaxLevel": false, "ChapterLevel": 18, "Chapter": 167, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 167, "EliteChallengeReward": 1167, "OneStarReward": 167, "TwoStarReward": 1167, "ThreeStarReward": 2167, "ExtraReward": []}, "168": {"Id": 168, "StringId": "chapter_168_1", "IsMaxLevel": false, "ChapterLevel": 18, "Chapter": 168, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 168, "EliteChallengeReward": 1168, "OneStarReward": 168, "TwoStarReward": 1168, "ThreeStarReward": 2168, "ExtraReward": []}, "169": {"Id": 169, "StringId": "chapter_169_1", "IsMaxLevel": false, "ChapterLevel": 18, "Chapter": 169, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 169, "EliteChallengeReward": 1169, "OneStarReward": 169, "TwoStarReward": 1169, "ThreeStarReward": 2169, "ExtraReward": []}, "170": {"Id": 170, "StringId": "chapter_170_1", "IsMaxLevel": false, "ChapterLevel": 18, "Chapter": 170, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 170, "EliteChallengeReward": 1170, "OneStarReward": 170, "TwoStarReward": 1170, "ThreeStarReward": 2170, "ExtraReward": []}, "171": {"Id": 171, "StringId": "chapter_171_1", "IsMaxLevel": false, "ChapterLevel": 18, "Chapter": 171, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 171, "EliteChallengeReward": 1171, "OneStarReward": 171, "TwoStarReward": 1171, "ThreeStarReward": 2171, "ExtraReward": []}, "172": {"Id": 172, "StringId": "chapter_172_1", "IsMaxLevel": false, "ChapterLevel": 18, "Chapter": 172, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 172, "EliteChallengeReward": 1172, "OneStarReward": 172, "TwoStarReward": 1172, "ThreeStarReward": 2172, "ExtraReward": []}, "173": {"Id": 173, "StringId": "chapter_173_1", "IsMaxLevel": false, "ChapterLevel": 18, "Chapter": 173, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 173, "EliteChallengeReward": 1173, "OneStarReward": 173, "TwoStarReward": 1173, "ThreeStarReward": 2173, "ExtraReward": []}, "174": {"Id": 174, "StringId": "chapter_174_1", "IsMaxLevel": false, "ChapterLevel": 19, "Chapter": 174, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 174, "EliteChallengeReward": 1174, "OneStarReward": 174, "TwoStarReward": 1174, "ThreeStarReward": 2174, "ExtraReward": []}, "175": {"Id": 175, "StringId": "chapter_175_1", "IsMaxLevel": false, "ChapterLevel": 19, "Chapter": 175, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 175, "EliteChallengeReward": 1175, "OneStarReward": 175, "TwoStarReward": 1175, "ThreeStarReward": 2175, "ExtraReward": []}, "176": {"Id": 176, "StringId": "chapter_176_1", "IsMaxLevel": false, "ChapterLevel": 19, "Chapter": 176, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 176, "EliteChallengeReward": 1176, "OneStarReward": 176, "TwoStarReward": 1176, "ThreeStarReward": 2176, "ExtraReward": []}, "177": {"Id": 177, "StringId": "chapter_177_1", "IsMaxLevel": false, "ChapterLevel": 19, "Chapter": 177, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 177, "EliteChallengeReward": 1177, "OneStarReward": 177, "TwoStarReward": 1177, "ThreeStarReward": 2177, "ExtraReward": []}, "178": {"Id": 178, "StringId": "chapter_178_1", "IsMaxLevel": false, "ChapterLevel": 19, "Chapter": 178, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 178, "EliteChallengeReward": 1178, "OneStarReward": 178, "TwoStarReward": 1178, "ThreeStarReward": 2178, "ExtraReward": []}, "179": {"Id": 179, "StringId": "chapter_179_1", "IsMaxLevel": false, "ChapterLevel": 19, "Chapter": 179, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 179, "EliteChallengeReward": 1179, "OneStarReward": 179, "TwoStarReward": 1179, "ThreeStarReward": 2179, "ExtraReward": []}, "180": {"Id": 180, "StringId": "chapter_180_1", "IsMaxLevel": false, "ChapterLevel": 19, "Chapter": 180, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 180, "EliteChallengeReward": 1180, "OneStarReward": 180, "TwoStarReward": 1180, "ThreeStarReward": 2180, "ExtraReward": []}, "181": {"Id": 181, "StringId": "chapter_181_1", "IsMaxLevel": false, "ChapterLevel": 19, "Chapter": 181, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 181, "EliteChallengeReward": 1181, "OneStarReward": 181, "TwoStarReward": 1181, "ThreeStarReward": 2181, "ExtraReward": []}, "182": {"Id": 182, "StringId": "chapter_182_1", "IsMaxLevel": false, "ChapterLevel": 19, "Chapter": 182, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 182, "EliteChallengeReward": 1182, "OneStarReward": 182, "TwoStarReward": 1182, "ThreeStarReward": 2182, "ExtraReward": []}, "183": {"Id": 183, "StringId": "chapter_183_1", "IsMaxLevel": false, "ChapterLevel": 19, "Chapter": 183, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 183, "EliteChallengeReward": 1183, "OneStarReward": 183, "TwoStarReward": 1183, "ThreeStarReward": 2183, "ExtraReward": []}, "184": {"Id": 184, "StringId": "chapter_184_1", "IsMaxLevel": false, "ChapterLevel": 20, "Chapter": 184, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 184, "EliteChallengeReward": 1184, "OneStarReward": 184, "TwoStarReward": 1184, "ThreeStarReward": 2184, "ExtraReward": []}, "185": {"Id": 185, "StringId": "chapter_185_1", "IsMaxLevel": false, "ChapterLevel": 20, "Chapter": 185, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 185, "EliteChallengeReward": 1185, "OneStarReward": 185, "TwoStarReward": 1185, "ThreeStarReward": 2185, "ExtraReward": []}, "186": {"Id": 186, "StringId": "chapter_186_1", "IsMaxLevel": false, "ChapterLevel": 20, "Chapter": 186, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 186, "EliteChallengeReward": 1186, "OneStarReward": 186, "TwoStarReward": 1186, "ThreeStarReward": 2186, "ExtraReward": []}, "187": {"Id": 187, "StringId": "chapter_187_1", "IsMaxLevel": false, "ChapterLevel": 20, "Chapter": 187, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 187, "EliteChallengeReward": 1187, "OneStarReward": 187, "TwoStarReward": 1187, "ThreeStarReward": 2187, "ExtraReward": []}, "188": {"Id": 188, "StringId": "chapter_188_1", "IsMaxLevel": false, "ChapterLevel": 20, "Chapter": 188, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 188, "EliteChallengeReward": 1188, "OneStarReward": 188, "TwoStarReward": 1188, "ThreeStarReward": 2188, "ExtraReward": []}, "189": {"Id": 189, "StringId": "chapter_189_1", "IsMaxLevel": false, "ChapterLevel": 20, "Chapter": 189, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 189, "EliteChallengeReward": 1189, "OneStarReward": 189, "TwoStarReward": 1189, "ThreeStarReward": 2189, "ExtraReward": []}, "190": {"Id": 190, "StringId": "chapter_190_1", "IsMaxLevel": false, "ChapterLevel": 20, "Chapter": 190, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 190, "EliteChallengeReward": 1190, "OneStarReward": 190, "TwoStarReward": 1190, "ThreeStarReward": 2190, "ExtraReward": []}, "191": {"Id": 191, "StringId": "chapter_191_1", "IsMaxLevel": false, "ChapterLevel": 20, "Chapter": 191, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 191, "EliteChallengeReward": 1191, "OneStarReward": 191, "TwoStarReward": 1191, "ThreeStarReward": 2191, "ExtraReward": []}, "192": {"Id": 192, "StringId": "chapter_192_1", "IsMaxLevel": false, "ChapterLevel": 20, "Chapter": 192, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 192, "EliteChallengeReward": 1192, "OneStarReward": 192, "TwoStarReward": 1192, "ThreeStarReward": 2192, "ExtraReward": []}, "193": {"Id": 193, "StringId": "chapter_193_1", "IsMaxLevel": true, "ChapterLevel": 20, "Chapter": 193, "Level": 1, "EliteMonsterAtkRatio": 2, "EliteMonsterDefRatio": 2, "EliteMonsterHpRatio": 2, "LevelType": 1, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 193, "EliteChallengeReward": 1193, "OneStarReward": 193, "TwoStarReward": 1193, "ThreeStarReward": 2193, "ExtraReward": []}, "194": {"Id": 194, "StringId": "chapter_15_2", "IsMaxLevel": false, "ChapterLevel": 0, "Chapter": 103, "Level": 2, "EliteMonsterAtkRatio": 0, "EliteMonsterDefRatio": 0, "EliteMonsterHpRatio": 0, "LevelType": 2, "KillRewardRougeTabCntScheme": 1, "CommonChallengeReward": 100, "EliteChallengeReward": 1100, "OneStarReward": 15, "TwoStarReward": 0, "ThreeStarReward": 0, "ExtraReward": []}}