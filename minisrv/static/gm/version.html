<!DOCTYPE html>
<html>
<head>
    <title>GM管理后台 - 版本管理</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            display: flex;
            min-height: 100vh;
            background-color: #f5f5f5;
        }

        .sidebar {
            width: 200px;
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar .header {
            padding: 0 20px 20px 20px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar .menu {
            margin-top: 20px;
        }

        .sidebar .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .sidebar .menu-item:hover {
            background-color: #34495e;
        }

        .sidebar .menu-item.active {
            background-color: #3498db;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-left: 200px;
        }

        .top-bar {
            background-color: white;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-info img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .panel {
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .panel-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
            font-size: 18px;
            background-color: #f8f9fa;
            border-radius: 5px 5px 0 0;
        }

        .panel-body {
            padding: 20px;
        }

        /* 导航标签 */
        .nav-tabs {
            border-bottom: 2px solid #3498db;
            margin-bottom: 20px;
            list-style: none;
            display: flex;
        }

        .nav-tabs li {
            margin-bottom: -2px;
        }

        .nav-tabs li a {
            display: block;
            border: none;
            border-radius: 0;
            color: #666;
            padding: 10px 20px;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s;
        }

        .nav-tabs li.active a {
            background-color: #3498db;
            color: white;
            border: none;
        }

        .nav-tabs li a:hover {
            background-color: #ecf0f1;
        }

        .nav-tabs li.active a:hover {
            background-color: #3498db;
        }

        /* 按钮样式 */
        .btn {
            display: inline-block;
            padding: 8px 16px;
            margin-right: 10px;
            margin-bottom: 10px;
            border: 1px solid transparent;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .btn-info {
            background-color: #3498db;
            border-color: #3498db;
            color: white;
        }

        .btn-success {
            background-color: #27ae60;
            border-color: #27ae60;
            color: white;
        }

        .btn-primary {
            background-color: #2980b9;
            border-color: #2980b9;
            color: white;
        }

        .btn-danger {
            background-color: #e74c3c;
            border-color: #e74c3c;
            color: white;
        }

        .btn-default {
            background-color: #95a5a6;
            border-color: #95a5a6;
            color: white;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }

        .action-buttons {
            text-align: right;
            margin-bottom: 20px;
        }

        /* 表格样式 */
        .table-responsive {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            vertical-align: middle;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: 2px solid #dee2e6;
        }

        .table-striped tbody tr:nth-child(odd) {
            background-color: #f9f9f9;
        }

        .table-bordered {
            border: 1px solid #ddd;
        }

        .table-bordered th,
        .table-bordered td {
            border: 1px solid #ddd;
        }

        /* 徽章样式 */
        .badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .badge-success {
            background-color: #27ae60;
            color: white;
        }

        .badge-warning {
            background-color: #f39c12;
            color: white;
        }

        /* 可编辑字段 */
        .editable {
            cursor: pointer;
            border-bottom: 1px dashed #ccc;
            padding: 2px 4px;
        }

        .editable:hover {
            background-color: #f8f9fa;
        }

        .url-editable {
            color: #3498db;
            text-decoration: underline;
            cursor: pointer;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1050;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal.fade {
            opacity: 0;
            transition: opacity 0.3s;
        }

        .modal.fade.in {
            opacity: 1;
        }

        .modal-dialog {
            position: relative;
            width: auto;
            margin: 10px;
            max-width: 600px;
            margin: 30px auto;
        }

        .modal-lg {
            max-width: 900px;
        }

        .modal-content {
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 3px 9px rgba(0,0,0,0.5);
            outline: 0;
        }

        .modal-header {
            padding: 15px;
            border-bottom: 1px solid #e5e5e5;
            background-color: #3498db;
            color: white;
            border-radius: 6px 6px 0 0;
        }

        .modal-title {
            margin: 0;
            font-size: 18px;
        }

        .modal-body {
            padding: 15px;
        }

        .modal-footer {
            padding: 15px;
            text-align: right;
            border-top: 1px solid #e5e5e5;
        }

        .close {
            float: right;
            font-size: 21px;
            font-weight: bold;
            line-height: 1;
            color: white;
            opacity: 0.8;
            cursor: pointer;
            background: none;
            border: none;
        }

        .close:hover {
            opacity: 1;
        }

        /* 表单样式 */
        .form-horizontal .form-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .control-label {
            font-weight: bold;
            text-align: right;
            padding-right: 15px;
        }

        .col-sm-3 {
            width: 25%;
        }

        .col-sm-9 {
            width: 75%;
        }

        .form-control {
            display: block;
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            line-height: 1.42857143;
            color: #555;
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 4px;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-control:focus {
            border-color: #3498db;
            outline: 0;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        textarea.form-control {
            resize: vertical;
        }

        /* 按钮组 */
        .btn-group {
            position: relative;
            display: inline-block;
            vertical-align: middle;
        }

        .btn-group .btn {
            position: relative;
            float: left;
            margin-right: 0;
        }

        .btn-group .btn:not(:first-child):not(:last-child) {
            border-radius: 0;
        }

        .btn-group .btn:first-child {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .btn-group .btn:last-child {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .btn-group .btn.active {
            background-color: #3498db;
            border-color: #3498db;
            color: white;
        }

        .btn-outline {
            background-color: transparent;
            color: #3498db;
            border-color: #3498db;
        }

        .btn-outline.active {
            background-color: #3498db;
            color: white;
        }

        /* 配置面板 */
        .config-panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
        }

        .config-panel pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            margin: 0;
            font-family: 'Courier New', monospace;
        }

        /* 开关样式 */
        .switchery {
            background-color: #fff;
            border: 1px solid #dfdfdf;
            border-radius: 20px;
            cursor: pointer;
            display: inline-block;
            height: 30px;
            position: relative;
            vertical-align: middle;
            width: 50px;
            transition: all 0.3s;
        }

        .switchery.checked {
            background-color: #3498db;
            border-color: #3498db;
        }

        .switchery > small {
            background: #fff;
            border-radius: 100%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
            height: 26px;
            position: absolute;
            top: 2px;
            left: 2px;
            width: 26px;
            transition: all 0.3s;
        }

        .switchery.checked > small {
            left: 22px;
        }

        /* 警告框 */
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .alert-warning {
            color: #8a6d3b;
            background-color: #fcf8e3;
            border-color: #faebcc;
        }

        /* 图标样式 */
        .fa {
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
        }

        .fa-mobile:before { content: "📱"; }
        .fa-apple:before { content: "🍎"; }
        .fa-android:before { content: "🤖"; }
        .fa-download:before { content: "⬇️"; }
        .fa-plus:before { content: "➕"; }
        .fa-upload:before { content: "⬆️"; }
        .fa-rocket:before { content: "🚀"; }
        .fa-cog:before { content: "⚙️"; }
        .fa-code:before { content: "💻"; }
        .fa-trash:before { content: "🗑️"; }
        .fa-save:before { content: "💾"; }
        .fa-times:before { content: "❌"; }
        .fa-warning:before { content: "⚠️"; }

        /* 工具提示 */
        .text-center {
            text-align: center;
        }

        /* 隐藏元素 */
        [hidden] {
            display: none !important;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }

            .main-content {
                margin-left: 0;
            }

            .modal-dialog {
                margin: 10px;
            }

            .form-horizontal .form-group {
                flex-direction: column;
                align-items: flex-start;
            }

            .col-sm-3,
            .col-sm-9 {
                width: 100%;
            }

            .control-label {
                text-align: left;
                padding-right: 0;
                margin-bottom: 5px;
            }
        }

        /* 子菜单样式 */
        .submenu {
            margin-top: 5px;
            padding-left: 20px;
        }

        .submenu-item {
            padding: 8px 15px;
            margin: 2px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .submenu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .submenu-item.active {
            background: #007bff;
            color: white;
        }

        .menu-item.active {
            background: rgba(255, 255, 255, 0.1);
            border-left: 3px solid #007bff;
        }

        .menu-item.active > .submenu {
            display: block;
        }

        .submenu {
            display: none;
        }

        .menu-item.active .submenu {
            display: block;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="header">
            <h2>GM管理后台</h2>
        </div>
        <div class="menu">
            <div class="menu-item" onclick="window.location.href='/gm/'">数据概览</div>
            <div class="menu-item" onclick="window.location.href='/gm/country'">国家限制</div>
            <div class="menu-item" onclick="window.location.href='/gm/users'">用户管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/items'">道具管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/mail'">邮件系统</div>
            <div class="menu-item" onclick="window.location.href='/gm/announce'">公告管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/activity'">活动管理</div>
            <div class="menu-item active">
                版本配置
                <div class="submenu">
                    <div class="submenu-item active" onclick="window.location.href='/gm/version'">客户端版本管理</div>
                    <div class="submenu-item" onclick="window.location.href='/gm/huatuo_release'">华佗发版</div>
                </div>
            </div>
            <div class="menu-item" onclick="window.location.href='/gm/logs'">系统日志</div>
        </div>
    </div>

    <div class="main-content">
        <div class="top-bar">
            <div class="page-title">版本管理</div>
            <div class="user-info">
                <span>管理员</span>
                <button onclick="logout()" class="btn btn-sm btn-default">登出</button>
            </div>
        </div>

        <div class="content-area">
            <!-- 版本管理面板 -->
            <div class="panel">
                <div class="panel-header">
                    <span class="fa fa-mobile"></span> 客户端版本配置
                </div>
                <div class="panel-body">
                    <!-- 平台选择标签 -->
                    <ul class="nav-tabs" role="tablist">
                        <li class="active" role="presentation">
                            <a href="#ios" data-platform="ios"><span class="fa fa-apple"></span> iOS</a>
                        </li>
                        <li role="presentation">
                            <a href="#android" data-platform="android"><span class="fa fa-android"></span> Android</a>
                        </li>
                    </ul>

                    <!-- 操作按钮 -->
                    <div class="action-buttons">
<!--                        <button type="button" class="btn btn-info btn-push-version">-->
<!--                            <span class="fa fa-download"></span> 生成配置文件-->
<!--                        </button>-->
                        <button type="button" class="btn btn-success btn-add-version">
                            <span class="fa fa-plus"></span> 添加版本
                        </button>
<!--                        <button type="button" class="btn btn-primary btn-add-s3">-->
<!--                            <span class="fa fa-upload"></span> 保存并上传S3-->
<!--                        </button>-->
<!--                        <button type="button" class="btn btn-primary btn-deploy-online">-->
<!--                            <span class="fa fa-rocket"></span> 发布到线上-->
<!--                        </button>-->
                    </div>

                    <!-- 版本列表表格 -->
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered" id="editableTable">
                            <thead>
                                <tr>
                                    <th>Client Version</th>
                                    <th>Bundle Version R</th>
                                    <th>Bundle Version G</th>
                                    <th>Bundle MD5</th>
                                    <th>Preload MD5</th>
                                    <th>Config MD5</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="datalist">
                                <!-- 版本数据将在这里动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 设置面板 -->
            <div class="panel">
                <div class="panel-header">
                    <span class="fa fa-cog"></span> 版本设置
                </div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                自动更新版本
                            </label>
                            <div class="col-sm-9">
                                <input type="checkbox" id="autoUpdate" name="autoUpdate" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                iOS Bundle URL
                            </label>
                            <div class="col-sm-9">
                                <a class="url url-editable" href="javascript:void(0)" data-type="text" data-pk="ios_bundle_url">https://cdn.example.com/ios/bundle/</a>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                Android Bundle URL
                            </label>
                            <div class="col-sm-9">
                                <a class="url url-editable" href="javascript:void(0)" data-type="text" data-pk="android_bundle_url">https://cdn.example.com/android/bundle/</a>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                Config URL
                            </label>
                            <div class="col-sm-9">
                                <a class="url url-editable" href="javascript:void(0)" data-type="text" data-pk="config_url">https://cdn.example.com/config/</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 配置预览面板 -->
            <div class="panel">
                <div class="panel-header">
                    <span class="fa fa-code"></span> 配置预览
                </div>
                <div class="panel-body">
                    <div class="config-panel">
                        <pre><code id="config-preview" class="json">{
  "auto_update": true,
  "urls": {
    "ios_bundle_url": "https://cdn.example.com/ios/bundle/",
    "android_bundle_url": "https://cdn.example.com/android/bundle/",
    "config_url": "https://cdn.example.com/config/"
  },
  "versions": {
    "ios": [],
    "android": []
  },
  "generated_at": "2024-01-01T00:00:00Z"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加版本模态框 -->
    <div class="modal fade" id="addVersion">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" onclick="closeModal('addVersion')">
                        <span>&times;</span>
                    </button>
                    <h4 class="modal-title"><span class="fa fa-plus"></span> 添加版本</h4>
                </div>
                <form id="myForm" class="form-horizontal">
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="version">
                                版本号:
                            </label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="version" name="version"
                                       placeholder="版本号 (例如: 6.5.0)" autocomplete="off" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                版本类型:
                            </label>
                            <div class="col-sm-9">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline btn-primary active" data-value="ios">
                                        <span class="fa fa-apple"></span> iOS
                                    </button>
                                    <button type="button" class="btn btn-outline btn-primary" data-value="android">
                                        <span class="fa fa-android"></span> Android
                                    </button>
                                </div>
                                <input type="hidden" id="platform-type" name="type" value="ios" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="bundle_version">
                                Bundle 版本:
                            </label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="bundle_version" name="bundle_version"
                                       placeholder="不含后缀 (例如: 6.5.0.233)" autocomplete="off" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="bundle_md5">
                                Bundle MD5:
                            </label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="bundle_md5" name="bundle_md5"
                                       placeholder="Bundle 文件的 MD5 值" autocomplete="off" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="preload_md5">
                                Preload MD5:
                            </label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="preload_md5" name="preload_md5"
                                       placeholder="Preload 文件的 MD5 值" autocomplete="off" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="config_md5">
                                Config MD5:
                            </label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="config_md5" name="config_md5"
                                       placeholder="Config 文件的 MD5 值" autocomplete="off" />
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" onclick="closeModal('addVersion')">
                            <span class="fa fa-times"></span> 取消
                        </button>
                        <button type="button" id="btn-save" class="btn btn-primary">
                            <span class="fa fa-save"></span> 保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 发布确认模态框 -->
    <div class="modal fade" id="deploy_comment">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" onclick="closeModal('deploy_comment')">
                        <span>&times;</span>
                    </button>
                    <h4 class="modal-title"><span class="fa fa-rocket"></span> 发布确认</h4>
                </div>
                <form id="deployForm" class="form-horizontal">
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <span class="fa fa-warning"></span>
                            <strong>注意:</strong> 发布操作将影响所有客户端，请确认配置无误后再进行发布。
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="comment">
                                发布说明:
                            </label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="comment" name="comment" rows="3"
                                          placeholder="请输入本次发布的说明信息..." required></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" onclick="closeModal('deploy_comment')">
                            <span class="fa fa-times"></span> 取消
                        </button>
                        <button type="button" id="btn-deploy" class="btn btn-danger">
                            <span class="fa fa-rocket"></span> 确认发布
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let currentPlatform = 'ios';

        // 简单的模态框控制
        function showModal(id) {
            const modal = document.getElementById(id);
            modal.style.display = 'block';
            modal.classList.add('in');
        }

        function closeModal(id) {
            const modal = document.getElementById(id);
            modal.style.display = 'none';
            modal.classList.remove('in');
        }

        // 简单的开关控制
        function initSwitchery() {
            const checkbox = document.getElementById('autoUpdate');
            const switchery = document.createElement('div');
            switchery.className = 'switchery';

            const small = document.createElement('small');
            switchery.appendChild(small);

            checkbox.style.display = 'none';
            checkbox.parentNode.insertBefore(switchery, checkbox.nextSibling);

            switchery.onclick = function() {
                checkbox.checked = !checkbox.checked;
                switchery.classList.toggle('checked', checkbox.checked);
                updateSettings();
            };

            switchery.classList.toggle('checked', checkbox.checked);
        }

        // 简单的通知系统
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 4px;
                color: white;
                z-index: 9999;
                max-width: 300px;
                word-wrap: break-word;
            `;

            switch(type) {
                case 'success':
                    toast.style.backgroundColor = '#27ae60';
                    break;
                case 'error':
                    toast.style.backgroundColor = '#e74c3c';
                    break;
                case 'warning':
                    toast.style.backgroundColor = '#f39c12';
                    break;
                default:
                    toast.style.backgroundColor = '#3498db';
            }

            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                document.body.removeChild(toast);
            }, 5000);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            const token = localStorage.getItem('gmToken');
            if (!token) {
                window.location.href = '/gm/login';
                return;
            }

            // 获取服务器版本信息
            // fetch('/gm/api/versions?platform=' + currentPlatform, {
            //     headers: {
            //         'X-GM-Token': localStorage.getItem('gmToken')
            //     }
            // })
            // .then(response => response.json())
            // .then(response => {
            //     if (response.code === 200 && response.data) {
            //         // 在页面标题旁显示版本号
            //         const pageTitle = document.querySelector('.page-title');
            //         pageTitle.innerHTML = `版本管理 <small>服务器版本: ${response.data.version || 'unknown'}</small>`;
            //     }
            // })
            // .catch(error => {
            //     console.error('Failed to fetch server version:', error);
            // });

            // 初始化页面
            initializePage();
            bindEvents();
            loadVersionData();
        });

        function initializePage() {
            // 初始化开关
            initSwitchery();

            // 初始化可编辑字段
            initEditableFields();
        }

        function initEditableFields() {
            const editableFields = document.querySelectorAll('.url-editable');
            editableFields.forEach(field => {
                field.addEventListener('click', function() {
                    const currentValue = this.textContent;
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.value = currentValue;
                    input.className = 'form-control';
                    input.style.display = 'inline-block';
                    input.style.width = '300px';

                    this.parentNode.replaceChild(input, this);
                    input.focus();

                    const saveEdit = () => {
                        const newValue = input.value;
                        const newField = document.createElement('a');
                        newField.className = 'url url-editable';
                        newField.href = 'javascript:void(0)';
                        newField.setAttribute('data-type', 'text');
                        newField.setAttribute('data-pk', field.getAttribute('data-pk'));
                        newField.textContent = newValue;

                        input.parentNode.replaceChild(newField, input);
                        initEditableFields(); // 重新绑定事件

                        // 保存到服务器
                        updateUrlSetting(field.getAttribute('data-pk'), newValue);
                    };

                    input.addEventListener('blur', saveEdit);
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            saveEdit();
                        }
                    });
                });
            });
        }

        function bindEvents() {
            // 平台切换
            const navTabs = document.querySelectorAll('.nav-tabs a');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    currentPlatform = this.getAttribute('data-platform');

                    // 更新活动状态
                    document.querySelectorAll('.nav-tabs li').forEach(li => li.classList.remove('active'));
                    this.parentElement.classList.add('active');

                    loadVersionData();
                });
            });

            // 按钮组切换
            const btnGroup = document.querySelectorAll('.btn-group .btn');
            btnGroup.forEach(btn => {
                btn.addEventListener('click', function() {
                    const group = this.parentElement;
                    group.querySelectorAll('.btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    const value = this.getAttribute('data-value');
                    document.getElementById('platform-type').value = value;
                });
            });

            // 添加版本
            document.querySelector('.btn-add-version').addEventListener('click', function() {
                showModal('addVersion');
                // 设置当前平台
                const platformBtns = document.querySelectorAll('.btn-group .btn');
                platformBtns.forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.getAttribute('data-value') === currentPlatform) {
                        btn.classList.add('active');
                    }
                });
                document.getElementById('platform-type').value = currentPlatform;
            });

            // 保存版本
            document.getElementById('btn-save').addEventListener('click', saveVersion);

            // 生成配置文件
            // document.querySelector('.btn-push-version').addEventListener('click', generateConfig);

            // 保存并上传S3
            // document.querySelector('.btn-add-s3').addEventListener('click', saveAndUploadS3);

            // 发布到线上
            // document.querySelector('.btn-deploy-online').addEventListener('click', function() {
            //     showModal('deploy_comment');
            // });

            // 确认发布
            document.getElementById('btn-deploy').addEventListener('click', deployOnline);
        }

        function loadVersionData() {
            fetch('/gm/api/versions?platform=' + currentPlatform, {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(response => {
                if (response.code === 200) {
                    renderVersionTable(response.data || []);
                } else {
                    showToast('Failed to load version data', 'error');
                    renderVersionTable([]);
                }
            })
            .catch(() => {
                showToast('Network error', 'error');
                renderVersionTable([]);
            });
        }

        function renderVersionTable(versions) {
            const tbody = document.getElementById('datalist');
            tbody.innerHTML = '';

            if (versions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center">暂无版本数据</td></tr>';
                return;
            }

            versions.forEach(function(version) {
                const row = document.createElement('tr');
                row.setAttribute('data-client-version', version.client_version);
                row.setAttribute('data-platform', version.platform);
                row.innerHTML = `
                    <td class="editable" data-field="client_version">${version.client_version || ''}</td>
                    <td class="editable" data-field="bundle_version_r">${version.bundle_version_r || ''}</td>
                    <td class="editable" data-field="bundle_version_g">${version.bundle_version_g || ''}</td>
                    <td class="editable" data-field="bundle_md5">${version.bundle_md5 || ''}</td>
                    <td class="editable" data-field="preload_md5">${version.preload_md5 || ''}</td>
                    <td class="editable" data-field="config_md5">${version.config_md5 || ''}</td>
                    <td>
                        <span class="badge ${version.state === 'published' ? 'badge-success' : 'badge-warning'}">
                            ${version.state || 'draft'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-success btn-publish" data-client-version="${version.client_version}" data-platform="${version.platform}">发布</button>
                        <button class="btn btn-sm btn-danger btn-delete" data-client-version="${version.client_version}" data-platform="${version.platform}">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // 绑定行内编辑事件
            bindRowEvents();
        }

        function bindRowEvents() {
            // 删除版本
            document.querySelectorAll('.btn-delete').forEach(btn => {
                btn.addEventListener('click', function() {
                    const clientVersion = this.getAttribute('data-client-version');
                    const platform = this.getAttribute('data-platform');
                    if (confirm('Are you sure you want to delete this version?')) {
                        deleteVersion(clientVersion, platform);
                    }
                });
            });

            // 发布版本
            document.querySelectorAll('.btn-publish').forEach(btn => {
                btn.addEventListener('click', function() {
                    const clientVersion = this.getAttribute('data-client-version');
                    const platform = this.getAttribute('data-platform');
                    publishVersion(clientVersion, platform);
                });
            });

            // 行内编辑
            document.querySelectorAll('.editable').forEach(cell => {
                cell.addEventListener('dblclick', function() {
                    const currentValue = this.textContent;
                    const field = this.getAttribute('data-field');
                    const row = this.closest('tr');
                    const clientVersion = row.getAttribute('data-client-version');
                    const platform = row.getAttribute('data-platform');

                    this.setAttribute('data-original-value', currentValue);

                    const input = document.createElement('input');
                    input.type = 'text';
                    input.className = 'form-control';
                    input.value = currentValue;
                    input.style.width = '100%';

                    this.innerHTML = '';
                    this.appendChild(input);
                    input.focus();

                    const saveEdit = () => {
                        const newValue = input.value;
                        updateVersionField(id, field, newValue, this);
                    };

                    input.addEventListener('blur', saveEdit);
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            saveEdit();
                        }
                    });
                });
            });
        }

        function saveVersion() {
            const formData = {
                version: document.getElementById('version').value,
                type: document.getElementById('platform-type').value,
                bundle_version: document.getElementById('bundle_version').value,
                bundle_md5: document.getElementById('bundle_md5').value,
                preload_md5: document.getElementById('preload_md5').value,
                config_md5: document.getElementById('config_md5').value
            };

            // 验证
            if (!formData.version || !formData.type) {
                showToast('Please fill in all required fields', 'error');
                return;
            }

            fetch('/gm/api/versions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(response => {
                if (response.code === 200) {
                    showToast('Version added successfully', 'success');
                    closeModal('addVersion');
                    document.getElementById('myForm').reset();
                    loadVersionData();
                } else {
                    showToast(response.message || 'Failed to add version', 'error');
                }
            })
            .catch(() => {
                showToast('Network error', 'error');
            });
        }

        function updateVersionField(clientVersion, platform, field, value, element) {
            const data = {};
            data[field] = value;

            fetch(`/gm/api/versions/${clientVersion}/${platform}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(response => {
                if (response.code === 200) {
                    element.textContent = value;
                    showToast('Updated successfully', 'success');
                } else {
                    element.textContent = element.getAttribute('data-original-value');
                    showToast('Failed to update', 'error');
                }
            })
            .catch(() => {
                element.textContent = element.getAttribute('data-original-value');
                showToast('Network error', 'error');
            });
        }

        function updateUrlSetting(key, value) {
            const settings = { urls: {} };
            settings.urls[key] = value;

            fetch('/gm/api/version/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(response => {
                if (response.code === 200) {
                    showToast('URL updated successfully', 'success');
                    updateConfigPreview();
                } else {
                    showToast('Failed to update URL', 'error');
                }
            })
            .catch(() => {
                showToast('Network error', 'error');
            });
        }

        function deleteVersion(clientVersion, platform) {
            fetch(`/gm/api/versions/${clientVersion}/${platform}`, {
                method: 'DELETE',
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(response => {
                if (response.code === 200) {
                    showToast('Version deleted successfully', 'success');
                    loadVersionData();
                } else {
                    showToast('Failed to delete version', 'error');
                }
            })
            .catch(() => {
                showToast('Network error', 'error');
            });
        }

        function publishVersion(clientVersion, platform) {
            updateVersionField(clientVersion, platform, 'state', 'published', null);
        }

        function generateConfig() {
            window.open('/gm/api/version/generate-config', '_blank');
        }

        function saveAndUploadS3() {
            fetch('/gm/api/version/save-upload-s3', {
                method: 'POST',
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(response => {
                if (response.code === 200) {
                    showToast('Config saved and uploaded to S3 successfully', 'success');
                } else {
                    showToast('Failed to save and upload', 'error');
                }
            })
            .catch(() => {
                showToast('Network error', 'error');
            });
        }

        function deployOnline() {
            const comment = document.getElementById('comment').value;

            fetch('/gm/api/version/deploy-online', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify({ comment: comment })
            })
            .then(response => response.json())
            .then(response => {
                if (response.code === 200) {
                    showToast('Deployed to online successfully', 'success');
                    closeModal('deploy_comment');
                    document.getElementById('comment').value = '';
                } else {
                    showToast('Failed to deploy', 'error');
                }
            })
            .catch(() => {
                showToast('Network error', 'error');
            });
        }

        function updateSettings() {
            const settings = {
                auto_update: document.getElementById('autoUpdate').checked,
                urls: {}
            };

            document.querySelectorAll('.url-editable').forEach(field => {
                const key = field.getAttribute('data-pk');
                const value = field.textContent;
                settings.urls[key] = value;
            });

            fetch('/gm/api/version/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(response => {
                if (response.code === 200) {
                    showToast('Settings updated successfully', 'success');
                    updateConfigPreview();
                } else {
                    showToast('Failed to update settings', 'error');
                }
            })
            .catch(() => {
                showToast('Network error', 'error');
            });
        }

        // 登出功能
        function logout() {
            localStorage.removeItem('gmToken');
            window.location.href = '/gm/login';
        }

        // 更新配置预览
        function updateConfigPreview() {
            const settings = {
                auto_update: document.getElementById('autoUpdate').checked,
                urls: {}
            };

            document.querySelectorAll('.url-editable').forEach(field => {
                const key = field.getAttribute('data-pk');
                const value = field.textContent;
                settings.urls[key] = value;
            });

            const config = {
                auto_update: settings.auto_update,
                urls: settings.urls,
                versions: {
                    ios: [],
                    android: []
                },
                generated_at: new Date().toISOString()
            };

            document.getElementById('config-preview').textContent = JSON.stringify(config, null, 2);
        }

        // 页面加载完成后更新配置预览
        setTimeout(updateConfigPreview, 1000);
    </script>
</body>
</html>