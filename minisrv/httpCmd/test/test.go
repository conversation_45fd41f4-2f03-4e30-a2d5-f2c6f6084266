package test

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
	"strconv"
)

func init() {
	http_post.AddPostHandler("test", test)
}
func test(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid")     // 获取查询参数q的值
		heroStr := values.Get("heroId") // 获取查询参数q的值
		if uidStr != "" {
			uid, _ := strconv.ParseInt(uidStr, 10, 64)
			heroId, _ := strconv.ParseInt(heroStr, 10, 64)
			calcHeroPower(ctx, uid, int32(heroId), w)
			return
		}
	})

}

func calcHeroPower(ctx context.Context, uid int64, heroId int32, w http.ResponseWriter) int64 {
	hero, err := model.GetHero(ctx, uid, heroId)
	if err != nil {
		return 0
	}
	if hero == nil {
		return 0
	}
	//等级
	power := int64(0)
	heroLevelLine := cfg_mgr.Cfg.HeroLevelTable.Get(hero.LevelId())
	if heroLevelLine == nil {
		hero.SetPower(ctx, power)
		return power
	}
	fmt.Fprintf(w, "等级 power:%d", heroLevelLine.Power)
	power += int64(heroLevelLine.Power)

	//星级
	heroStarLine := cfg_mgr.Cfg.HeroStarTable.Get(hero.StarId())
	if heroStarLine == nil {
		hero.SetPower(ctx, power)
		return power
	}
	power += int64(heroStarLine.Power)
	fmt.Fprintf(w, "星级 power:%d", heroStarLine.Power)

	//技能

	skillModels := model.GetHeroSkills(ctx, uid, heroId)
	for _, skillModel := range skillModels {
		skillLine := cfg_mgr.Cfg.HeroSkillAwakeTable.Get(skillModel.ConfigId())
		if skillLine == nil {
			continue
		}
		power += int64(skillLine.Power)
		fmt.Fprintf(w, "技能 power:%d", skillLine.Power)
	}

	//基因
	heroGeneLine := cfg_mgr.Cfg.HeroGeneTable.Get(hero.GeneConfigId())
	if heroGeneLine != nil {
		power += int64(heroGeneLine.Power)
		fmt.Fprintf(w, "基因 power:%d", heroGeneLine.Power)
	}

	hero.SetPower(ctx, power)
	fmt.Fprintf(w, "总 power:%d", power)
	return power
}
