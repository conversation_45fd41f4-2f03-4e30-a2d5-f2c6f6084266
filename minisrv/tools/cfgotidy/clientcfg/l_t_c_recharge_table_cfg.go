// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type LTCRechargeTableCfg struct {
	Id       int32        `json:"Id"`       // Id
	StringId string       `json:"StringId"` // StringId
	Score    int32        `json:"Score"`    // 积分数
	Reward   []*RewardKVS `json:"Reward"`   // 奖励
	Image    string       `json:"Image"`    // 背景图
}

func NewLTCRechargeTableCfg() *LTCRechargeTableCfg {
	return &LTCRechargeTableCfg{
		Id:       0,
		StringId: "",
		Score:    0,
		Reward:   []*RewardKVS{},
		Image:    "",
	}
}

func NewMockLTCRechargeTableCfg() *LTCRechargeTableCfg {
	return &LTCRechargeTableCfg{
		Id:       0,
		StringId: "",
		Score:    0,
		Reward:   []*RewardKVS{NewMockRewardKVS()},
		Image:    "",
	}
}

type LTCRechargeTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*LTCRechargeTableCfg
	localIds         map[int32]struct{}
}

func NewLTCRechargeTable(configs *Configs) *LTCRechargeTable {
	return &LTCRechargeTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*LTCRechargeTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *LTCRechargeTable) Get(key int32) *LTCRechargeTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *LTCRechargeTable) GetAll() map[int32]*LTCRechargeTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *LTCRechargeTable) put(key int32, value *LTCRechargeTableCfg, local bool) *LTCRechargeTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *LTCRechargeTable) putFromInheritedTable(key int32, value *LTCRechargeTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[LTCRechargeTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *LTCRechargeTable) Put(key int32, value *LTCRechargeTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[LTCRechargeTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *LTCRechargeTable) PutAll(m map[int32]*LTCRechargeTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *LTCRechargeTable) Range(f func(v *LTCRechargeTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *LTCRechargeTable) Filter(filterFuncs ...func(v *LTCRechargeTableCfg) bool) map[int32]*LTCRechargeTableCfg {
	filtered := map[int32]*LTCRechargeTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *LTCRechargeTable) FilterSlice(filterFuncs ...func(v *LTCRechargeTableCfg) bool) []*LTCRechargeTableCfg {
	filtered := []*LTCRechargeTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *LTCRechargeTable) FilterKeys(filterFuncs ...func(v *LTCRechargeTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *LTCRechargeTable) satisfied(v *LTCRechargeTableCfg, filterFuncs ...func(v *LTCRechargeTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *LTCRechargeTable) setupIndexes() error {
	return nil
}

func (t *LTCRechargeTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *LTCRechargeTableCfg) bindRefs(c *Configs) {
	for _, e := range r.Reward {
		e.bindRefs(c)
	}
}

func (t *LTCRechargeTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[LTCRechargeTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewLTCRechargeTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LTCRechargeTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[LTCRechargeTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// Score
		{
			if record[t.getIndexInCsv("Score")] == "" {
				recordCfg.Score = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Score")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LTCRechargeTable]unmarshal csv record failed, varName=Score, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Score")], err)
					} else {
						return fmt.Errorf("[LTCRechargeTable]unmarshal csv record failed, varName=Score, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Score")], err)
					}
				}
				recordCfg.Score = int32(cfgoInt)
			}
		}
		// Reward
		{
			cfgoMeetNilForRewardOfRecordCfg := false
			// element 0 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("Reward1RewardType")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						var err error
						cfgoElemOfRewardOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward1RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [LTCRechargeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward1RewardType")], err)
							} else {
								return fmt.Errorf("[LTCRechargeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("Reward1RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("Reward1RewardValue")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Reward1RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [LTCRechargeTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward1RewardValue")], err)
							} else {
								return fmt.Errorf("[LTCRechargeTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("Reward1RewardValue")], err)
							}
						}
						cfgoElemOfRewardOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}
			// element 1 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("Reward2RewardType")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						var err error
						cfgoElemOfRewardOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward2RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [LTCRechargeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward2RewardType")], err)
							} else {
								return fmt.Errorf("[LTCRechargeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("Reward2RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("Reward2RewardValue")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Reward2RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [LTCRechargeTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward2RewardValue")], err)
							} else {
								return fmt.Errorf("[LTCRechargeTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("Reward2RewardValue")], err)
							}
						}
						cfgoElemOfRewardOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}
			// element 2 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("Reward3RewardType")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						var err error
						cfgoElemOfRewardOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward3RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [LTCRechargeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward3RewardType")], err)
							} else {
								return fmt.Errorf("[LTCRechargeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("Reward3RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("Reward3RewardValue")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Reward3RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [LTCRechargeTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward3RewardValue")], err)
							} else {
								return fmt.Errorf("[LTCRechargeTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("Reward3RewardValue")], err)
							}
						}
						cfgoElemOfRewardOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}
			// element 3 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("Reward4RewardType")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						var err error
						cfgoElemOfRewardOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward4RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [LTCRechargeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward4RewardType")], err)
							} else {
								return fmt.Errorf("[LTCRechargeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("Reward4RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("Reward4RewardValue")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Reward4RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [LTCRechargeTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward4RewardValue")], err)
							} else {
								return fmt.Errorf("[LTCRechargeTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("Reward4RewardValue")], err)
							}
						}
						cfgoElemOfRewardOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}
			// element 4 of Reward
			if !cfgoMeetNilForRewardOfRecordCfg {
				cfgoMeetNilForRewardOfRecordCfg = true
				var cfgoElemOfRewardOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("Reward5RewardType")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						var err error
						cfgoElemOfRewardOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("Reward5RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [LTCRechargeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward5RewardType")], err)
							} else {
								return fmt.Errorf("[LTCRechargeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("Reward5RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("Reward5RewardValue")] != "" {
						cfgoMeetNilForRewardOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Reward5RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [LTCRechargeTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("Reward5RewardValue")], err)
							} else {
								return fmt.Errorf("[LTCRechargeTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("Reward5RewardValue")], err)
							}
						}
						cfgoElemOfRewardOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardOfRecordCfg {
					recordCfg.Reward = append(recordCfg.Reward, cfgoElemOfRewardOfRecordCfg)
				}
			}

		}
		// Image
		{
			recordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Image")])
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [LTCRechargeTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[LTCRechargeTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *LTCRechargeTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "LTCRechargeTable.csv") && (!strings.HasPrefix(fileName, "LTCRechargeTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for LTCRechargeTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[LTCRechargeTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[LTCRechargeTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[LTCRechargeTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[LTCRechargeTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[LTCRechargeTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[LTCRechargeTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[LTCRechargeTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[LTCRechargeTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [LTCRechargeTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *LTCRechargeTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[LTCRechargeTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [LTCRechargeTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *LTCRechargeTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[LTCRechargeTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *LTCRechargeTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[LTCRechargeTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[LTCRechargeTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
