// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type DailyTasksTableCfg struct {
	Id              int32             `json:"Id"`              // Id
	ReqFunction     int32             `json:"ReqFunction"`     // 前置功能
	ReqFunctionRef  *FunctionTableCfg `json:"-"`               // 前置功能
	TaskType        TaskType          `json:"TaskType"`        // 任务类型
	TaskCounterType TaskCounterType   `json:"TaskCounterType"` // 任务计数类型
	Formula         string            `json:"Formula"`         // 达成条件
	Value           int32             `json:"Value"`           // 值
	GoTo            int32             `json:"GoTo"`            // 跳转
	GoToRef         *GoToTableCfg     `json:"-"`               // 跳转
	Desc            string            `json:"Desc"`            // 任务描述
	Param           string            `json:"Param"`           // 描述参数
	RewardType      []int32           `json:"RewardType"`      // 奖励类型
	RewardTypeRef   []*ItemTableCfg   `json:"-"`               // 奖励类型
	RewardValue     []int32           `json:"RewardValue"`     // 奖励数量
	Score           int32             `json:"Score"`           // 积分数量
}

func NewDailyTasksTableCfg() *DailyTasksTableCfg {
	return &DailyTasksTableCfg{
		Id:              0,
		ReqFunction:     0,
		ReqFunctionRef:  nil,
		TaskType:        TaskType(enumDefaultValue),
		TaskCounterType: TaskCounterType(enumDefaultValue),
		Formula:         "",
		Value:           0,
		GoTo:            0,
		GoToRef:         nil,
		Desc:            "",
		Param:           "",
		RewardType:      []int32{},
		RewardTypeRef:   []*ItemTableCfg{},
		RewardValue:     []int32{},
		Score:           0,
	}
}

func NewMockDailyTasksTableCfg() *DailyTasksTableCfg {
	return &DailyTasksTableCfg{
		Id:              0,
		ReqFunction:     0,
		ReqFunctionRef:  nil,
		TaskType:        TaskType(enumDefaultValue),
		TaskCounterType: TaskCounterType(enumDefaultValue),
		Formula:         "",
		Value:           0,
		GoTo:            0,
		GoToRef:         nil,
		Desc:            "",
		Param:           "",
		RewardType:      []int32{0},
		RewardTypeRef:   []*ItemTableCfg{},
		RewardValue:     []int32{0},
		Score:           0,
	}
}

type DailyTasksTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*DailyTasksTableCfg
	localIds         map[int32]struct{}
}

func NewDailyTasksTable(configs *Configs) *DailyTasksTable {
	return &DailyTasksTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*DailyTasksTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *DailyTasksTable) Get(key int32) *DailyTasksTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *DailyTasksTable) GetAll() map[int32]*DailyTasksTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *DailyTasksTable) put(key int32, value *DailyTasksTableCfg, local bool) *DailyTasksTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *DailyTasksTable) putFromInheritedTable(key int32, value *DailyTasksTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[DailyTasksTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *DailyTasksTable) Put(key int32, value *DailyTasksTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[DailyTasksTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *DailyTasksTable) PutAll(m map[int32]*DailyTasksTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *DailyTasksTable) Range(f func(v *DailyTasksTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *DailyTasksTable) Filter(filterFuncs ...func(v *DailyTasksTableCfg) bool) map[int32]*DailyTasksTableCfg {
	filtered := map[int32]*DailyTasksTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *DailyTasksTable) FilterSlice(filterFuncs ...func(v *DailyTasksTableCfg) bool) []*DailyTasksTableCfg {
	filtered := []*DailyTasksTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *DailyTasksTable) FilterKeys(filterFuncs ...func(v *DailyTasksTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *DailyTasksTable) satisfied(v *DailyTasksTableCfg, filterFuncs ...func(v *DailyTasksTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *DailyTasksTable) setupIndexes() error {
	return nil
}

func (t *DailyTasksTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *DailyTasksTableCfg) bindRefs(c *Configs) {
	r.ReqFunctionRef = c.FunctionTable.Get(r.ReqFunction)
	r.GoToRef = c.GoToTable.Get(r.GoTo)
	for _, e := range r.RewardType {
		cfgoRefRecord := c.ItemTable.Get(e)
		r.RewardTypeRef = append(r.RewardTypeRef, cfgoRefRecord)
	}
}

func (t *DailyTasksTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[DailyTasksTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewDailyTasksTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [DailyTasksTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[DailyTasksTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// ReqFunction
		if record[t.getIndexInCsv("ReqFunction")] == "" {
			recordCfg.ReqFunction = 0
		} else {
			var err error
			recordCfg.ReqFunction, err = configs.FunctionTable.getIdByRef(record[t.getIndexInCsv("ReqFunction")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [DailyTasksTable]unmarshal csv record failed, varName=ReqFunction, type=ref@FunctionTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("ReqFunction")], err)
				} else {
					return fmt.Errorf("[DailyTasksTable]unmarshal csv record failed, varName=ReqFunction, type=ref@FunctionTable, value=%s, err:[%s]", record[t.getIndexInCsv("ReqFunction")], err)
				}
			}
		}
		// TaskType
		{
			if record[t.getIndexInCsv("TaskType")] == "" {
				recordCfg.TaskType = TaskType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseTaskType(record[t.getIndexInCsv("TaskType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [DailyTasksTable]unmarshal csv record failed, varName=TaskType, type=enum@TaskType, value=%s, err:[%s]\n", record[t.getIndexInCsv("TaskType")], err)
					} else {
						return fmt.Errorf("[DailyTasksTable]unmarshal csv record failed, varName=TaskType, type=enum@TaskType, value=%s, err:[%s]", record[t.getIndexInCsv("TaskType")], err)
					}
				}
				recordCfg.TaskType = cfgoEnum
			}
		}
		// TaskCounterType
		{
			if record[t.getIndexInCsv("TaskCounterType")] == "" {
				recordCfg.TaskCounterType = TaskCounterType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseTaskCounterType(record[t.getIndexInCsv("TaskCounterType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [DailyTasksTable]unmarshal csv record failed, varName=TaskCounterType, type=enum@TaskCounterType, value=%s, err:[%s]\n", record[t.getIndexInCsv("TaskCounterType")], err)
					} else {
						return fmt.Errorf("[DailyTasksTable]unmarshal csv record failed, varName=TaskCounterType, type=enum@TaskCounterType, value=%s, err:[%s]", record[t.getIndexInCsv("TaskCounterType")], err)
					}
				}
				recordCfg.TaskCounterType = cfgoEnum
			}
		}
		// Formula
		{
			recordCfg.Formula = strings.TrimSpace(record[t.getIndexInCsv("Formula")])
		}
		// Value
		{
			if record[t.getIndexInCsv("Value")] == "" {
				recordCfg.Value = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Value")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [DailyTasksTable]unmarshal csv record failed, varName=Value, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Value")], err)
					} else {
						return fmt.Errorf("[DailyTasksTable]unmarshal csv record failed, varName=Value, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Value")], err)
					}
				}
				recordCfg.Value = int32(cfgoInt)
			}
		}
		// GoTo
		if record[t.getIndexInCsv("GoTo")] == "" {
			recordCfg.GoTo = 0
		} else {
			var err error
			recordCfg.GoTo, err = configs.GoToTable.getIdByRef(record[t.getIndexInCsv("GoTo")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [DailyTasksTable]unmarshal csv record failed, varName=GoTo, type=ref@GoToTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("GoTo")], err)
				} else {
					return fmt.Errorf("[DailyTasksTable]unmarshal csv record failed, varName=GoTo, type=ref@GoToTable, value=%s, err:[%s]", record[t.getIndexInCsv("GoTo")], err)
				}
			}
		}
		// Desc
		{
			recordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc")])
		}
		// Param
		{
			recordCfg.Param = strings.TrimSpace(record[t.getIndexInCsv("Param")])
		}
		// RewardType
		{
			if record[t.getIndexInCsv("RewardType")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("RewardType")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfRewardType int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfRewardType = 0
					} else {
						var err error
						cfgoElemOfRewardType, err = configs.ItemTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DailyTasksTable]unmarshal record failed, cannot parse ref@ItemTable in vector, varName=RewardType, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[DailyTasksTable]unmarshal record failed, cannot parse ref@ItemTable in vector, varName=RewardType, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.RewardType = append(recordCfg.RewardType, cfgoElemOfRewardType)
				}
			}
		}
		// RewardValue
		{
			if record[t.getIndexInCsv("RewardValue")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("RewardValue")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfRewardValue int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfRewardValue = 0
					} else {
						cfgoInt, err := strconv.Atoi(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DailyTasksTable]unmarshal record failed, cannot parse int in vector, varName=RewardValue, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[DailyTasksTable]unmarshal record failed, cannot parse int in vector, varName=RewardValue, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
						cfgoElemOfRewardValue = int32(cfgoInt)
					}

					recordCfg.RewardValue = append(recordCfg.RewardValue, cfgoElemOfRewardValue)
				}
			}
		}
		// Score
		{
			if record[t.getIndexInCsv("Score")] == "" {
				recordCfg.Score = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Score")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [DailyTasksTable]unmarshal csv record failed, varName=Score, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Score")], err)
					} else {
						return fmt.Errorf("[DailyTasksTable]unmarshal csv record failed, varName=Score, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Score")], err)
					}
				}
				recordCfg.Score = int32(cfgoInt)
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [DailyTasksTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[DailyTasksTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *DailyTasksTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "DailyTasksTable.csv") && (!strings.HasPrefix(fileName, "DailyTasksTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for DailyTasksTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[DailyTasksTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[DailyTasksTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[DailyTasksTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[DailyTasksTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[DailyTasksTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[DailyTasksTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[DailyTasksTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[DailyTasksTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [DailyTasksTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *DailyTasksTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[DailyTasksTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [DailyTasksTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *DailyTasksTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[DailyTasksTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *DailyTasksTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[DailyTasksTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[DailyTasksTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
