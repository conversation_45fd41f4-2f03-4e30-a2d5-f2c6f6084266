// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type LTCRechargeTableCfg struct {
	Id       int32        `json:"Id"`       // Id
	StringId string       `json:"StringId"` // StringId
	Score    int32        `json:"Score"`    // 积分数
	Reward   []*RewardKVS `json:"Reward"`   // 奖励
}

func NewLTCRechargeTableCfg() *LTCRechargeTableCfg {
	return &LTCRechargeTableCfg{
		Id:       0,
		StringId: "",
		Score:    0,
		Reward:   []*RewardKVS{},
	}
}

type LTCRechargeTable struct {
	records  map[int32]*LTCRechargeTableCfg
	localIds map[int32]struct{}
}

func NewLTCRechargeTable() *LTCRechargeTable {
	return &LTCRechargeTable{
		records:  map[int32]*LTCRechargeTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *LTCRechargeTable) Get(key int32) *LTCRechargeTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *LTCRechargeTable) GetAll() map[int32]*LTCRechargeTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *LTCRechargeTable) put(key int32, value *LTCRechargeTableCfg, local bool) *LTCRechargeTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *LTCRechargeTable) Range(f func(v *LTCRechargeTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *LTCRechargeTable) Filter(filterFuncs ...func(v *LTCRechargeTableCfg) bool) map[int32]*LTCRechargeTableCfg {
	filtered := map[int32]*LTCRechargeTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *LTCRechargeTable) FilterSlice(filterFuncs ...func(v *LTCRechargeTableCfg) bool) []*LTCRechargeTableCfg {
	filtered := []*LTCRechargeTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *LTCRechargeTable) FilterKeys(filterFuncs ...func(v *LTCRechargeTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *LTCRechargeTable) satisfied(v *LTCRechargeTableCfg, filterFuncs ...func(v *LTCRechargeTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *LTCRechargeTable) setupIndexes() error {
	return nil
}

func (t *LTCRechargeTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *LTCRechargeTableCfg) bindRefs(c *Configs) {
	for _, e := range r.Reward {
		e.bindRefs(c)
	}
}
