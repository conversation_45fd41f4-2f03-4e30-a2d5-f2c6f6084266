{"1": {"Id": 1, "GemQualityType": 1, "CanReforge": false, "Reforge": {"CostType": 0, "CostValue": 0}}, "2": {"Id": 2, "GemQualityType": 2, "CanReforge": false, "Reforge": {"CostType": 0, "CostValue": 0}}, "3": {"Id": 3, "GemQualityType": 3, "CanReforge": false, "Reforge": {"CostType": 0, "CostValue": 0}}, "4": {"Id": 4, "GemQualityType": 4, "CanReforge": false, "Reforge": {"CostType": 0, "CostValue": 0}}, "5": {"Id": 5, "GemQualityType": 5, "CanReforge": true, "Reforge": {"CostType": 80, "CostValue": 6}}, "6": {"Id": 6, "GemQualityType": 6, "CanReforge": true, "Reforge": {"CostType": 80, "CostValue": 12}}, "7": {"Id": 7, "GemQualityType": 7, "CanReforge": true, "Reforge": {"CostType": 80, "CostValue": 20}}}