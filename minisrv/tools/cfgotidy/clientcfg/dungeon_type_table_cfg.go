// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type DungeonTypeTableCfg struct {
	Id            int32           `json:"Id"`          // Id
	DungeonType   DungeonType     `json:"DungeonType"` // 副本类型
	Name          string          `json:"Name"`        // 名字
	Desc          string          `json:"Desc"`        // 描述
	Buff          []*IDPStr       `json:"Buff"`        // Buff图-文-参
	Image         string          `json:"Image"`       // 背景图
	LevelScene    string          `json:"LevelScene"`  // 美术场景id
	ShowReward    []int32         `json:"ShowReward"`  // 展示奖励
	ShowRewardRef []*ItemTableCfg `json:"-"`           // 展示奖励
}

func NewDungeonTypeTableCfg() *DungeonTypeTableCfg {
	return &DungeonTypeTableCfg{
		Id:            0,
		DungeonType:   DungeonType(enumDefaultValue),
		Name:          "",
		Desc:          "",
		Buff:          []*IDPStr{},
		Image:         "",
		LevelScene:    "",
		ShowReward:    []int32{},
		ShowRewardRef: []*ItemTableCfg{},
	}
}

func NewMockDungeonTypeTableCfg() *DungeonTypeTableCfg {
	return &DungeonTypeTableCfg{
		Id:            0,
		DungeonType:   DungeonType(enumDefaultValue),
		Name:          "",
		Desc:          "",
		Buff:          []*IDPStr{NewMockIDPStr()},
		Image:         "",
		LevelScene:    "",
		ShowReward:    []int32{0},
		ShowRewardRef: []*ItemTableCfg{},
	}
}

type DungeonTypeTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*DungeonTypeTableCfg
	localIds         map[int32]struct{}
}

func NewDungeonTypeTable(configs *Configs) *DungeonTypeTable {
	return &DungeonTypeTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*DungeonTypeTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *DungeonTypeTable) Get(key int32) *DungeonTypeTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *DungeonTypeTable) GetAll() map[int32]*DungeonTypeTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *DungeonTypeTable) put(key int32, value *DungeonTypeTableCfg, local bool) *DungeonTypeTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *DungeonTypeTable) putFromInheritedTable(key int32, value *DungeonTypeTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[DungeonTypeTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *DungeonTypeTable) Put(key int32, value *DungeonTypeTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[DungeonTypeTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *DungeonTypeTable) PutAll(m map[int32]*DungeonTypeTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *DungeonTypeTable) Range(f func(v *DungeonTypeTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *DungeonTypeTable) Filter(filterFuncs ...func(v *DungeonTypeTableCfg) bool) map[int32]*DungeonTypeTableCfg {
	filtered := map[int32]*DungeonTypeTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *DungeonTypeTable) FilterSlice(filterFuncs ...func(v *DungeonTypeTableCfg) bool) []*DungeonTypeTableCfg {
	filtered := []*DungeonTypeTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *DungeonTypeTable) FilterKeys(filterFuncs ...func(v *DungeonTypeTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *DungeonTypeTable) satisfied(v *DungeonTypeTableCfg, filterFuncs ...func(v *DungeonTypeTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *DungeonTypeTable) setupIndexes() error {
	return nil
}

func (t *DungeonTypeTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *DungeonTypeTableCfg) bindRefs(c *Configs) {
	for _, e := range r.Buff {
		e.bindRefs(c)
	}
	for _, e := range r.ShowReward {
		cfgoRefRecord := c.ItemTable.Get(e)
		r.ShowRewardRef = append(r.ShowRewardRef, cfgoRefRecord)
	}
}

func (t *DungeonTypeTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[DungeonTypeTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewDungeonTypeTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [DungeonTypeTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[DungeonTypeTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// DungeonType
		{
			if record[t.getIndexInCsv("DungeonType")] == "" {
				recordCfg.DungeonType = DungeonType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseDungeonType(record[t.getIndexInCsv("DungeonType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [DungeonTypeTable]unmarshal csv record failed, varName=DungeonType, type=enum@DungeonType, value=%s, err:[%s]\n", record[t.getIndexInCsv("DungeonType")], err)
					} else {
						return fmt.Errorf("[DungeonTypeTable]unmarshal csv record failed, varName=DungeonType, type=enum@DungeonType, value=%s, err:[%s]", record[t.getIndexInCsv("DungeonType")], err)
					}
				}
				recordCfg.DungeonType = cfgoEnum
			}
		}
		// Name
		{
			recordCfg.Name = strings.TrimSpace(record[t.getIndexInCsv("Name")])
		}
		// Desc
		{
			recordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc")])
		}
		// Buff
		{
			cfgoMeetNilForBuffOfRecordCfg := false
			// element 0 of Buff
			if !cfgoMeetNilForBuffOfRecordCfg {
				cfgoMeetNilForBuffOfRecordCfg = true
				var cfgoElemOfBuffOfRecordCfg *IDPStr = NewIDPStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Buff1Image")]) != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoElemOfBuffOfRecordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Buff1Image")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Buff1Desc")]) != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoElemOfBuffOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Buff1Desc")])
					}
				}
				{
					if record[t.getIndexInCsv("Buff1Param")] != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("Buff1Param")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DungeonTypeTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBuffOfRecordCfg.Param, value=%s, err:[%s]\n", record[t.getIndexInCsv("Buff1Param")], err)
							} else {
								return fmt.Errorf("[DungeonTypeTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBuffOfRecordCfg.Param, value=%s, err:[%s]", record[t.getIndexInCsv("Buff1Param")], err)
							}
						}
						cfgoElemOfBuffOfRecordCfg.Param = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForBuffOfRecordCfg {
					recordCfg.Buff = append(recordCfg.Buff, cfgoElemOfBuffOfRecordCfg)
				}
			}
			// element 1 of Buff
			if !cfgoMeetNilForBuffOfRecordCfg {
				cfgoMeetNilForBuffOfRecordCfg = true
				var cfgoElemOfBuffOfRecordCfg *IDPStr = NewIDPStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Buff2Image")]) != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoElemOfBuffOfRecordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Buff2Image")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Buff2Desc")]) != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoElemOfBuffOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Buff2Desc")])
					}
				}
				{
					if record[t.getIndexInCsv("Buff2Param")] != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("Buff2Param")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DungeonTypeTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBuffOfRecordCfg.Param, value=%s, err:[%s]\n", record[t.getIndexInCsv("Buff2Param")], err)
							} else {
								return fmt.Errorf("[DungeonTypeTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBuffOfRecordCfg.Param, value=%s, err:[%s]", record[t.getIndexInCsv("Buff2Param")], err)
							}
						}
						cfgoElemOfBuffOfRecordCfg.Param = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForBuffOfRecordCfg {
					recordCfg.Buff = append(recordCfg.Buff, cfgoElemOfBuffOfRecordCfg)
				}
			}
			// element 2 of Buff
			if !cfgoMeetNilForBuffOfRecordCfg {
				cfgoMeetNilForBuffOfRecordCfg = true
				var cfgoElemOfBuffOfRecordCfg *IDPStr = NewIDPStr()
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Buff3Image")]) != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoElemOfBuffOfRecordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Buff3Image")])
					}
				}
				{
					if strings.TrimSpace(record[t.getIndexInCsv("Buff3Desc")]) != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoElemOfBuffOfRecordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Buff3Desc")])
					}
				}
				{
					if record[t.getIndexInCsv("Buff3Param")] != "" {
						cfgoMeetNilForBuffOfRecordCfg = false
						cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("Buff3Param")], 32)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [DungeonTypeTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBuffOfRecordCfg.Param, value=%s, err:[%s]\n", record[t.getIndexInCsv("Buff3Param")], err)
							} else {
								return fmt.Errorf("[DungeonTypeTable]unmarshal record failed, cannot parse float in collection, elemVarName=cfgoElemOfBuffOfRecordCfg.Param, value=%s, err:[%s]", record[t.getIndexInCsv("Buff3Param")], err)
							}
						}
						cfgoElemOfBuffOfRecordCfg.Param = float32(cfgoFloat)
					}
				}

				if !cfgoMeetNilForBuffOfRecordCfg {
					recordCfg.Buff = append(recordCfg.Buff, cfgoElemOfBuffOfRecordCfg)
				}
			}

		}
		// Image
		{
			recordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Image")])
		}
		// LevelScene
		{
			recordCfg.LevelScene = strings.TrimSpace(record[t.getIndexInCsv("LevelScene")])
		}
		// ShowReward
		{
			cfgoMeetNilForShowRewardOfRecordCfg := false
			// element 0 of ShowReward
			if !cfgoMeetNilForShowRewardOfRecordCfg {
				cfgoMeetNilForShowRewardOfRecordCfg = true
				var cfgoElemOfShowRewardOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("ShowReward1")] != "" {
					cfgoMeetNilForShowRewardOfRecordCfg = false
					var err error
					cfgoElemOfShowRewardOfRecordCfg, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("ShowReward1")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [DungeonTypeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfShowRewardOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("ShowReward1")], err)
						} else {
							return fmt.Errorf("[DungeonTypeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfShowRewardOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("ShowReward1")], err)
						}
					}
				}

				if !cfgoMeetNilForShowRewardOfRecordCfg {
					recordCfg.ShowReward = append(recordCfg.ShowReward, cfgoElemOfShowRewardOfRecordCfg)
				}
			}
			// element 1 of ShowReward
			if !cfgoMeetNilForShowRewardOfRecordCfg {
				cfgoMeetNilForShowRewardOfRecordCfg = true
				var cfgoElemOfShowRewardOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("ShowReward2")] != "" {
					cfgoMeetNilForShowRewardOfRecordCfg = false
					var err error
					cfgoElemOfShowRewardOfRecordCfg, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("ShowReward2")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [DungeonTypeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfShowRewardOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("ShowReward2")], err)
						} else {
							return fmt.Errorf("[DungeonTypeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfShowRewardOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("ShowReward2")], err)
						}
					}
				}

				if !cfgoMeetNilForShowRewardOfRecordCfg {
					recordCfg.ShowReward = append(recordCfg.ShowReward, cfgoElemOfShowRewardOfRecordCfg)
				}
			}
			// element 2 of ShowReward
			if !cfgoMeetNilForShowRewardOfRecordCfg {
				cfgoMeetNilForShowRewardOfRecordCfg = true
				var cfgoElemOfShowRewardOfRecordCfg int32 = 0
				if record[t.getIndexInCsv("ShowReward3")] != "" {
					cfgoMeetNilForShowRewardOfRecordCfg = false
					var err error
					cfgoElemOfShowRewardOfRecordCfg, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("ShowReward3")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [DungeonTypeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfShowRewardOfRecordCfg, value=%s, err:[%s]\n", record[t.getIndexInCsv("ShowReward3")], err)
						} else {
							return fmt.Errorf("[DungeonTypeTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfShowRewardOfRecordCfg, value=%s, err:[%s]", record[t.getIndexInCsv("ShowReward3")], err)
						}
					}
				}

				if !cfgoMeetNilForShowRewardOfRecordCfg {
					recordCfg.ShowReward = append(recordCfg.ShowReward, cfgoElemOfShowRewardOfRecordCfg)
				}
			}

		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [DungeonTypeTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[DungeonTypeTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *DungeonTypeTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "DungeonTypeTable.csv") && (!strings.HasPrefix(fileName, "DungeonTypeTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for DungeonTypeTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[DungeonTypeTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[DungeonTypeTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[DungeonTypeTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[DungeonTypeTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[DungeonTypeTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[DungeonTypeTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[DungeonTypeTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[DungeonTypeTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [DungeonTypeTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *DungeonTypeTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[DungeonTypeTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [DungeonTypeTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *DungeonTypeTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[DungeonTypeTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *DungeonTypeTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[DungeonTypeTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[DungeonTypeTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
