package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/alliance"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 变更阶级头衔
func (_ KnightsHandlers) EditAllianceStepNameHandler(ctx *wctx.Context, req *wrpc.EditAllianceStepNameRequest) (*wrpc.EditAllianceStepNameReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		err = alliance.EditAllianceStepName(ctx, req.UID, req.AllianceId, req.StepName)
		rawResp = &wrpc.EditAllianceStepNameReply{Result: err == nil}
	})
	return rawResp.(*wrpc.EditAllianceStepNameReply), err
}
