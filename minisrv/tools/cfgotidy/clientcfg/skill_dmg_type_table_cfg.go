// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type SkillDmgTypeTableCfg struct {
	Id           int32        `json:"Id"`           // Id
	SkillDmgType SkillDmgType `json:"SkillDmgType"` // 技能伤害类型
	Name         string       `json:"Name"`         // 技能伤害类型名
	NameColor    string       `json:"NameColor"`    // 伤害类型名颜色色值
	Desc         string       `json:"Desc"`         // 技能伤害类型描述
	Image        string       `json:"Image"`        // 技能伤害类型图片
	WeakDesc     string       `json:"WeakDesc"`     // 弱点元素描述
	WeakImage    string       `json:"WeakImage"`    // 弱点元素图片
	ResistDesc   string       `json:"ResistDesc"`   // 抗性元素描述
	ResistImage  string       `json:"ResistImage"`  // 抗性元素图片
	ImmuneDesc   string       `json:"ImmuneDesc"`   // 免疫元素描述
	ImmuneImage  string       `json:"ImmuneImage"`  // 免疫元素图片
}

func NewSkillDmgTypeTableCfg() *SkillDmgTypeTableCfg {
	return &SkillDmgTypeTableCfg{
		Id:           0,
		SkillDmgType: SkillDmgType(enumDefaultValue),
		Name:         "",
		NameColor:    "",
		Desc:         "",
		Image:        "",
		WeakDesc:     "",
		WeakImage:    "",
		ResistDesc:   "",
		ResistImage:  "",
		ImmuneDesc:   "",
		ImmuneImage:  "",
	}
}

func NewMockSkillDmgTypeTableCfg() *SkillDmgTypeTableCfg {
	return &SkillDmgTypeTableCfg{
		Id:           0,
		SkillDmgType: SkillDmgType(enumDefaultValue),
		Name:         "",
		NameColor:    "",
		Desc:         "",
		Image:        "",
		WeakDesc:     "",
		WeakImage:    "",
		ResistDesc:   "",
		ResistImage:  "",
		ImmuneDesc:   "",
		ImmuneImage:  "",
	}
}

type SkillDmgTypeTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*SkillDmgTypeTableCfg
	localIds         map[int32]struct{}
}

func NewSkillDmgTypeTable(configs *Configs) *SkillDmgTypeTable {
	return &SkillDmgTypeTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*SkillDmgTypeTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *SkillDmgTypeTable) Get(key int32) *SkillDmgTypeTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *SkillDmgTypeTable) GetAll() map[int32]*SkillDmgTypeTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *SkillDmgTypeTable) put(key int32, value *SkillDmgTypeTableCfg, local bool) *SkillDmgTypeTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *SkillDmgTypeTable) putFromInheritedTable(key int32, value *SkillDmgTypeTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[SkillDmgTypeTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *SkillDmgTypeTable) Put(key int32, value *SkillDmgTypeTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[SkillDmgTypeTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *SkillDmgTypeTable) PutAll(m map[int32]*SkillDmgTypeTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *SkillDmgTypeTable) Range(f func(v *SkillDmgTypeTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *SkillDmgTypeTable) Filter(filterFuncs ...func(v *SkillDmgTypeTableCfg) bool) map[int32]*SkillDmgTypeTableCfg {
	filtered := map[int32]*SkillDmgTypeTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *SkillDmgTypeTable) FilterSlice(filterFuncs ...func(v *SkillDmgTypeTableCfg) bool) []*SkillDmgTypeTableCfg {
	filtered := []*SkillDmgTypeTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *SkillDmgTypeTable) FilterKeys(filterFuncs ...func(v *SkillDmgTypeTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *SkillDmgTypeTable) satisfied(v *SkillDmgTypeTableCfg, filterFuncs ...func(v *SkillDmgTypeTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *SkillDmgTypeTable) setupIndexes() error {
	return nil
}

func (t *SkillDmgTypeTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *SkillDmgTypeTableCfg) bindRefs(c *Configs) {
}

func (t *SkillDmgTypeTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[SkillDmgTypeTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewSkillDmgTypeTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [SkillDmgTypeTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[SkillDmgTypeTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// SkillDmgType
		{
			if record[t.getIndexInCsv("SkillDmgType")] == "" {
				recordCfg.SkillDmgType = SkillDmgType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseSkillDmgType(record[t.getIndexInCsv("SkillDmgType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [SkillDmgTypeTable]unmarshal csv record failed, varName=SkillDmgType, type=enum@SkillDmgType, value=%s, err:[%s]\n", record[t.getIndexInCsv("SkillDmgType")], err)
					} else {
						return fmt.Errorf("[SkillDmgTypeTable]unmarshal csv record failed, varName=SkillDmgType, type=enum@SkillDmgType, value=%s, err:[%s]", record[t.getIndexInCsv("SkillDmgType")], err)
					}
				}
				recordCfg.SkillDmgType = cfgoEnum
			}
		}
		// Name
		{
			recordCfg.Name = strings.TrimSpace(record[t.getIndexInCsv("Name")])
		}
		// NameColor
		{
			recordCfg.NameColor = strings.TrimSpace(record[t.getIndexInCsv("NameColor")])
		}
		// Desc
		{
			recordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc")])
		}
		// Image
		{
			recordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Image")])
		}
		// WeakDesc
		{
			recordCfg.WeakDesc = strings.TrimSpace(record[t.getIndexInCsv("WeakDesc")])
		}
		// WeakImage
		{
			recordCfg.WeakImage = strings.TrimSpace(record[t.getIndexInCsv("WeakImage")])
		}
		// ResistDesc
		{
			recordCfg.ResistDesc = strings.TrimSpace(record[t.getIndexInCsv("ResistDesc")])
		}
		// ResistImage
		{
			recordCfg.ResistImage = strings.TrimSpace(record[t.getIndexInCsv("ResistImage")])
		}
		// ImmuneDesc
		{
			recordCfg.ImmuneDesc = strings.TrimSpace(record[t.getIndexInCsv("ImmuneDesc")])
		}
		// ImmuneImage
		{
			recordCfg.ImmuneImage = strings.TrimSpace(record[t.getIndexInCsv("ImmuneImage")])
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [SkillDmgTypeTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[SkillDmgTypeTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *SkillDmgTypeTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "SkillDmgTypeTable.csv") && (!strings.HasPrefix(fileName, "SkillDmgTypeTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for SkillDmgTypeTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[SkillDmgTypeTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[SkillDmgTypeTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[SkillDmgTypeTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[SkillDmgTypeTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[SkillDmgTypeTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[SkillDmgTypeTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[SkillDmgTypeTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[SkillDmgTypeTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [SkillDmgTypeTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *SkillDmgTypeTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[SkillDmgTypeTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [SkillDmgTypeTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *SkillDmgTypeTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[SkillDmgTypeTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *SkillDmgTypeTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[SkillDmgTypeTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[SkillDmgTypeTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
