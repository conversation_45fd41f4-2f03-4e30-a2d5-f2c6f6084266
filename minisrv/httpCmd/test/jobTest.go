package test

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/enum"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/benefit"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
	"strconv"
)

func init() {
	http_post.AddPostHandler("jobTest", jobTest)
}

func jobTest(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid") // 获取查询参数q的值
		if uidStr == "" {
			fmt.Fprintf(w, "need uid")
			return
		}
		uid, _ := strconv.ParseInt(uidStr, 10, 64)
		benefit.ChangeTimeBenefitByTrace(ctx, uid, 1, enum.BuildingTrace, 100, 10)
		fmt.Fprintf(w, "success")
	})

}
