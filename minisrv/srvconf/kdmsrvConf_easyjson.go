// Code generated by <PERSON>j<PERSON> for marshaling/unmarshaling. DO NOT EDIT.

package srvconf

import (
	json "encoding/json"
	easyjson "github.com/mailru/easyjson"
	jlexer "github.com/mailru/easyjson/jlexer"
	jwriter "github.com/mailru/easyjson/jwriter"
)

// suppress unused package warning
var (
	_ *json.RawMessage
	_ *jlexer.Lexer
	_ *jwriter.Writer
	_ easyjson.Marshaler
)

func easyjsonFe453bbbDecodeBitbucketOrgKingsgroupGogKnightsMinisrvSrvconf(in *jlexer.Lexer, out *kdmsrvConf) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "BackupGameServerUrl":
			out.BackupGameServerURL = string(in.String())
		case "CallBackJob":
			out.CallBackJob = bool(in.Bool())
		case "EtcdAddrs":
			if in.IsNull() {
				in.Skip()
				out.EtcdAddrs = nil
			} else {
				in.Delim('[')
				if out.EtcdAddrs == nil {
					if !in.IsDelim(']') {
						out.EtcdAddrs = make([]string, 0, 4)
					} else {
						out.EtcdAddrs = []string{}
					}
				} else {
					out.EtcdAddrs = (out.EtcdAddrs)[:0]
				}
				for !in.IsDelim(']') {
					var v1 string
					v1 = string(in.String())
					out.EtcdAddrs = append(out.EtcdAddrs, v1)
					in.WantComma()
				}
				in.Delim(']')
			}
		case "EtcdClusterDir":
			out.EtcdClusterDir = string(in.String())
		case "GameServerMaxRetry":
			out.GameServerMaxRetry = int32(in.Int32())
		case "GameServerUrl":
			out.GameServerURL = string(in.String())
		case "KdmSrvTTL":
			out.KdmSrvTTL = int32(in.Int32())
		case "MaxOngoingRequests":
			out.MaxOngoingRequests = int32(in.Int32())
		case "NoticeUrl":
			out.NoticeURL = string(in.String())
		case "NumberOfReportGroups":
			out.NumberOfReportGroups = int32(in.Int32())
		case "ServerSecret":
			out.ServerSecret = string(in.String())
		case "PaymentServerSecret":
			out.PaymentServerSecret = string(in.String())
		case "ORM":
			if in.IsNull() {
				in.Skip()
				out.Orm = nil
			} else {
				if out.Orm == nil {
					out.Orm = new(struct {
						MaxPoolSize int32 `json:"MaxPoolSize"`
						RedoLog     *struct {
							Dir          string `json:"Dir"`
							Enabled      bool   `json:"Enabled"`
							SyncInterval string `json:"SyncInterval"`
						} `json:"RedoLog"`
						Suffix map[string]string `json:"Suffix"`
						Tables map[string]*struct {
							Concurrence   int32  `json:"Concurrence"`
							FlushInterval string `json:"FlushInterval"`
							TimeOut       string `json:"TimeOut"`
						} `json:"Tables"`
						TicketsCollection string `json:"TicketsCollection"`
						TicketsDB         string `json:"TicketsDB"`
						URI               string `json:"Uri"`
					})
				}
				easyjsonFe453bbbDecode(in, out.Orm)
			}
		case "PathFindingMap":
			out.PathFindingMap = string(in.String())
		case "S3Config":
			if in.IsNull() {
				in.Skip()
				out.S3Config = nil
			} else {
				if out.S3Config == nil {
					out.S3Config = new(struct {
						AwsAccessKeyID     string `json:"Aws_access_key_id"`
						AwsSecretAccessKey string `json:"Aws_secret_access_key"`
						Bucket             string `json:"Bucket"`
						Online             bool   `json:"Online"`
						Prefix             string `json:"Prefix"`
						Region             string `json:"Region"`
					})
				}
				easyjsonFe453bbbDecode1(in, out.S3Config)
			}
		case "SessionTokenExpireInSecs":
			out.SessionTokenExpireInSecs = int32(in.Int32())
		case "StargateAgent":
			if in.IsNull() {
				in.Skip()
				out.StargateAgent = nil
			} else {
				if out.StargateAgent == nil {
					out.StargateAgent = new(struct {
						Category  int32  `json:"Category"`
						ProxyName string `json:"ProxyName"`
					})
				}
				easyjsonFe453bbbDecode2(in, out.StargateAgent)
			}
		case "AsyncPushConfig":
			if in.IsNull() {
				in.Skip()
				out.AsyncPushConfig = nil
			} else {
				if out.AsyncPushConfig == nil {
					out.AsyncPushConfig = new(struct {
						AsyncPushEnabled     bool   `json:"AsyncPushEnabled"`
						AsyncPushInterval    string `json:"AsyncPushInterval"`
						AsyncPushConcurrence uint32 `json:"AsyncPushConcurrence"`
					})
				}
				easyjsonFe453bbbDecode3(in, out.AsyncPushConfig)
			}
		case "BiConfig":
			if in.IsNull() {
				in.Skip()
				out.BiConfig = nil
			} else {
				if out.BiConfig == nil {
					out.BiConfig = new(struct {
						BI_PATH string `json:"BI_PATH"`
					})
				}
				easyjsonFe453bbbDecode4(in, out.BiConfig)
			}
		case "PaymentConf":
			if in.IsNull() {
				in.Skip()
				out.PaymentConf = nil
			} else {
				if out.PaymentConf == nil {
					out.PaymentConf = new(struct {
						PaymentServer string `json:"PaymentServer"`
						ChannelInfo   map[string]*struct {
							AppId           string `json:"AppId"`
							PaymentPlatform string `json:"PaymentPlatform"`
						}
					})
				}
				easyjsonFe453bbbDecode5(in, out.PaymentConf)
			}
		case "RankRedisAddr":
			out.RankRedisAddr = string(in.String())
		case "RootDir":
			out.RootDir = string(in.String())
		case "IPCountryPath":
			out.IPCountryPath = string(in.String())
		case "ClientConfig":
			if in.IsNull() {
				in.Skip()
				out.ClientConfig = nil
			} else {
				if out.ClientConfig == nil {
					out.ClientConfig = new(struct {
						Cdn string `json:"cdn"`
					})
				}
				easyjsonFe453bbbDecode6(in, out.ClientConfig)
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonFe453bbbEncodeBitbucketOrgKingsgroupGogKnightsMinisrvSrvconf(out *jwriter.Writer, in kdmsrvConf) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"BackupGameServerUrl\":"
		out.RawString(prefix[1:])
		out.String(string(in.BackupGameServerURL))
	}
	{
		const prefix string = ",\"CallBackJob\":"
		out.RawString(prefix)
		out.Bool(bool(in.CallBackJob))
	}
	{
		const prefix string = ",\"EtcdAddrs\":"
		out.RawString(prefix)
		if in.EtcdAddrs == nil && (out.Flags&jwriter.NilSliceAsEmpty) == 0 {
			out.RawString("null")
		} else {
			out.RawByte('[')
			for v2, v3 := range in.EtcdAddrs {
				if v2 > 0 {
					out.RawByte(',')
				}
				out.String(string(v3))
			}
			out.RawByte(']')
		}
	}
	{
		const prefix string = ",\"EtcdClusterDir\":"
		out.RawString(prefix)
		out.String(string(in.EtcdClusterDir))
	}
	{
		const prefix string = ",\"GameServerMaxRetry\":"
		out.RawString(prefix)
		out.Int32(int32(in.GameServerMaxRetry))
	}
	{
		const prefix string = ",\"GameServerUrl\":"
		out.RawString(prefix)
		out.String(string(in.GameServerURL))
	}
	{
		const prefix string = ",\"KdmSrvTTL\":"
		out.RawString(prefix)
		out.Int32(int32(in.KdmSrvTTL))
	}
	{
		const prefix string = ",\"MaxOngoingRequests\":"
		out.RawString(prefix)
		out.Int32(int32(in.MaxOngoingRequests))
	}
	{
		const prefix string = ",\"NoticeUrl\":"
		out.RawString(prefix)
		out.String(string(in.NoticeURL))
	}
	{
		const prefix string = ",\"NumberOfReportGroups\":"
		out.RawString(prefix)
		out.Int32(int32(in.NumberOfReportGroups))
	}
	{
		const prefix string = ",\"ServerSecret\":"
		out.RawString(prefix)
		out.String(string(in.ServerSecret))
	}
	{
		const prefix string = ",\"PaymentServerSecret\":"
		out.RawString(prefix)
		out.String(string(in.PaymentServerSecret))
	}
	{
		const prefix string = ",\"ORM\":"
		out.RawString(prefix)
		if in.Orm == nil {
			out.RawString("null")
		} else {
			easyjsonFe453bbbEncode(out, *in.Orm)
		}
	}
	{
		const prefix string = ",\"PathFindingMap\":"
		out.RawString(prefix)
		out.String(string(in.PathFindingMap))
	}
	{
		const prefix string = ",\"S3Config\":"
		out.RawString(prefix)
		if in.S3Config == nil {
			out.RawString("null")
		} else {
			easyjsonFe453bbbEncode1(out, *in.S3Config)
		}
	}
	{
		const prefix string = ",\"SessionTokenExpireInSecs\":"
		out.RawString(prefix)
		out.Int32(int32(in.SessionTokenExpireInSecs))
	}
	{
		const prefix string = ",\"StargateAgent\":"
		out.RawString(prefix)
		if in.StargateAgent == nil {
			out.RawString("null")
		} else {
			easyjsonFe453bbbEncode2(out, *in.StargateAgent)
		}
	}
	{
		const prefix string = ",\"AsyncPushConfig\":"
		out.RawString(prefix)
		if in.AsyncPushConfig == nil {
			out.RawString("null")
		} else {
			easyjsonFe453bbbEncode3(out, *in.AsyncPushConfig)
		}
	}
	{
		const prefix string = ",\"BiConfig\":"
		out.RawString(prefix)
		if in.BiConfig == nil {
			out.RawString("null")
		} else {
			easyjsonFe453bbbEncode4(out, *in.BiConfig)
		}
	}
	{
		const prefix string = ",\"PaymentConf\":"
		out.RawString(prefix)
		if in.PaymentConf == nil {
			out.RawString("null")
		} else {
			easyjsonFe453bbbEncode5(out, *in.PaymentConf)
		}
	}
	{
		const prefix string = ",\"RankRedisAddr\":"
		out.RawString(prefix)
		out.String(string(in.RankRedisAddr))
	}
	{
		const prefix string = ",\"RootDir\":"
		out.RawString(prefix)
		out.String(string(in.RootDir))
	}
	{
		const prefix string = ",\"IPCountryPath\":"
		out.RawString(prefix)
		out.String(string(in.IPCountryPath))
	}
	{
		const prefix string = ",\"ClientConfig\":"
		out.RawString(prefix)
		if in.ClientConfig == nil {
			out.RawString("null")
		} else {
			easyjsonFe453bbbEncode6(out, *in.ClientConfig)
		}
	}
	out.RawByte('}')
}

// MarshalJSON supports json.Marshaler interface
func (v kdmsrvConf) MarshalJSON() ([]byte, error) {
	w := jwriter.Writer{}
	easyjsonFe453bbbEncodeBitbucketOrgKingsgroupGogKnightsMinisrvSrvconf(&w, v)
	return w.Buffer.BuildBytes(), w.Error
}

// MarshalEasyJSON supports easyjson.Marshaler interface
func (v kdmsrvConf) MarshalEasyJSON(w *jwriter.Writer) {
	easyjsonFe453bbbEncodeBitbucketOrgKingsgroupGogKnightsMinisrvSrvconf(w, v)
}

// UnmarshalJSON supports json.Unmarshaler interface
func (v *kdmsrvConf) UnmarshalJSON(data []byte) error {
	r := jlexer.Lexer{Data: data}
	easyjsonFe453bbbDecodeBitbucketOrgKingsgroupGogKnightsMinisrvSrvconf(&r, v)
	return r.Error()
}

// UnmarshalEasyJSON supports easyjson.Unmarshaler interface
func (v *kdmsrvConf) UnmarshalEasyJSON(l *jlexer.Lexer) {
	easyjsonFe453bbbDecodeBitbucketOrgKingsgroupGogKnightsMinisrvSrvconf(l, v)
}
func easyjsonFe453bbbDecode6(in *jlexer.Lexer, out *struct {
	Cdn string `json:"cdn"`
}) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "cdn":
			out.Cdn = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonFe453bbbEncode6(out *jwriter.Writer, in struct {
	Cdn string `json:"cdn"`
}) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"cdn\":"
		out.RawString(prefix[1:])
		out.String(string(in.Cdn))
	}
	out.RawByte('}')
}
func easyjsonFe453bbbDecode5(in *jlexer.Lexer, out *struct {
	PaymentServer string `json:"PaymentServer"`
	ChannelInfo   map[string]*struct {
		AppId           string `json:"AppId"`
		PaymentPlatform string `json:"PaymentPlatform"`
	}
}) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "PaymentServer":
			out.PaymentServer = string(in.String())
		case "ChannelInfo":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.ChannelInfo = make(map[string]*struct {
					AppId           string `json:"AppId"`
					PaymentPlatform string `json:"PaymentPlatform"`
				})
				for !in.IsDelim('}') {
					key := string(in.String())
					in.WantColon()
					var v4 *struct {
						AppId           string `json:"AppId"`
						PaymentPlatform string `json:"PaymentPlatform"`
					}
					if in.IsNull() {
						in.Skip()
						v4 = nil
					} else {
						if v4 == nil {
							v4 = new(struct {
								AppId           string `json:"AppId"`
								PaymentPlatform string `json:"PaymentPlatform"`
							})
						}
						easyjsonFe453bbbDecode7(in, v4)
					}
					(out.ChannelInfo)[key] = v4
					in.WantComma()
				}
				in.Delim('}')
			}
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonFe453bbbEncode5(out *jwriter.Writer, in struct {
	PaymentServer string `json:"PaymentServer"`
	ChannelInfo   map[string]*struct {
		AppId           string `json:"AppId"`
		PaymentPlatform string `json:"PaymentPlatform"`
	}
}) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"PaymentServer\":"
		out.RawString(prefix[1:])
		out.String(string(in.PaymentServer))
	}
	{
		const prefix string = ",\"ChannelInfo\":"
		out.RawString(prefix)
		if in.ChannelInfo == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v5First := true
			for v5Name, v5Value := range in.ChannelInfo {
				if v5First {
					v5First = false
				} else {
					out.RawByte(',')
				}
				out.String(string(v5Name))
				out.RawByte(':')
				if v5Value == nil {
					out.RawString("null")
				} else {
					easyjsonFe453bbbEncode7(out, *v5Value)
				}
			}
			out.RawByte('}')
		}
	}
	out.RawByte('}')
}
func easyjsonFe453bbbDecode7(in *jlexer.Lexer, out *struct {
	AppId           string `json:"AppId"`
	PaymentPlatform string `json:"PaymentPlatform"`
}) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "AppId":
			out.AppId = string(in.String())
		case "PaymentPlatform":
			out.PaymentPlatform = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonFe453bbbEncode7(out *jwriter.Writer, in struct {
	AppId           string `json:"AppId"`
	PaymentPlatform string `json:"PaymentPlatform"`
}) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"AppId\":"
		out.RawString(prefix[1:])
		out.String(string(in.AppId))
	}
	{
		const prefix string = ",\"PaymentPlatform\":"
		out.RawString(prefix)
		out.String(string(in.PaymentPlatform))
	}
	out.RawByte('}')
}
func easyjsonFe453bbbDecode4(in *jlexer.Lexer, out *struct {
	BI_PATH string `json:"BI_PATH"`
}) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "BI_PATH":
			out.BI_PATH = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonFe453bbbEncode4(out *jwriter.Writer, in struct {
	BI_PATH string `json:"BI_PATH"`
}) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"BI_PATH\":"
		out.RawString(prefix[1:])
		out.String(string(in.BI_PATH))
	}
	out.RawByte('}')
}
func easyjsonFe453bbbDecode3(in *jlexer.Lexer, out *struct {
	AsyncPushEnabled     bool   `json:"AsyncPushEnabled"`
	AsyncPushInterval    string `json:"AsyncPushInterval"`
	AsyncPushConcurrence uint32 `json:"AsyncPushConcurrence"`
}) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "AsyncPushEnabled":
			out.AsyncPushEnabled = bool(in.Bool())
		case "AsyncPushInterval":
			out.AsyncPushInterval = string(in.String())
		case "AsyncPushConcurrence":
			out.AsyncPushConcurrence = uint32(in.Uint32())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonFe453bbbEncode3(out *jwriter.Writer, in struct {
	AsyncPushEnabled     bool   `json:"AsyncPushEnabled"`
	AsyncPushInterval    string `json:"AsyncPushInterval"`
	AsyncPushConcurrence uint32 `json:"AsyncPushConcurrence"`
}) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"AsyncPushEnabled\":"
		out.RawString(prefix[1:])
		out.Bool(bool(in.AsyncPushEnabled))
	}
	{
		const prefix string = ",\"AsyncPushInterval\":"
		out.RawString(prefix)
		out.String(string(in.AsyncPushInterval))
	}
	{
		const prefix string = ",\"AsyncPushConcurrence\":"
		out.RawString(prefix)
		out.Uint32(uint32(in.AsyncPushConcurrence))
	}
	out.RawByte('}')
}
func easyjsonFe453bbbDecode2(in *jlexer.Lexer, out *struct {
	Category  int32  `json:"Category"`
	ProxyName string `json:"ProxyName"`
}) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "Category":
			out.Category = int32(in.Int32())
		case "ProxyName":
			out.ProxyName = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonFe453bbbEncode2(out *jwriter.Writer, in struct {
	Category  int32  `json:"Category"`
	ProxyName string `json:"ProxyName"`
}) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"Category\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.Category))
	}
	{
		const prefix string = ",\"ProxyName\":"
		out.RawString(prefix)
		out.String(string(in.ProxyName))
	}
	out.RawByte('}')
}
func easyjsonFe453bbbDecode1(in *jlexer.Lexer, out *struct {
	AwsAccessKeyID     string `json:"Aws_access_key_id"`
	AwsSecretAccessKey string `json:"Aws_secret_access_key"`
	Bucket             string `json:"Bucket"`
	Online             bool   `json:"Online"`
	Prefix             string `json:"Prefix"`
	Region             string `json:"Region"`
}) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "Aws_access_key_id":
			out.AwsAccessKeyID = string(in.String())
		case "Aws_secret_access_key":
			out.AwsSecretAccessKey = string(in.String())
		case "Bucket":
			out.Bucket = string(in.String())
		case "Online":
			out.Online = bool(in.Bool())
		case "Prefix":
			out.Prefix = string(in.String())
		case "Region":
			out.Region = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonFe453bbbEncode1(out *jwriter.Writer, in struct {
	AwsAccessKeyID     string `json:"Aws_access_key_id"`
	AwsSecretAccessKey string `json:"Aws_secret_access_key"`
	Bucket             string `json:"Bucket"`
	Online             bool   `json:"Online"`
	Prefix             string `json:"Prefix"`
	Region             string `json:"Region"`
}) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"Aws_access_key_id\":"
		out.RawString(prefix[1:])
		out.String(string(in.AwsAccessKeyID))
	}
	{
		const prefix string = ",\"Aws_secret_access_key\":"
		out.RawString(prefix)
		out.String(string(in.AwsSecretAccessKey))
	}
	{
		const prefix string = ",\"Bucket\":"
		out.RawString(prefix)
		out.String(string(in.Bucket))
	}
	{
		const prefix string = ",\"Online\":"
		out.RawString(prefix)
		out.Bool(bool(in.Online))
	}
	{
		const prefix string = ",\"Prefix\":"
		out.RawString(prefix)
		out.String(string(in.Prefix))
	}
	{
		const prefix string = ",\"Region\":"
		out.RawString(prefix)
		out.String(string(in.Region))
	}
	out.RawByte('}')
}
func easyjsonFe453bbbDecode(in *jlexer.Lexer, out *struct {
	MaxPoolSize int32 `json:"MaxPoolSize"`
	RedoLog     *struct {
		Dir          string `json:"Dir"`
		Enabled      bool   `json:"Enabled"`
		SyncInterval string `json:"SyncInterval"`
	} `json:"RedoLog"`
	Suffix map[string]string `json:"Suffix"`
	Tables map[string]*struct {
		Concurrence   int32  `json:"Concurrence"`
		FlushInterval string `json:"FlushInterval"`
		TimeOut       string `json:"TimeOut"`
	} `json:"Tables"`
	TicketsCollection string `json:"TicketsCollection"`
	TicketsDB         string `json:"TicketsDB"`
	URI               string `json:"Uri"`
}) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "MaxPoolSize":
			out.MaxPoolSize = int32(in.Int32())
		case "RedoLog":
			if in.IsNull() {
				in.Skip()
				out.RedoLog = nil
			} else {
				if out.RedoLog == nil {
					out.RedoLog = new(struct {
						Dir          string `json:"Dir"`
						Enabled      bool   `json:"Enabled"`
						SyncInterval string `json:"SyncInterval"`
					})
				}
				easyjsonFe453bbbDecode8(in, out.RedoLog)
			}
		case "Suffix":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Suffix = make(map[string]string)
				for !in.IsDelim('}') {
					key := string(in.String())
					in.WantColon()
					var v6 string
					v6 = string(in.String())
					(out.Suffix)[key] = v6
					in.WantComma()
				}
				in.Delim('}')
			}
		case "Tables":
			if in.IsNull() {
				in.Skip()
			} else {
				in.Delim('{')
				out.Tables = make(map[string]*struct {
					Concurrence   int32  `json:"Concurrence"`
					FlushInterval string `json:"FlushInterval"`
					TimeOut       string `json:"TimeOut"`
				})
				for !in.IsDelim('}') {
					key := string(in.String())
					in.WantColon()
					var v7 *struct {
						Concurrence   int32  `json:"Concurrence"`
						FlushInterval string `json:"FlushInterval"`
						TimeOut       string `json:"TimeOut"`
					}
					if in.IsNull() {
						in.Skip()
						v7 = nil
					} else {
						if v7 == nil {
							v7 = new(struct {
								Concurrence   int32  `json:"Concurrence"`
								FlushInterval string `json:"FlushInterval"`
								TimeOut       string `json:"TimeOut"`
							})
						}
						easyjsonFe453bbbDecode9(in, v7)
					}
					(out.Tables)[key] = v7
					in.WantComma()
				}
				in.Delim('}')
			}
		case "TicketsCollection":
			out.TicketsCollection = string(in.String())
		case "TicketsDB":
			out.TicketsDB = string(in.String())
		case "Uri":
			out.URI = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonFe453bbbEncode(out *jwriter.Writer, in struct {
	MaxPoolSize int32 `json:"MaxPoolSize"`
	RedoLog     *struct {
		Dir          string `json:"Dir"`
		Enabled      bool   `json:"Enabled"`
		SyncInterval string `json:"SyncInterval"`
	} `json:"RedoLog"`
	Suffix map[string]string `json:"Suffix"`
	Tables map[string]*struct {
		Concurrence   int32  `json:"Concurrence"`
		FlushInterval string `json:"FlushInterval"`
		TimeOut       string `json:"TimeOut"`
	} `json:"Tables"`
	TicketsCollection string `json:"TicketsCollection"`
	TicketsDB         string `json:"TicketsDB"`
	URI               string `json:"Uri"`
}) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"MaxPoolSize\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.MaxPoolSize))
	}
	{
		const prefix string = ",\"RedoLog\":"
		out.RawString(prefix)
		if in.RedoLog == nil {
			out.RawString("null")
		} else {
			easyjsonFe453bbbEncode8(out, *in.RedoLog)
		}
	}
	{
		const prefix string = ",\"Suffix\":"
		out.RawString(prefix)
		if in.Suffix == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v8First := true
			for v8Name, v8Value := range in.Suffix {
				if v8First {
					v8First = false
				} else {
					out.RawByte(',')
				}
				out.String(string(v8Name))
				out.RawByte(':')
				out.String(string(v8Value))
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"Tables\":"
		out.RawString(prefix)
		if in.Tables == nil && (out.Flags&jwriter.NilMapAsEmpty) == 0 {
			out.RawString(`null`)
		} else {
			out.RawByte('{')
			v9First := true
			for v9Name, v9Value := range in.Tables {
				if v9First {
					v9First = false
				} else {
					out.RawByte(',')
				}
				out.String(string(v9Name))
				out.RawByte(':')
				if v9Value == nil {
					out.RawString("null")
				} else {
					easyjsonFe453bbbEncode9(out, *v9Value)
				}
			}
			out.RawByte('}')
		}
	}
	{
		const prefix string = ",\"TicketsCollection\":"
		out.RawString(prefix)
		out.String(string(in.TicketsCollection))
	}
	{
		const prefix string = ",\"TicketsDB\":"
		out.RawString(prefix)
		out.String(string(in.TicketsDB))
	}
	{
		const prefix string = ",\"Uri\":"
		out.RawString(prefix)
		out.String(string(in.URI))
	}
	out.RawByte('}')
}
func easyjsonFe453bbbDecode9(in *jlexer.Lexer, out *struct {
	Concurrence   int32  `json:"Concurrence"`
	FlushInterval string `json:"FlushInterval"`
	TimeOut       string `json:"TimeOut"`
}) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "Concurrence":
			out.Concurrence = int32(in.Int32())
		case "FlushInterval":
			out.FlushInterval = string(in.String())
		case "TimeOut":
			out.TimeOut = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonFe453bbbEncode9(out *jwriter.Writer, in struct {
	Concurrence   int32  `json:"Concurrence"`
	FlushInterval string `json:"FlushInterval"`
	TimeOut       string `json:"TimeOut"`
}) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"Concurrence\":"
		out.RawString(prefix[1:])
		out.Int32(int32(in.Concurrence))
	}
	{
		const prefix string = ",\"FlushInterval\":"
		out.RawString(prefix)
		out.String(string(in.FlushInterval))
	}
	{
		const prefix string = ",\"TimeOut\":"
		out.RawString(prefix)
		out.String(string(in.TimeOut))
	}
	out.RawByte('}')
}
func easyjsonFe453bbbDecode8(in *jlexer.Lexer, out *struct {
	Dir          string `json:"Dir"`
	Enabled      bool   `json:"Enabled"`
	SyncInterval string `json:"SyncInterval"`
}) {
	isTopLevel := in.IsStart()
	if in.IsNull() {
		if isTopLevel {
			in.Consumed()
		}
		in.Skip()
		return
	}
	in.Delim('{')
	for !in.IsDelim('}') {
		key := in.UnsafeFieldName(false)
		in.WantColon()
		if in.IsNull() {
			in.Skip()
			in.WantComma()
			continue
		}
		switch key {
		case "Dir":
			out.Dir = string(in.String())
		case "Enabled":
			out.Enabled = bool(in.Bool())
		case "SyncInterval":
			out.SyncInterval = string(in.String())
		default:
			in.SkipRecursive()
		}
		in.WantComma()
	}
	in.Delim('}')
	if isTopLevel {
		in.Consumed()
	}
}
func easyjsonFe453bbbEncode8(out *jwriter.Writer, in struct {
	Dir          string `json:"Dir"`
	Enabled      bool   `json:"Enabled"`
	SyncInterval string `json:"SyncInterval"`
}) {
	out.RawByte('{')
	first := true
	_ = first
	{
		const prefix string = ",\"Dir\":"
		out.RawString(prefix[1:])
		out.String(string(in.Dir))
	}
	{
		const prefix string = ",\"Enabled\":"
		out.RawString(prefix)
		out.Bool(bool(in.Enabled))
	}
	{
		const prefix string = ",\"SyncInterval\":"
		out.RawString(prefix)
		out.String(string(in.SyncInterval))
	}
	out.RawByte('}')
}
