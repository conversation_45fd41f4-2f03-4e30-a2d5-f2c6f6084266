package hero

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"context"
	"strconv"
)

func UpgradeGeneLevel(ctx context.Context, uid int64, heroId int32) error {
	heroModel, err := model.GetHero(ctx, uid, heroId)
	if err != nil {
		return kdmerr.SysDBError.CastErrorf("db error")
	}
	curGeneLine := cfg_mgr.Cfg.HeroGeneTable.Get(heroModel.GeneConfigId())
	if curGeneLine == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("hero gene config not found")
	}
	nextGenLines := cfg_mgr.Cfg.HeroGeneTable.FilterSlice(func(v *servercfg.HeroGeneTableCfg) bool {
		return v.HeroID == curGeneLine.HeroID && v.HeroGeneLevel == curGeneLine.HeroGeneLevel+1
	})
	if len(nextGenLines) == 0 {
		return kdmerr.SysInvalidArguments.CastErrorf("next hero gene config not found")
	}
	nextGenLine := nextGenLines[0]
	sequence := util.GenerateSequenceString()
	for _, v := range nextGenLine.LevelUp {
		err = consumable.DeductItem(ctx, uid, v.CostType, int64(v.CostValue), bi.ItemFlowReason_HERO_GENE_UPGRADE, strconv.Itoa(int(nextGenLine.Id)), sequence)
		if err != nil {
			return err
		}
	}
	heroModel.SetGeneConfigId(ctx, nextGenLine.Id)
	heroLine := cfg_mgr.Cfg.HeroTable.Get(heroId)
	levelLine := cfg_mgr.Cfg.HeroLevelTable.Get(heroModel.LevelId())
	starLine := cfg_mgr.Cfg.HeroStarTable.Get(heroModel.StarId())
	logger.LogCustom(ctx, uid, "legend_hero", logger.NewDetail().Put("action", "gene_up").
		Put("hero_id", heroId).Put("quality", heroLine.HeroQuality.String()).Put("level", levelLine.HeroLevel).Put("star", starLine.HeroStarLevel).
		Put("gene_level_before", curGeneLine.HeroGeneLevel).Put("gene_level_after", nextGenLine.HeroGeneLevel))
	quest.Notify(ctx, uid, servercfg.TaskType_HeroGeneUp, map[string]string{}, 1)
	quest.Notify(ctx, uid, servercfg.TaskType_HeroGeneUpTo, map[string]string{}, 1)
	quest.Notify(ctx, uid, servercfg.TaskType_TotalHeroGeneUp, map[string]string{}, 1)
	return nil
}
