// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type MonsterSkillTableCfg struct {
	Id                    int32                      `json:"Id"`                 // Id
	SkillCoolDown         float32                    `json:"SkillCoolDown"`      // 冷却时间/s
	SkillRange            float32                    `json:"SkillRange"`         // 施法距离/m
	SkillType             SkillType                  `json:"SkillType"`          // 技能类型
	Action                string                     `json:"Action"`             // 动作
	TimeCharge            int32                      `json:"TimeCharge"`         // 动作时间/ms
	Audio                 string                     `json:"Audio"`              // 音效
	PassiveSkillEffect    []int32                    `json:"PassiveSkillEffect"` // 被动技能组
	PassiveSkillEffectRef []*HeroSkillEffectTableCfg `json:"-"`                  // 被动技能组
	ActiveSkillEffect     []int32                    `json:"ActiveSkillEffect"`  // 主动技能段组[]
	ActiveSkillEffectRef  []*HeroSkillEffectTableCfg `json:"-"`                  // 主动技能段组[]
}

func NewMonsterSkillTableCfg() *MonsterSkillTableCfg {
	return &MonsterSkillTableCfg{
		Id:                    0,
		SkillCoolDown:         0.0,
		SkillRange:            0.0,
		SkillType:             SkillType(enumDefaultValue),
		Action:                "",
		TimeCharge:            0,
		Audio:                 "",
		PassiveSkillEffect:    []int32{},
		PassiveSkillEffectRef: []*HeroSkillEffectTableCfg{},
		ActiveSkillEffect:     []int32{},
		ActiveSkillEffectRef:  []*HeroSkillEffectTableCfg{},
	}
}

func NewMockMonsterSkillTableCfg() *MonsterSkillTableCfg {
	return &MonsterSkillTableCfg{
		Id:                    0,
		SkillCoolDown:         0.0,
		SkillRange:            0.0,
		SkillType:             SkillType(enumDefaultValue),
		Action:                "",
		TimeCharge:            0,
		Audio:                 "",
		PassiveSkillEffect:    []int32{0},
		PassiveSkillEffectRef: []*HeroSkillEffectTableCfg{},
		ActiveSkillEffect:     []int32{0},
		ActiveSkillEffectRef:  []*HeroSkillEffectTableCfg{},
	}
}

type MonsterSkillTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*MonsterSkillTableCfg
	localIds         map[int32]struct{}
}

func NewMonsterSkillTable(configs *Configs) *MonsterSkillTable {
	return &MonsterSkillTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*MonsterSkillTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *MonsterSkillTable) Get(key int32) *MonsterSkillTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *MonsterSkillTable) GetAll() map[int32]*MonsterSkillTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *MonsterSkillTable) put(key int32, value *MonsterSkillTableCfg, local bool) *MonsterSkillTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *MonsterSkillTable) putFromInheritedTable(key int32, value *MonsterSkillTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[MonsterSkillTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *MonsterSkillTable) Put(key int32, value *MonsterSkillTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[MonsterSkillTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *MonsterSkillTable) PutAll(m map[int32]*MonsterSkillTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *MonsterSkillTable) Range(f func(v *MonsterSkillTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *MonsterSkillTable) Filter(filterFuncs ...func(v *MonsterSkillTableCfg) bool) map[int32]*MonsterSkillTableCfg {
	filtered := map[int32]*MonsterSkillTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *MonsterSkillTable) FilterSlice(filterFuncs ...func(v *MonsterSkillTableCfg) bool) []*MonsterSkillTableCfg {
	filtered := []*MonsterSkillTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *MonsterSkillTable) FilterKeys(filterFuncs ...func(v *MonsterSkillTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *MonsterSkillTable) satisfied(v *MonsterSkillTableCfg, filterFuncs ...func(v *MonsterSkillTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *MonsterSkillTable) setupIndexes() error {
	return nil
}

func (t *MonsterSkillTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *MonsterSkillTableCfg) bindRefs(c *Configs) {
	for _, e := range r.PassiveSkillEffect {
		cfgoRefRecord := c.HeroSkillEffectTable.Get(e)
		r.PassiveSkillEffectRef = append(r.PassiveSkillEffectRef, cfgoRefRecord)
	}
	for _, e := range r.ActiveSkillEffect {
		cfgoRefRecord := c.HeroSkillEffectTable.Get(e)
		r.ActiveSkillEffectRef = append(r.ActiveSkillEffectRef, cfgoRefRecord)
	}
}

func (t *MonsterSkillTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[MonsterSkillTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewMonsterSkillTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterSkillTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[MonsterSkillTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// SkillCoolDown
		{
			if record[t.getIndexInCsv("SkillCoolDown")] == "" {
				recordCfg.SkillCoolDown = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("SkillCoolDown")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterSkillTable]unmarshal csv record failed, varName=SkillCoolDown, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("SkillCoolDown")], err)
					} else {
						return fmt.Errorf("[MonsterSkillTable]unmarshal csv record failed, varName=SkillCoolDown, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("SkillCoolDown")], err)
					}
				}
				recordCfg.SkillCoolDown = float32(cfgoFloat)
			}
		}
		// SkillRange
		{
			if record[t.getIndexInCsv("SkillRange")] == "" {
				recordCfg.SkillRange = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("SkillRange")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterSkillTable]unmarshal csv record failed, varName=SkillRange, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("SkillRange")], err)
					} else {
						return fmt.Errorf("[MonsterSkillTable]unmarshal csv record failed, varName=SkillRange, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("SkillRange")], err)
					}
				}
				recordCfg.SkillRange = float32(cfgoFloat)
			}
		}
		// SkillType
		{
			if record[t.getIndexInCsv("SkillType")] == "" {
				recordCfg.SkillType = SkillType(enumDefaultValue)
			} else {
				cfgoEnum, err := parseSkillType(record[t.getIndexInCsv("SkillType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterSkillTable]unmarshal csv record failed, varName=SkillType, type=enum@SkillType, value=%s, err:[%s]\n", record[t.getIndexInCsv("SkillType")], err)
					} else {
						return fmt.Errorf("[MonsterSkillTable]unmarshal csv record failed, varName=SkillType, type=enum@SkillType, value=%s, err:[%s]", record[t.getIndexInCsv("SkillType")], err)
					}
				}
				recordCfg.SkillType = cfgoEnum
			}
		}
		// Action
		{
			recordCfg.Action = strings.TrimSpace(record[t.getIndexInCsv("Action")])
		}
		// TimeCharge
		{
			if record[t.getIndexInCsv("TimeCharge")] == "" {
				recordCfg.TimeCharge = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("TimeCharge")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [MonsterSkillTable]unmarshal csv record failed, varName=TimeCharge, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("TimeCharge")], err)
					} else {
						return fmt.Errorf("[MonsterSkillTable]unmarshal csv record failed, varName=TimeCharge, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("TimeCharge")], err)
					}
				}
				recordCfg.TimeCharge = int32(cfgoInt)
			}
		}
		// Audio
		{
			recordCfg.Audio = strings.TrimSpace(record[t.getIndexInCsv("Audio")])
		}
		// PassiveSkillEffect
		{
			if record[t.getIndexInCsv("PassiveSkillEffect")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("PassiveSkillEffect")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfPassiveSkillEffect int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfPassiveSkillEffect = 0
					} else {
						var err error
						cfgoElemOfPassiveSkillEffect, err = configs.HeroSkillEffectTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MonsterSkillTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in vector, varName=PassiveSkillEffect, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MonsterSkillTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in vector, varName=PassiveSkillEffect, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.PassiveSkillEffect = append(recordCfg.PassiveSkillEffect, cfgoElemOfPassiveSkillEffect)
				}
			}
		}
		// ActiveSkillEffect
		{
			if record[t.getIndexInCsv("ActiveSkillEffect")] != "" {
				cfgoSplitStrs := strings.FieldsFunc(record[t.getIndexInCsv("ActiveSkillEffect")], func(r rune) bool {
					if r == ';' {
						return true
					}
					if r == '；' {
						return true
					}
					return false
				})
				for _, cfgoSplitStr := range cfgoSplitStrs {
					var cfgoElemOfActiveSkillEffect int32 = 0
					if cfgoSplitStr == "" {
						cfgoElemOfActiveSkillEffect = 0
					} else {
						var err error
						cfgoElemOfActiveSkillEffect, err = configs.HeroSkillEffectTable.getIdByRef(cfgoSplitStr)
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [MonsterSkillTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in vector, varName=ActiveSkillEffect, value=%s, err:[%s]\n", cfgoSplitStr, err)
							} else {
								return fmt.Errorf("[MonsterSkillTable]unmarshal record failed, cannot parse ref@HeroSkillEffectTable in vector, varName=ActiveSkillEffect, value=%s, err:[%s]", cfgoSplitStr, err)
							}
						}
					}

					recordCfg.ActiveSkillEffect = append(recordCfg.ActiveSkillEffect, cfgoElemOfActiveSkillEffect)
				}
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [MonsterSkillTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[MonsterSkillTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *MonsterSkillTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "MonsterSkillTable.csv") && (!strings.HasPrefix(fileName, "MonsterSkillTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for MonsterSkillTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[MonsterSkillTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[MonsterSkillTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[MonsterSkillTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[MonsterSkillTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[MonsterSkillTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[MonsterSkillTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[MonsterSkillTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[MonsterSkillTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [MonsterSkillTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *MonsterSkillTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[MonsterSkillTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [MonsterSkillTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *MonsterSkillTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[MonsterSkillTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *MonsterSkillTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[MonsterSkillTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[MonsterSkillTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
