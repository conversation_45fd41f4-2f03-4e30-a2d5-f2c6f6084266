package item

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"context"
	"strconv"
)

func UserItem(ctx context.Context, uid int64, itemId int32, amount int64, useType int32, para1 int64, exParas map[int32]int32) error {
	if amount <= 0 {
		return kdmerr.SysInvalidArguments.CastErrorf("amount is")
	}
	if amount > model.GetConsumableQuantity(ctx, uid, itemId) {
		return kdmerr.SysInvalidArguments.CastErrorf("amount is not enough")
	}
	line := cfg_mgr.Cfg.ItemTable.Get(itemId)
	var err error
	switch line.Type {
	case servercfg.ItemType_Diamond:
		amount, err = Currency(ctx, uid, line, amount, para1)
	case servercfg.ItemType_HeroFragment:
		amount, err = HeroFragment(ctx, uid, line, amount, para1)
	case servercfg.ItemType_UniversalLegendaryHeroFragment, servercfg.ItemType_UniversalEpicHeroFragment, servercfg.ItemType_UniversalRareHeroFragment:
		amount, err = UniversalHeroFragment(ctx, uid, line, amount, para1)
	case servercfg.ItemType_ChestSelfSelect:
		amount, err = ChestSelfSelect(ctx, uid, line, amount, exParas)
	default:
		return kdmerr.SysInvalidArguments.CastErrorf("have no use function")
	}
	if err != nil {
		return err
	}
	sequence := util.GenerateSequenceString()
	if amount > 0 {
		err = consumable.DeductItem(ctx, uid, itemId, amount, bi.ItemFlowReason_USE_ITEM, line.Type.String()+":"+strconv.Itoa(int(para1)), sequence)
	}
	return err
}
