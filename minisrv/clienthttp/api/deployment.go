package api

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"context"
	"encoding/json"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
)

// FunctionOpenRequest function_open请求的结构
type FunctionOpenRequest struct {
	Class  string                 `json:"class"`
	Method string                 `json:"method"`
	Params map[string]interface{} `json:"params"`
}

// handleFunctionOpen 处理function_open格式的请求
func handleFunctionOpen(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	var req FunctionOpenRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		jsonResponse(w, http.StatusBadRequest, "Invalid JSON request body", nil)
		return
	}

	// 根据method字段路由到相应的处理函数
	switch req.Method {
	case "uploadVersionDiff2":
		handleUploadVersionDiffFromJSON(w, r, req.Params)
	case "uploadBundle2":
		handleUploadBundleFromJSON(w, r, req.Params)
	case "generateAppVersion":
		handleGenerateAppVersionFromJSON(w, r, req.Params)
	default:
		jsonResponse(w, http.StatusBadRequest, "Unsupported method: "+req.Method, nil)
	}
}

// handleUploadVersionDiff2 处理直接的uploadVersionDiff2请求
func handleUploadVersionDiff2(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	// 解析表单数据
	if err := r.ParseMultipartForm(32 << 20); err != nil { // 32MB max memory
		if err := r.ParseForm(); err != nil {
			jsonResponse(w, http.StatusBadRequest, "Failed to parse form data", nil)
			return
		}
	}

	// 从表单或查询参数中获取值
	baseVersion := getParam(r, "base_version")
	if baseVersion == "" {
		baseVersion = getParam(r, "version") // 兼容不同的参数名
	}
	targetVersion := getParam(r, "target_version")
	platform := getParam(r, "platform")
	diffData := getParam(r, "data")
	jobId := getParam(r, "build_job")

	if baseVersion == "" || platform == "" {
		jsonResponse(w, http.StatusBadRequest, "version and platform are required", nil)
		return
	}

	var err error
	tasklet.Invoke(r.Context(), "uploadVersionDiff2", func(ctx context.Context) {
		versionModel, _ := model.GetHuatuoVersionModel(ctx, platform, baseVersion)
		if versionModel == nil {
			versionModel, err = orm.Create[*model.HuatuoVersionModel](ctx, &minirpc.HuatuoVersion{
				Platform:      platform,
				ClientVersion: baseVersion,
				TargetVersion: targetVersion,
				Data:          diffData,
				JobId:         jobId,
			})
		} else {
			if targetVersion != "" {
				versionModel.SetTargetVersion(ctx, targetVersion)
			}
			if diffData != "" {
				versionModel.SetData(ctx, diffData)
			}
			if jobId != "" {
				versionModel.SetJobId(ctx, jobId)
			}
		}
	})

	if err != nil {
		jsonResponse(w, http.StatusInternalServerError, "Failed to upload version diff: "+err.Error(), nil)
		return
	}

	response := map[string]interface{}{
		"base_version":   baseVersion,
		"target_version": targetVersion,
		"platform":       platform,
	}

	jsonResponse(w, http.StatusOK, "Version diff uploaded successfully", response)
}

// handleUploadVersionDiffFromJSON 处理来自JSON的uploadVersionDiff2请求
func handleUploadVersionDiffFromJSON(w http.ResponseWriter, r *http.Request, params map[string]interface{}) {
	// 从params中提取参数
	platform := getStringParam(params, "platform")
	baseVersion := getStringParam(params, "version") // 注意：JSON中使用"version"而不是"base_version"
	targetVersion := getStringParam(params, "target_version")
	diffData := getStringParam(params, "data")
	jobId := getStringParam(params, "build_job")

	if baseVersion == "" || platform == "" {
		jsonResponse(w, http.StatusBadRequest, "version and platform are required", nil)
		return
	}

	var err error
	tasklet.Invoke(r.Context(), "uploadVersionDiff2", func(ctx context.Context) {
		versionModel, _ := model.GetHuatuoVersionModel(ctx, platform, baseVersion)
		if versionModel == nil {
			versionModel, err = orm.Create[*model.HuatuoVersionModel](ctx, &minirpc.HuatuoVersion{
				Platform:      platform,
				ClientVersion: baseVersion,
				TargetVersion: targetVersion,
				Data:          diffData,
				JobId:         jobId,
			})
		} else {
			if targetVersion != "" {
				versionModel.SetTargetVersion(ctx, targetVersion)
			}
			if diffData != "" {
				versionModel.SetData(ctx, diffData)
			}
			if jobId != "" {
				versionModel.SetJobId(ctx, jobId)
			}
		}
	})

	if err != nil {
		jsonResponse(w, http.StatusInternalServerError, "Failed to upload version diff: "+err.Error(), nil)
		return
	}

	response := map[string]interface{}{
		"base_version":   baseVersion,
		"target_version": targetVersion,
		"platform":       platform,
	}

	jsonResponse(w, http.StatusOK, "Version diff uploaded successfully", response)
}

// handleUploadBundle2 处理直接的uploadBundle2请求
func handleUploadBundle2(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	// 解析表单数据
	if err := r.ParseForm(); err != nil {
		jsonResponse(w, http.StatusBadRequest, "Failed to parse form data", nil)
		return
	}

	baseVersion := getParam(r, "version")
	platform := getParam(r, "platform")
	gogCfg := getParam(r, "bundle_cfg_gog")
	gogPath := getParam(r, "bundle_path_gog")
	mcCfg := getParam(r, "bundle_cfg_mc")
	mcPath := getParam(r, "bundle_path_mc")
	jobId := getParam(r, "build_job")

	if baseVersion == "" || platform == "" {
		jsonResponse(w, http.StatusBadRequest, "version and platform are required", nil)
		return
	}

	versionModel, _ := model.GetHuatuoVersionModel(context.Background(), platform, baseVersion)
	if versionModel == nil {
		versionModel, _ = orm.Create[*model.HuatuoVersionModel](context.Background(), &minirpc.ClientVersion{
			Platform:      platform,
			ClientVersion: baseVersion,
		})
	}
	ctx := context.Background()
	versionModel.SetBundlePathGog(ctx, gogPath)
	versionModel.SetBundleCfgGog(ctx, gogCfg)
	versionModel.SetBundleCfgMc(ctx, mcCfg)
	versionModel.SetBundlePathMc(ctx, mcPath)
	versionModel.SetJobId(ctx, jobId)

	response := map[string]interface{}{
		"version":  baseVersion,
		"platform": platform,
	}

	jsonResponse(w, http.StatusOK, "Bundle uploaded successfully", response)
}

// handleUploadBundleFromJSON 处理来自JSON的uploadBundle2请求
func handleUploadBundleFromJSON(w http.ResponseWriter, r *http.Request, params map[string]interface{}) {
	baseVersion := getStringParam(params, "version")
	platform := getStringParam(params, "platform")
	gogCfg := getStringParam(params, "bundle_cfg_gog")
	gogPath := getStringParam(params, "bundle_path_gog")
	mcCfg := getStringParam(params, "bundle_cfg_mc")
	mcPath := getStringParam(params, "bundle_path_mc")
	jobId := getStringParam(params, "build_job")

	if baseVersion == "" || platform == "" {
		jsonResponse(w, http.StatusBadRequest, "version and platform are required", nil)
		return
	}

	versionModel, _ := model.GetHuatuoVersionModel(context.Background(), platform, baseVersion)
	if versionModel == nil {
		versionModel, _ = orm.Create[*model.HuatuoVersionModel](context.Background(), &minirpc.ClientVersion{
			Platform:      platform,
			ClientVersion: baseVersion,
		})
	}
	ctx := context.Background()
	versionModel.SetBundlePathGog(ctx, gogPath)
	versionModel.SetBundleCfgGog(ctx, gogCfg)
	versionModel.SetBundleCfgMc(ctx, mcCfg)
	versionModel.SetBundlePathMc(ctx, mcPath)
	versionModel.SetJobId(ctx, jobId)

	response := map[string]interface{}{
		"version":  baseVersion,
		"platform": platform,
	}

	jsonResponse(w, http.StatusOK, "Bundle uploaded successfully", response)
}

// handleGenerateAppVersion 处理直接的generateAppVersion请求
func handleGenerateAppVersion(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	values := r.URL.Query()
	packageVersion := values.Get("version")
	platform := values.Get("platform")
	bvr := values.Get("bvr")
	bvg := values.Get("bvg")
	bundleMd5 := values.Get("bundle_md5")
	preloadMd5 := values.Get("preload_md5")
	configMd5 := values.Get("config_md5")

	if packageVersion == "" || platform == "" {
		jsonResponse(w, http.StatusBadRequest, "version and platform are required", nil)
		return
	}

	// TODO: 实现addAppVersion逻辑
	_ = bvr
	_ = bvg
	_ = bundleMd5
	_ = preloadMd5
	_ = configMd5

	response := map[string]interface{}{
		"version":  packageVersion,
		"platform": platform,
	}

	jsonResponse(w, http.StatusOK, "App version generated successfully", response)
}

// handleGenerateAppVersionFromJSON 处理来自JSON的generateAppVersion请求
func handleGenerateAppVersionFromJSON(w http.ResponseWriter, r *http.Request, params map[string]interface{}) {
	packageVersion := getStringParam(params, "version")
	platform := getStringParam(params, "platform")
	// 获取其他参数但暂时不使用
	_ = getStringParam(params, "bvr")
	_ = getStringParam(params, "bvg")
	_ = getStringParam(params, "bundle_md5")
	_ = getStringParam(params, "preload_md5")
	_ = getStringParam(params, "config_md5")

	if packageVersion == "" || platform == "" {
		jsonResponse(w, http.StatusBadRequest, "version and platform are required", nil)
		return
	}

	// TODO: 实现addAppVersion逻辑，这里需要从原来的代码中迁移
	response := map[string]interface{}{
		"version":  packageVersion,
		"platform": platform,
	}

	jsonResponse(w, http.StatusOK, "App version generated successfully", response)
}

// getParam 从表单或查询参数中获取值
func getParam(r *http.Request, key string) string {
	// 优先从multipart form获取
	if r.MultipartForm != nil {
		if values := r.MultipartForm.Value[key]; len(values) > 0 {
			return values[0]
		}
	}
	// 然后从普通表单数据获取
	if value := r.FormValue(key); value != "" {
		return value
	}
	// 最后从查询参数获取
	return r.URL.Query().Get(key)
}

// getStringParam 从params map中获取字符串参数
func getStringParam(params map[string]interface{}, key string) string {
	if value, ok := params[key]; ok {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}
