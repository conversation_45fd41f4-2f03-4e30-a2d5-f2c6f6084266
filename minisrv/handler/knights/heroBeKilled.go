package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/main_line_stage"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// status 0:未解锁 1:已解锁 2:已领取
// 英雄被杀死
func (_ KnightsHandlers) HeroBeKilledHandler(ctx *wctx.Context, req *wrpc.HeroBeKilledRequest) (*wrpc.HeroBeKilledReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		main_line_stage.HeroBeKilled(ctx, req.UID, req.HeroId)
		replay := wrpc.HeroBeKilledReply{
			Result: true,
		}
		rawResp = &replay
		err = nil
	})
	return rawResp.(*wrpc.HeroBeKilledReply), err
}
