package http_post

import (
	"fmt"
	"github.com/julienschmidt/httprouter"
	"strings"
)

var HandlerMap = make(map[string]httprouter.Handle)

func AddPostHandler(name string, handler httprouter.Handle) {
	p := "/cmd/" + name
	if _, ok := HandlerMap[p]; ok {
		panic(fmt.<PERSON><PERSON><PERSON>("name duplication detected. httpcmd: %s", name))
	}
	pathLower := strings.ToLower(p)
	if _, ok := HandlerMap[pathLower]; ok {
		panic(fmt.<PERSON><PERSON><PERSON>("name duplication detected. httpcmd: %s", name))
	}

	HandlerMap[p] = handler
	if pathLower != p {
		HandlerMap[pathLower] = handler
	}
}
