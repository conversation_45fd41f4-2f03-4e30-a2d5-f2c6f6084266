package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 获取排行榜主页信息
func (_ KnightsHandlers) GetRankHomeInfoHandler(ctx *wctx.Context, req *wrpc.GetRankHomeInfoRequest) (*wrpc.GetRankHomeInfoReply, error) {

	var ret wrpc.RankHomeInfo
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		//// 获取关卡排行榜第一名
		stageRankList := rank.GetStageLevelRankInfoList(ctx, req.UID, 1)
		var stageFirstInfo *wrpc.LevelRankInfo
		if len(stageRankList) > 0 {
			stageFirstInfo = stageRankList[0]
		}
		//
		//// 获取战力排行榜第一名
		powerRankList := rank.GetPowerRankInfoList(ctx, req.UID, 1)
		var powerFirstInfo *wrpc.PowerRankInfo
		if len(powerRankList) > 0 {
			powerFirstInfo = powerRankList[0]
		}

		//
		//// 获取联盟战力排行榜第一名
		var guildFirstInfo *wrpc.GuildPowerRankInfo
		guildRankList := rank.GetGuildPowerRankInfoList(ctx, 0, 1)

		if len(guildRankList) > 0 {
			guildFirstInfo = guildRankList[0]
		}

		ret.PowerRankInfo = powerFirstInfo
		ret.LevelRankInfo = stageFirstInfo
		ret.GuildPowerRankInfo = guildFirstInfo

	})

	return &wrpc.GetRankHomeInfoReply{
		Result: &ret,
	}, err
}
