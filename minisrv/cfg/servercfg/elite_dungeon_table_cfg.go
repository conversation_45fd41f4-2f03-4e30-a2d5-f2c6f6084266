// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type EliteDungeonTableCfg struct {
	Id           int32              `json:"Id"`        // Id
	StringId     string             `json:"StringId"`  // StringId
	Inherit      int32              `json:"Inherit"`   // 主线通关解锁，并继承其信息
	InheritRef   *MainLevelTableCfg `json:"-"`         // 主线通关解锁，并继承其信息
	DirectWin    int32              `json:"DirectWin"` // 通过该关卡可直接领3星
	DirectWinRef *MainLevelTableCfg `json:"-"`         // 通过该关卡可直接领3星
}

func NewEliteDungeonTableCfg() *EliteDungeonTableCfg {
	return &EliteDungeonTableCfg{
		Id:           0,
		StringId:     "",
		Inherit:      0,
		InheritRef:   nil,
		DirectWin:    0,
		DirectWinRef: nil,
	}
}

type EliteDungeonTable struct {
	records  map[int32]*EliteDungeonTableCfg
	localIds map[int32]struct{}
}

func NewEliteDungeonTable() *EliteDungeonTable {
	return &EliteDungeonTable{
		records:  map[int32]*EliteDungeonTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *EliteDungeonTable) Get(key int32) *EliteDungeonTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *EliteDungeonTable) GetAll() map[int32]*EliteDungeonTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *EliteDungeonTable) put(key int32, value *EliteDungeonTableCfg, local bool) *EliteDungeonTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *EliteDungeonTable) Range(f func(v *EliteDungeonTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *EliteDungeonTable) Filter(filterFuncs ...func(v *EliteDungeonTableCfg) bool) map[int32]*EliteDungeonTableCfg {
	filtered := map[int32]*EliteDungeonTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *EliteDungeonTable) FilterSlice(filterFuncs ...func(v *EliteDungeonTableCfg) bool) []*EliteDungeonTableCfg {
	filtered := []*EliteDungeonTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *EliteDungeonTable) FilterKeys(filterFuncs ...func(v *EliteDungeonTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *EliteDungeonTable) satisfied(v *EliteDungeonTableCfg, filterFuncs ...func(v *EliteDungeonTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *EliteDungeonTable) setupIndexes() error {
	return nil
}

func (t *EliteDungeonTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *EliteDungeonTableCfg) bindRefs(c *Configs) {
	r.InheritRef = c.MainLevelTable.Get(r.Inherit)
	r.DirectWinRef = c.MainLevelTable.Get(r.DirectWin)
}
