package hotfix

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/g"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"net/http"
)

func init() {
	http_post.AddPostHandler("hotUpdateJson", hotUpdateJson)
	http_post.AddPostHandler("getValue", getValue)
}
func hotUpdateJson(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	path := g.RootDir + "/cfg"
	err := cfg_mgr.Cfg.LoadJson(path)
	if err != nil {
		fmt.Fprintf(w, "load json failed, err=%v", err)
		return
	}
	fmt.Fprintf(w, "load json success")
}
func getValue(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	line := cfg_mgr.Cfg.MailTable.Get(1)
	fmt.Fprintf(w, "stage not exist"+line.Title)
}
