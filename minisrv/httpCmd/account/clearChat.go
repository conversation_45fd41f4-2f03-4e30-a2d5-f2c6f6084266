package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/g"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"context"
	"fmt"
	"github.com/highras/rtm-server-sdk-go/src/rtm"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
)

func init() {
	http_post.AddPostHandler("clearChat", clearChat)
}
func clearChat(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		g.ClientRtm.ClearProjectMessage(rtm.All)
		fmt.Fprintf(w, "success")
	})

}
