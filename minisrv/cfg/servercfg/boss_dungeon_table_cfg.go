// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type BossDungeonTableCfg struct {
	Id        int32            `json:"Id"`       // Id
	StringId  string           `json:"StringId"` // StringId
	Day       int32            `json:"Day"`      // 每周第x天
	Boss      int32            `json:"Boss"`     // Boss信息
	BossRef   *MonsterTableCfg `json:"-"`        // Boss信息
	Reward    []int32          `json:"Reward"`   // 奖励预览
	RewardRef []*ItemTableCfg  `json:"-"`        // 奖励预览
}

func NewBossDungeonTableCfg() *BossDungeonTableCfg {
	return &BossDungeonTableCfg{
		Id:        0,
		StringId:  "",
		Day:       0,
		Boss:      0,
		BossRef:   nil,
		Reward:    []int32{},
		RewardRef: []*ItemTableCfg{},
	}
}

type BossDungeonTable struct {
	records  map[int32]*BossDungeonTableCfg
	localIds map[int32]struct{}
}

func NewBossDungeonTable() *BossDungeonTable {
	return &BossDungeonTable{
		records:  map[int32]*BossDungeonTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *BossDungeonTable) Get(key int32) *BossDungeonTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *BossDungeonTable) GetAll() map[int32]*BossDungeonTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *BossDungeonTable) put(key int32, value *BossDungeonTableCfg, local bool) *BossDungeonTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *BossDungeonTable) Range(f func(v *BossDungeonTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *BossDungeonTable) Filter(filterFuncs ...func(v *BossDungeonTableCfg) bool) map[int32]*BossDungeonTableCfg {
	filtered := map[int32]*BossDungeonTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *BossDungeonTable) FilterSlice(filterFuncs ...func(v *BossDungeonTableCfg) bool) []*BossDungeonTableCfg {
	filtered := []*BossDungeonTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *BossDungeonTable) FilterKeys(filterFuncs ...func(v *BossDungeonTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *BossDungeonTable) satisfied(v *BossDungeonTableCfg, filterFuncs ...func(v *BossDungeonTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *BossDungeonTable) setupIndexes() error {
	return nil
}

func (t *BossDungeonTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *BossDungeonTableCfg) bindRefs(c *Configs) {
	r.BossRef = c.MonsterTable.Get(r.Boss)
	for _, e := range r.Reward {
		cfgoRefRecord := c.ItemTable.Get(e)
		r.RewardRef = append(r.RewardRef, cfgoRefRecord)
	}
}
