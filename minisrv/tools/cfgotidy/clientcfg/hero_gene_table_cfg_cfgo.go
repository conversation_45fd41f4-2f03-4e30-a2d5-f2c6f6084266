// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"text/template"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t HeroGeneTable) saveJson(dir string) error {
	return t.SaveJsonWithSuffix(dir, "", t.Filter<PERSON>(func(v *HeroGeneTableCfg) bool {
		return true
	}))
}

func (t HeroGeneTable) SaveJsonWithSuffix(dir string, suffix string, cfgs []*HeroGeneTableCfg) error {
	jsonPath := filepath.Join(dir, "HeroGeneTable"+suffix+".json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[HeroGeneTable]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	// sort
	s := cfgoHeroGeneTableSlice{}
	for _, cfgoCfg := range cfgs {
		if _, ok := t.localIds[cfgoCfg.Id]; !ok {
			continue
		}
		s = append(s, cfgoCfg)
	}
	sort.Sort(s)

	w := bufio.NewWriter(f)
	w.WriteString("{\n")
	w.WriteString("\t\"Cells\": ")
	bytes, err := json.MarshalIndent(s, "\t", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	w.WriteString("\n}\n")

	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

type cfgoHeroGeneTableSlice []*HeroGeneTableCfg

func (x cfgoHeroGeneTableSlice) Len() int           { return len(x) }
func (x cfgoHeroGeneTableSlice) Less(i, j int) bool { return x[i].Id < x[j].Id }
func (x cfgoHeroGeneTableSlice) Swap(i, j int)      { x[i], x[j] = x[j], x[i] }

func (t *HeroGeneTable) LoadJsonByPath(jsonPath string, configs *Configs) error {
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[HeroGeneTable]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*HeroGeneTableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[HeroGeneTable]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		if err = t.Put(k, v); err != nil {
			return fmt.Errorf("[HeroGeneTable]put record failed, path=%s, err=[%w]", jsonPath, err)
		}
	}
	if err = t.setupIndexes(); err != nil {
		return fmt.Errorf("[HeroGeneTable]setup indexes failed, path=%s, err=[%w]", jsonPath, err)
	}
	return nil
}

func (t HeroGeneTable) saveMeta(dir string) error {
	return t.SaveMetaWithSuffix(dir, "")
}

func (t HeroGeneTable) SaveMetaWithSuffix(dir string, suffix string) error {
	fbPlus := false
	td := &TableData{}
	if err := json.Unmarshal(string2Bytes(HeroGeneTableJsonContent), td); err != nil {
		return fmt.Errorf("[client]unmarshal json failed, err=[%w]", err)
	}
	if len(suffix) > 0 {
		td.FileName = td.FileName + suffix
		td.TableName = td.TableName + suffix
	}
	path := filepath.Join(dir, td.FileName+".fbs")
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	defer f.Close()

	tplStr := FlatBufferTableTpl
	if fbPlus {
		tplStr = FlatBufferPlusTableTpl
	}
	tpl, err := template.New("table_schema").Parse(tplStr)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}

	if err = tpl.Execute(f, td); err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	return nil
}

var HeroGeneTableJsonContent string = `{
		"FileName": "HeroGeneTable",
		"ConstTable": false,
		"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
		"TableName": "HeroGeneTable",
		"Key": {
			"FieldName": "Id",
			"FieldType": "int32"
		},
		"Fields": [
			{
				"FieldName": "HeroID",
				"FieldType": "int32"
			},
			{
				"FieldName": "HeroGeneLevel",
				"FieldType": "int32"
			},
			{
				"FieldName": "IsMax",
				"FieldType": "bool"
			},
			{
				"FieldName": "LevelUp",
				"FieldType": "[CostKVS]"
			},
			{
				"FieldName": "Power",
				"FieldType": "int32"
			},
			{
				"FieldName": "Attr",
				"FieldType": "AttrStr"
			},
			{
				"FieldName": "AllHeroCritDmgUp",
				"FieldType": "float"
			},
			{
				"FieldName": "IsUnlockExtraSkillEffect",
				"FieldType": "bool"
			},
			{
				"FieldName": "RefHeroStarLevel",
				"FieldType": "int32"
			},
			{
				"FieldName": "SkillTitle",
				"FieldType": "string"
			},
			{
				"FieldName": "SkillDesc",
				"FieldType": "string"
			},
			{
				"FieldName": "SkillDescParam",
				"FieldType": "[string]"
			},
			{
				"FieldName": "DetailSkillDesc",
				"FieldType": "string"
			},
			{
				"FieldName": "DetailSkillDescParam",
				"FieldType": "[string]"
			},
			{
				"FieldName": "Icon",
				"FieldType": "string"
			},
			{
				"FieldName": "IconLocked",
				"FieldType": "string"
			},
			{
				"FieldName": "IsSkillPreview",
				"FieldType": "bool"
			},
			{
				"FieldName": "SkillPreview",
				"FieldType": "string"
			},
			{
				"FieldName": "IsPopUp",
				"FieldType": "bool"
			},
			{
				"FieldName": "PopupName",
				"FieldType": "string"
			},
			{
				"FieldName": "PopupDesc",
				"FieldType": "string"
			},
			{
				"FieldName": "PopupDescParam",
				"FieldType": "[string]"
			},
			{
				"FieldName": "PVEPassiveSkillEffect",
				"FieldType": "[int32]"
			}
		]
	}`
