<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>华佗发版 2 - GM管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            font-size: 14px;
            line-height: 1.42857143;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .btn-deploy-online{position: absolute; right: 20px; top:90px;}
        .btn-add-bundle{position: absolute; right: 120px; top:90px;}
        .btn-add-version{position: absolute; right: 270px; top:90px;}
        .btn-latest-version{position: absolute; right: 405px; top:90px;}
        .table-striped>tbody>tr:nth-of-type(odd).version-selected, .version-selected{ background-color: palegreen;}
        .btn-sync-bundle-data{margin-top: 5px;}
        .log-container {
            background-color: #1e1e1e;
            color: #fff;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status-pending { background-color: #ffc107; color: #000; }
        .status-building { background-color: #17a2b8; color: #fff; }
        .status-success { background-color: #28a745; color: #fff; }
        .status-failed { background-color: #dc3545; color: #fff; }
        
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 250px;
            height: 100vh;
            background: #2c3e50;
            color: white;
            z-index: 1000;
        }
        
        .header {
            padding: 20px;
            background: #34495e;
            text-align: center;
        }
        
        .menu {
            padding: 0;
        }
        
        .menu-item {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 1px solid #34495e;
            transition: background-color 0.3s;
        }
        
        .menu-item:hover {
            background: #34495e;
        }
        
        .menu-item.active {
            background: #3498db;
        }
        
        .main-content {
            margin-left: 250px;
            min-height: 100vh;
        }
        
        .page {
            padding: 20px;
        }
        
        .page-header {
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 500;
            margin: 0;
        }
        
        .panel {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
            box-shadow: 0 1px 1px rgba(0,0,0,.05);
        }
        
        .panel-body {
            padding: 20px;
        }
        
        .nav-tabs {
            border-bottom: 1px solid #ddd;
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
        }
        
        .nav-tabs > li {
            margin-bottom: -1px;
            position: relative;
        }
        
        .nav-tabs > li > a {
            margin-right: 2px;
            line-height: 1.42857143;
            border: 1px solid transparent;
            border-radius: 4px 4px 0 0;
            padding: 10px 15px;
            text-decoration: none;
            color: #555;
            display: block;
            background-color: transparent;
        }
        
        .nav-tabs > li.active > a {
            color: #555;
            cursor: default;
            background-color: #fff;
            border: 1px solid #ddd;
            border-bottom-color: transparent;
        }
        
        .nav-tabs > li > a:hover {
            border-color: #eee #eee #ddd;
        }
        
        .tab-content {
            padding-top: 20px;
        }
        
        .table {
            width: 100%;
            max-width: 100%;
            margin-bottom: 20px;
            border-collapse: collapse;
            background-color: transparent;
        }
        
        .table > thead > tr > th,
        .table > tbody > tr > th,
        .table > tfoot > tr > th,
        .table > thead > tr > td,
        .table > tbody > tr > td,
        .table > tfoot > tr > td {
            padding: 8px;
            line-height: 1.42857143;
            vertical-align: top;
            border-top: 1px solid #ddd;
        }
        
        .table > thead > tr > th {
            vertical-align: bottom;
            border-bottom: 2px solid #ddd;
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        .table-striped > tbody > tr:nth-of-type(odd) {
            background-color: #f9f9f9;
        }
        
        .table-striped > tbody > tr:hover {
            background-color: #f0f0f0;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1050;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal.in {
            display: block;
        }
        
        .modal-dialog {
            position: relative;
            width: auto;
            margin: 10px;
            max-width: 600px;
            margin: 30px auto;
        }
        
        .modal-content {
            position: relative;
            background-color: #fff;
            border: 1px solid #999;
            border-radius: 6px;
            box-shadow: 0 3px 9px rgba(0,0,0,.5);
        }
        
        .modal-header {
            padding: 15px;
            border-bottom: 1px solid #e5e5e5;
        }
        
        .modal-title {
            margin: 0;
            line-height: 1.42857143;
            font-size: 18px;
        }
        
        .modal-body {
            position: relative;
            padding: 15px;
        }
        
        .modal-footer {
            padding: 15px;
            text-align: right;
            border-top: 1px solid #e5e5e5;
        }
        
        .form-control {
            display: block;
            width: 100%;
            height: 34px;
            padding: 6px 12px;
            font-size: 14px;
            line-height: 1.42857143;
            color: #555;
            background-color: #fff;
            background-image: none;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
            transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
        }
        
        .form-control:focus {
            border-color: #66afe9;
            outline: 0;
            box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
        }
        
        textarea.form-control {
            height: auto;
            resize: vertical;
        }
        
        select.form-control {
            height: 34px;
            line-height: 34px;
        }
        
        .btn {
            display: inline-block;
            padding: 6px 12px;
            margin-bottom: 0;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.42857143;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            cursor: pointer;
            border: 1px solid transparent;
            border-radius: 4px;
            text-decoration: none;
            user-select: none;
            background-image: none;
        }
        
        .btn:focus,
        .btn:active:focus {
            outline: thin dotted;
            outline: 5px auto -webkit-focus-ring-color;
            outline-offset: -2px;
        }
        
        .btn:hover,
        .btn:focus {
            color: #333;
            text-decoration: none;
        }
        
        .btn-primary {
            color: #fff;
            background-color: #337ab7;
            border-color: #2e6da4;
        }
        
        .btn-primary:hover,
        .btn-primary:focus {
            color: #fff;
            background-color: #286090;
            border-color: #204d74;
        }
        
        .btn-success {
            color: #fff;
            background-color: #5cb85c;
            border-color: #4cae4c;
        }
        
        .btn-success:hover,
        .btn-success:focus {
            color: #fff;
            background-color: #449d44;
            border-color: #398439;
        }
        
        .btn-default {
            color: #333;
            background-color: #fff;
            border-color: #ccc;
        }
        
        .btn-default:hover,
        .btn-default:focus {
            color: #333;
            background-color: #e6e6e6;
            border-color: #adadad;
        }
        
        .btn-xs {
            padding: 1px 5px;
            font-size: 12px;
            line-height: 1.5;
            border-radius: 3px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
            line-height: 1.5;
            border-radius: 3px;
        }
        
        .btn-info {
            color: #fff;
            background-color: #5bc0de;
            border-color: #46b8da;
        }
        
        .btn-warning {
            color: #fff;
            background-color: #f0ad4e;
            border-color: #eea236;
        }
        
        .btn-danger {
            color: #fff;
            background-color: #d9534f;
            border-color: #d43f3a;
        }
        
        .form-horizontal .form-group {
            margin-left: -15px;
            margin-right: -15px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .form-horizontal .form-group:after {
            content: "";
            display: table;
            clear: both;
        }
        
        .form-horizontal .control-label {
            text-align: right;
            margin-bottom: 0;
            padding-top: 7px;
        }
        
        .col-sm-3 {
            width: 25%;
            float: left;
            padding-left: 15px;
            padding-right: 15px;
        }
        
        .col-sm-9 {
            width: 75%;
            float: left;
            padding-left: 15px;
            padding-right: 15px;
        }
        
        .col-sm-10 {
            width: 83.33333333%;
            float: left;
            padding-left: 15px;
            padding-right: 15px;
        }
        
        .col-sm-12 {
            width: 100%;
            float: left;
            padding-left: 15px;
            padding-right: 15px;
        }
        
        .col-md-4 {
            width: 33.33333333%;
            float: left;
            padding-left: 15px;
            padding-right: 15px;
        }
        
        .row {
            margin-left: -15px;
            margin-right: -15px;
        }
        
        .row:before,
        .row:after {
            content: " ";
            display: table;
        }
        
        .row:after {
            clear: both;
        }
        
        .row-lg {
            margin-bottom: 20px;
        }
        
        .close {
            float: right;
            font-size: 21px;
            font-weight: bold;
            line-height: 1;
            color: #000;
            text-shadow: 0 1px 0 #fff;
            opacity: .2;
            background: transparent;
            border: 0;
            cursor: pointer;
            padding: 0;
        }
        
        .close:hover,
        .close:focus {
            color: #000;
            text-decoration: none;
            cursor: pointer;
            opacity: .5;
        }
        
        .label {
            display: inline;
            padding: .2em .6em .3em;
            font-size: 75%;
            font-weight: bold;
            line-height: 1;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: .25em;
        }
        
        .label-default {
            background-color: #777;
        }
        
        .label-primary {
            background-color: #337ab7;
        }
        
        .label-success {
            background-color: #5cb85c;
        }
        
        .label-info {
            background-color: #5bc0de;
        }
        
        .label-warning {
            background-color: #f0ad4e;
        }
        
        .label-danger {
            background-color: #d9534f;
        }
        
        .text-center {
            text-align: center;
        }
        
        .icon {
            margin-right: 5px;
        }
        
        .fa-rocket:before { content: "🚀"; }
        .fa-bug:before { content: "🐛"; }
        .fa-fire:before { content: "🔥"; }
        .fa-eye:before { content: "👁"; }
        .fa-download:before { content: "⬇"; }
        .fa-refresh:before { content: "🔄"; }
        .wb-arrow-up:before { content: "⬆"; }
        .wb-plus:before { content: "+"; }
        
        h5 {
            font-size: 16px;
            margin-top: 0;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        input[type="radio"] {
            margin-right: 5px;
        }
        
        .padding-top-20 {
            padding-top: 20px;
        }
        
        .editable-table {
            border: 1px solid #ddd;
        }
        
        .editable-table th,
        .editable-table td {
            border: 1px solid #ddd;
        }

        /* 子菜单样式 */
        .submenu {
            margin-top: 5px;
            padding-left: 20px;
        }

        .submenu-item {
            padding: 8px 15px;
            margin: 2px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .submenu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .submenu-item.active {
            background: #007bff;
            color: white;
        }

        .menu-item.active {
            background: rgba(255, 255, 255, 0.1);
            border-left: 3px solid #007bff;
        }

        .menu-item.active > .submenu {
            display: block;
        }

        .submenu {
            display: none;
        }

        .menu-item.active .submenu {
            display: block;
        }
    </style>
</head>
<body>
<div class="sidebar">
    <div class="header">
        <h2>GM管理后台</h2>
    </div>
    <div class="menu">
        <div class="menu-item" onclick="window.location.href='/gm/'">数据概览</div>
        <div class="menu-item" onclick="window.location.href='/gm/country'">国家限制</div>
        <div class="menu-item" onclick="window.location.href='/gm/users'">用户管理</div>
        <div class="menu-item" onclick="window.location.href='/gm/items'">道具管理</div>
        <div class="menu-item" onclick="window.location.href='/gm/mail'">邮件系统</div>
        <div class="menu-item" onclick="window.location.href='/gm/announce'">公告管理</div>
        <div class="menu-item" onclick="window.location.href='/gm/activity'">活动管理</div>
        <div class="menu-item active">
            版本配置
            <div class="submenu">
                <div class="submenu-item" onclick="window.location.href='/gm/version'">客户端版本管理</div>
                <div class="submenu-item active" onclick="window.location.href='/gm/huatuo_release'">华佗发版</div>
            </div>
        </div>
        <div class="menu-item" onclick="window.location.href='/gm/logs'">系统日志</div>
    </div>
</div>

<div class="main-content">
    <!-- Page -->
    <div class="page">
        <div class="page-header">
            <h1 class="page-title">Version Config</h1>
        </div>
        <div class="page-content">
            <div class="panel">
                <div class="panel-body">
                    <div class="row row-lg">
                        <div class="col-md-4" style="padding-left: 0px;">
                            <select name="history_version" id="history_version" class="form-control">
                                <option value="0">最新版本</option>
                            </select>
                        </div>
                    </div>
                    <div class="row row-lg" style="margin-top: 20px;">
                        <ul class="nav nav-tabs" data-plugin="nav-tabs" role="tablist">
                            <li class="ios active" role="presentation">
                                <a href="#ios"><i class="icon fa-apple" aria-hidden="true"></i> IOS</a>
                            </li>
                            <li class="android" role="presentation">
                                <a href="#android"><i class="icon fa-android" aria-hidden="true"></i> Android</a>
                            </li>
                            <li class="win64" role="presentation">
                                <a href="#win64"><i class="icon fa-windows" aria-hidden="true"></i> Win64</a>
                            </li>
                            <li class="uwp" role="presentation">
                                <a href="#uwp"><i class="icon fa-windows" aria-hidden="true"></i> UWP</a>
                            </li>
                        </ul>
                        <button type="button" class="btn btn-primary btn-latest-version">
                            <i class="icon wb-arrow-up" aria-hidden="true"></i> 发布最新数据(master)
                        </button>
                        <button type="button" class="btn btn-success btn-add-version">
                            <i class="icon wb-plus" aria-hidden="true"></i> 添加版本
                        </button>
                        <button type="button" class="btn btn-success btn-add-bundle">
                            <i class="icon wb-plus" aria-hidden="true"></i> 添加Bundle
                        </button>
                        <button type="button" class="btn btn-primary btn-default btn-deploy-online">
                            <i class="icon wb-arrow-up" aria-hidden="true"></i> 发布
                        </button>

                        <div class="tab-content padding-top-20">
                            <table class="editable-table table table-striped" id="editableTable">
                                <thead>
                                <tr>
                                    <th width="10%">安装包版本</th>
                                    <th width="10%">目标版本</th>
                                    <th width="40%" style="max-width: 240px;">Data</th>
                                    <th width="10%">bundle_path_mc</th>
                                    <th width="10%">bundle_cfg_mc</th>
                                    <th width="10%">bundle_path_gog</th>
                                    <th width="10%">bundle_cfg_gog</th>
                                    <th width="5">JobId</th>
                                    <th width="15%">Actions</th>
                                </tr>
                                </thead>
                                <tbody id="datalist">
                                </tbody>
                                <tfoot>
                                <tr>
                                    <th>安装包版本</th>
                                    <th>目标版本</th>
                                    <th style="max-width: 240px;">Data</th>
                                    <th>bundle_path_mc</th>
                                    <th>bundle_cfg_mc</th>
                                    <th>bundle_path_gog</th>
                                    <th>bundle_cfg_gog</th>
                                    <th>JobId</th>
                                    <th>Actions</th>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel">
                <div class="panel-body">
                    <div class="row row-lg">
                        <h5>当前生效配置</h5>
                    </div>
                    <div class="row row-lg">
                            <pre><code class="json" id="current-config">{
  "versions": [],
  "settings": {},
  "generated_at": "2024-01-01T00:00:00Z"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade modal-3d-flip-vertical" id="addVersionModal" aria-hidden="true"
         aria-labelledby="examplePositionCenter" role="dialog" tabindex="-1">
        <div class="modal-dialog modal-center">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">添加版本</h4>
                </div>
                <form id="myForm" class="form-horizontal">
                    <div class="modal-body" style="padding-top: 0px; padding-bottom: 0px;">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">平台:</label>
                            <div class="col-sm-9">
                                <input type="radio" id="android_checkbox" name="platform" value="android" checked>安卓
                                <input type="radio" id="ios_checkbox" name="platform" value="ios"> IOS
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="version">安装包版本:</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="version" name="version"
                                       placeholder="Version Number (Example: 1.5.1)" autocomplete="off"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="target_version">目标版本:</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="target_version" name="target_version"
                                       placeholder="Version Number (Example: 1.5.2)" autocomplete="off"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="job_id">JobId:</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="job_id" name="job_id"
                                       placeholder="Version Number (Example: 111)" autocomplete="off"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="data">当前版本内容:</label>
                            <div class="col-sm-9">
                                <textarea id="data" name="data" rows="4" cols="50" placeholder="json数据" class="form-control"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="saveAddVersionBtn" class="btn btn-primary">保存</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal" id="closeAddModalBtn">关闭
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade modal-3d-flip-vertical" id="addBundle" aria-hidden="true"
         aria-labelledby="examplePositionCenter" role="dialog" tabindex="-1">
        <div class="modal-dialog modal-center">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">添加Bundle</h4>
                </div>
                <form id="myFormBundle" class="form-horizontal">
                    <div class="modal-body" style="padding-top: 0px; padding-bottom: 0px;">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">平台:</label>
                            <div class="col-sm-9">
                                <input type="radio" id="android_checkbox_b" name="platform" value="android" checked>安卓
                                <input type="radio" id="ios_checkbox_b" name="platform" value="ios"> IOS
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="version">安装包版本:</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="version_b" name="version"
                                       placeholder="Version Number (Example: 1.5.1)" autocomplete="off"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="bundle_path_mc_b">bundle_path_mc:</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="bundle_path_mc_b" name="bundle_path_mc" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="bundle_cfg_mc_b">bundle_cfg_mc:</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="bundle_cfg_mc_b" name="bundle_cfg_mc" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="bundle_path_gog_b">bundle_path_gog:</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="bundle_path_gog_b" name="bundle_path_gog" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="bundle_cfg_gog_b">bundle_cfg_gog:</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="bundle_cfg_gog_b" name="bundle_cfg_gog" />
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="saveAddBundleBtn" class="btn btn-primary">保存</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal" id="closeAddBundleBtn">关闭
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade modal-3d-flip-vertical" id="deploy_comment" aria-hidden="true" aria-labelledby="examplePositionCenter" role="dialog" tabindex="-1">
        <div class="modal-dialog modal-center">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">备注</h4>
                </div>
                <form id="crsCommentForm" class="form-horizontal">
                    <div class="modal-body" style="padding-top: 0px; padding-bottom: 0px;">
                        <div class="form-group">
                            <div class="col-sm-10">
                                <input type="text" class="form-control" id="comment" name="comment" placeholder="" autocomplete="off" />
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="btn-deploy" class="btn btn-primary">提交</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="modal fade modal-3d-flip-vertical" id="version-select-area" aria-hidden="true" aria-labelledby="examplePositionCenter"
         role="dialog" tabindex="-1">
        <div class="modal-dialog modal-center" style="width: 700px;height: 900px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">勾选</h4>
                </div>
                <div class="modal-body" >
                    <input type="hidden" id="sync-platform" value="">
                    <input type="hidden" id="sync-version" value="">
                    <div class="form-group" id="version-list" style="height: 500px;overflow-y: auto">
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12" style="text-align: center; margin-top: -30px;">
                            <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="sync-bundle-data-save">Save</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑版本弹窗 -->
    <div class="modal fade modal-3d-flip-vertical" id="editVersionModal" aria-hidden="true"
         aria-labelledby="examplePositionCenter" role="dialog" tabindex="-1">
        <div class="modal-dialog modal-center">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">编辑版本</h4>
                </div>
                <form id="editForm" class="form-horizontal">
                    <div class="modal-body" style="padding-top: 0px; padding-bottom: 0px;">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">平台:</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="edit_platform" readonly>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">安装包版本:</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="edit_version" readonly>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="edit_target_version">目标版本:</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="edit_target_version" name="target_version"
                                       placeholder="Target Version" autocomplete="off"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="edit_job_id">JobId:</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="edit_job_id" name="job_id"
                                       placeholder="Job ID" autocomplete="off"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="edit_data">当前版本内容:</label>
                            <div class="col-sm-9">
                                <textarea id="edit_data" name="data" rows="4" cols="50" placeholder="json数据" class="form-control"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="saveEditVersionBtn" class="btn btn-primary">保存</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal" id="closeEditModalBtn">关闭</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    var verList = [];
    var historyList = [];
    var isOnline = true;
    var currentPlatform = 'ios';

    document.addEventListener('DOMContentLoaded', function() {
        initializePage();
        bindEvents();
        loadVersionData();
    });

    function initializePage() {
        // 初始化标签页
        const navTabs = document.querySelectorAll('.nav-tabs a');
        navTabs.forEach(function(tab) {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                // 移除所有active类
                document.querySelectorAll('.nav-tabs li').forEach(li => li.classList.remove('active'));
                // 添加active类到当前标签
                this.parentElement.classList.add('active');
                currentPlatform = this.getAttribute('href').substring(1);
                console.log('切换到平台:', currentPlatform);
                loadVersionData();
            });
        });
        
        // 初始化时加载默认平台数据
        loadVersionData();
    }

    function bindEvents() {
        // 添加版本按钮
        document.querySelector('.btn-add-version').addEventListener('click', function() {
            const platformRadio = document.querySelector('input[name="platform"][value="' + currentPlatform + '"]');
            if (platformRadio) {
                platformRadio.checked = true;
            }
            showModal('addVersionModal');
        });

        // 保存编辑版本
        document.getElementById('saveEditVersionBtn').addEventListener('click', function() {
            saveEditVersion();
        });

        // 添加Bundle按钮
        document.querySelector('.btn-add-bundle').addEventListener('click', function() {
            // 设置模态框中的平台选择为当前平台
            const platformRadio = document.querySelector('input[name="platform"][value="' + currentPlatform + '"]');
            if (platformRadio) {
                platformRadio.checked = true;
            }
            showModal('addBundle');
        });

        // 发布按钮
        document.querySelector('.btn-deploy-online').addEventListener('click', function() {
            showModal('deploy_comment');
        });

        // 发布最新数据按钮
        document.querySelector('.btn-latest-version').addEventListener('click', function() {
            deployLatest();
        });

        // 保存版本
        document.getElementById('saveAddVersionBtn').addEventListener('click', function() {
            saveVersion();
        });

        // 保存Bundle
        document.getElementById('saveAddBundleBtn').addEventListener('click', function() {
            saveBundle();
        });

        // 确认发布
        document.getElementById('btn-deploy').addEventListener('click', function() {
            confirmDeploy();
        });

        // 关闭模态框
        document.querySelectorAll('.close, [data-dismiss="modal"]').forEach(function(btn) {
            btn.addEventListener('click', function() {
                const modal = this.closest('.modal');
                if (modal) {
                    closeModal(modal.id);
                }
            });
        });
    }

    function showModal(id) {
        const modal = document.getElementById(id);
        if (modal) {
            modal.classList.add('in');
            modal.style.display = 'block';
        }
    }

    function closeModal(id) {
        const modal = document.getElementById(id);
        if (modal) {
            modal.classList.remove('in');
            modal.style.display = 'none';
        }
    }

    function loadVersionData() {
        console.log('加载平台数据:', currentPlatform);
        fetch('/gm/api/huatuo/versions?platform=' + currentPlatform)
            .then(response => response.json())
            .then(response => {
                if (response.code === 200) {
                    renderVersionTable(response.data || []);
                    updateCurrentConfig(response.data || []);
                } else {
                    console.error('Failed to load version data:', response.message);
                    renderVersionTable([]);
                }
            })
            .catch(error => {
                console.error('Network error:', error);
                renderVersionTable([]);
            });
    }

    function updateCurrentConfig(versions) {
        // 更新当前生效配置显示
        const config = {
            platform: currentPlatform,
            versions: versions,
            settings: {},
            generated_at: new Date().toISOString()
        };
        
        const configElement = document.getElementById('current-config');
        if (configElement) {
            configElement.textContent = JSON.stringify(config, null, 2);
        }
    }

    function renderVersionTable(versions) {
        const tbody = document.getElementById('datalist');
        if (!tbody) return;
        
        tbody.innerHTML = '';

        if (versions.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" style="text-align: center;">暂无' + currentPlatform.toUpperCase() + '版本数据</td></tr>';
            return;
        }

        versions.forEach(function(item) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.version || ''}</td>
                <td>${item.target_version || ''}</td>
                <td style="max-width: 240px; word-break: break-all;">${item.data || ''}</td>
                <td>${item.bundle_path_mc || ''}</td>
                <td>${item.bundle_cfg_mc || ''}</td>
                <td>${item.bundle_path_gog || ''}</td>
                <td>${item.bundle_cfg_gog || ''}</td>
                <td>${item.job_id || ''}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editVersion('${item.version}')">编辑</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteVersion('${item.version}')">删除</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    function editVersion(version) {
        console.log('编辑版本:', version, '平台:', currentPlatform);
        
        // 获取版本详情
        fetch('/gm/api/huatuo/versions?platform=' + currentPlatform)
            .then(response => response.json())
            .then(response => {
                if (response.code === 200) {
                    const versionData = response.data.find(item => item.version === version);
                    if (versionData) {
                        // 填充编辑表单
                        document.getElementById('edit_platform').value = currentPlatform;
                        document.getElementById('edit_version').value = version;
                        document.getElementById('edit_target_version').value = versionData.target_version || '';
                        document.getElementById('edit_job_id').value = versionData.job_id || '';
                        document.getElementById('edit_data').value = versionData.data || '';
                        
                        showModal('editVersionModal');
                    } else {
                        alert('未找到版本数据');
                    }
                } else {
                    alert('获取版本数据失败: ' + response.message);
                }
            })
            .catch(error => {
                console.error('Error fetching version data:', error);
                alert('获取版本数据失败，请检查网络连接');
            });
    }

    function deleteVersion(version) {
        if (confirm('确定要删除版本 ' + version + ' 吗？此操作不可撤销。')) {
            console.log('删除版本:', version, '平台:', currentPlatform);

            fetch('/gm/api/huatuo/versions/' + currentPlatform + '/' + version, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(response => {
                if (response.code === 200) {
                    alert('版本已删除');
                    // 重新加载数据
                    loadVersionData();
                } else {
                    alert('删除失败: ' + (response.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error deleting version:', error);
                alert('删除失败，请检查网络连接');
            });
        }
    }

    function saveVersion() {
        const platformRadio = document.querySelector('input[name="platform"]:checked');
        const formData = {
            platform: platformRadio ? platformRadio.value : '',
            version: document.getElementById('version').value,
            target_version: document.getElementById('target_version').value,
            job_id: document.getElementById('job_id').value,
            data: document.getElementById('data').value
        };

        if (!formData.platform || !formData.version) {
            alert('请填写必要字段');
            return;
        }

        fetch('/gm/api/huatuo/versions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(response => {
            if (response.code === 200) {
                console.log('版本添加成功');
                closeModal('addVersionModal');
                // 清空表单
                document.getElementById('myForm').reset();
                // 重新加载数据
                loadVersionData();
            } else {
                alert('添加失败: ' + response.message);
            }
        })
        .catch(error => {
            console.error('Network error:', error);
            alert('网络错误');
        });
    }

    function saveBundle() {
        const platformRadio = document.querySelector('input[name="platform"]:checked');
        const formData = {
            platform: platformRadio ? platformRadio.value : '',
            version: document.getElementById('version_b').value,
            bundle_path_mc: document.getElementById('bundle_path_mc_b').value,
            bundle_cfg_mc: document.getElementById('bundle_cfg_mc_b').value,
            bundle_path_gog: document.getElementById('bundle_path_gog_b').value,
            bundle_cfg_gog: document.getElementById('bundle_cfg_gog_b').value
        };

        if (!formData.platform || !formData.version) {
            alert('请填写必要字段');
            return;
        }

        fetch('/gm/api/huatuo/bundles', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(response => {
            if (response.code === 200) {
                console.log('Bundle添加成功');
                closeModal('addBundle');
                // 清空表单
                document.getElementById('myFormBundle').reset();
                // 重新加载数据
                loadVersionData();
            } else {
                alert('添加失败: ' + response.message);
            }
        })
        .catch(error => {
            console.error('Network error:', error);
            alert('网络错误');
        });
    }

    function deployLatest() {
        console.log('发布最新数据(master) - 平台:', currentPlatform);
        // TODO: 实现发布最新数据功能
    }

    function confirmDeploy() {
        const comment = document.getElementById('comment').value;
        console.log('确认发布 - 平台:', currentPlatform, '备注:', comment);
        closeModal('deploy_comment');
        // TODO: 实现发布功能
    }

    function saveEditVersion() {
        const platform = document.getElementById('edit_platform').value;
        const version = document.getElementById('edit_version').value;
        
        const formData = {
            target_version: document.getElementById('edit_target_version').value,
            job_id: document.getElementById('edit_job_id').value,
            data: document.getElementById('edit_data').value
        };

        fetch('/gm/api/huatuo/versions/' + platform + '/' + version, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(response => {
            if (response.code === 200) {
                alert('版本更新成功');
                closeModal('editVersionModal');
                // 清空表单
                document.getElementById('editForm').reset();
                // 重新加载数据
                loadVersionData();
            } else {
                alert('更新失败: ' + response.message);
            }
        })
        .catch(error => {
            console.error('Network error:', error);
            alert('网络错误');
        });
    }
</script>
</body>
</html>