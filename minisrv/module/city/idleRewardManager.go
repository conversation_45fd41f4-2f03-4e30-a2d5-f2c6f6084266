package city

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/benefit"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"context"
	"encoding/json"
	"time"
)

var userDataKeys []minirpc.UserDataType
var userDataUpdateKeys []minirpc.UserDataType

func init() {
	userDataKeys = []minirpc.UserDataType{minirpc.UserDataTypeIdleRewardTime1, minirpc.UserDataTypeIdleRewardTime2, minirpc.UserDataTypeIdleRewardTime3, minirpc.UserDataTypeIdleRewardTime4}
	userDataUpdateKeys = []minirpc.UserDataType{minirpc.UserDataTypeIdleUpdateTime1, minirpc.UserDataTypeIdleUpdateTime2, minirpc.UserDataTypeIdleUpdateTime3, minirpc.UserDataTypeIdleUpdateTime4}
}

func CollectIdleReward(ctx context.Context, uid int64) (map[int32]int64, error) {

	userData, err := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeIdleLastCollectTime))
	if err != nil {
		return nil, err
	}
	rewards := userData.Content()
	if rewards == nil {
		rewards = make(map[int32]int64)
	}
	idleRewardTimeLine := cfg_mgr.Cfg.IdleRewardTime.Get(1)
	now := time.Now().Unix()
	duration := now - userData.Value()
	if now-userData.Value() < int64(idleRewardTimeLine.MinTime)*int64(time.Minute.Seconds()) {
		return nil, nil
	}
	userData.SetValue(ctx, now)
	sequence := util.GenerateSequenceString()
	rewards, _ = model.ChangeItem(ctx, uid, rewards)
	for k, v := range rewards {
		model.AddItem(ctx, uid, k, v, bi.ItemFlowReason_IDLE_REWARD, "1", sequence)
	}
	userData.SetContent(ctx, nil)
	for k, _ := range userDataKeys {
		userData1, _ := model.GetUserData(ctx, uid, int32(userDataKeys[k]))
		userData1.SetValue(ctx, now)
		userUpdateData, _ := model.GetUserData(ctx, uid, int32(userDataUpdateKeys[k]))
		userUpdateData.SetValue(ctx, 0)
	}
	stage, _ := model.GetMainLineStage(ctx, uid)
	stageLine := cfg_mgr.Cfg.MainLevelTable.Get(stage.UnlockStageId())
	curQuestId := ""
	if stageLine != nil {
		curQuestId = stageLine.StringId
	}
	jsonData, _ := json.Marshal(rewards)
	duration = util.MinInt64(duration, int64(idleRewardTimeLine.MaxTime)*int64(time.Minute.Seconds()))
	logger.LogCustom(ctx, uid, "idle", logger.NewDetail().Put("action", "reward").Put("type", 0).
		Put("level", curQuestId).Put("reward", string(jsonData)).Put("time_duration", duration))
	quest.Notify(ctx, uid, servercfg.TaskType_claim_idle_reward, map[string]string{}, 1)
	return rewards, nil
}

func GetIdleReward(ctx context.Context, uid int64) (map[int32]int64, bool, error) {
	userData, err := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeIdleLastCollectTime))
	if err != nil {
		return nil, false, err
	}
	rewards := userData.Content()
	if rewards == nil {
		rewards = make(map[int32]int64)
	}
	idleRewardTimeLine := cfg_mgr.Cfg.IdleRewardTime.Get(1)
	now := time.Now().Unix()
	stage, _ := model.GetMainLineStage(ctx, uid)
	stageLine := cfg_mgr.Cfg.MainLevelTable.Get(stage.UnlockStageId() - 1)
	if stageLine == nil {
		stageLines := cfg_mgr.Cfg.MainLevelTable.FilterSlice(func(v *servercfg.MainLevelTableCfg) bool {
			return v.IsMaxLevel == true
		})
		if stageLines == nil {
			return nil, false, nil
		}
		stageLine = stageLines[0]
	}
	if stageLine.IsMaxLevel {
		if stage.StageFinishStatus()[stage.UnlockStageId()] != nil {
			stageLine = cfg_mgr.Cfg.MainLevelTable.Get(stage.UnlockStageId())
		}
	}
	idelRewardLines := cfg_mgr.Cfg.IdleRewardTable.FilterSlice(func(v *servercfg.IdleRewardTableCfg) bool {
		return v.Level == stageLine.Id
	})
	if idelRewardLines == nil {
		return nil, false, nil
	}
	isFull := true
	DefaultRewardRefreshTimes := [...]int32{idleRewardTimeLine.DefaultReward1RefreshTime, idleRewardTimeLine.DefaultReward2RefreshTime, idleRewardTimeLine.DefaultReward3RefreshTime, idleRewardTimeLine.DefaultReward4RefreshTime}

	// 安全获取奖励类型，避免数组越界
	DefaultRewardTypes := [4]int32{}
	DefaultRewardValues := [4]int32{}

	for i := 0; i < 4; i++ {
		if i < len(idelRewardLines[0].Reward) && idelRewardLines[0].Reward[i] != nil {
			DefaultRewardTypes[i] = idelRewardLines[0].Reward[i].RewardType
			DefaultRewardValues[i] = idelRewardLines[0].Reward[i].RewardValue
		} else {
			DefaultRewardTypes[i] = 0
			DefaultRewardValues[i] = 0
		}
	}
	maxTimeAddPrecent := benefit.GetBenefitValue(ctx, uid, constDef.BENEFIT_AFK_TIME_UP)
	for k, _ := range userDataKeys {
		userData1, _ := model.GetUserData(ctx, uid, int32(userDataKeys[k]))
		idle := (now - userData1.Value()) / int64(time.Minute/time.Second)
		if idle < int64(float64(idleRewardTimeLine.MaxTime)*(1+maxTimeAddPrecent)) {
			isFull = false
		}
		//剩余时间
		userUpdateData, _ := model.GetUserData(ctx, uid, int32(userDataUpdateKeys[k]))
		idle = util.MinInt64(idle, int64(idleRewardTimeLine.MaxTime))
		idle -= userUpdateData.Value()

		idle = util.MaxInt64(idle, 0)
		count := idle / int64(DefaultRewardRefreshTimes[k])
		if count > 0 && DefaultRewardValues[k] > 0 {
			rewards = util.CombineRewards(rewards, map[int32]int64{DefaultRewardTypes[k]: int64(DefaultRewardValues[k]) * count})
		}
		userUpdateData.SetValue(ctx, userUpdateData.Value()+count*int64(DefaultRewardRefreshTimes[k]))
	}
	userData.SetContent(ctx, rewards)
	return rewards, isFull, nil
}
