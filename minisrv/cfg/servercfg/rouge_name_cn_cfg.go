// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type RougeNameCnCfg struct {
	Id       int32  `json:"Id"`       // Id
	StringId string `json:"StringId"` // StringId
	Col      int32  `json:"Col"`      // 中文名列数
	Name     string `json:"Name"`     // 中文名
}

func NewRougeNameCnCfg() *RougeNameCnCfg {
	return &RougeNameCnCfg{
		Id:       0,
		StringId: "",
		Col:      0,
		Name:     "",
	}
}

type RougeNameCn struct {
	records  map[int32]*RougeNameCnCfg
	localIds map[int32]struct{}
}

func NewRougeNameCn() *RougeNameCn {
	return &RougeNameCn{
		records:  map[int32]*RougeNameCnCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *RougeNameCn) Get(key int32) *RougeNameCnCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *RougeNameCn) GetAll() map[int32]*RougeNameCnCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *RougeNameCn) put(key int32, value *RougeNameCnCfg, local bool) *RougeNameCnCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *RougeNameCn) Range(f func(v *RougeNameCnCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *RougeNameCn) Filter(filterFuncs ...func(v *RougeNameCnCfg) bool) map[int32]*RougeNameCnCfg {
	filtered := map[int32]*RougeNameCnCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *RougeNameCn) FilterSlice(filterFuncs ...func(v *RougeNameCnCfg) bool) []*RougeNameCnCfg {
	filtered := []*RougeNameCnCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *RougeNameCn) FilterKeys(filterFuncs ...func(v *RougeNameCnCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *RougeNameCn) satisfied(v *RougeNameCnCfg, filterFuncs ...func(v *RougeNameCnCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *RougeNameCn) setupIndexes() error {
	return nil
}

func (t *RougeNameCn) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *RougeNameCnCfg) bindRefs(c *Configs) {
}
