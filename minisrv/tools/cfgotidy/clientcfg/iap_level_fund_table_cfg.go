// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type IapLevelFundTableCfg struct {
	Id              int32                 `json:"Id"`           // Id
	StringId        string                `json:"StringId"`     // StringId
	Unlock          int32                 `json:"Unlock"`       // 通过xx关解锁
	UnlockRef       *MainLevelTableCfg    `json:"-"`            // 通过xx关解锁
	Stage           int32                 `json:"Stage"`        // 阶段
	IapPackageId    int32                 `json:"IapPackageId"` // 内购商品id
	IapPackageIdRef *IapPackageTableCfg   `json:"-"`            // 内购商品id
	Limit           PurchaseLimitType     `json:"Limit"`        // 限购类型
	Times           int32                 `json:"Times"`        // 限购次数
	Tag             string                `json:"Tag"`          // 页签描述
	TagParam        string                `json:"TagParam"`     // 描述参数
	Title           string                `json:"Title"`        // 大标题
	Desc            string                `json:"Desc"`         // 描述
	Help            int32                 `json:"Help"`         // 帮助
	HelpRef         *HelpInfoMainTableCfg `json:"-"`            // 帮助
	Image           string                `json:"Image"`        // 背景图1
	Image2          string                `json:"Image2"`       // 背景图2
	Discount        string                `json:"Discount"`     // 折扣
}

func NewIapLevelFundTableCfg() *IapLevelFundTableCfg {
	return &IapLevelFundTableCfg{
		Id:              0,
		StringId:        "",
		Unlock:          0,
		UnlockRef:       nil,
		Stage:           0,
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
		Tag:             "",
		TagParam:        "",
		Title:           "",
		Desc:            "",
		Help:            0,
		HelpRef:         nil,
		Image:           "",
		Image2:          "",
		Discount:        "",
	}
}

func NewMockIapLevelFundTableCfg() *IapLevelFundTableCfg {
	return &IapLevelFundTableCfg{
		Id:              0,
		StringId:        "",
		Unlock:          0,
		UnlockRef:       nil,
		Stage:           0,
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
		Tag:             "",
		TagParam:        "",
		Title:           "",
		Desc:            "",
		Help:            0,
		HelpRef:         nil,
		Image:           "",
		Image2:          "",
		Discount:        "",
	}
}

type IapLevelFundTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*IapLevelFundTableCfg
	localIds         map[int32]struct{}
}

func NewIapLevelFundTable(configs *Configs) *IapLevelFundTable {
	return &IapLevelFundTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*IapLevelFundTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *IapLevelFundTable) Get(key int32) *IapLevelFundTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapLevelFundTable) GetAll() map[int32]*IapLevelFundTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapLevelFundTable) put(key int32, value *IapLevelFundTableCfg, local bool) *IapLevelFundTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *IapLevelFundTable) putFromInheritedTable(key int32, value *IapLevelFundTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[IapLevelFundTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *IapLevelFundTable) Put(key int32, value *IapLevelFundTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[IapLevelFundTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *IapLevelFundTable) PutAll(m map[int32]*IapLevelFundTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapLevelFundTable) Range(f func(v *IapLevelFundTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapLevelFundTable) Filter(filterFuncs ...func(v *IapLevelFundTableCfg) bool) map[int32]*IapLevelFundTableCfg {
	filtered := map[int32]*IapLevelFundTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapLevelFundTable) FilterSlice(filterFuncs ...func(v *IapLevelFundTableCfg) bool) []*IapLevelFundTableCfg {
	filtered := []*IapLevelFundTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapLevelFundTable) FilterKeys(filterFuncs ...func(v *IapLevelFundTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapLevelFundTable) satisfied(v *IapLevelFundTableCfg, filterFuncs ...func(v *IapLevelFundTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapLevelFundTable) setupIndexes() error {
	return nil
}

func (t *IapLevelFundTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapLevelFundTableCfg) bindRefs(c *Configs) {
	r.UnlockRef = c.MainLevelTable.Get(r.Unlock)
	r.IapPackageIdRef = c.IapPackageTable.Get(r.IapPackageId)
	r.HelpRef = c.HelpInfoMainTable.Get(r.Help)
}

func (t *IapLevelFundTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[IapLevelFundTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewIapLevelFundTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapLevelFundTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[IapLevelFundTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// Unlock
		if record[t.getIndexInCsv("Unlock")] == "" {
			recordCfg.Unlock = 0
		} else {
			var err error
			recordCfg.Unlock, err = configs.MainLevelTable.getIdByRef(record[t.getIndexInCsv("Unlock")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [IapLevelFundTable]unmarshal csv record failed, varName=Unlock, type=ref@MainLevelTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Unlock")], err)
				} else {
					return fmt.Errorf("[IapLevelFundTable]unmarshal csv record failed, varName=Unlock, type=ref@MainLevelTable, value=%s, err:[%s]", record[t.getIndexInCsv("Unlock")], err)
				}
			}
		}
		// Stage
		{
			if record[t.getIndexInCsv("Stage")] == "" {
				recordCfg.Stage = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Stage")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapLevelFundTable]unmarshal csv record failed, varName=Stage, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Stage")], err)
					} else {
						return fmt.Errorf("[IapLevelFundTable]unmarshal csv record failed, varName=Stage, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Stage")], err)
					}
				}
				recordCfg.Stage = int32(cfgoInt)
			}
		}
		// IapPackageId
		if record[t.getIndexInCsv("IapPackageId")] == "" {
			recordCfg.IapPackageId = 0
		} else {
			var err error
			recordCfg.IapPackageId, err = configs.IapPackageTable.getIdByRef(record[t.getIndexInCsv("IapPackageId")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [IapLevelFundTable]unmarshal csv record failed, varName=IapPackageId, type=ref@IapPackageTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("IapPackageId")], err)
				} else {
					return fmt.Errorf("[IapLevelFundTable]unmarshal csv record failed, varName=IapPackageId, type=ref@IapPackageTable, value=%s, err:[%s]", record[t.getIndexInCsv("IapPackageId")], err)
				}
			}
		}
		// Limit
		{
			if record[t.getIndexInCsv("Limit")] == "" {
				recordCfg.Limit = PurchaseLimitType(enumDefaultValue)
			} else {
				cfgoEnum, err := parsePurchaseLimitType(record[t.getIndexInCsv("Limit")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapLevelFundTable]unmarshal csv record failed, varName=Limit, type=enum@PurchaseLimitType, value=%s, err:[%s]\n", record[t.getIndexInCsv("Limit")], err)
					} else {
						return fmt.Errorf("[IapLevelFundTable]unmarshal csv record failed, varName=Limit, type=enum@PurchaseLimitType, value=%s, err:[%s]", record[t.getIndexInCsv("Limit")], err)
					}
				}
				recordCfg.Limit = cfgoEnum
			}
		}
		// Times
		{
			if record[t.getIndexInCsv("Times")] == "" {
				recordCfg.Times = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Times")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapLevelFundTable]unmarshal csv record failed, varName=Times, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Times")], err)
					} else {
						return fmt.Errorf("[IapLevelFundTable]unmarshal csv record failed, varName=Times, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Times")], err)
					}
				}
				recordCfg.Times = int32(cfgoInt)
			}
		}
		// Tag
		{
			recordCfg.Tag = strings.TrimSpace(record[t.getIndexInCsv("Tag")])
		}
		// TagParam
		{
			recordCfg.TagParam = strings.TrimSpace(record[t.getIndexInCsv("TagParam")])
		}
		// Title
		{
			recordCfg.Title = strings.TrimSpace(record[t.getIndexInCsv("Title")])
		}
		// Desc
		{
			recordCfg.Desc = strings.TrimSpace(record[t.getIndexInCsv("Desc")])
		}
		// Help
		if record[t.getIndexInCsv("Help")] == "" {
			recordCfg.Help = 0
		} else {
			var err error
			recordCfg.Help, err = configs.HelpInfoMainTable.getIdByRef(record[t.getIndexInCsv("Help")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [IapLevelFundTable]unmarshal csv record failed, varName=Help, type=ref@HelpInfoMainTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Help")], err)
				} else {
					return fmt.Errorf("[IapLevelFundTable]unmarshal csv record failed, varName=Help, type=ref@HelpInfoMainTable, value=%s, err:[%s]", record[t.getIndexInCsv("Help")], err)
				}
			}
		}
		// Image
		{
			recordCfg.Image = strings.TrimSpace(record[t.getIndexInCsv("Image")])
		}
		// Image2
		{
			recordCfg.Image2 = strings.TrimSpace(record[t.getIndexInCsv("Image2")])
		}
		// Discount
		{
			recordCfg.Discount = strings.TrimSpace(record[t.getIndexInCsv("Discount")])
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [IapLevelFundTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[IapLevelFundTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *IapLevelFundTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "IapLevelFundTable.csv") && (!strings.HasPrefix(fileName, "IapLevelFundTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for IapLevelFundTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[IapLevelFundTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[IapLevelFundTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[IapLevelFundTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[IapLevelFundTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[IapLevelFundTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[IapLevelFundTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[IapLevelFundTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[IapLevelFundTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [IapLevelFundTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *IapLevelFundTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[IapLevelFundTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [IapLevelFundTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *IapLevelFundTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[IapLevelFundTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *IapLevelFundTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[IapLevelFundTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[IapLevelFundTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
