package account

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/hero"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/main_line_stage"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
	"sort"
	"strconv"
)

func init() {
	http_post.AddPostHandler("fullAccount", fullAccount)
}

func fullItem(ctx context.Context, uid int64) {
	itemLines := cfg_mgr.Cfg.ItemTable.GetAll()
	for _, v := range itemLines {
		if !v.AutoUse {
			consumable.AddItem(ctx, uid, v.Id, **********, bi.ItemFlowReason_TEST, "", "")
		}
	}
}

func fullHero(ctx context.Context, uid int64) {
	heroLines := cfg_mgr.Cfg.HeroTable.GetAll()
	for _, v := range heroLines {
		heroModel, _ := hero.AddHero(ctx, uid, v.Id, bi.ItemFlowReason_TEST, 0)
		levelId := int32(0)
		maxLevel := int32(0)
		levelLines := cfg_mgr.Cfg.HeroLevelTable.GetAll()
		//sort.Slice(levelLines, func(i, j int) bool {
		//	return levelLines[int32(i)].HeroLevel < levelLines[int32(j)].HeroLevel
		//})
		for _, levelLine := range levelLines {
			if levelLine.PlanID != v.LevelPlanID {
				continue
			}
			if levelLine.HeroLevel <= maxLevel {
				continue
			}
			maxLevel = levelLine.HeroLevel
			levelId = levelLine.Id
		}
		heroModel.SetLevelId(ctx, levelId)

		starId := int32(0)
		maxStarLevel := int32(0)
		starLines := cfg_mgr.Cfg.HeroStarTable.GetAll()
		//sort.Slice(starLines, func(i, j int) bool {
		//	return starLines[int32(i)].HeroStarLevel < starLines[int32(j)].HeroStarLevel
		//})
		for _, starLine := range starLines {
			if starLine.PlanID != v.StarPlanID {
				continue
			}
			if starLine.HeroStarLevel <= maxStarLevel {
				continue
			}
			maxStarLevel = starLine.HeroStarLevel
			starId = starLine.Id
		}
		heroModel.SetStarId(ctx, starId)

		geneId := int32(0)
		maxGeneLevel := int32(0)
		geneLines := cfg_mgr.Cfg.HeroGeneTable.GetAll()
		//sort.Slice(geneLines, func(i, j int) bool {
		//	return geneLines[int32(i)].HeroGeneLevel < geneLines[int32(j)].HeroGeneLevel
		//})
		for _, geneLine := range geneLines {
			if geneLine.HeroID != v.Id {
				continue
			}
			if geneLine.HeroGeneLevel <= maxGeneLevel {
				continue
			}
			maxGeneLevel = geneLine.HeroGeneLevel
			geneId = geneLine.Id
		}
		heroModel.SetGeneConfigId(ctx, geneId)
		hero.CheckHeroSkillUnlock(ctx, heroModel)
	}
}

func fullStage(ctx context.Context, uid int64, unlockId int32) {
	lines := cfg_mgr.Cfg.MainLevelTable.FilterSlice(func(v *servercfg.MainLevelTableCfg) bool {
		return true
	})
	sort.Slice(lines, func(i, j int) bool {
		return lines[int32(i)].Id < lines[int32(j)].Id
	})
	for _, v := range lines {
		if v.Id >= unlockId {
			continue
		}
		unlock := model.LockUser(ctx, uid)
		main_line_stage.StartMainStage(ctx, uid, v.Id, false)

		LevelStruct := &wrpc.LevelStruct{
			CardIds:  []int32{1},
			TotalExp: 100,
			HeroHp:   []int32{1, 1, 1},
		}
		main_line_stage.FinishMainStage(ctx, uid, LevelStruct, false, v.Id, false, 3, 1, 100, map[int32]float32{}, map[int32]float32{}, map[int32]float32{})
		unlock()
	}
}

func unlockMonster(ctx context.Context, uid int64) {
	userData, _ := model.GetUserData(ctx, uid, int32(minirpc.UserDataTypeMonsterBookReward))
	content := userData.Content()
	if content == nil {
		content = make(map[int32]int64)
	}
	lines := cfg_mgr.Cfg.MonsterTable.GetAll()
	for _, v := range lines {
		content[v.MonsterType] = int64(1)
	}
	userData.SetContent(ctx, content)
}

func fullAccount(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	ctx := r.Context()
	tasklet.Invoke(ctx, r.Method, func(ctx context.Context) {
		//内容
		values := r.URL.Query()
		//通过uid删除
		uidStr := values.Get("uid")    // 获取查询参数q的值
		userType := values.Get("type") // 获取查询参数q的值
		if uidStr != "" {
			uid, _ := strconv.ParseInt(uidStr, 10, 64)
			if userType == "high" {
				fmt.Fprintf(w, "高级账号通过所有关卡，账号资源拉满（抽卡、养成、金币、所有植物）")
				stageId := int32(100)
				fullItem(ctx, uid)
				fullHero(ctx, uid)
				fullStage(ctx, uid, stageId)
				unlockMonster(ctx, uid)
			} else {
				stageId := int32(3)
				fullItem(ctx, uid)
				//fullHero(ctx, uid)
				fullStage(ctx, uid, stageId)
				fmt.Fprintf(w, "中级账号通过新手关，账号资源拉满（抽卡、养成、金币")
			}

			return
		}
	})

}
