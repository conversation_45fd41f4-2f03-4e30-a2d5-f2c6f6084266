package cmd

import (
	cfgotidy "bitbucket.org/kingsgroup/gog-knights/minisrv/tools/cfgotidy"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/tools/cfgotidy/utils"
	"errors"
	"fmt"
	"gitlab-ee.funplus.io/mg-server/example/tools/cfgotidy/module"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	//"gitlab-ee.funplus.io/mg-server/example/tools/cfgotidy"
	//
	//"gitlab-ee.funplus.io/mg-server/example/tools/cfgotidy/utils"

	"github.com/spf13/cobra"
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "cfgotidy",
	Short: "Tidy config data tool",
	Long: `Cfgotidy provides a complete solution for tidying game config data.
Example: cfgotidy --csvDir="config/csv" --levelDataDir="level_data" --server --serverjsondir="config/server_json" --client --clientjsondir="config/client_json"`,
	Run: func(cmd *cobra.Command, args []string) {
		tidy()
	},
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	rootCmd.PersistentFlags().BoolVarP(&debug, "debug", "", true, "Whether debug mode, debug mode will skip errors")

	rootCmd.PersistentFlags().StringVarP(&csvDir, "csvdir", "d", "", "The directory of csv data")

	rootCmd.PersistentFlags().StringVarP(&levelDataDir, "levelDataDir", "", "", "The directory of level data")

	rootCmd.PersistentFlags().BoolVarP(&outputServer, "server", "s", false, "Whether output server data")
	rootCmd.PersistentFlags().StringVarP(&cfgotidy.ServerJsonDir, "serverjsondir", "", "", "The directory of server json data, task effect only when --server is set")

	rootCmd.PersistentFlags().BoolVarP(&outputClient, "client", "c", false, "Whether output client data")
	rootCmd.PersistentFlags().StringVarP(&clientJsonDir, "clientjsondir", "", "", "The directory of client json data, task effect only when --client is set")
	rootCmd.PersistentFlags().StringVarP(&clientMetaDir, "clientmetadir", "", "", "The directory of client meta file, task effect only when --client is set")

	rootCmd.PersistentFlags().StringVarP(&dataVersion, "dataversion", "", "", "The data version that will be saved in version.json")
}

func tidy() {
	if debug {
		fmt.Println("debug mode, will skip errors...")
	} else {
		fmt.Println("release mode")
	}

	err := utils.ServerConfigs.LoadCsv(csvDir, debug)
	if err != nil {
		fmt.Printf("load server csv failed, err=[%s]\n", err)
		os.Exit(2)
	}
	if err = utils.ClientConfigs.LoadCsv(csvDir, debug); err != nil {
		fmt.Printf("load client csv failed, err=[%s]\n", err)
		os.Exit(3)
	}
	if err = utils.TempConfigs.LoadCsv(csvDir, debug); err != nil {
		fmt.Printf("load temp csv failed, err=[%s]\n", err)
		os.Exit(4)
	}

	if err = utils.ServerConfigs.SetDataVersion(dataVersion); err != nil {
		fmt.Printf("set data version failed, err=[%s]\n", err)
		os.Exit(5)
	}

	if err := prepare(); err != nil {
		fmt.Printf("prepare failed, err=[%s]\n", err)
		if !debug {
			os.Exit(5)
		}
	}
	//
	//for _, m := range module.Modules {
	//	if err = m.Init(levelDataDir); err != nil {
	//		fmt.Printf("[%s]init error: %s\n", m.Name(), err)
	//		if !debug {
	//			os.Exit(6)
	//		}
	//	}
	//}
	//for _, m := range module.Modules {
	//	if err = m.Handle(); err != nil {
	//		fmt.Printf("[%s]handle error: %s\n", m.Name(), err)
	//		if !debug {
	//			os.Exit(7)
	//		}
	//	}
	//}

	if outputClient {
		for _, m := range module.Modules {
			if err = m.SaveClientCustomizedCfg(clientJsonDir, clientMetaDir); err != nil {
				fmt.Printf("[%s]save client customized cfg_mgr error: %s\n", m.Name(), err)
				if !debug {
					os.Exit(7)
				}
			}
		}
	}

	if outputServer {
		// 服务器SaveJson
		if err = utils.ServerConfigs.SaveJson(cfgotidy.ServerJsonDir); err != nil {
			fmt.Printf("server save json failed, err=[%s]\n", err)
			os.Exit(8)
		}
	}

	if outputClient {
		// 客户端SaveJson
		if err = utils.ClientConfigs.SaveJson(clientJsonDir); err != nil {
			fmt.Printf("client save json failed, err=[%s]\n", err)
			os.Exit(9)
		}
		// 客户端SaveFbs
		if err = utils.ClientConfigs.SaveMeta(clientMetaDir); err != nil {
			fmt.Printf("client save fbs failed, err=[%s]\n", err)
			os.Exit(10)
		}

	}

	fmt.Println("done.")
}

func prepare() error {
	if outputServer {
		if err := createDirIfNotExist(cfgotidy.ServerJsonDir); err != nil {
			return fmt.Errorf("[cfgotidy]create dir failed, path=%s, err=[%w]", cfgotidy.ServerJsonDir, err)
		}
		if err := removeDirFiles(cfgotidy.ServerJsonDir, ".json"); err != nil {
			return fmt.Errorf("[cfgotidy]clear dir failed, path=%s, err=[%w]", cfgotidy.ServerJsonDir, err)
		}
	}

	if outputClient {
		if err := createDirIfNotExist(clientJsonDir); err != nil {
			return fmt.Errorf("[cfgotidy]create dir failed, path=%s, err=[%w]", clientJsonDir, err)
		}
		if err := removeDirFiles(clientJsonDir, ".json"); err != nil {
			return fmt.Errorf("[cfgotidy]clear dir failed, path=%s, err=[%w]", clientJsonDir, err)
		}
		if err := createDirIfNotExist(clientMetaDir); err != nil {
			return fmt.Errorf("[cfgotidy]create dir failed, path=%s, err=[%w]", clientMetaDir, err)
		}
		if err := removeDirFiles(clientMetaDir, ".fbs"); err != nil {
			return fmt.Errorf("[cfgotidy]clear dir failed, path=%s, err=[%w]", clientMetaDir, err)
		}
		if err := removeDirFiles(clientMetaDir, ".lua"); err != nil {
			return fmt.Errorf("[cfgotidy]clear dir failed, path=%s, err=[%w]", clientMetaDir, err)
		}
		if err := removeDirFiles(clientMetaDir, ".txt"); err != nil {
			return fmt.Errorf("[cfgotidy]clear dir failed, path=%s, err=[%w]", clientMetaDir, err)
		}
		if err := removeDirFiles(clientMetaDir, ".xml"); err != nil {
			return fmt.Errorf("[cfgotidy]clear dir failed, path=%s, err=[%w]", clientMetaDir, err)
		}
	}
	return nil
}

func createDirIfNotExist(dir string) error {
	dirInfo, err := os.Stat(dir)
	if err != nil {
		if !os.IsNotExist(err) {
			return err
		}
		err = os.Mkdir(dir, os.ModePerm)
		if err != nil {
			return err
		}
	} else {
		if !dirInfo.IsDir() {
			return errors.New(dir + " is not a directory")
		}
	}
	return nil
}

func removeDirFiles(dir string, suffix string) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	for _, file := range files {
		fileName := file.Name()
		if strings.HasSuffix(fileName, suffix) {
			filepath.Join(dir, fileName)
			if err = os.Remove(filepath.Join(dir, fileName)); err != nil {
				return err
			}
		}
	}
	return nil
}

var debug bool
var csvDir string

var levelDataDir string // 关卡编辑器导出数据目录

var outputServer bool

var outputClient bool
var clientJsonDir string
var clientMetaDir string

var dataVersion string
