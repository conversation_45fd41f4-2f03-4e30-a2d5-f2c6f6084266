package iap

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/misc/push"
	"context"
	"fmt"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/ds"
	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
	"net/http"
	"strconv"
)

type PaymentInfo struct {
	Uid           int64  `json:"uid"`
	Appid         string `json:"appid"`
	Appservid     int    `json:"appservid"`
	ProductID     string `json:"product_id"`
	Gateway       string `json:"gateway"`
	ClientIP      string `json:"client_ip"`
	CheckPayLimit int    `json:"check_pay_limit"`
}

type PostData struct {
	OrderID   int64 `json:"order_id"`
	UID       int64 `json:"uid"`
	ProductId int32 `json:"product_id"`
}

type RetJson struct {
	ReturnCode string `json:"return_code"`
	ReturnMsg  string `json:"return_msg"`
}

// PrepareOrder 下订单
func PrepareOrder(ctx context.Context, uid int64, productId int32, platform string, currency string, amount int64, extra string) (string, error) {
	//line := cfg_mgr.Cfg.IapPackageTable.Get(productId)
	//if line == nil {
	//	return "", kdmerr.SysInvalidArguments.CastErrorf("productId not found")
	//}
	//if !checkIapBuyTimes(ctx, uid, line) {
	//	return "", kdmerr.SysInvalidArguments.CastErrorf("iap buy times limit")
	//}
	//userDevice, _, _ := model.GetUserDevice(ctx, uid)
	//orderId, err := requestOrderData(uid, productId, "", userDevice.ChannelId())
	//if err != nil {
	//	return "", kdmerr.SysInvalidArguments.WrapError(err)
	//}
	//orderModel, _ := model.GetPaymentOrderModel(ctx, uid, int32(orderId))
	//if orderModel.Status() != model.ORDER_STATE_PREPARE {
	//	return "", kdmerr.SysInvalidArguments.CastErrorf("order status error")
	//}
	//orderModel.SetStatus(ctx, model.ORDER_STATE_SUBMITTED)
	return "", nil

}

func GenReturnJson(code string, msg string) string {
	response := RetJson{
		ReturnCode: code,
		ReturnMsg:  msg,
	}
	jsonData, _ := json.Marshal(response)
	return string(jsonData)
}

// SubmitOrder 提交订单
func SubmitOrder(ctx context.Context, uid int64, packageId int32, orderId string, productId string, seq int32, w http.ResponseWriter) error {
	orderModel, _ := model.GetPaymentOrderModel(ctx, uid, orderId)
	rewardsLog := map[int32]int64{}
	if orderModel.Status() == model.ORDER_STATE_PREPARE || orderId == "test" {
		//发奖励

		iapLine := cfg_mgr.Cfg.IapPackageTable.Get(int32(packageId))
		if iapLine == nil {
			if w != nil {
				fmt.Fprintf(w, GenReturnJson("FAIL", " packageId id is error:"+strconv.Itoa(int(packageId))))
			}

			return kdmerr.SysInvalidArguments.CastErrorf("packageId id is error:" + strconv.Itoa(int(packageId)))
		}

		if iapLine.PayIDRef.StringId != productId {
			if w != nil {
				fmt.Fprintf(w, GenReturnJson("FAIL", " product id is error:"+productId))
			}
			return kdmerr.SysInvalidArguments.CastErrorf("product id is error:" + productId)
		}
		rewardLine := iapLine.IapPackageRewardIdRef
		seqStr := util.GenerateSequenceString()
		iapBuyModel, _ := model.GetUserIapBuyTimes(ctx, uid)

		//首次双倍奖励
		totalBuyTImes := iapBuyModel.IapTotalBuyTimes()
		if totalBuyTImes == nil {
			totalBuyTImes = map[int32]int64{}
		}
		rate := int64(1)
		if totalBuyTImes[packageId] == 0 && iapLine.IapPackageType == servercfg.IapPackageType_Diamond {
			diamondShopLine := cfg_mgr.Cfg.IapPackageDiamondShopTable.FilterSlice(func(v *servercfg.IapPackageDiamondShopTableCfg) bool {
				return v.IapPackageId == packageId
			})
			if diamondShopLine != nil && len(diamondShopLine) > 0 && diamondShopLine[0].FirstDoubleReward {
				rate = 2
			}
		}
		if iapLine.IapPackageType == servercfg.IapPackageType_Regular {
			e := submitRegularOrder(ctx, uid, packageId, orderId, productId, seq, w)
			if e != nil {
				return e
			}
		} else if iapLine.IapPackageType == servercfg.IapPackageType_Fund {
			e := SubmitGrowthFund(ctx, uid, packageId, orderId, productId, seq, w)
			if e != nil {
				return e
			}
		} else if iapLine.IapPackageType == servercfg.IapPackageType_First {
			e := SubmitFirstCharge(ctx, uid, packageId, orderId, productId, seq, w)
			if e != nil {
				return e
			}
		} else if iapLine.IapPackageType == servercfg.IapPackageType_DailySale {
			e := SubmitDailySale(ctx, uid, packageId, orderId, productId, seq, w)
			if e != nil {
				return e
			}
		} else if iapLine.IapPackageType == servercfg.IapPackageType_MonthCard {
			e := SubmitMonthCard(ctx, uid, packageId)
			if e != nil {
				return e
			}
		}
		for k, _ := range rewardLine.RewardType {
			model.AddItem(ctx, uid, rewardLine.RewardType[k], int64(rewardLine.RewardValue[k])*rate, bi.ItemFlowReason_BUY_GIFT_PACK, strconv.Itoa(int(packageId)), seqStr)
			rewardsLog[rewardLine.RewardType[k]] += int64(rewardLine.RewardValue[k]) * rate
		}
		orderModel.SetProductId(ctx, int32(packageId))
		orderModel.SetStatus(ctx, model.ORDER_STATE_SUBMITTED)
		totalBuyTImes[packageId]++
		iapBuyModel.SetIapTotalBuyTimes(ctx, totalBuyTImes)
	}
	if w != nil {
		fmt.Fprintf(w, GenReturnJson("SUCCESS", "SUCCESS"))
	}
	pushData := push.NewNotificationWithPayload(minirpc.PushCmdType_Order_Finish, &minirpc.OrderResult{Seq: seq, OrderId: orderId, PackageId: packageId, Rewards: rewardsLog})
	user, _ := model.GetUserModel(ctx, uid)
	ds.SyncCustom(ctx, pushData, []ds.Observer{user})
	trace := map[string]string{
		"type": strconv.Itoa(int(1)),
	}
	quest.Notify(ctx, uid, servercfg.TaskType_Shopping, trace, 1)
	return nil
}

func checkIapBuyTimes(ctx context.Context, uid int64, line *servercfg.IapPackageTableCfg) bool {
	iapBuyModel, _ := model.GetUserIapBuyTimes(ctx, uid)
	if iapBuyModel == nil {
		return false
	}
	return true
}

func GetIapList(ctx context.Context, uid int64) ([]*wrpc.GiftStruct, error) {
	var result []*wrpc.GiftStruct
	for _, v := range servercfg.IapPackageTypeEnums {
		if v == servercfg.IapPackageType_Regular {
			requestList, _ := GetRegularPackList(ctx, uid)
			result = append(result, requestList...)
		}
	}
	return result, nil

}
