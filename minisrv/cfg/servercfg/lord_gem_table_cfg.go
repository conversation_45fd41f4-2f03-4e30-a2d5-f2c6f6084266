// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type LordGemTableCfg struct {
	Id                 int32                    `json:"Id"`              // Id
	Item               int32                    `json:"Item"`            // 对应道具
	ItemRef            *ItemTableCfg            `json:"-"`               // 对应道具
	Hero               []int32                  `json:"Hero"`            // 对应英雄
	HeroRef            []*HeroTableCfg          `json:"-"`               // 对应英雄
	Image              string                   `json:"Image"`           // 小图
	GemAffixId         int32                    `json:"GemAffixId"`      // 词条id（用于替换）
	LordEquipType      int32                    `json:"LordEquipType"`   // 宝石位置类型
	LordEquipTypeRef   *LordEquipTypeTableCfg   `json:"-"`               // 宝石位置类型
	GemQualityType     int32                    `json:"GemQualityType"`  // 宝石品质
	GemQualityTypeRef  *GemQualityTypeTableCfg  `json:"-"`               // 宝石品质
	GemAffixQuality    int32                    `json:"GemAffixQuality"` // 宝石词条品质
	GemAffixQualityRef *GemAffixQualityTableCfg `json:"-"`               // 宝石词条品质
	Modifier           int32                    `json:"Modifier"`        // 修改器
	ModifierRef        *ModifierTableCfg        `json:"-"`               // 修改器
	Weight             int32                    `json:"Weight"`          // 合成/洗练权重
}

func NewLordGemTableCfg() *LordGemTableCfg {
	return &LordGemTableCfg{
		Id:                 0,
		Item:               0,
		ItemRef:            nil,
		Hero:               []int32{},
		HeroRef:            []*HeroTableCfg{},
		Image:              "",
		GemAffixId:         0,
		LordEquipType:      0,
		LordEquipTypeRef:   nil,
		GemQualityType:     0,
		GemQualityTypeRef:  nil,
		GemAffixQuality:    0,
		GemAffixQualityRef: nil,
		Modifier:           0,
		ModifierRef:        nil,
		Weight:             0,
	}
}

type LordGemTable struct {
	records  map[int32]*LordGemTableCfg
	localIds map[int32]struct{}
}

func NewLordGemTable() *LordGemTable {
	return &LordGemTable{
		records:  map[int32]*LordGemTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *LordGemTable) Get(key int32) *LordGemTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *LordGemTable) GetAll() map[int32]*LordGemTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *LordGemTable) put(key int32, value *LordGemTableCfg, local bool) *LordGemTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *LordGemTable) Range(f func(v *LordGemTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *LordGemTable) Filter(filterFuncs ...func(v *LordGemTableCfg) bool) map[int32]*LordGemTableCfg {
	filtered := map[int32]*LordGemTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *LordGemTable) FilterSlice(filterFuncs ...func(v *LordGemTableCfg) bool) []*LordGemTableCfg {
	filtered := []*LordGemTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *LordGemTable) FilterKeys(filterFuncs ...func(v *LordGemTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *LordGemTable) satisfied(v *LordGemTableCfg, filterFuncs ...func(v *LordGemTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *LordGemTable) setupIndexes() error {
	return nil
}

func (t *LordGemTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *LordGemTableCfg) bindRefs(c *Configs) {
	r.ItemRef = c.ItemTable.Get(r.Item)
	for _, e := range r.Hero {
		cfgoRefRecord := c.HeroTable.Get(e)
		r.HeroRef = append(r.HeroRef, cfgoRefRecord)
	}
	r.LordEquipTypeRef = c.LordEquipTypeTable.Get(r.LordEquipType)
	r.GemQualityTypeRef = c.GemQualityTypeTable.Get(r.GemQualityType)
	r.GemAffixQualityRef = c.GemAffixQualityTable.Get(r.GemAffixQuality)
	r.ModifierRef = c.ModifierTable.Get(r.Modifier)
}
