package lord

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/constDef"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/consumable"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/quest"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"strconv"
)

const SLOT_CNT = 5
const GEM_INFO_LIST_AMOUNT = 3

func AddLordGem(ctx context.Context, uid int64, gemId int32, amount int32, reason bi.ItemFlowReason, subReason string) error {
	gemLine := cfg_mgr.Cfg.LordGemTable.Get(gemId)
	if gemLine == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("gem id is error")
	}
	model.AddLordGem(ctx, uid, gemId, amount, reason, subReason)
	trace := map[string]string{
		"type": strconv.Itoa(int(gemLine.Id)),
	}
	quest.Notify(ctx, uid, servercfg.TaskType_GemQuality, trace, int64(amount))
	return nil
}

// 合成宝石
func GemCraft(ctx context.Context, uid int64, crafts []*wrpc.LordGemCraft) ([]int32, error) {
	var retGems []int32
	for _, v := range crafts {
		craftLine := cfg_mgr.Cfg.LordGemCraftTable.Get(v.GemCraftId)
		if craftLine == nil {
			return nil, kdmerr.SysInvalidArguments.CastErrorf("gemCraftId is error")
		}
		canGen := true
		lastGenType := int32(0)
		totalAmount := int32(0)
		for gemId, amount := range v.GemIds {
			totalAmount += amount
			gemLine := cfg_mgr.Cfg.LordGemTable.Get(gemId)
			if gemLine == nil {
				canGen = false
				break
			}
			if lastGenType == 0 {
				lastGenType = gemLine.LordEquipType
			}
			//if lastGenType != gemLine.LordEquipType {
			//	canGen = false
			//	break
			//}
			if gemLine.GemQualityType != craftLine.PreCraftQuality {
				canGen = false
				break
			}
		}
		if totalAmount != craftLine.GemCnt*v.Amount {
			return nil, kdmerr.SysInvalidArguments.CastErrorf("gem count is error")
		}
		//合成
		if canGen {
			canGenGemLines := cfg_mgr.Cfg.LordGemTable.FilterSlice(func(v *servercfg.LordGemTableCfg) bool {
				return v.GemQualityType == craftLine.PostCraftQuality && v.LordEquipType == lastGenType
			})
			gemIdWeight := map[int32]int32{}
			for _, line := range canGenGemLines {
				gemIdWeight[line.Id] = line.Weight
			}
			for gemId, amount := range v.GemIds {
				gemModel := model.GetLordGem(ctx, uid, gemId)
				if gemModel == nil || gemModel.Amount() < amount {
					return nil, kdmerr.SysInvalidArguments.CastErrorf("gem is not enough")
				}
				err := model.SubtractGem(ctx, uid, gemId, amount, bi.ItemFlowReason_Lord_Gem_Craft, strconv.Itoa(int(craftLine.Id)))
				if err != nil {
					return nil, err
				}
			}
			newGemMap := map[int32]int32{}
			for i := int32(0); i < v.Amount; i++ {
				combineId := util.RandomByWeight(gemIdWeight)
				if combineId > 0 {
					retGems = append(retGems, combineId)
					newGemMap[combineId]++

				}
			}
			for k, nv := range newGemMap {
				model.AddLordGem(ctx, uid, k, int32(nv), bi.ItemFlowReason_Lord_Gem_Craft, strconv.Itoa(int(craftLine.Id)))
				gemLine := cfg_mgr.Cfg.LordGemTable.Get(k)
				quality := gemLine.GemQualityType
				trace := map[string]string{
					"quality": strconv.Itoa(int(quality)),
				}
				quest.Notify(ctx, uid, servercfg.TaskType_GemCraft, trace, int64(nv))
			}
		}
	}
	return retGems, nil
}

// 洗炼
func EnhanceGem(ctx context.Context, uid int64, gemId int32) (int32, error) {
	preGemLine := cfg_mgr.Cfg.LordGemTable.Get(gemId)
	if preGemLine == nil {
		return 0, kdmerr.SysDBError.CastErrorf("gem is is error")
	}
	gemModel := model.GetLordGem(ctx, uid, gemId)
	if gemModel.Amount()-gemModel.BeUsed() < 1 {
		return 0, kdmerr.SysDBError.CastErrorf("amount is less than 1")
	}

	reforgeLines := cfg_mgr.Cfg.LordGemReforgeTable.FilterSlice(func(v *servercfg.LordGemReforgeTableCfg) bool {
		return preGemLine.GemQualityType == v.GemQualityType
	})
	if reforgeLines == nil || !reforgeLines[0].CanReforge {
		return 0, kdmerr.SysDBError.CastErrorf("can not reforge")
	}
	seq := util.GenerateSequenceString()

	consumable.DeductItem(ctx, uid, reforgeLines[0].Reforge.CostType, int64(reforgeLines[0].Reforge.CostValue), bi.ItemFlowReason_Lord_Gem_Reforge, strconv.Itoa(int(reforgeLines[0].Id)), seq)
	gemLines := cfg_mgr.Cfg.LordGemTable.FilterSlice(func(v *servercfg.LordGemTableCfg) bool {
		return v.GemQualityType == preGemLine.GemQualityType && v.LordEquipType == preGemLine.LordEquipType && v.Id != preGemLine.Id
	})
	if gemLines == nil {
		return 0, kdmerr.SysDBError.CastErrorf("can not reforge")
	}
	idWeights := map[int32]int32{}
	for _, v := range gemLines {
		idWeights[v.Id] += v.Weight
	}
	nextId := util.RandomByWeight(idWeights)

	model.SubtractGem(ctx, uid, gemId, 1, bi.ItemFlowReason_Lord_Gem_Reforge, strconv.Itoa(int(reforgeLines[0].Id)))
	model.AddLordGem(ctx, uid, nextId, 1, bi.ItemFlowReason_Lord_Gem_Reforge, strconv.Itoa(int(reforgeLines[0].Id)))
	return nextId, nil
}

// 装备
func EquipGem(ctx context.Context, uid int64, gemId int32, posId int32, needUpdate bool, bySwitch bool, equipModel *model.LordEquip) error {
	if posId > SLOT_CNT {
		return kdmerr.SysInvalidArguments.CastErrorf("pos id is error")
	}

	// 检测玩家通关关卡数是否满足槽位解锁条件
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	unlockedStageId := stageModel.UnlockStageId()

	// 根据槽位检查解锁条件（需要配置表支持）
	// 假设配置表中有槽位解锁关卡的配置
	if !checkGemSlotUnlock(ctx, posId, unlockedStageId) {
		return kdmerr.SysInvalidArguments.CastErrorf("gem slot %d not unlocked, need stage %d", posId, getRequiredStageForSlot(posId))
	}

	gemLine := cfg_mgr.Cfg.LordGemTable.Get(gemId)
	if gemLine == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("gem id is error")
	}
	gemModel := model.GetLordGem(ctx, uid, gemId)
	if gemModel == nil || gemModel.Amount() < 1 {
		return kdmerr.SysInvalidArguments.CastErrorf("need more gems")
	}
	//寻找合适的装备
	if equipModel == nil {
		equipModel = model.GetLordEquipModel(ctx, uid, gemLine.LordEquipType)
	}

	if equipModel == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("can not find equipment")
	}
	//equipLine := cfg_mgr.Cfg.LordEquipTable.Get(equipModel.ConfigId())

	gemInfo := equipModel.GeneInfo()
	if gemInfo == nil {
		gemInfo = map[int32]int32{}
	}
	for _, v := range gemInfo {
		if v == 0 {
			continue
		}
		curGemLine := cfg_mgr.Cfg.LordGemTable.Get(v)
		if curGemLine.GemAffixId == gemLine.GemAffixId && gemLine.GemQualityType <= curGemLine.GemQualityType {
			return kdmerr.SysInvalidArguments.CastErrorf("can not equip same affix %d , %d", gemLine.Id, curGemLine.Id)
		}
		if v == gemId {
			return kdmerr.SysInvalidArguments.CastErrorf("can not equip same gem")
		}
	}
	if gemInfo[posId] > 0 {
		err := UnEquipGem(ctx, uid, gemLine.LordEquipType, posId, false, false, equipModel)
		if err != nil {
			return err
		}
	}
	if !bySwitch {
		gemModel.SetBeUsed(ctx, gemModel.BeUsed()+1)
	}
	//model.SubtractGem(ctx, uid, gemId, 1, bi.ItemFlowReason_Lord_Gem_Equip, strconv.Itoa(int(equipModel.TypeId())))
	gemInfo[posId] = gemId
	equipModel.SetGeneInfo(ctx, gemInfo)
	if !needUpdate {
		return nil
	}
	UpdateEquipGem(ctx, equipModel)
	return nil
}

// 卸下
func UnEquipGem(ctx context.Context, uid int64, equipId int32, pos int32, needUpdate bool, bySwitch bool, equipModel *model.LordEquip) error {
	if pos > SLOT_CNT {
		return kdmerr.SysInvalidArguments.CastErrorf("pos id is error")
	}
	if equipModel == nil {
		equipModel = model.GetLordEquipModel(ctx, uid, equipId)
	}

	gemInfo := equipModel.GeneInfo()
	if gemInfo[pos] == 0 {
		return kdmerr.SysInvalidArguments.CastErrorf("pos have no gem")
	}
	gemModel := model.GetLordGem(ctx, uid, gemInfo[pos])
	if gemModel == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("have no gem")
	}
	if !bySwitch {
		gemModel.SetBeUsed(ctx, util.MaxInt32(gemModel.BeUsed()-1, 0))

	}
	//model.AddLordGem(ctx, uid, gemInfo[pos], 1, bi.ItemFlowReason_Lord_Gem_UnEquip, strconv.Itoa(int(equipId)))
	delete(gemInfo, pos)
	equipModel.SetGeneInfo(ctx, gemInfo)
	if needUpdate {
		UpdateEquipGem(ctx, equipModel)
	}
	return nil
}

func LockGem(ctx context.Context, uid int64, gemId int32) error {
	gemModel := model.GetLordGem(ctx, uid, gemId)
	if gemModel == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("have no gem")
	}
	gemModel.SetIsLock(ctx, true)
	return nil
}

func UnlockGem(ctx context.Context, uid int64, gemId int32) error {
	gemModel := model.GetLordGem(ctx, uid, gemId)
	if gemModel == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("have no gem")
	}
	gemModel.SetIsLock(ctx, false)
	return nil
}

func UpdateEquipGem(ctx context.Context, equipModel *model.LordEquip) {
	userInfo, _ := model.GetUserModel(ctx, equipModel.Uid())
	curGemInfoId := userInfo.CurGemInfoId()
	genInfo := equipModel.GeneInfo()
	if genInfo == nil {
		return
	}
	genInfoList := equipModel.GeneInfoList()
	if genInfoList == nil {
		genInfoList = []*minirpc.GeneInfoList{}
		for i := int32(0); i < GEM_INFO_LIST_AMOUNT; i++ {
			info := &minirpc.GeneInfoList{
				GeneInfo: nil,
			}
			genInfoList = append(genInfoList, info)
		}
	}
	info := &minirpc.GeneInfoList{
		GeneInfo: util.CopyMap(genInfo),
	}
	totalPower := int32(0)
	for _, v := range genInfo {
		line := cfg_mgr.Cfg.LordGemTable.Get(v)
		if line != nil {
			totalPower += line.GemQualityTypeRef.Power
		}
	}
	genInfoList[curGemInfoId] = info
	equipModel.SetGeneInfoList(ctx, genInfoList)
	UpdatePower(ctx, equipModel)
	logger.LogCustom(ctx, equipModel.Uid(), "gem", logger.NewDetail().
		Put("action", "config").
		Put("config_id", curGemInfoId+1).
		Put("id", equipModel.TypeId()).
		Put("gem_info", util.FormatRewards(genInfo)).
		Put("total_power", totalPower))
}

func SwitchEquipGem(ctx context.Context, uid int64, listId int32) error {
	if listId >= GEM_INFO_LIST_AMOUNT {
		return kdmerr.SysInvalidArguments.CastErrorf("list id is more than 2")
	}
	userInfo, _ := model.GetUserModel(ctx, uid)
	curGemInfoId := userInfo.CurGemInfoId()
	if curGemInfoId == listId {
		return kdmerr.SysInvalidArguments.CastErrorf("cur list id is this")
	}
	equipments := model.GetAllLordEquipModels(ctx, uid)
	if equipments == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("have no equipments")
	}
	for _, equipment := range equipments {
		//初始化列表
		genInfoList := equipment.GeneInfoList()
		if genInfoList == nil {
			genInfoList = []*minirpc.GeneInfoList{}
			for i := int32(0); i < GEM_INFO_LIST_AMOUNT; i++ {
				info := &minirpc.GeneInfoList{
					GeneInfo: nil,
				}
				genInfoList = append(genInfoList, info)
			}
		}
		info := genInfoList[listId]
		//拆除原来宝石
		genInfo := equipment.GeneInfo()
		if genInfo != nil {
			for k, _ := range genInfo {
				UnEquipGem(ctx, uid, equipment.TypeId(), k, false, true, equipment)
			}
		}

		if info != nil && info.GetGeneInfo() != nil {
			for k, v := range info.GetGeneInfo() {
				EquipGem(ctx, uid, v, k, false, true, equipment)
			}
		}

	}
	userInfo.SetCurGemInfoId(ctx, listId)
	return nil
}

// 检查宝石槽位是否解锁
func checkGemSlotUnlock(ctx context.Context, posId int32, unlockedStageId int32) bool {
	requiredStage := getRequiredStageForSlot(posId)
	return unlockedStageId > requiredStage
}

// 获取槽位所需的关卡数
func getRequiredStageForSlot(posId int32) int32 {
	// 从 gameconfig 获取槽位解锁关卡配置
	configKey := constDef.GAME_CONFIG_LORD_EQUIP_UNLOCK_1 + posId
	requiredStage := cfg_mgr.GetGameConfigInt64Value(configKey)

	if requiredStage > 0 {
		return int32(requiredStage)
	}
	return 1 // 默认值
}
