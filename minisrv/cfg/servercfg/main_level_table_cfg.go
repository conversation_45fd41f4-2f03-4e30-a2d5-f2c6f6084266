// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type MainLevelTableCfg struct {
	Id                             int32                               `json:"Id"`                          // Id
	StringId                       string                              `json:"StringId"`                    // StringId
	IsMaxLevel                     bool                                `json:"IsMaxLevel"`                  // 是否最大关卡
	ChapterLevel                   int32                               `json:"ChapterLevel"`                // 所属章节
	ChapterLevelRef                *ChapterLevelTableCfg               `json:"-"`                           // 所属章节
	Chapter                        int32                               `json:"Chapter"`                     // 章
	Level                          int32                               `json:"Level"`                       // 关卡
	EliteMonsterAtkRatio           float32                             `json:"EliteMonsterAtkRatio"`        // 精英关卡怪物攻击放大系数（废弃）
	EliteMonsterDefRatio           float32                             `json:"EliteMonsterDefRatio"`        // 精英关卡怪物防御放大系数（废弃）
	EliteMonsterHpRatio            float32                             `json:"EliteMonsterHpRatio"`         // 精英关卡怪物血量放大系数（废弃）
	LevelType                      LevelType                           `json:"LevelType"`                   // 关卡类型
	KillRewardRougeTabCntScheme    int32                               `json:"KillRewardRougeTabCntScheme"` // 方案
	KillRewardRougeTabCntSchemeRef *MainLevelRogueRewardWeightTableCfg `json:"-"`                           // 方案
	CommonChallengeReward          int32                               `json:"CommonChallengeReward"`       // 普通关卡挑战奖励
	CommonChallengeRewardRef       *MainLevelRewardTableCfg            `json:"-"`                           // 普通关卡挑战奖励
	EliteChallengeReward           int32                               `json:"EliteChallengeReward"`        // 精英关卡挑战奖励
	EliteChallengeRewardRef        *MainLevelRewardTableCfg            `json:"-"`                           // 精英关卡挑战奖励
	OneStarReward                  int32                               `json:"OneStarReward"`               // 1星通关奖励（仅普通关）
	OneStarRewardRef               *MainLevelPassRewardTableCfg        `json:"-"`                           // 1星通关奖励（仅普通关）
	TwoStarReward                  int32                               `json:"TwoStarReward"`               // 2星通关奖励（仅普通关）
	TwoStarRewardRef               *MainLevelPassRewardTableCfg        `json:"-"`                           // 2星通关奖励（仅普通关）
	ThreeStarReward                int32                               `json:"ThreeStarReward"`             // 3星通关奖励（仅普通关）
	ThreeStarRewardRef             *MainLevelPassRewardTableCfg        `json:"-"`                           // 3星通关奖励（仅普通关）
	ExtraReward                    []*RewardKVS                        `json:"ExtraReward"`                 // 额外奖励
}

func NewMainLevelTableCfg() *MainLevelTableCfg {
	return &MainLevelTableCfg{
		Id:                             0,
		StringId:                       "",
		IsMaxLevel:                     false,
		ChapterLevel:                   0,
		ChapterLevelRef:                nil,
		Chapter:                        0,
		Level:                          0,
		EliteMonsterAtkRatio:           0.0,
		EliteMonsterDefRatio:           0.0,
		EliteMonsterHpRatio:            0.0,
		LevelType:                      LevelType(enumDefaultValue),
		KillRewardRougeTabCntScheme:    0,
		KillRewardRougeTabCntSchemeRef: nil,
		CommonChallengeReward:          0,
		CommonChallengeRewardRef:       nil,
		EliteChallengeReward:           0,
		EliteChallengeRewardRef:        nil,
		OneStarReward:                  0,
		OneStarRewardRef:               nil,
		TwoStarReward:                  0,
		TwoStarRewardRef:               nil,
		ThreeStarReward:                0,
		ThreeStarRewardRef:             nil,
		ExtraReward:                    []*RewardKVS{},
	}
}

type MainLevelTable struct {
	records  map[int32]*MainLevelTableCfg
	localIds map[int32]struct{}
}

func NewMainLevelTable() *MainLevelTable {
	return &MainLevelTable{
		records:  map[int32]*MainLevelTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *MainLevelTable) Get(key int32) *MainLevelTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *MainLevelTable) GetAll() map[int32]*MainLevelTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *MainLevelTable) put(key int32, value *MainLevelTableCfg, local bool) *MainLevelTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *MainLevelTable) Range(f func(v *MainLevelTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *MainLevelTable) Filter(filterFuncs ...func(v *MainLevelTableCfg) bool) map[int32]*MainLevelTableCfg {
	filtered := map[int32]*MainLevelTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *MainLevelTable) FilterSlice(filterFuncs ...func(v *MainLevelTableCfg) bool) []*MainLevelTableCfg {
	filtered := []*MainLevelTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *MainLevelTable) FilterKeys(filterFuncs ...func(v *MainLevelTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *MainLevelTable) satisfied(v *MainLevelTableCfg, filterFuncs ...func(v *MainLevelTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *MainLevelTable) setupIndexes() error {
	return nil
}

func (t *MainLevelTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *MainLevelTableCfg) bindRefs(c *Configs) {
	r.ChapterLevelRef = c.ChapterLevelTable.Get(r.ChapterLevel)
	r.KillRewardRougeTabCntSchemeRef = c.MainLevelRogueRewardWeightTable.Get(r.KillRewardRougeTabCntScheme)
	r.CommonChallengeRewardRef = c.MainLevelRewardTable.Get(r.CommonChallengeReward)
	r.EliteChallengeRewardRef = c.MainLevelRewardTable.Get(r.EliteChallengeReward)
	r.OneStarRewardRef = c.MainLevelPassRewardTable.Get(r.OneStarReward)
	r.TwoStarRewardRef = c.MainLevelPassRewardTable.Get(r.TwoStarReward)
	r.ThreeStarRewardRef = c.MainLevelPassRewardTable.Get(r.ThreeStarReward)
	for _, e := range r.ExtraReward {
		e.bindRefs(c)
	}
}
