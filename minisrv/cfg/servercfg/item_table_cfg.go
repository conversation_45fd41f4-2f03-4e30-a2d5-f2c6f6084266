// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type ItemTableCfg struct {
	Id                  int32                     `json:"Id"`                  // Id
	StringId            string                    `json:"StringId"`            // StringId
	Type                ItemType                  `json:"Type"`                // 类型
	Value               int32                     `json:"Value"`               // 值
	Benefits            []int32                   `json:"Benefits"`            // 对应增益
	BenefitsRef         []*BenefitsTableCfg       `json:"-"`                   // 对应增益
	BenefitsValue       []float32                 `json:"BenefitsValue"`       // 增益值
	Quality             int32                     `json:"Quality"`             // 道具品质
	QualityRef          *ItemQualityTableCfg      `json:"-"`                   // 道具品质
	AutoUse             bool                      `json:"AutoUse"`             // 获得自动使用
	RepeatAutoTransform bool                      `json:"RepeatAutoTransform"` // 重复自动转化
	ChestDropGroup      int32                     `json:"ChestDropGroup"`      // 随机箱对应的掉落组
	ChestDropGroupRef   *DropGroupTableCfg        `json:"-"`                   // 随机箱对应的掉落组
	ChestSelect         int32                     `json:"ChestSelect"`         // 自选箱
	ChestSelectRef      *SelectChestGroupTableCfg `json:"-"`                   // 自选箱
	MaxUseCnt           int32                     `json:"MaxUseCnt"`           // 单次最大使用数量
	IsDiamond           bool                      `json:"IsDiamond"`           // 是否直接跳转商城
	DiamondExchange     bool                      `json:"DiamondExchange"`     // 是否可以用钻石兑换
	DiamondCnt          int32                     `json:"DiamondCnt"`          // 钻石数量
	ShowSource          bool                      `json:"ShowSource"`          // 是否显示道具获取来源
	ItemSource          []int32                   `json:"ItemSource"`          // 道具来源
	ItemSourceRef       []*ItemSourceTableCfg     `json:"-"`                   // 道具来源
	InBag               bool                      `json:"InBag"`               // 是否进背包
	BagType             BagType                   `json:"BagType"`             // 背包类型
}

func NewItemTableCfg() *ItemTableCfg {
	return &ItemTableCfg{
		Id:                  0,
		StringId:            "",
		Type:                ItemType(enumDefaultValue),
		Value:               0,
		Benefits:            []int32{},
		BenefitsRef:         []*BenefitsTableCfg{},
		BenefitsValue:       []float32{},
		Quality:             0,
		QualityRef:          nil,
		AutoUse:             false,
		RepeatAutoTransform: false,
		ChestDropGroup:      0,
		ChestDropGroupRef:   nil,
		ChestSelect:         0,
		ChestSelectRef:      nil,
		MaxUseCnt:           0,
		IsDiamond:           false,
		DiamondExchange:     false,
		DiamondCnt:          0,
		ShowSource:          false,
		ItemSource:          []int32{},
		ItemSourceRef:       []*ItemSourceTableCfg{},
		InBag:               false,
		BagType:             BagType(enumDefaultValue),
	}
}

type ItemTable struct {
	records  map[int32]*ItemTableCfg
	localIds map[int32]struct{}
}

func NewItemTable() *ItemTable {
	return &ItemTable{
		records:  map[int32]*ItemTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *ItemTable) Get(key int32) *ItemTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *ItemTable) GetAll() map[int32]*ItemTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *ItemTable) put(key int32, value *ItemTableCfg, local bool) *ItemTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *ItemTable) Range(f func(v *ItemTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *ItemTable) Filter(filterFuncs ...func(v *ItemTableCfg) bool) map[int32]*ItemTableCfg {
	filtered := map[int32]*ItemTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *ItemTable) FilterSlice(filterFuncs ...func(v *ItemTableCfg) bool) []*ItemTableCfg {
	filtered := []*ItemTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *ItemTable) FilterKeys(filterFuncs ...func(v *ItemTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *ItemTable) satisfied(v *ItemTableCfg, filterFuncs ...func(v *ItemTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *ItemTable) setupIndexes() error {
	return nil
}

func (t *ItemTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *ItemTableCfg) bindRefs(c *Configs) {
	for _, e := range r.Benefits {
		cfgoRefRecord := c.BenefitsTable.Get(e)
		r.BenefitsRef = append(r.BenefitsRef, cfgoRefRecord)
	}
	r.QualityRef = c.ItemQualityTable.Get(r.Quality)
	r.ChestDropGroupRef = c.DropGroupTable.Get(r.ChestDropGroup)
	r.ChestSelectRef = c.SelectChestGroupTable.Get(r.ChestSelect)
	for _, e := range r.ItemSource {
		cfgoRefRecord := c.ItemSourceTable.Get(e)
		r.ItemSourceRef = append(r.ItemSourceRef, cfgoRefRecord)
	}
}
