package api

import (
	"encoding/json"
	"github.com/julienschmidt/httprouter"
	"net/http"
)

// RegisterRoutes 注册客户端API路由
func RegisterRoutes(router *httprouter.Router) {
	// 版本相关API
	router.POST("/api/version/check", handleVersionCheck)
	router.GET("/api/version/info", handleVersionInfo)

	// 配置相关API
	router.GET("/api/config/client", handleClientConfig)

	// 部署相关API (从原来的httpcmd迁移过来，支持function_open格式)
	router.POST("/api/function_open", handleFunctionOpen)

	// 兼容原有的端点
	router.POST("/cmd/function_open", handleFunctionOpen)
	router.POST("/cmd/uploadVersionDiff2", handleUploadVersionDiff2)
	router.POST("/cmd/uploadBundle2", handleUploadBundle2)
	router.POST("/cmd/generateAppVersion", handleGenerateAppVersion)
}

// 通用JSON响应函数
func jsonResponse(w http.ResponseWriter, statusCode int, message string, data interface{}) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := map[string]interface{}{
		"code":    statusCode,
		"message": message,
	}

	if data != nil {
		response["data"] = data
	}

	json.NewEncoder(w).Encode(response)
}

// handleVersionCheck 处理版本检查请求
func handleVersionCheck(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	var req struct {
		Platform      string `json:"platform"`
		ClientVersion string `json:"client_version"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		jsonResponse(w, http.StatusBadRequest, "Invalid request body", nil)
		return
	}

	if req.Platform == "" || req.ClientVersion == "" {
		jsonResponse(w, http.StatusBadRequest, "Platform and client_version are required", nil)
		return
	}

	// TODO: 实现版本检查逻辑
	response := map[string]interface{}{
		"need_update":    false,
		"latest_version": req.ClientVersion,
		"download_url":   "",
		"force_update":   false,
	}

	jsonResponse(w, http.StatusOK, "Version check completed", response)
}

// handleVersionInfo 处理版本信息请求
func handleVersionInfo(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	platform := r.URL.Query().Get("platform")

	if platform == "" {
		jsonResponse(w, http.StatusBadRequest, "Platform parameter is required", nil)
		return
	}

	// TODO: 从数据库获取版本信息
	response := map[string]interface{}{
		"platform":        platform,
		"current_version": "1.0.0",
		"latest_version":  "1.0.0",
		"update_required": false,
	}

	jsonResponse(w, http.StatusOK, "Version info retrieved", response)
}

// handleClientConfig 处理客户端配置请求
func handleClientConfig(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	// TODO: 从配置系统获取客户端配置
	config := map[string]interface{}{
		"server_url":    "http://127.0.0.1:10013",
		"cdn_url":       "https://cdn.example.com",
		"debug_enabled": false,
		"log_level":     "info",
		"feature_flags": map[string]bool{
			"new_ui":        true,
			"beta_features": false,
		},
	}

	jsonResponse(w, http.StatusOK, "Client config retrieved", config)
}
