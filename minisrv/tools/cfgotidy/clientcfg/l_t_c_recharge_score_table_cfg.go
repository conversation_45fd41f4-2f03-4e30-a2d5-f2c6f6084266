// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type LTCRechargeScoreTableCfg struct {
	Id       int32   `json:"Id"`       // Id
	StringId string  `json:"StringId"` // StringId
	Score    int32   `json:"Score"`    // 积分数
	Usd      float32 `json:"Usd"`      // 多少美元
	Cny      float32 `json:"Cny"`      // 多少人民币
	Krw      float32 `json:"Krw"`      // 多少韩元
}

func NewLTCRechargeScoreTableCfg() *LTCRechargeScoreTableCfg {
	return &LTCRechargeScoreTableCfg{
		Id:       0,
		StringId: "",
		Score:    0,
		Usd:      0.0,
		Cny:      0.0,
		Krw:      0.0,
	}
}

func NewMockLTCRechargeScoreTableCfg() *LTCRechargeScoreTableCfg {
	return &LTCRechargeScoreTableCfg{
		Id:       0,
		StringId: "",
		Score:    0,
		Usd:      0.0,
		Cny:      0.0,
		Krw:      0.0,
	}
}

type LTCRechargeScoreTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*LTCRechargeScoreTableCfg
	localIds         map[int32]struct{}
}

func NewLTCRechargeScoreTable(configs *Configs) *LTCRechargeScoreTable {
	return &LTCRechargeScoreTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*LTCRechargeScoreTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *LTCRechargeScoreTable) Get(key int32) *LTCRechargeScoreTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *LTCRechargeScoreTable) GetAll() map[int32]*LTCRechargeScoreTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *LTCRechargeScoreTable) put(key int32, value *LTCRechargeScoreTableCfg, local bool) *LTCRechargeScoreTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *LTCRechargeScoreTable) putFromInheritedTable(key int32, value *LTCRechargeScoreTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[LTCRechargeScoreTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *LTCRechargeScoreTable) Put(key int32, value *LTCRechargeScoreTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[LTCRechargeScoreTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *LTCRechargeScoreTable) PutAll(m map[int32]*LTCRechargeScoreTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *LTCRechargeScoreTable) Range(f func(v *LTCRechargeScoreTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *LTCRechargeScoreTable) Filter(filterFuncs ...func(v *LTCRechargeScoreTableCfg) bool) map[int32]*LTCRechargeScoreTableCfg {
	filtered := map[int32]*LTCRechargeScoreTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *LTCRechargeScoreTable) FilterSlice(filterFuncs ...func(v *LTCRechargeScoreTableCfg) bool) []*LTCRechargeScoreTableCfg {
	filtered := []*LTCRechargeScoreTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *LTCRechargeScoreTable) FilterKeys(filterFuncs ...func(v *LTCRechargeScoreTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *LTCRechargeScoreTable) satisfied(v *LTCRechargeScoreTableCfg, filterFuncs ...func(v *LTCRechargeScoreTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *LTCRechargeScoreTable) setupIndexes() error {
	return nil
}

func (t *LTCRechargeScoreTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *LTCRechargeScoreTableCfg) bindRefs(c *Configs) {
}

func (t *LTCRechargeScoreTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[LTCRechargeScoreTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewLTCRechargeScoreTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LTCRechargeScoreTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[LTCRechargeScoreTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// Score
		{
			if record[t.getIndexInCsv("Score")] == "" {
				recordCfg.Score = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Score")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LTCRechargeScoreTable]unmarshal csv record failed, varName=Score, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Score")], err)
					} else {
						return fmt.Errorf("[LTCRechargeScoreTable]unmarshal csv record failed, varName=Score, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Score")], err)
					}
				}
				recordCfg.Score = int32(cfgoInt)
			}
		}
		// Usd
		{
			if record[t.getIndexInCsv("Usd")] == "" {
				recordCfg.Usd = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("Usd")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LTCRechargeScoreTable]unmarshal csv record failed, varName=Usd, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("Usd")], err)
					} else {
						return fmt.Errorf("[LTCRechargeScoreTable]unmarshal csv record failed, varName=Usd, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("Usd")], err)
					}
				}
				recordCfg.Usd = float32(cfgoFloat)
			}
		}
		// Cny
		{
			if record[t.getIndexInCsv("Cny")] == "" {
				recordCfg.Cny = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("Cny")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LTCRechargeScoreTable]unmarshal csv record failed, varName=Cny, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("Cny")], err)
					} else {
						return fmt.Errorf("[LTCRechargeScoreTable]unmarshal csv record failed, varName=Cny, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("Cny")], err)
					}
				}
				recordCfg.Cny = float32(cfgoFloat)
			}
		}
		// Krw
		{
			if record[t.getIndexInCsv("Krw")] == "" {
				recordCfg.Krw = 0
			} else {
				cfgoFloat, err := strconv.ParseFloat(record[t.getIndexInCsv("Krw")], 32)
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [LTCRechargeScoreTable]unmarshal csv record failed, varName=Krw, type=float, value=%s, err:[%s]\n", record[t.getIndexInCsv("Krw")], err)
					} else {
						return fmt.Errorf("[LTCRechargeScoreTable]unmarshal csv record failed, varName=Krw, type=float, value=%s, err:[%s]", record[t.getIndexInCsv("Krw")], err)
					}
				}
				recordCfg.Krw = float32(cfgoFloat)
			}
		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [LTCRechargeScoreTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[LTCRechargeScoreTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *LTCRechargeScoreTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "LTCRechargeScoreTable.csv") && (!strings.HasPrefix(fileName, "LTCRechargeScoreTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for LTCRechargeScoreTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[LTCRechargeScoreTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[LTCRechargeScoreTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[LTCRechargeScoreTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[LTCRechargeScoreTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[LTCRechargeScoreTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[LTCRechargeScoreTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[LTCRechargeScoreTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[LTCRechargeScoreTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [LTCRechargeScoreTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *LTCRechargeScoreTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[LTCRechargeScoreTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [LTCRechargeScoreTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *LTCRechargeScoreTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[LTCRechargeScoreTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *LTCRechargeScoreTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[LTCRechargeScoreTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[LTCRechargeScoreTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
