<!DOCTYPE html>
<html>
<head>
    <title>道具管理 - GM管理后台</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 200px;
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
        }

        .sidebar .header {
            padding: 0 20px 20px 20px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar .menu {
            margin-top: 20px;
        }

        .sidebar .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .sidebar .menu-item:hover {
            background-color: #34495e;
        }

        .sidebar .menu-item.active {
            background-color: #3498db;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .top-bar {
            background-color: white;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .panel {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .logout-btn {
            padding: 8px 15px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .logout-btn:hover {
            background-color: #c0392b;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }

        .edit-btn {
            background-color: #3498db;
            color: white;
        }

        .delete-btn {
            background-color: #e74c3c;
            color: white;
        }

        .add-btn {
            background-color: #27ae60;
            color: white;
        }

        .search-container {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .search-btn {
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 4px;
            width: 80%;
            max-width: 600px;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: #f5f5f5;
        }

        .pagination button.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .table-actions {
            display: flex;
            gap: 5px;
        }

        .item-type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .type-weapon { background-color: #e74c3c; color: white; }
        .type-armor { background-color: #3498db; color: white; }
        .type-consumable { background-color: #27ae60; color: white; }
        .type-material { background-color: #f39c12; color: white; }
        .type-other { background-color: #95a5a6; color: white; }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="header">
            <h2>GM管理后台</h2>
        </div>
        <div class="menu">
            <div class="menu-item" onclick="window.location.href='/gm/'">数据概览</div>
            <div class="menu-item" onclick="window.location.href='/gm/country'">国家限制</div>
            <div class="menu-item" onclick="window.location.href='/gm/users'">用户管理</div>
            <div class="menu-item active">道具管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/mail'">邮件系统</div>
            <div class="menu-item" onclick="window.location.href='/gm/announce'">公告管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/activity'">活动管理</div>
            <div class="menu-item">
                版本配置
                <div class="submenu">
                    <div class="submenu-item" onclick="window.location.href='/gm/version'">客户端版本管理</div>
                    <div class="submenu-item" onclick="window.location.href='/gm/huatuo_release'">华佗发版</div>
                </div>
            </div>
            <div class="menu-item" onclick="window.location.href='/gm/logs'">系统日志</div>
        </div>
    </div>

    <div class="main-content">
        <div class="top-bar">
            <h1 id="pageTitle">道具管理</h1>
            <div>
                <button id="backToUsersBtn" class="action-btn" style="background-color: #95a5a6; color: white; margin-right: 10px; display: none;" onclick="backToUsers()">返回用户列表</button>
                <button class="logout-btn" onclick="logout()">退出登录</button>
            </div>
        </div>

        <div class="content">
            <div class="panel">
                <div class="search-container">
                    <input type="text" id="itemSearchInput" class="search-input" placeholder="输入道具ID或名称搜索">
                    <button onclick="searchItems()" class="search-btn">搜索</button>
                    <button onclick="clearSearch()" class="search-btn" style="background-color: #95a5a6;">清除</button>
                    <button onclick="showAddItemDialog()" class="action-btn add-btn">添加道具</button>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>道具ID</th>
                            <th>名称</th>
                            <th>类型</th>
                            <th>描述</th>
                            <th>数量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="items-table-body">
                        <!-- 道具数据将在这里动态加载 -->
                    </tbody>
                </table>

                <!-- 分页控件 -->
                <div class="pagination">
                    <button id="prevPage" onclick="changePage(-1)">上一页</button>
                    <span id="pageInfo">第 1 页，共 1 页</span>
                    <button id="nextPage" onclick="changePage(1)">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑道具弹窗 -->
    <div id="itemModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加道具</h3>
                <span class="close" onclick="closeItemModal()">&times;</span>
            </div>
            <div id="modalBody">
                <div class="form-group">
                    <label>道具ID:</label>
                    <input type="number" id="editItemId" placeholder="道具ID">
                </div>
                <div class="form-group">
                    <label>道具名称:</label>
                    <input type="text" id="editItemName" placeholder="道具名称">
                </div>
                <div class="form-group">
                    <label>道具类型:</label>
                    <select id="editItemType">
                        <option value="weapon">武器</option>
                        <option value="armor">装备</option>
                        <option value="consumable">消耗品</option>
                        <option value="material">材料</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>道具描述:</label>
                    <textarea id="editItemDescription" rows="3" placeholder="道具描述"></textarea>
                </div>
                <div class="form-group">
                    <label>初始数量:</label>
                    <input type="number" id="editItemQuantity" placeholder="数量" min="0">
                </div>
                <div class="form-group">
                    <button onclick="saveItemChanges()" class="action-btn edit-btn">保存</button>
                    <button onclick="closeItemModal()" class="action-btn" style="background-color: #95a5a6; color: white;">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 道具详情弹窗 -->
    <div id="itemDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>道具详情</h3>
                <span class="close" onclick="closeItemDetailModal()">&times;</span>
            </div>
            <div id="itemDetailBody">
                <!-- 道具详情内容将在这里动态加载 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 1;
        let searchTerm = '';
        let isEditMode = false;
        let currentUserId = null; // 当前查看的用户ID
        let currentUserName = ''; // 当前查看的用户名

        // 页面加载时的初始化
        window.onload = function() {
            const token = localStorage.getItem('gmToken');
            if (!token) {
                window.location.href = '/gm/login';
                return;
            }

            // 解析URL参数
            const urlParams = new URLSearchParams(window.location.search);
            currentUserId = urlParams.get('uid');

            // 验证token
            fetch('/gm/api/verify-token', {
                headers: {
                    'X-GM-Token': token
                }
            })
            .then(response => {
                if (!response.ok) {
                    localStorage.removeItem('gmToken');
                    window.location.href = '/gm/login';
                    return;
                }
                // Token有效，初始化页面
                initializePage();
            })
            .catch(error => {
                console.error('Token验证失败:', error);
                localStorage.removeItem('gmToken');
                window.location.href = '/gm/login';
            });
        }

        function initializePage() {
            if (currentUserId) {
                // 显示特定用户的道具
                setupUserItemsView();
            } else {
                // 显示所有道具（管理员模式）
                setupAdminItemsView();
            }
            loadItems();
        }

        function setupUserItemsView() {
            // 设置用户道具查看模式
            document.getElementById('pageTitle').textContent = `用户道具 (ID: ${currentUserId})`;
            document.getElementById('backToUsersBtn').style.display = 'inline-block';

            // 隐藏添加道具按钮（用户模式下不允许添加）
            const addBtn = document.querySelector('.add-btn');
            if (addBtn) {
                addBtn.style.display = 'none';
            }

            // 获取用户信息
            fetchUserInfo();
        }

        function setupAdminItemsView() {
            // 设置管理员道具管理模式
            document.getElementById('pageTitle').textContent = '道具管理';
            document.getElementById('backToUsersBtn').style.display = 'none';
        }

        function fetchUserInfo() {
            if (!currentUserId) return;

            fetch(`/gm/api/users/${currentUserId}`, {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data && data.data) {
                    currentUserName = data.data.name;
                    document.getElementById('pageTitle').textContent = `${currentUserName} 的道具 (ID: ${currentUserId})`;
                }
            })
            .catch(error => {
                console.error('Error fetching user info:', error);
            });
        }

        function backToUsers() {
            window.location.href = '/gm/users';
        }

        function searchItems() {
            searchTerm = document.getElementById('itemSearchInput').value.trim();
            currentPage = 1;
            loadItems();
        }

        function clearSearch() {
            document.getElementById('itemSearchInput').value = '';
            searchTerm = '';
            currentPage = 1;
            loadItems();
        }

        function loadItems() {
            const requestBody = {
                page: currentPage,
                pageSize: pageSize,
                search: searchTerm
            };

            // 如果有用户ID，添加到请求中
            if (currentUserId) {
                requestBody.uid = currentUserId;
            }

            fetch('/gm/api/items', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify(requestBody)
            })
            .then(response => response.json())
            .then(data => {
                console.log('API Response:', data);
                const tbody = document.getElementById('items-table-body');
                tbody.innerHTML = '';

                if (data && data.data && data.data.items) {
                    data.data.items.forEach(item => {
                        const tr = document.createElement('tr');
                        const typeClass = `type-${item.type || 'other'}`;
                        const typeName = getTypeName(item.type);

                        // 根据是否为用户模式显示不同的操作按钮
                        const actionButtons = currentUserId ?
                            `<button class="action-btn edit-btn" onclick="editItem(${item.itemId || item.id})">编辑</button>
                             <button class="action-btn delete-btn" onclick="deleteItem(${item.itemId || item.id})">删除</button>
                             <button class="action-btn" style="background-color: #f39c12; color: white;" onclick="viewItemDetails(${item.itemId || item.id})">详情</button>` :
                            `<button class="action-btn" style="background-color: #f39c12; color: white;" onclick="viewItemDetails(${item.itemId || item.id})">详情</button>`;

                        tr.innerHTML = `
                            <td>${item.itemId || item.id}</td>
                            <td>${item.name}</td>
                            <td><span class="item-type-badge ${typeClass}">${typeName}</span></td>
                            <td>${item.description || '暂无描述'}</td>
                            <td>${item.quantity || 0}</td>
                            <td class="table-actions">
                                ${actionButtons}
                            </td>
                        `;
                        tbody.appendChild(tr);
                    });

                    // 更新分页信息
                    updatePagination(data.data);
                } else {
                    // 显示示例数据
                    showSampleData();
                }
            })
            .catch(error => {
                console.error('Error loading items:', error);
                // 显示示例数据作为fallback
                showSampleData();
            });
        }

        function showSampleData() {
            const tbody = document.getElementById('items-table-body');
            tbody.innerHTML = '';

            const sampleItems = [
                { id: 1001, name: '铁剑', type: 'weapon', description: '普通的铁制剑', quantity: 50 },
                { id: 1002, name: '皮甲', type: 'armor', description: '基础防护装备', quantity: 30 },
                { id: 1003, name: '生命药水', type: 'consumable', description: '恢复100点生命值', quantity: 100 },
                { id: 1004, name: '铁矿石', type: 'material', description: '制作武器的材料', quantity: 200 },
                { id: 1005, name: '神秘宝石', type: 'other', description: '用途未明的宝石', quantity: 5 }
            ];

            sampleItems.forEach(item => {
                const tr = document.createElement('tr');
                const typeClass = `type-${item.type}`;
                const typeName = getTypeName(item.type);

                // 根据是否为用户模式显示不同的操作按钮
                const actionButtons = currentUserId ?
                    `<button class="action-btn" style="background-color: #f39c12; color: white;" onclick="viewItemDetails(${item.id})">详情</button>` :
                    `<button class="action-btn edit-btn" onclick="editItem(${item.id})">编辑</button>
                     <button class="action-btn delete-btn" onclick="deleteItem(${item.id})">删除</button>
                     <button class="action-btn" style="background-color: #f39c12; color: white;" onclick="viewItemDetails(${item.id})">详情</button>`;

                tr.innerHTML = `
                    <td>${item.id}</td>
                    <td>${item.name}</td>
                    <td><span class="item-type-badge ${typeClass}">${typeName}</span></td>
                    <td>${item.description}</td>
                    <td>${item.quantity}</td>
                    <td class="table-actions">
                        ${actionButtons}
                    </td>
                `;
                tbody.appendChild(tr);
            });

            // 更新分页信息
            updatePagination({ total: sampleItems.length, page: 1, pageSize: 10 });
        }

        function getTypeName(type) {
            const typeMap = {
                'weapon': '武器',
                'armor': '装备',
                'consumable': '消耗品',
                'material': '材料',
                'other': '其他'
            };
            return typeMap[type] || '其他';
        }

        function updatePagination(data) {
            const total = data.total || 0;
            totalPages = Math.ceil(total / pageSize);

            document.getElementById('pageInfo').textContent = `第 ${currentPage} 页，共 ${totalPages} 页 (总计 ${total} 个道具)`;
            document.getElementById('prevPage').disabled = currentPage <= 1;
            document.getElementById('nextPage').disabled = currentPage >= totalPages;
        }

        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                loadItems();
            }
        }

        function showAddItemDialog() {
            isEditMode = false;
            document.getElementById('modalTitle').textContent = '添加道具';
            document.getElementById('editItemId').value = '';
            document.getElementById('editItemName').value = '';
            document.getElementById('editItemType').value = 'other';
            document.getElementById('editItemDescription').value = '';
            document.getElementById('editItemQuantity').value = '1';
            document.getElementById('editItemId').disabled = false;
            document.getElementById('itemModal').style.display = 'block';
        }

        function editItem(itemId) {
            isEditMode = true;
            document.getElementById('modalTitle').textContent = '编辑道具';

            // 获取道具详情
            fetch(`/gm/api/items/${itemId}`, {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data && data.data) {
                    const item = data.data;
                    document.getElementById('editItemId').value = item.itemId || item.id;
                    document.getElementById('editItemName').value = item.name;
                    document.getElementById('editItemType').value = item.type || 'other';
                    document.getElementById('editItemDescription').value = item.description || '';
                    document.getElementById('editItemQuantity').value = item.quantity || 0;
                    document.getElementById('editItemId').disabled = true;
                    document.getElementById('itemModal').style.display = 'block';
                } else {
                    alert('获取道具信息失败');
                }
            })
            .catch(error => {
                console.error('Error fetching item details:', error);
                // 使用模拟数据
                document.getElementById('editItemId').value = itemId;
                document.getElementById('editItemName').value = 'Sample Item ' + itemId;
                document.getElementById('editItemType').value = 'other';
                document.getElementById('editItemDescription').value = '示例道具描述';
                document.getElementById('editItemQuantity').value = 1;
                document.getElementById('editItemId').disabled = true;
                document.getElementById('itemModal').style.display = 'block';
            });
        }

        function viewItemDetails(itemId) {
            fetch(`/gm/api/items/${itemId}`, {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data && data.data) {
                    const item = data.data;
                    showItemDetailModal(item);
                } else {
                    // 使用模拟数据
                    const mockItem = {
                        id: itemId,
                        name: 'Sample Item ' + itemId,
                        type: 'other',
                        description: '这是一个示例道具',
                        quantity: 1,
                        createTime: new Date().toISOString(),
                        updateTime: new Date().toISOString()
                    };
                    showItemDetailModal(mockItem);
                }
            })
            .catch(error => {
                console.error('Error fetching item details:', error);
                alert('获取道具详情失败');
            });
        }

        function showItemDetailModal(item) {
            const detailBody = document.getElementById('itemDetailBody');
            const typeName = getTypeName(item.type);

            detailBody.innerHTML = `
                <div class="form-group">
                    <label>道具ID:</label>
                    <p>${item.itemId || item.id}</p>
                </div>
                <div class="form-group">
                    <label>道具名称:</label>
                    <p>${item.name}</p>
                </div>
                <div class="form-group">
                    <label>道具类型:</label>
                    <p><span class="item-type-badge type-${item.type || 'other'}">${typeName}</span></p>
                </div>
                <div class="form-group">
                    <label>道具描述:</label>
                    <p>${item.description || '暂无描述'}</p>
                </div>
                <div class="form-group">
                    <label>当前数量:</label>
                    <p>${item.quantity || 0}</p>
                </div>
                <div class="form-group">
                    <label>创建时间:</label>
                    <p>${item.createTime ? new Date(item.createTime).toLocaleString() : '未知'}</p>
                </div>
                <div class="form-group">
                    <label>更新时间:</label>
                    <p>${item.updateTime ? new Date(item.updateTime).toLocaleString() : '未知'}</p>
                </div>
            `;

            document.getElementById('itemDetailModal').style.display = 'block';
        }

        function deleteItem(itemId) {
            if (confirm('确定要删除这个道具吗？此操作不可撤销。')) {
                fetch('/gm/api/items/delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-GM-Token': localStorage.getItem('gmToken')
                    },
                    body: JSON.stringify({
                        itemId: itemId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data && data.code === 200) {
                        alert('道具已删除');
                        loadItems();
                    } else {
                        alert('删除失败: ' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error deleting item:', error);
                    alert('删除失败，请检查网络连接');
                });
            }
        }

        function saveItemChanges() {
            const itemData = {
                itemId: parseInt(document.getElementById('editItemId').value),
                name: document.getElementById('editItemName').value,
                type: document.getElementById('editItemType').value,
                description: document.getElementById('editItemDescription').value,
                quantity: parseInt(document.getElementById('editItemQuantity').value)
            };

            // 验证必填字段
            if (!itemData.name) {
                alert('请填写道具名称');
                return;
            }

            if (!itemData.itemId && !isEditMode) {
                alert('请填写道具ID');
                return;
            }

            const url = isEditMode ? '/gm/api/items/update' : '/gm/api/items/add';

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify(itemData)
            })
            .then(response => response.json())
            .then(data => {
                if (data && data.code === 200) {
                    alert(isEditMode ? '道具信息已更新' : '道具已添加');
                    closeItemModal();
                    loadItems();
                } else {
                    alert('操作失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error saving item:', error);
                alert('操作失败，请检查网络连接');
            });
        }

        function closeItemModal() {
            document.getElementById('itemModal').style.display = 'none';
        }

        function closeItemDetailModal() {
            document.getElementById('itemDetailModal').style.display = 'none';
        }

        function logout() {
            localStorage.removeItem('gmToken');
            window.location.href = '/gm/login';
        }

        // 点击弹窗外部关闭弹窗
        window.onclick = function(event) {
            const itemModal = document.getElementById('itemModal');
            const detailModal = document.getElementById('itemDetailModal');

            if (event.target === itemModal) {
                closeItemModal();
            }
            if (event.target === detailModal) {
                closeItemDetailModal();
            }
        }
    </script>
</body>
</html>
