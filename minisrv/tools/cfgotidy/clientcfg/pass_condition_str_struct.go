// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

type PassConditionStr struct {
	PassType PassType `json:"PassType"` // 任务类型
	Formula  string   `json:"Formula"`  // 达成条件
	Value    int32    `json:"Value"`    // 值
	Desc     string   `json:"Desc"`     // 任务描述
	Param    string   `json:"Param"`    // 描述参数
}

func NewPassConditionStr() *PassConditionStr {
	return &PassConditionStr{
		PassType: PassType(enumDefaultValue),
		Formula:  "",
		Value:    0,
		Desc:     "",
		Param:    "",
	}
}

func NewMockPassConditionStr() *PassConditionStr {
	return &PassConditionStr{
		PassType: PassType(enumDefaultValue),
		Formula:  "",
		Value:    0,
		Desc:     "",
		Param:    "",
	}
}

func (s *PassConditionStr) bindRefs(c *Configs) {
}
