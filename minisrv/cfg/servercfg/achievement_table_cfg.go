// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type AchievementTableCfg struct {
	Id              int32           `json:"Id"`              // Id
	Group           int32           `json:"Group"`           // 成就组（同组只会展示1个）
	Sub             int32           `json:"Sub"`             // 成就组内排序（从小到大）
	TaskType        TaskType        `json:"TaskType"`        // 任务类型
	TaskCounterType TaskCounterType `json:"TaskCounterType"` // 任务计数类型
	Formula         string          `json:"Formula"`         // 达成条件
	Value           int32           `json:"Value"`           // 值
	Reward          []*RewardKVS    `json:"Reward"`          // 奖励
}

func NewAchievementTableCfg() *AchievementTableCfg {
	return &AchievementTableCfg{
		Id:              0,
		Group:           0,
		Sub:             0,
		TaskType:        TaskType(enumDefaultValue),
		TaskCounterType: TaskCounterType(enumDefaultValue),
		Formula:         "",
		Value:           0,
		Reward:          []*RewardKVS{},
	}
}

type AchievementTable struct {
	records  map[int32]*AchievementTableCfg
	localIds map[int32]struct{}
}

func NewAchievementTable() *AchievementTable {
	return &AchievementTable{
		records:  map[int32]*AchievementTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *AchievementTable) Get(key int32) *AchievementTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *AchievementTable) GetAll() map[int32]*AchievementTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *AchievementTable) put(key int32, value *AchievementTableCfg, local bool) *AchievementTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *AchievementTable) Range(f func(v *AchievementTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *AchievementTable) Filter(filterFuncs ...func(v *AchievementTableCfg) bool) map[int32]*AchievementTableCfg {
	filtered := map[int32]*AchievementTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *AchievementTable) FilterSlice(filterFuncs ...func(v *AchievementTableCfg) bool) []*AchievementTableCfg {
	filtered := []*AchievementTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *AchievementTable) FilterKeys(filterFuncs ...func(v *AchievementTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *AchievementTable) satisfied(v *AchievementTableCfg, filterFuncs ...func(v *AchievementTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *AchievementTable) setupIndexes() error {
	return nil
}

func (t *AchievementTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *AchievementTableCfg) bindRefs(c *Configs) {
	for _, e := range r.Reward {
		e.bindRefs(c)
	}
}
