package hotfix

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"context"
	"encoding/json"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
	"net/http"
)

func init() {
	http_post.AddPostHandler("generateAppVersion", generateAppVersion)
	http_post.AddPostHandler("uploadBundle2", uploadBundle2)
	http_post.AddPostHandler("uploadVersionDiff2", uploadVersionDiff2)
	http_post.AddPostHandler("uploadVersions", functionOpen)
}

func generateAppVersion(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	values := r.URL.Query()
	packageVersion := values.Get("version")
	platform := values.Get("platform")
	bvr := values.Get("bvr")
	bvg := values.Get("bvg")
	bundleMd5 := values.Get("bundle_md5")
	preloadMd5 := values.Get("preload_md5")
	configMd5 := values.Get("config_md5")

	if packageVersion == "" || platform == "" {
		http.Error(w, "version and platform are required", http.StatusBadRequest)
		return
	}

	// 添加应用版本到数据库
	if err := addAppVersion(platform, packageVersion, bvr, bvg, bundleMd5, preloadMd5, configMd5); err != nil {
		http.Error(w, "Failed to add app version: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	response := map[string]interface{}{
		"code":    200,
		"message": "App version generated successfully",
		"data": map[string]interface{}{
			"version":  packageVersion,
			"platform": platform,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// addAppVersion 添加应用版本，类似PHP的_addAppVersion方法
func addAppVersion(platform, packageVersion, bvr, bvg, bundleMd5, preloadMd5, configMd5 string) error {
	ctx := context.Background()

	// 检查版本是否已存在
	existingVersion, err := model.GetClientVersionModel(ctx, platform, packageVersion)
	if err != nil {
		return fmt.Errorf("failed to check existing version: %w", err)
	}

	if existingVersion != nil {
		// 更新现有版本
		if bvr != "" {
			existingVersion.SetBundleVersionR(ctx, bvr)
		}
		if bvg != "" {
			existingVersion.SetBundleVersionG(ctx, bvg)
		}
		if bundleMd5 != "" {
			existingVersion.SetBundleMd5(ctx, bundleMd5)
		}
		if preloadMd5 != "" {
			existingVersion.SetPreloadMd5(ctx, preloadMd5)
		}
		if configMd5 != "" {
			existingVersion.SetConfigMd5(ctx, configMd5)
		}
		return nil
	}

	// 创建新版本
	_, err = orm.Create[*model.ClientVersionModel](ctx, &minirpc.ClientVersion{
		Platform:       platform,
		ClientVersion:  packageVersion,
		BundleVersionR: bvr,
		BundleVersionG: bvg,
		BundleMd5:      bundleMd5,
		PreloadMd5:     preloadMd5,
		ConfigMd5:      configMd5,
	})

	if err != nil {
		return fmt.Errorf("failed to create version: %w", err)
	}

	return nil
}

func uploadBundle2(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	// 解析表单数据
	if err := r.ParseForm(); err != nil {
		http.Error(w, "Failed to parse form data", http.StatusBadRequest)
		return
	}

	baseVersion := getParam(r, "version")
	platform := getParam(r, "platform")
	gogCfg := getParam(r, "bundle_cfg_gog")
	gogPath := getParam(r, "bundle_path_gog")
	mcCfg := getParam(r, "bundle_cfg_mc")
	mcPath := getParam(r, "bundle_path_mc")
	jobId := getParam(r, "build_job")

	if baseVersion == "" || platform == "" {
		http.Error(w, "version, platform are required", http.StatusBadRequest)
		return
	}

	versionModel, _ := model.GetHuatuoVersionModel(context.Background(), platform, baseVersion)
	if versionModel == nil {
		versionModel, _ = orm.Create[*model.HuatuoVersionModel](context.Background(), &minirpc.ClientVersion{
			Platform:      platform,
			ClientVersion: baseVersion,
		})
	}
	ctx := context.Background()
	versionModel.SetBundlePathGog(ctx, gogPath)
	versionModel.SetBundleCfgGog(ctx, gogCfg)
	versionModel.SetBundleCfgMc(ctx, mcCfg)
	versionModel.SetBundlePathMc(ctx, mcPath)
	versionModel.SetJobId(ctx, jobId)

	// 返回成功响应
	response := map[string]interface{}{
		"code":    200,
		"message": "Bundle uploaded successfully",
		"data": map[string]interface{}{
			"version":  baseVersion,
			"platform": platform,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func uploadVersionDiff2(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	// 解析表单数据
	if err := r.ParseMultipartForm(32 << 20); err != nil { // 32MB max memory
		if err := r.ParseForm(); err != nil {
			http.Error(w, "Failed to parse form data", http.StatusBadRequest)
			return
		}
	}

	// 从表单或查询参数中获取值
	baseVersion := getParam(r, "base_version")
	targetVersion := getParam(r, "target_version")
	platform := getParam(r, "platform")
	diffData := getParam(r, "data")
	jobId := getParam(r, "build_job")

	if baseVersion == "" || platform == "" {
		http.Error(w, "base_version, platform are required", http.StatusBadRequest)
		return
	}

	var err error
	tasklet.Invoke(r.Context(), "uploadVersionDiff2", func(ctx context.Context) {
		versionModel, _ := model.GetHuatuoVersionModel(ctx, platform, baseVersion)
		if versionModel == nil {
			versionModel, err = orm.Create[*model.HuatuoVersionModel](ctx, &minirpc.HuatuoVersion{
				Platform:      platform,
				ClientVersion: baseVersion,
				TargetVersion: targetVersion,
				Data:          diffData,
				JobId:         jobId,
			})
		} else {
			if targetVersion != "" {
				versionModel.SetTargetVersion(ctx, targetVersion)
			}
			if diffData != "" {
				versionModel.SetData(ctx, diffData)
			}
			if jobId != "" {
				versionModel.SetJobId(ctx, jobId)
			}
		}
	})

	if err != nil {
		http.Error(w, "Failed to upload version diff: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	response := map[string]interface{}{
		"code":    200,
		"message": "Version diff uploaded successfully",
		"data": map[string]interface{}{
			"base_version":   baseVersion,
			"target_version": targetVersion,
			"platform":       platform,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// getParam 从表单或查询参数中获取值
func getParam(r *http.Request, key string) string {
	// 优先从multipart form获取
	if r.MultipartForm != nil {
		if values := r.MultipartForm.Value[key]; len(values) > 0 {
			return values[0]
		}
	}
	// 然后从普通表单数据获取
	if value := r.FormValue(key); value != "" {
		return value
	}
	// 最后从查询参数获取
	return r.URL.Query().Get(key)
}

// FunctionOpenRequest 定义function_open请求的结构
type FunctionOpenRequest struct {
	Class  string                 `json:"class"`
	Method string                 `json:"method"`
	Params map[string]interface{} `json:"params"`
}

// functionOpen 处理function_open格式的请求
func functionOpen(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
	var req FunctionOpenRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON request body", http.StatusBadRequest)
		return
	}

	// 根据method字段路由到相应的处理函数
	switch req.Method {
	case "uploadVersionDiff2":
		handleUploadVersionDiff2FromJSON(w, r, req.Params)
	case "uploadBundle2":
		handleUploadBundle2FromJSON(w, r, req.Params)
	case "generateAppVersion":
		handleGenerateAppVersionFromJSON(w, r, req.Params)
	default:
		http.Error(w, fmt.Sprintf("Unsupported method: %s", req.Method), http.StatusBadRequest)
	}
}

// handleUploadVersionDiff2FromJSON 处理来自JSON的uploadVersionDiff2请求
func handleUploadVersionDiff2FromJSON(w http.ResponseWriter, r *http.Request, params map[string]interface{}) {
	// 从params中提取参数
	platform := getStringParam(params, "platform")
	baseVersion := getStringParam(params, "version") // 注意：JSON中使用"version"而不是"base_version"
	targetVersion := getStringParam(params, "target_version")
	diffData := getStringParam(params, "data")
	jobId := getStringParam(params, "build_job")

	if baseVersion == "" || platform == "" {
		http.Error(w, "version and platform are required", http.StatusBadRequest)
		return
	}

	var err error
	tasklet.Invoke(r.Context(), "uploadVersionDiff2", func(ctx context.Context) {
		versionModel, _ := model.GetHuatuoVersionModel(ctx, platform, baseVersion)
		if versionModel == nil {
			versionModel, err = orm.Create[*model.HuatuoVersionModel](ctx, &minirpc.HuatuoVersion{
				Platform:      platform,
				ClientVersion: baseVersion,
				TargetVersion: targetVersion,
				Data:          diffData,
				JobId:         jobId,
			})
		} else {
			if targetVersion != "" {
				versionModel.SetTargetVersion(ctx, targetVersion)
			}
			if diffData != "" {
				versionModel.SetData(ctx, diffData)
			}
			if jobId != "" {
				versionModel.SetJobId(ctx, jobId)
			}
		}
	})

	if err != nil {
		http.Error(w, "Failed to upload version diff: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	response := map[string]interface{}{
		"code":    200,
		"message": "Version diff uploaded successfully",
		"data": map[string]interface{}{
			"base_version":   baseVersion,
			"target_version": targetVersion,
			"platform":       platform,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleUploadBundle2FromJSON 处理来自JSON的uploadBundle2请求
func handleUploadBundle2FromJSON(w http.ResponseWriter, r *http.Request, params map[string]interface{}) {
	baseVersion := getStringParam(params, "version")
	platform := getStringParam(params, "platform")
	gogCfg := getStringParam(params, "bundle_cfg_gog")
	gogPath := getStringParam(params, "bundle_path_gog")
	mcCfg := getStringParam(params, "bundle_cfg_mc")
	mcPath := getStringParam(params, "bundle_path_mc")
	jobId := getStringParam(params, "build_job")

	if baseVersion == "" || platform == "" {
		http.Error(w, "version, platform are required", http.StatusBadRequest)
		return
	}

	versionModel, _ := model.GetHuatuoVersionModel(context.Background(), platform, baseVersion)
	if versionModel == nil {
		versionModel, _ = orm.Create[*model.HuatuoVersionModel](context.Background(), &minirpc.ClientVersion{
			Platform:      platform,
			ClientVersion: baseVersion,
		})
	}
	ctx := context.Background()
	versionModel.SetBundlePathGog(ctx, gogPath)
	versionModel.SetBundleCfgGog(ctx, gogCfg)
	versionModel.SetBundleCfgMc(ctx, mcCfg)
	versionModel.SetBundlePathMc(ctx, mcPath)
	versionModel.SetJobId(ctx, jobId)

	// 返回成功响应
	response := map[string]interface{}{
		"code":    200,
		"message": "Bundle uploaded successfully",
		"data": map[string]interface{}{
			"version":  baseVersion,
			"platform": platform,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleGenerateAppVersionFromJSON 处理来自JSON的generateAppVersion请求
func handleGenerateAppVersionFromJSON(w http.ResponseWriter, r *http.Request, params map[string]interface{}) {
	packageVersion := getStringParam(params, "version")
	platform := getStringParam(params, "platform")
	bvr := getStringParam(params, "bvr")
	bvg := getStringParam(params, "bvg")
	bundleMd5 := getStringParam(params, "bundle_md5")
	preloadMd5 := getStringParam(params, "preload_md5")
	configMd5 := getStringParam(params, "config_md5")

	if packageVersion == "" || platform == "" {
		http.Error(w, "version and platform are required", http.StatusBadRequest)
		return
	}

	// 添加应用版本到数据库
	if err := addAppVersion(platform, packageVersion, bvr, bvg, bundleMd5, preloadMd5, configMd5); err != nil {
		http.Error(w, "Failed to add app version: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	response := map[string]interface{}{
		"code":    200,
		"message": "App version generated successfully",
		"data": map[string]interface{}{
			"version":  packageVersion,
			"platform": platform,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// getStringParam 从params map中获取字符串参数
func getStringParam(params map[string]interface{}, key string) string {
	if value, ok := params[key]; ok {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}
