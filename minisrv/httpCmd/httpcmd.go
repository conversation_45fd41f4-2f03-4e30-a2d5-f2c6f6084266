package httpcmd

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/gm"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/httpcmd/http_post"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"fmt"
	"github.com/julienschmidt/httprouter"
	"gitlab-ee.funplus.io/mg-server/mgsl/log"
	"net/http"
	"runtime/debug"
	"strconv"
	"strings"
)

var (
	httpSrv  *http.Server
	HttpPort = 0
	router   = httprouter.New()
)

func panicHandler(w http.ResponseWriter, r *http.Request, err interface{}) {
	msg := fmt.Sprintf("<httpcmd> panic: %+v\n%s", err, debug.Stack())
	_, _ = fmt.Fprintf(w, `{"err": %q}`, msg)
	log.Error(msg)
}

func initRouter() {
	router.PanicHandler = panicHandler

	// 注册所有HTTP处理器
	for p, h := range http_post.HandlerMap {
		router.POST(p, h)
		router.GET(p, h)
	}

	// 注册静态文件服务
	router.ServeFiles("/static/*filepath", http.Dir("./static"))

	// 注册GM路由
	gm.RegisterRoutes(router)
}

func ServeHttp(addr string) error {
	httpSrv = &http.Server{Addr: addr, Handler: router}
	log.Infow("<httpcmd> listening on " + addr)

	// convert http listen port from string to int
	port, err := strconv.Atoi(strings.TrimLeft(addr, ":"))
	if err != nil {
		return kdmerr.SysInvalidArguments.WrapError(err)
	}
	HttpPort = port

	// start http server
	if err := httpSrv.ListenAndServe(); err != nil {
		return kdmerr.SysInvalidArguments.WrapError(err)
	}

	return nil
}

func init() {
	initRouter()

	// 注册GM路由
	_ = "minisrv/httpcmd/gm" // 引入GM包以触发init
}
