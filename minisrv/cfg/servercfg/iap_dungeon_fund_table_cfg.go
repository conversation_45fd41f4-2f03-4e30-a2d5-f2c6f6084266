// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type IapDungeonFundTableCfg struct {
	Id              int32               `json:"Id"`           // Id
	StringId        string              `json:"StringId"`     // StringId
	Unlock          int32               `json:"Unlock"`       // 前置功能
	UnlockRef       *FunctionTableCfg   `json:"-"`            // 前置功能
	Type            DungeonType         `json:"Type"`         // 副本类型
	Stage           int32               `json:"Stage"`        // 阶段
	IapPackageId    int32               `json:"IapPackageId"` // 内购商品id
	IapPackageIdRef *IapPackageTableCfg `json:"-"`            // 内购商品id
	Limit           PurchaseLimitType   `json:"Limit"`        // 限购类型
	Times           int32               `json:"Times"`        // 限购次数
}

func NewIapDungeonFundTableCfg() *IapDungeonFundTableCfg {
	return &IapDungeonFundTableCfg{
		Id:              0,
		StringId:        "",
		Unlock:          0,
		UnlockRef:       nil,
		Type:            DungeonType(enumDefaultValue),
		Stage:           0,
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
	}
}

type IapDungeonFundTable struct {
	records  map[int32]*IapDungeonFundTableCfg
	localIds map[int32]struct{}
}

func NewIapDungeonFundTable() *IapDungeonFundTable {
	return &IapDungeonFundTable{
		records:  map[int32]*IapDungeonFundTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *IapDungeonFundTable) Get(key int32) *IapDungeonFundTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapDungeonFundTable) GetAll() map[int32]*IapDungeonFundTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapDungeonFundTable) put(key int32, value *IapDungeonFundTableCfg, local bool) *IapDungeonFundTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapDungeonFundTable) Range(f func(v *IapDungeonFundTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapDungeonFundTable) Filter(filterFuncs ...func(v *IapDungeonFundTableCfg) bool) map[int32]*IapDungeonFundTableCfg {
	filtered := map[int32]*IapDungeonFundTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapDungeonFundTable) FilterSlice(filterFuncs ...func(v *IapDungeonFundTableCfg) bool) []*IapDungeonFundTableCfg {
	filtered := []*IapDungeonFundTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapDungeonFundTable) FilterKeys(filterFuncs ...func(v *IapDungeonFundTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapDungeonFundTable) satisfied(v *IapDungeonFundTableCfg, filterFuncs ...func(v *IapDungeonFundTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapDungeonFundTable) setupIndexes() error {
	return nil
}

func (t *IapDungeonFundTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapDungeonFundTableCfg) bindRefs(c *Configs) {
	r.UnlockRef = c.FunctionTable.Get(r.Unlock)
	r.IapPackageIdRef = c.IapPackageTable.Get(r.IapPackageId)
}
